package com.weihengtech.ecos.containers;

import com.weihengtech.ecos.handlers.ICustomProcessHandler;
import com.weihengtech.ecos.interfaces.IAroundProcessHandler;
import com.weihengtech.ecos.interfaces.IGlobalProcessHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ProcessHandlerContainer {

	private List<IGlobalProcessHandler> globalProcessHandlerList;

	private List<IAroundProcessHandler> aroundProcessHandlerList;

	private List<ICustomProcessHandler> customProcessHandlerList;
}
