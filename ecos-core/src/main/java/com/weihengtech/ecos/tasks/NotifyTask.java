package com.weihengtech.ecos.tasks;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.InsightAdapter;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.enums.global.MailModelEnum;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyAvgStatisticsDto;
import com.weihengtech.ecos.model.dos.ClientEnergyNotifyDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.service.app.ClientEnergyNotifyService;
import com.weihengtech.ecos.service.thirdpart.MessageService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class NotifyTask {

	@Resource
	private ClientEnergyNotifyService clientEnergyNotifyService;

	@Resource
	private HubService hubService;

	@Resource
	private StrategyService strategyService;

	@Resource
	private InsightAdapter insightAdapter;

	@Resource
	private MessageService messageService;

	@Resource
	private ClientUserService clientUserService;

	@XxlJob("energyNotify")
	public void energyNotify() {
		log.info("energyNotify");
		List<ClientEnergyNotifyDo> list = clientEnergyNotifyService
				.list(Wrappers.<ClientEnergyNotifyDo>lambdaQuery().eq(ClientEnergyNotifyDo::getOpen, 1));
		for (ClientEnergyNotifyDo clientEnergyNotifyDo : list) {
			ThreadUtil.execAsync(() -> {
				BigDecimal homeEnergyLast24Hour = computeHomeEnergyLast24Hour(clientEnergyNotifyDo.getDeviceId());
				BigDecimal threshold = new BigDecimal(String.valueOf(clientEnergyNotifyDo.getThreshold()));
				if (threshold.compareTo(homeEnergyLast24Hour) < 0) {
					Long userId = clientEnergyNotifyDo.getUserId();
					ClientUserDo clientUserDo = clientUserService.getById(userId);
					InsightDeviceEnergyAvgStatisticsDto lastSevenDayDto = insightAdapter.getOffsetDaysDeviceEnergy(
							String.valueOf(clientEnergyNotifyDo.getDeviceId()), -7, clientUserDo.getTimeZone());
					messageService.sendEmail(clientEnergyNotifyDo.getEmail(), MailModelEnum.CLIENT_ENERGY_NOTIFY,
							lastSevenDayDto.getMaxEnergy().toPlainString(),
							lastSevenDayDto.getAvgEnergy().toPlainString(),
							lastSevenDayDto.getMinEnergy().toPlainString()
					);
				}
			});
		}
	}

	private BigDecimal computeHomeEnergyLast24Hour(Long deviceId) {
		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(deviceId);
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
		Dict endData = timeSeriesDatabaseService.lastPoint(hybridSinglePhaseDO.getDeviceName(),
				CommonConstants.HOME_ENERGY, System.currentTimeMillis()
		);
		Dict startData = timeSeriesDatabaseService.lastPoint(hybridSinglePhaseDO.getDeviceName(),
				CommonConstants.HOME_ENERGY, System.currentTimeMillis() - 24 * 3600 * 1000L
		);

		BigDecimal endFromSolar = new BigDecimal(endData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV));
		BigDecimal endToGrid = new BigDecimal(endData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID));
		BigDecimal endFromGrid = new BigDecimal(endData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID));

		BigDecimal startFromSolar = new BigDecimal(startData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV));
		BigDecimal startToGrid = new BigDecimal(startData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID));
		BigDecimal startFromGrid = new BigDecimal(startData.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID));

		BigDecimal fromSolar = NumberUtil.sub(endFromSolar, startFromSolar);
		BigDecimal toGrid = NumberUtil.sub(endToGrid, startToGrid);
		BigDecimal fromGrid = NumberUtil.sub(endFromGrid, startFromGrid);

		return NumberUtil.round(NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid), toGrid), 2, RoundingMode.HALF_UP);
	}
}
