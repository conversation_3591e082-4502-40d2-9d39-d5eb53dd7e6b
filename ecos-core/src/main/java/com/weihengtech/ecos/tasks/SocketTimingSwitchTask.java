package com.weihengtech.ecos.tasks;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.model.dos.ClientChargeRecordReadDo;
import com.weihengtech.ecos.model.dos.ClientChargeTaskDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.service.charger.ClientChargeRecordReadService;
import com.weihengtech.ecos.service.charger.ClientChargeTaskService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: 插座定时开关任务
 * @author: jiahao.jin
 * @create: 2024-02-28 19:54
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class SocketTimingSwitchTask {

    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private HubService hubService;
    @Resource
    private ClientChargeTaskService clientChargeTaskService;
    @Resource
    private ClientChargeRecordReadService clientChargeRecordReadService;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;
    @Resource
    private ChargeStationService chargeStationService;

    @XxlJob("socketSwitchTask")
    public void socketSwitchTask() throws Exception {
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        log.info("socketSwitchTask " + param);
        String deviceId = param.split("//")[0];
        String socketSwitch = param.split("//")[1];
        String weeks = param.split("//")[2];
        String taskId = param.split("//")[3];


        List<HybridSinglePhaseDO> hybridSinglePhaseDoList = hubService.getBatchById(false, Collections.singletonList(Long.valueOf(deviceId)));

        if (hybridSinglePhaseDoList.size() != 1) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }

        HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoList.get(0);
        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
        if (!deviceOnline) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }

        Boolean aBoolean = ecosIotApi.switchTuyaSocket(wifiSn, cloud, !Objects.equals(socketSwitch, "0"), 1);
        if (!aBoolean) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
        System.out.println("socketSwitchTask执行一次 " + param);

        // 如果是仅一次执行，就去任务表关闭这次任务状态
        if (Objects.equals(weeks, "0")) {
            ClientChargeTaskDo clientChargeTaskDo = clientChargeTaskService.getById(taskId);
            clientChargeTaskDo.setStatus(false);
            clientChargeTaskDo.setUpdateTime(System.currentTimeMillis());
            clientChargeTaskService.updateById(clientChargeTaskDo);
        }
    }

    @XxlJob("chargeStationChargingTask")
    public void chargeStationChargingTask() throws Exception {
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        log.info("chargeStationChargingTask " + param);
        String deviceId = param.split("//")[0];
        // 0：停止充电，1：开始充电
        String charging = param.split("//")[1];
        String weeks = param.split("//")[2];
        // 预约充电记录表ID
        String taskId = param.split("//")[3];
        // 预期充电功率
        String power =  param.split("//")[4];

        ClientChargeTaskDo clientChargeTaskDo = clientChargeTaskService.getById(taskId);
        // 如果是仅一次执行，就去任务表关闭这次任务状态
        if (Objects.equals(weeks, "0")) {
            log.info("仅执行一次定时任务：{}", clientChargeTaskDo.getId());
            clientChargeTaskDo.setStatus(false);
            clientChargeTaskDo.setUpdateTime(System.currentTimeMillis());
            clientChargeTaskService.updateById(clientChargeTaskDo);
        }
        if (Objects.equals(charging, "1")) {
            try {
                log.info("{}开始充电，星期：{}，taskId：{}", deviceId, weeks, taskId);
                chargeStationService.startCharging(String.valueOf(clientChargeTaskDo.getUserId()), deviceId, power);
                // 开始充电成功
                createTimingStartChargingRecord(clientChargeTaskDo, 1, 0, 0);
            } catch (Exception e) {
                // 开始充电失败
                createTimingStartChargingRecord(clientChargeTaskDo, 0, 0, -1);
            }

        } else {
            try {
                log.info("{}停止充电，星期：{}，taskId：{}", deviceId, weeks, taskId);
                chargeStationService.stopCharging(String.valueOf(clientChargeTaskDo.getUserId()), deviceId);
                // 停止充电成功
                updateTimingStartChargingRecord(taskId, 1, 1);
            } catch (Exception e) {
                // 停止充电失败
                updateTimingStartChargingRecord(taskId, 0, -1);
            }
        }
    }

    private void createTimingStartChargingRecord(ClientChargeTaskDo clientChargeTaskDo, Integer startStatus, Integer stopStatus, Integer status) {
        List<Long> userIds = middleClientUserDeviceService.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getDeviceId, clientChargeTaskDo.getDeviceId())).stream().map(MiddleClientUserDeviceDo::getUserId).collect(Collectors.toList());
        List<ClientChargeRecordReadDo> clientChargeRecordReadDoList = new ArrayList<>();
        long currentTimeMillis = System.currentTimeMillis();
        // 创建预约定时任务记录
        for (Long userId : userIds) {
            ClientChargeRecordReadDo clientChargeRecordReadDo = new ClientChargeRecordReadDo();
            long chargeRecordId = snowFlakeUtil.generateId();
            clientChargeRecordReadDo.setId(chargeRecordId);
            clientChargeRecordReadDo.setUserId(userId);
            clientChargeRecordReadDo.setChargeTaskId(clientChargeTaskDo.getId());
            clientChargeRecordReadDo.setStartChargeStatus(startStatus);
            clientChargeRecordReadDo.setStartChargeTime(currentTimeMillis);
            clientChargeRecordReadDo.setStopChargeStatus(stopStatus);
            clientChargeRecordReadDo.setStopChargeTime(currentTimeMillis);
            clientChargeRecordReadDo.setReaded(0);
            clientChargeRecordReadDo.setStatus(status);

            clientChargeRecordReadDoList.add(clientChargeRecordReadDo);
        }
        ActionFlagUtil.assertTrue(clientChargeRecordReadService.saveBatch(clientChargeRecordReadDoList));
    }

    private void updateTimingStartChargingRecord(String taskId, Integer stopStatus, Integer status) {


        List<ClientChargeRecordReadDo> clientChargeRecordReadDoList = clientChargeRecordReadService.list(Wrappers.<ClientChargeRecordReadDo>lambdaQuery()
                .eq(ClientChargeRecordReadDo::getChargeTaskId, taskId)
                .eq(ClientChargeRecordReadDo::getStatus, 0));
        long currentTimeMillis = System.currentTimeMillis();
        List<ClientChargeRecordReadDo> newClientChargeRecordReadDoList = new ArrayList<>();
        for (ClientChargeRecordReadDo clientChargeRecordReadDo : clientChargeRecordReadDoList) {
            clientChargeRecordReadDo.setStopChargeStatus(stopStatus);
            clientChargeRecordReadDo.setStopChargeTime(currentTimeMillis);
            clientChargeRecordReadDo.setStatus(status);
            newClientChargeRecordReadDoList.add(clientChargeRecordReadDo);
        }

        ActionFlagUtil.assertTrue(clientChargeRecordReadService.updateBatchById(newClientChargeRecordReadDoList));
    }


}
