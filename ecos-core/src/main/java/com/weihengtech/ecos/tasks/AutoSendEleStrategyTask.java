package com.weihengtech.ecos.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.adapter.CustomizeAdapter;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.enums.ele.StrategyModeEnum;
import com.weihengtech.ecos.enums.thirdpart.PriceUnitEnum;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.prometheus.ScheduledTaskMetrics;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自动下发电价策略定时任务
 *
 * <AUTHOR>
 * @date 2024/10/23 14:39
 * @version 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class AutoSendEleStrategyTask {

	@Resource
	private CustomizeAdapter customizeAdapter;
	@Resource
	private RetryService retryService;
	@Resource
	private HubService hubService;
	@Resource
	private ClientCustomizeService clientCustomizeService;
	@Resource
	private ScheduledTaskMetrics scheduledTaskMetrics;

	@XxlJob("autoGenEleStrategyTask")
	public void autoSendEleStrategyTask() {
		// 查询已开启自动化策略的设备
		List<StrategyCustomizeDTO> autoStrategyDevices = clientCustomizeService.getAutoStrategyDevices();
		if (CollUtil.isEmpty(autoStrategyDevices)) {
			printLog("未开启自动化策略的设备");
			return;
		}
		List<Long> deviceIdList = autoStrategyDevices.stream().map(StrategyCustomizeDTO::getDeviceId).collect(Collectors.toList());
		List<HybridSinglePhaseDO> deviceInfoList = hubService.getBatchById(false, deviceIdList);
		if (CollUtil.isEmpty(deviceInfoList)) {
			printLog("未查询到设备信息");
			return;
		}
		Set<Long> existsIdSet = deviceInfoList.stream().map(HybridSinglePhaseDO::getId).collect(Collectors.toSet());
		// 对于不存在的设备，不予处理
		autoStrategyDevices.removeIf(i -> !existsIdSet.contains(i.getDeviceId()));
		Map<Long, String> ratePowerMap = deviceInfoList.stream()
				.collect(Collectors.toMap(HybridSinglePhaseDO::getId, HybridSinglePhaseDO::getRatedPower));
		Integer time = Optional.ofNullable(XxlJobHelper.getJobParam())
				.map(JSONUtil::parseObj)
				.map(i -> i.getInt("time", 0))
				.orElse(0);
		String timezone = Optional.ofNullable(XxlJobHelper.getJobParam())
				.map(JSONUtil::parseObj)
				.map(i -> i.getStr("timezone", "Europe/Amsterdam"))
				.orElse("Europe/Amsterdam");
		printLog(String.format("查询电价时间为:%s，时区为：%s, 开始下发策略", time, timezone));


		for (StrategyCustomizeDTO deviceCus : autoStrategyDevices) {
			// 查询额定功率
			String ratedPowerStr = ratePowerMap.get(deviceCus.getDeviceId());
			if (StrUtil.isBlank(ratedPowerStr) &&
					(deviceCus.getDefChargePower() == null && deviceCus.getDefDischargePower() == null)) {
				printLog(String.format("未查询到设备 %s 的额定功率，且用户未设置默认充放电功率，不自动下发策略", deviceCus.getDeviceId()));
				continue;
			}
			printLog(String.format("设备 %s 开始下发策略：", deviceCus.getDeviceId()));
			int ratedPower = Integer.parseInt(ratedPowerStr.substring(0, ratedPowerStr.length() - 1));
			// 计算策略，如果未开启了家庭关联策略，则仅使用电价策略
			EleStrategyPreviewVO param = EleStrategyPreviewVO.builder()
					.homeId(deviceCus.getHomeId())
					.time(time)
					.priceImportType(deviceCus.getPriceImportType())
					.strategyMode(deviceCus.getStrategyMode())
					.timezone(Optional.ofNullable(deviceCus.getTimezone()).orElse(timezone))
					.region(deviceCus.getRegion())
					.purchaseTax(deviceCus.getPurchaseTax())
					.ratedPower(ratedPower)
					.defChargePower(deviceCus.getDefChargePower())
					.defDischargePower(deviceCus.getDefDischargePower())
					.priceUnit(PriceUnitEnum.kWh.name()).build();
			if (deviceCus.getAutoHomeStrategy() == 1 || StrategyModeEnum.HOME_LOAD.getCode().equals(deviceCus.getStrategyMode())) {
				param.setDeviceId(deviceCus.getDeviceId());
				param.setDayType(calDayType(timezone));
			}
			// 生成策略并下发
			try {
				CustomizeInfoEzDto customizeInfoEzDto = customizeAdapter.queryStrategy(param);

				// 构建充放电策略
				List<ChargingStructDTO> chargingList = customizeInfoEzDto.getChargingList();
				List<ChargingStructDTO> dischargingList = customizeInfoEzDto.getDischargingList();
				if (CollUtil.isEmpty(chargingList) && CollUtil.isEmpty(dischargingList)) {
					printLog(String.format("%s的充放电策略为空", deviceCus.getDeviceId()));
					continue;
				}
				printLog(String.format("%s下发充电策略为：%s", deviceCus.getDeviceId(), JSONUtil.toJsonStr(chargingList)));
				printLog(String.format("%s下发放电策略为：%s", deviceCus.getDeviceId(), JSONUtil.toJsonStr(dischargingList)));
				List<ChargingStructDTO> copyChargingList = BeanUtil.copyToList(chargingList, ChargingStructDTO.class);
				List<ChargingStructDTO> copyDischargingList = BeanUtil.copyToList(dischargingList, ChargingStructDTO.class);

				retryService.writeCustomizeV2(CustomizeInfoEzV2Vo.builder()
						.timezone(timezone)
						.deviceId(String.valueOf(deviceCus.getDeviceId()))
						.chargeUseMode(1)
						.regularSoc(deviceCus.getBatteryMin())
						.regularFeedIn(deviceCus.getMaxFeedIn())
						.chargingList(copyChargingList)
						.dischargingList(copyDischargingList)
						.build());
			} catch (Exception e) {
				scheduledTaskMetrics.recordFailure("autoGenEleStrategyTask");
				printLog(String.format("下发策略失败：%s", e.getMessage()), e);
			}
		}
	}

	/** 计算当前时间的星期类型 */
	private Integer calDayType(String timezone) {
		ZonedDateTime now = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(timezone));
		DayOfWeek dow = now.getDayOfWeek();
		if (dow == DayOfWeek.SATURDAY || dow == DayOfWeek.SUNDAY) {
			return 0;
		}
		return 1;
	}

	/** 打印日志 */
	private void printLog(String msg, Exception... e) {
		if (e.length == 0) {
			log.info(msg);
			XxlJobHelper.log(msg);
		}else {
			log.error(msg, e);
			XxlJobHelper.log(msg, e);
		}
	}
}
