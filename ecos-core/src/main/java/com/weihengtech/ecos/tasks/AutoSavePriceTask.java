package com.weihengtech.ecos.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.dtos.ElePriceHistoryDTO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.influx.api.FluxWriteApi;
import com.weihengtech.influx.utils.HashUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动持久化家庭电价信息
 *
 * <AUTHOR>
 * @date 2024/10/23 14:39
 * @version 1.0
 */
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class AutoSavePriceTask {

	private static final Logger log = LoggerFactory.getLogger(AutoSavePriceTask.class);

	@Value("${ele.price.bucket:ecos_ele_price}")
	private String bucket;
	@Value("${ele.price.measurement:home}")
	private String measurement;
	@Value("${influxdb.org}")
	private String org;

	@Resource
	private ClientHomeService clientHomeService;
	@Resource
	private ClientHomeUserService clientHomeUserService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private FluxWriteApi fluxWriteApi;

	@XxlJob("autoSaveHomeElePrice")
	public void autoSaveHomeElePrice() {
		// 获取开启了家庭电价的家庭列表
		List<ClientHomeDo> homeList = clientHomeService.queryHomePriceHomeList();
		if (CollUtil.isEmpty(homeList)) {
			printLog("开启家庭电价的家庭为空");
			return;
		}
		// 获取家庭电价
		for (ClientHomeDo clientHome : homeList) {
			// 获取用户时区
			String userTimezone = clientHomeUserService.getHomeUserTimezone(String.valueOf(clientHome.getId()));
			if (userTimezone == null) {
				printLog(String.format("获取用户时区为空， 家庭为：%s", clientHome.getId()));
				continue;
			}
			HomeElePriceService homeElePriceService = strategyService.chooseHomeElePriceService(clientHome.getElePriceType());
			List<EleDayAheadPriceDto> priceList = homeElePriceService.queryOriginalElePrice(clientHome.getId().toString(),
					0, userTimezone);
			if (CollUtil.isEmpty(priceList)) {
				printLog(String.format("获取家庭电价为空，家庭为：%s", clientHome.getId()));
				continue;
			}
			List<Point> points = priceList.stream()
					.map(i -> ElePriceHistoryDTO.builder()
							.price(i.getAverage())
							.tax(i.getTax())
							.timestamp(i.getStartTimeUnix()).build())
					.map(i -> {
						String measurement = HashUtils.elfHashStr(this.measurement, clientHome.getId().toString());
						Point point = Point.measurement(measurement);
						point.addTag("home_id", String.valueOf(clientHome.getId()));
						point.addFields(JSONUtil.parseObj(JSONUtil.toJsonStr(i)));
						point.time(i.getTimestamp(), WritePrecision.S);
						return point;
					})
					.collect(Collectors.toList());
            try {
                fluxWriteApi.writePointList(bucket, org, measurement, points);
				printLog(String.format("推送influxDB成功：%s", clientHome.getId()));
            } catch (Exception e) {
                printLog(String.format("电价推送influxdb异常：%s", clientHome.getId()), e);
            }
        }
	}

	/** 打印日志 */
	private void printLog(String msg, Exception... e) {
		if (e.length == 0) {
			log.info(msg);
			XxlJobHelper.log(msg);
		}else {
			log.error(msg, e);
			XxlJobHelper.log(msg, e);
		}
	}
}
