package com.weihengtech.ecos.tasks;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.enums.global.AccountClearRecordStateEnum;
import com.weihengtech.ecos.model.dos.ClientAccountClearRecordDo;
import com.weihengtech.ecos.service.global.ClientAccountClearRecordService;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class AccountTask {

	@Resource
	private ClientAccountClearRecordService clientAccountClearRecordService;

	@Resource
	private ClientUserService clientUserService;

	@Resource
	private ClientUserRoleService clientUserRoleService;

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;

	@XxlJob("clearClientAccountTask")
	public void clearClientAccountTask() {
		log.info("clearClientAccountTask");
		List<ClientAccountClearRecordDo> needDelAccountRecordList = clientAccountClearRecordService
				.list(Wrappers.<ClientAccountClearRecordDo>lambdaQuery()
						.eq(ClientAccountClearRecordDo::getState, AccountClearRecordStateEnum.ING.getCode())
						.lt(ClientAccountClearRecordDo::getClearTime, System.currentTimeMillis()));

		for (ClientAccountClearRecordDo clientAccountClearRecordDo : needDelAccountRecordList) {
			ThreadUtil.execAsync(new RunnableTask.ClearAccountTask(clientAccountClearRecordDo.getId()));
		}
	}
}
