package com.weihengtech.ecos.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.enums.global.AccountClearRecordStateEnum;
import com.weihengtech.ecos.enums.global.MailModelEnum;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.model.dos.*;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.ClientAccountClearRecordService;
import com.weihengtech.ecos.service.thirdpart.MessageService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.InitUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class RunnableTask {

	public static class ClearAccountTask implements Runnable {

		private final Long recordId;

		public ClearAccountTask(Long recordId) {
			this.recordId = recordId;
		}

		@Override
		@DSTransactional
		public void run() {
			ClientAccountClearRecordService clientAccountClearRecordService = InitUtil
					.getBean(ClientAccountClearRecordService.class);
			if (null != recordId) {
				ClientAccountClearRecordDo clientAccountClearRecordDo = clientAccountClearRecordService
						.getById(recordId);
				if (AccountClearRecordStateEnum.ING.getCode() == clientAccountClearRecordDo.getState()) {

					log.info("执行注销任务  {}", clientAccountClearRecordDo.getId());

					Long userId = clientAccountClearRecordDo.getUserId();
					MiddleClientUserDeviceService middleClientUserDeviceService = InitUtil
							.getBean(MiddleClientUserDeviceService.class);
					List<MiddleClientUserDeviceDo> middleClientUserDeviceDoList = middleClientUserDeviceService
							.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
									.eq(MiddleClientUserDeviceDo::getUserId, userId));
					List<Long> wannaDelMiddleClientUserIdList = new ArrayList<>();
					for (MiddleClientUserDeviceDo middleClientUserDeviceDo : middleClientUserDeviceDoList) {
						Integer master = middleClientUserDeviceDo.getMaster();
						if (1 == master) {
							List<MiddleClientUserDeviceDo> slaveDoList = middleClientUserDeviceService.list(
									Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
											.eq(
													MiddleClientUserDeviceDo::getDeviceId,
													middleClientUserDeviceDo.getDeviceId()
											)
							);
							wannaDelMiddleClientUserIdList.addAll(slaveDoList.stream()
									.map(MiddleClientUserDeviceDo::getId).collect(Collectors.toList()));
						} else {
							wannaDelMiddleClientUserIdList.add(middleClientUserDeviceDo.getId());
						}
					}


					// 判断用户是否有家庭，有就删除所有的家庭，删除所有家庭相关数据
					HubService hubService = InitUtil
							.getBean(HubService.class);
					ClientHomeUserService clientHomeUserService = InitUtil
							.getBean(ClientHomeUserService.class);
					ClientHomeService clientHomeService = InitUtil
							.getBean(ClientHomeService.class);
					ClientHomeDeviceService clientHomeDeviceService = InitUtil
							.getBean(ClientHomeDeviceService.class);
					ChargeStationService chargeStationService = InitUtil
							.getBean(ChargeStationService.class);
					SinglePlugSocketService singlePlugSocketService = InitUtil
							.getBean(SinglePlugSocketService.class);
					List<ClientHomeUserDo> homeUserDoList = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
							.eq(ClientHomeUserDo::getUserId, userId));
					List<Long> masterHomeIds = homeUserDoList.stream().filter(clientHomeUserDo -> clientHomeUserDo.getRelationType() == 1).map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());

					if (homeUserDoList.size() > 0) {
						// 只有自己是主人才能删除相应的配置
						List<Long> masterHomeId1 = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
								.eq(ClientHomeDo::getHomeType, 1)
								.in(ClientHomeDo::getId, masterHomeIds)).stream().map(ClientHomeDo::getId).collect(Collectors.toList());

						// 查询家庭下绑定的设备
						Map<Long, ClientHomeDeviceDo> clientHomeDeviceDoMap = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
										.in(ClientHomeDeviceDo::getHomeId, masterHomeId1))
								.stream().collect(Collectors.toMap(ClientHomeDeviceDo::getDeviceId, Function.identity(), (existing, replacement) -> existing));

						// 清除设备的配置、清除设备的定时任务
						if (!clientHomeDeviceDoMap.isEmpty()) {

							List<Long> deviceIds = new ArrayList<>(clientHomeDeviceDoMap.keySet());
							Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(false, deviceIds)
									.stream()
									.collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));
							List<Long> chargeStationIds = hybridSinglePhaseDoMap.entrySet().stream()
									.filter(entry -> entry.getValue().getResourceSeriesId() == 104)
									.map(Map.Entry::getKey)
									.collect(Collectors.toList());
							if (chargeStationIds.size() > 0) {
								chargeStationService.deleteDevicesConfigAndTask(chargeStationIds);
							}
							List<HybridSinglePhaseDO> sockets = hybridSinglePhaseDoMap.entrySet().stream()
									.filter(entry -> entry.getValue().getResourceSeriesId() == 105)
									.map(Map.Entry::getValue)
									.collect(Collectors.toList());
							if (sockets.size() > 0) {
								singlePlugSocketService.deleteDevicesConfigAndTask(sockets);
							}

							// 清除家庭-设备关系表数据
							ActionFlagUtil.assertTrue(clientHomeDeviceService.remove(Wrappers.<ClientHomeDeviceDo>lambdaQuery().in(ClientHomeDeviceDo::getHomeId, masterHomeIds)));
						}

						// 清除家庭-用户关系表数据
						ActionFlagUtil.assertTrue(clientHomeUserService.remove(Wrappers.<ClientHomeUserDo>lambdaQuery().eq(ClientHomeUserDo::getUserId, userId)));
						// 清除家庭表数据
						ActionFlagUtil.assertTrue(clientHomeService.remove(Wrappers.<ClientHomeDo>lambdaQuery().in(ClientHomeDo::getId, masterHomeIds)));
					}


					if (CollUtil.isNotEmpty(wannaDelMiddleClientUserIdList)) {
						middleClientUserDeviceService.removeByIds(wannaDelMiddleClientUserIdList);
					}
					ClientUserService clientUserService = InitUtil.getBean(ClientUserService.class);
					ClientUserRoleService clientUserRoleService = InitUtil.getBean(ClientUserRoleService.class);

					ClientUserDo clientUserDo = clientUserService.getById(userId);
					clientUserService.removeById(userId);
					clientUserRoleService.remove(
							Wrappers.<ClientUserRoleDo>lambdaQuery().eq(ClientUserRoleDo::getClientUserId, userId));
					clientAccountClearRecordDo.setState(AccountClearRecordStateEnum.DELETED.getCode());
					clientAccountClearRecordService.updateById(clientAccountClearRecordDo);
					log.info("注销任务执行成功: {}", clientAccountClearRecordDo.getId());
					if (clientUserDo != null && StrUtil.isNotBlank(clientUserDo.getEmail())) {
						val messageService = InitUtil.getBean(MessageService.class);
						messageService.sendEmail(clientUserDo.getEmail(), MailModelEnum.CLIENT_CLEAR_FINISH, clientUserDo.getUsername());
					}
				}
			}
		}
	}
}
