package com.weihengtech.ecos.api.fallback;

import cn.hutool.json.JSONObject;

/**
 * <AUTHOR>
 */
public class FallBackResponse {

	public static JSONObject failResponse() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.set("state", "FAILURE");
		jsonObject.set("result", "false");
		return jsonObject;
	}

	public static JSONObject successResponse() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.set("state", "SUCCESS");
		jsonObject.set("result", "true");
		return jsonObject;
	}
}
