package com.weihengtech.ecos.api;

import cn.hutool.json.JSONObject;
import com.weihengtech.ecos.api.fallback.WeatherFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-25 16:19
 **/
@FeignClient(name = "Weather", url = "${custom.url.weather.api}", decode404 = true, fallback = WeatherFallback.class)
public interface WeatherApi {

    @GetMapping("/now?gzip=n")
    JSONObject nowWeather(
            @RequestParam(name = "key", required = true) String key,
            @RequestParam(name = "location", required = true) String location,
            @RequestParam(name = "lang", required = false) String lang,
            @RequestParam(name = "unit", required = false) String unit
    );

    @GetMapping("/3d?gzip=n")
    JSONObject threeDayWeather(
            @RequestParam(name = "key", required = true) String key,
            @RequestParam(name = "location", required = true) String location,
            @RequestParam(name = "lang", required = false) String lang,
            @RequestParam(name = "unit", required = false) String unit
    );

    @GetMapping("/7d?gzip=n")
    JSONObject sevenDayWeather(
            @RequestParam(name = "key", required = true) String key,
            @RequestParam(name = "location", required = true) String location,
            @RequestParam(name = "lang", required = false) String lang,
            @RequestParam(name = "unit", required = false) String unit
    );

    @GetMapping("24h?gzip=n")
    JSONObject roundTheClockWeather(
            @RequestParam(name = "key", required = true) String key,
            @RequestParam(name = "location", required = true) String location,
            @RequestParam(name = "lang", required = false) String lang,
            @RequestParam(name = "unit", required = false) String unit
    );
}
