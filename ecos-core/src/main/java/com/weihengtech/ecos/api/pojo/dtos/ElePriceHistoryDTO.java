package com.weihengtech.ecos.api.pojo.dtos;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/18 16:04
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价持久化数据结构")
public class ElePriceHistoryDTO {

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 时间
     */
    private Long timestamp;
}
