package com.weihengtech.ecos.api.pojo.dtos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-21 15:39
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "预测电价回参")
public class EleDayAheadPriceDto {

    @ApiModelProperty(name = "country", value = "国家")
    private String country;

    @ApiModelProperty(name = "region", value = "地区")
    private String region;

    @ApiModelProperty(name = "currency", value = "货币单位")
    private String currency;

    @ApiModelProperty(name = "type", value = "电价类型：Day-Ahead-预测电价")
    private String type;

    @ApiModelProperty(name = "average", value = "预测价格")
    private BigDecimal average;

    @ApiModelProperty(name = "startTimeUnix", value = "预测时间点，秒级时间戳")
    private Long startTimeUnix;

    /**
     * 购电税费，只有零售商电价用，批发电价无此数据
     */
    private BigDecimal tax;
}
