package com.weihengtech.ecos.api.fallback;

import cn.hutool.json.JSONObject;
import com.weihengtech.ecos.api.WeatherApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-25 16:31
 **/
@Component
@Slf4j
public class WeatherFallback implements WeatherApi {
    @Override
    public JSONObject nowWeather(String key, String location, String lang, String unit) {
        log.warn("com.weihengtech.api.fallback.WeatherFallback.nowWeather：{}-{}-{}-{}", key,location,lang,unit);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }

    @Override
    public JSONObject threeDayWeather(String key, String location, String lang, String unit) {
        log.warn("com.weihengtech.api.fallback.WeatherFallback.threeDayWeather：{}-{}-{}-{}", key,location,lang,unit);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }

    @Override
    public JSONObject sevenDayWeather(String key, String location, String lang, String unit) {
        log.warn("com.weihengtech.api.fallback.WeatherFallback.sevenDayWeather：{}-{}-{}-{}", key,location,lang,unit);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }

    @Override
    public JSONObject roundTheClockWeather(String key, String location, String lang, String unit) {
        log.warn("com.weihengtech.api.fallback.WeatherFallback.roundTheClockWeather：{}-{}-{}-{}", key,location,lang,unit);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }
}
