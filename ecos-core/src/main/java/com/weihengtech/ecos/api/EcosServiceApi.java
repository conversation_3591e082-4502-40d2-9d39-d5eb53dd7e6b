package com.weihengtech.ecos.api;

import com.weihengtech.ecos.api.config.EcosServiceConfiguration;
import com.weihengtech.ecos.api.fallback.EcosServiceFallback;
import com.weihengtech.ecos.api.pojo.dtos.GlobalDeviceConfigDto;
import com.weihengtech.ecos.api.pojo.dtos.GuideAgreementVersionDto;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundDTO;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.api.pojo.vos.EcosDeviceConfigQueryVo;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.global.ResourceCategoryTreeDto;
import com.weihengtech.ecos.model.dtos.charger.V2ClientChargeRecordDto;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.thirdpart.SystemInfoUpdVO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordSaveVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordUpdateVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "EcosService", url = "${custom.url.service}", decode404 = true, fallback = EcosServiceFallback.class, configuration = EcosServiceConfiguration.class)
public interface EcosServiceApi {

	@PostMapping("/specific/speedup/once/{deviceId}")
    EmptyResponse speedupOnce(@PathVariable("deviceId") String deviceId);

	@PostMapping("/ecos/config/query")
    DataResponse<GlobalDeviceConfigDto> queryDeviceConfig(@RequestBody @Valid EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo);

	@GetMapping("/ecos/global/agreement/latest")
	DataResponse<GuideAgreementVersionDto> getAgreementVersion();

	@GetMapping("/hybridSinglePhase/getAgentId")
	DataResponse<String> getAgentId(@RequestParam("deviceId") String deviceId);

	@GetMapping("/ecos/device/agent")
	DataResponse<List<BindInfoDTO>> getAgentsByIds(@RequestBody List<Long> ids);

	@GetMapping("/ecos/device/queryByDeviceName")
	DataResponse<HybridSinglePhaseDO> queryByDeviceName(@RequestParam("deviceName") String deviceName);

	@GetMapping("/ecos/device/queryById")
	DataResponse<HybridSinglePhaseDO> queryById(@RequestParam("deviceId") Long deviceId);

	@PostMapping("/ecos/device/saveNewDevice")
	EmptyResponse saveNewDevice(@RequestBody HybridSinglePhaseDO hybridSinglePhaseDO);

	@PostMapping("/ecos/device/updateById")
	EmptyResponse updateById(@RequestBody HybridSinglePhaseDO hybridSinglePhaseDO);

	@GetMapping("/ecos/device/nowBindDeviceList")
	DataResponse<List<HybridSinglePhaseDO>> nowBindDeviceList(
			@RequestParam("wifiSn") String wifiSn
	);

	@GetMapping("/ecos/device/listOtherBindDevice")
	DataResponse<List<HybridSinglePhaseDO>> listOtherBindDevice(
			@RequestParam("wifiSn") String wifiSn,
			@RequestParam("deviceName") String deviceName
	);

	@GetMapping("/ecos/version")
	DataResponse<GlobalVersionDto> ecosLatestVersion(@RequestParam("language") String language);

	@GetMapping("/ecos/enestVersion")
	DataResponse<GlobalEnestLatestVersionDto> enestLatestVersion(@RequestParam("language") String language);

	@PutMapping("/system_info")
	EmptyResponse updSystemInfo(@RequestBody SystemInfoUpdVO updVO);

	@PostMapping("/ecos/device/batch_query")
	DataResponse<List<HybridSinglePhaseDO>> queryBatchById(@RequestParam(required = false) Boolean isNeedExt, @RequestBody List<Long> ids);

	@PostMapping("/ecos/socket/save")
	InResponse saveSocket(@RequestBody HybridSinglePhaseDO hybridSinglePhaseDO);

	@PostMapping("/ecos/socket/upd")
	InResponse updSocket(@RequestBody HybridSinglePhaseDO hybridSinglePhaseDO);

	@PostMapping("/ecos/charger/save")
	InResponse saveCharger(@RequestBody ChargerSaveVO chargerSaveVO);

	@PostMapping("/ecos/charger/upd")
	InResponse updCharger(@RequestBody ChargerSaveVO chargerSaveVO);

	@PostMapping("/ecos/charger/ext/upd")
	InResponse updChargerExtInfo(@RequestBody ExtInfoDto chargerSaveVO);


	@GetMapping("/resource/category/tree")
	DataResponse<List<ResourceCategoryTreeDto>> resourceCategoryTree();

	@PostMapping("/ac-charger/device/record/page")
	DataResponse<PageInfoDTO<V2ClientChargeRecordDto>> pageChargeRecord(@RequestBody V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo);

	@GetMapping("/ac-charger/device/record/last")
	DataResponse<V2ClientChargeRecordDto> queryLastRecord(@RequestParam Long deviceId, @RequestParam Boolean isFilterCharing);

	@PostMapping("/ac-charger/device/record/save")
	EmptyResponse saveChargeRecord(@RequestBody V2ClientChargeRecordSaveVo v2ClientChargeRecordSaveVo);

	@PutMapping("/ac-charger/device/record/upd")
	EmptyResponse updChargeRecord(@RequestBody V2ClientChargeRecordUpdateVo v2ClientChargeRecordUpdateVo);

	@PostMapping("/ecos/install/bound")
	EmptyResponse boundInstall(@RequestBody InstallBoundDTO item);

	@GetMapping("/ecos/install/info")
	DataResponse<InstallBoundInfoDTO> getBindInstallInfo(@RequestParam String deviceId);

	@GetMapping("/device/storage/export")
	@ApiOperation(value = "获取动态输出信息")
	DataResponse<DynamicExportDTO> dynamicExport(@RequestParam String deviceName);

	@GetMapping("/device/storage/design")
	@ApiOperation(value = "获取设备设计相关信息")
	DataResponse<DynamicDesignDTO> designInfo(@RequestParam String deviceName);

	@PostMapping("/device/storage/save")
	@ApiOperation(value = "保存动态输出信息")
	EmptyResponse dynamicSave(@RequestBody @Valid DynamicSaveVO param);

	@GetMapping("/device/storage/test")
	@ApiOperation(value = "动态输出生效校验")
	DataResponse<DynamicTestDTO> dynamicTest(@RequestParam String deviceName);

	@PostMapping("/device/storage/switch")
	@ApiOperation(value = "动态输出使能开关")
	EmptyResponse dynamicSwitch(@RequestBody @Valid DynamicSwitchVO param);
}
