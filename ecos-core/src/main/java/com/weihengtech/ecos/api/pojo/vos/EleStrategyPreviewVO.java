package com.weihengtech.ecos.api.pojo.vos;

import com.weihengtech.ecos.enums.thirdpart.PriceUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/30 11:49
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价自动策略预览入参")
public class EleStrategyPreviewVO {

    // 以下参数为生成电价必须参数：通过电价导入类型区分家庭电价和自定义电价
    @ApiModelProperty(name = "homeId", value = "家庭Id")
    private Long homeId;

    @ApiModelProperty(name = "priceImportType", value = "电价导入类型")
    private Integer priceImportType;

    @ApiModelProperty(name = "time", value = "查询时间，昨天：-1，今天：0，明天：1")
    private Integer time;

    @ApiModelProperty(name = "timezone", value = "时区")
    private String timezone;

    @ApiModelProperty(name = "region", value = "地区（自定义电价类型参数）")
    private String region;

    @ApiModelProperty(name = "priceUnit", value = "电价单位，默认是MWh，兼容老版本")
    private String priceUnit = PriceUnitEnum.MWh.name();

    // 以下参数为生成策略必须参数：通过自动策略模式区分普通、联动家庭负载、联动成本收益
    @ApiModelProperty(name = "strategyMode", value = "策略模式")
    private Integer strategyMode;

    @ApiModelProperty(name = "deviceName", value = "设备id（联动家庭负载需要）")
    private Long deviceId;

    @ApiModelProperty(name = "dayType", value = "日期类型，0：周末，1：工作日")
    private Integer dayType;

    @ApiModelProperty(name = "purchaseTax", value = "电价税费：默认是0")
    private BigDecimal purchaseTax = BigDecimal.ZERO;

    @ApiModelProperty(name = "ratedPower", value = "额定功率")
    private Integer ratedPower;

    @ApiModelProperty(name = "defChargePower", value = "自定义充电功率")
    private Integer defChargePower;

    @ApiModelProperty(name = "defDischargePower", value = "自定义放电功率")
    private Integer defDischargePower;

}
