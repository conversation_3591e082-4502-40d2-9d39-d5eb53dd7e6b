package com.weihengtech.ecos.api.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.NotificationCenterApi;
import com.weihengtech.ecos.api.pojo.base.NotificationResponse;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchMailVo;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchSmsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotificationCenterFallback implements NotificationCenterApi {
	@Override
	public NotificationResponse<String> v1AsyncMail(NotificationBatchMailVo notificationBatchMailVo) {
		log.warn("NotificationCenterFallback#v1AsyncMail: " + JSONUtil.toJsonStr(notificationBatchMailVo));
		return fail();
	}

	@Override
	public NotificationResponse<String> v1AsyncSms(NotificationBatchSmsVo notificationBatchSmsVo) {
		log.warn("NotificationCenterFallback#v1AsyncSms: " + JSONUtil.toJsonStr(notificationBatchSmsVo));
		return fail();
	}

	private <T> NotificationResponse<T> fail() {
		NotificationResponse<T> stringNotificationResponse = new NotificationResponse<>();
		stringNotificationResponse.setCode(500);
		stringNotificationResponse.setMsg("failure");
		stringNotificationResponse.setSuccess(false);
		stringNotificationResponse.setT(System.currentTimeMillis());
		return stringNotificationResponse;
	}
}
