package com.weihengtech.ecos.api.config;


import feign.Request;
import feign.Response;
import feign.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static feign.Util.UTF_8;
import static feign.Util.decodeOrDefault;
import static feign.Util.valuesOrEmpty;

/**
 * 自定义feign日志打印，参数过大截断
 *
 * <AUTHOR>
 * @date 2023/7/12 17:16
 * @version 1.0
 */
public class InfoFeignLogger extends feign.Logger {

    /** 日志打印字符最大长度 */
    private static final Integer LOG_RES_MAX_LEN = 2000;

    private final Logger logger;

    InfoFeignLogger() {
        this(feign.Logger.class);
    }

    private InfoFeignLogger(Class<?> clazz) {
        this(LoggerFactory.getLogger(clazz));
    }

    private InfoFeignLogger(Logger logger) {
        this.logger = logger;
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        // 请求日志放在结果处理中打印，每次调用，只打印一个日志
    }

    /**
     * 获取请求参数
     * @param logLevel
     * @param request
     * @return
     */
    private String getRequestLog(Level logLevel, Request request) {
        // 打印request header
        List<String> headerList = new ArrayList<>(request.headers().size());
        for (String field : request.headers().keySet()) {
            for (String value : valuesOrEmpty(request.headers(), field)) {
                headerList.add(String.format("%s: %s", field, value));
            }
        }
        // 打印请求内容
        String reqBodyText = null;
        if (request.body() != null) {
            if (logLevel.ordinal() >= Level.FULL.ordinal()) {
                reqBodyText = request.charset() != null ? new String(request.body(), request.charset()) : null;
            }
        }
        // 打印request请求
        return String.format("%s %s, header=%s, bodyParam=%s", request.httpMethod(), request.url(),
                StringUtils.collectionToDelimitedString(headerList, ";"), reqBodyText);
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime)
            throws IOException {
        if (logger.isInfoEnabled()) {

            String reason = response.reason() != null && logLevel.compareTo(Level.NONE) > 0 ?
                    " " + response.reason() : "";
            int status = response.status();
            if (logLevel.ordinal() >= Level.HEADERS.ordinal()) {
                if (response.body() != null && !(status == 204 || status == 205)) {
                    // HTTP 204 No Content "...response MUST NOT include a message-body"
                    // HTTP 205 Reset Content "...response MUST NOT include an entity"
                    byte[] bodyData = Util.toByteArray(response.body().asInputStream());
                    // 打印请求结果
                    Request request = response.request();
                    log(configKey, "---> HTTP 状态码：%s%s (耗时：%sms) \n %s \n responseBody:%s", status, reason, elapsedTime,
                            getRequestLog(logLevel, request), getResponseBodyStr(request, bodyData, logLevel));
                    return response.toBuilder().body(bodyData).build();
                } else {
                    // 打印请求结果
                    log(configKey, "---> HTTP 状态码：%s%s (耗时：%sms) \n %s \n body大小：0 byte", status, reason, elapsedTime,
                            getRequestLog(logLevel, response.request()));
                }
            }
            return response;
        }
        return response;
    }

    private String getResponseBodyStr(Request request, byte[] bodyData, Level logLevel)  {
        if (Request.HttpMethod.GET.equals(request.httpMethod()) || ignoreUrl(request.url())) {
            // 过滤get和部分接口调用返回的body（内容过大，且无用）
            byte[] subBodyData = new byte[50];
            System.arraycopy(bodyData, 0, subBodyData, 0,
                    Math.min(bodyData.length, subBodyData.length));
            return decodeOrDefault(subBodyData, UTF_8, "response body为空");
        }
        String responseBody = null;
        // 判断返回参数的大小，过大进行截断
        if (bodyData.length > 2 * LOG_RES_MAX_LEN) {
            byte[] subBodyData = new byte[2 * LOG_RES_MAX_LEN];
            System.arraycopy(bodyData, 0, subBodyData, 0, 2 * LOG_RES_MAX_LEN);
            if (logLevel.ordinal() >= Level.FULL.ordinal() && subBodyData.length > 0) {
                // 打印response的body
                responseBody = decodeOrDefault(subBodyData, UTF_8, "response body为空");
            }
        }else {
            if (logLevel.ordinal() >= Level.FULL.ordinal() && bodyData.length > 0) {
                // 打印response的body
                responseBody = decodeOrDefault(bodyData, UTF_8, "response body为空");
            }
        }
        return responseBody;
    }

    /** 过滤部分url，不做log */
    private boolean ignoreUrl(String url) {
        return url.contains("/ecos/device/batch_query") ||
                url.contains("/ac-charger/device/record/page");
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(String.format(methodTag(configKey) + format, args));
        }
    }
}
