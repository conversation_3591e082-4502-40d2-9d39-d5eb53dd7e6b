package com.weihengtech.ecos.api.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.EcosEleApi;
import com.weihengtech.ecos.common.InResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-21 11:04
 **/
@Slf4j
@Component
public class EcosEleFallback implements EcosEleApi {

    @Override
    public InResponse<Object> getEleTimeZone() {
        log.warn("EcosEleFallback#getEleTimeZone: fail");
        return InResponse.fail();
    }

    @Override
    public InResponse<Object> getEleCountryRegion(String dataSource) {
        log.warn("EcosEleFallback#getEleCountryRegion: fail");
        return InResponse.fail();
    }

    @Override
    public InResponse<Object> getEleDataSource() {
        log.warn("EcosEleFallback#getEleDataSource: fail");
        return InResponse.fail();
    }

    @Override
    public InResponse<Object> getEleDayAheadPrice(String dataSource,
                                                  String country,
                                                  List<String> region,
                                                  Integer intervalSeconds,
                                                  String type,
                                                  String startTime,
                                                  String endTime,
                                                  String priceUnit) {
        log.warn("EcosEleFallback#getEleDayAheadPrice: {}-{}-{}-{}-{}-{}-{}",dataSource,country, JSONUtil.toJsonStr(region),JSONUtil.toJsonStr(intervalSeconds),type,startTime,endTime);
        return InResponse.fail();
    }


}
