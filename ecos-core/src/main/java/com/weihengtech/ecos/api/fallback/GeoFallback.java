package com.weihengtech.ecos.api.fallback;

import cn.hutool.json.JSONObject;
import com.weihengtech.ecos.api.GeoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-26 11:02
 **/
@Component
@Slf4j
public class GeoFallback implements GeoApi {
    @Override
    public JSONObject queryCity(String key, String location, String adm, String range, String number, String lang) {
        log.warn("com.weihengtech.api.fallback.GeoFallback.queryCity：{}-{}-{}-{}-{}-{}", key, location, adm, range, number, lang);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }
}
