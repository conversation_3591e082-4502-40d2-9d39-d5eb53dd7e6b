package com.weihengtech.ecos.api.config;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableMap;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EcosServiceConfiguration {

	@Value("${custom.auth.access-key}")
	private String accessKey;

	@Value("${custom.auth.access-secret}")
	private String accessSecret;

	@Bean
	public RequestInterceptor headerInterceptor() {
		return requestTemplate -> {
			List<String> accessKeyList = ListUtil.toList(accessKey);
			List<String> accessSecretList = ListUtil.toList(accessSecret);
			List<String> contentTypeList = ListUtil.toList("application/json;charset=utf-8");
			Map<String, Collection<String>> headers = ImmutableMap.of("AccessKey", accessKeyList, "AccessSecret",
					accessSecretList, "Content-Type", contentTypeList
			);
			requestTemplate.headers(headers);
		};
	}
}
