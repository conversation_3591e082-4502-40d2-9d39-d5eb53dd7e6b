package com.weihengtech.ecos.api.config;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableMap;
import com.weihengtech.ecos.utils.EleUtil;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-21 11:12
 **/
public class EcosEleConfiguration {

    @Value("${custom.ele.salt}")
    private String salt;

    @Value("${custom.ele.name}")
    private String name;
    @Bean
    public RequestInterceptor headerInterceptor() {
        return requestTemplate -> {
            List<String> contentTypeList = ListUtil.toList("application/json;charset=utf-8");
            Map<String, Collection<String>> headers = ImmutableMap.of(
                    "sign", ListUtil.toList(EleUtil.createSign(name,salt)),
                    "Content-Type", contentTypeList
            );
            requestTemplate.headers(headers);
        };
    }
}
