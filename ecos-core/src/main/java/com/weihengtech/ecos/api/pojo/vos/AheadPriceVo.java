package com.weihengtech.ecos.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-22 14:17
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价预测接口查询入参")
public class AheadPriceVo {

    @ApiModelProperty(name = "region", value = "地区", required = true)
    @NotNull(message = "err.not.null")
    private List<String> region;

    @ApiModelProperty(name = "intervalSeconds", value = "间隔时间(单位秒，荷兰仅支持3600s，德国支持3600s和900s)", required = true)
    private Integer intervalSeconds;

    @ApiModelProperty(name = "time", value = "查询时间，昨天：-1，今天：0，明天：1", required = true)
    private Integer time;

    private String timezone;

    private String priceUnit;
}
