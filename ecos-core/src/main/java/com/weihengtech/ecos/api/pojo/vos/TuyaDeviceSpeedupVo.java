package com.weihengtech.ecos.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "设备提高采样率入参")
public class TuyaDeviceSpeedupVo {

	@ApiModelProperty(name = "deviceId", value = "设备涂鸦id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
