package com.weihengtech.ecos.api.config;

import feign.Logger;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * feign 配置类：定义日志级别、远程调用携带鉴权header、okHttp客户端
 *
 * <AUTHOR>
 * @date 2023/7/20 13:46
 * @version 1.0
 */
@EnableFeignClients(basePackages = "com.weihengtech.ecos.api")
@Configuration
public class FeignConfig {

    @Bean
    Logger.Level logger() {
        return Logger.Level.FULL;
    }

    @Bean
    Logger infoFeignLogger() {
        return new InfoFeignLogger();
    }
}
