package com.weihengtech.ecos.api;


import cn.hutool.json.JSONObject;
import com.weihengtech.ecos.api.fallback.GeoFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(name = "Geo", url = "${custom.url.weather.geoapi}", decode404 = true, fallback = GeoFallback.class)
public interface GeoApi {

    @GetMapping("/lookup?gzip=n")
    JSONObject queryCity(
            @RequestParam(name = "key", required = true) String key,
            @RequestParam(name = "location", required = true) String location,
            @RequestParam(name = "adm", required = false) String adm,
            @RequestParam(name = "range", required = false) String range,
            @RequestParam(name = "number", required = false) String number,
            @RequestParam(name = "lang", required = false) String lang
    );
}
