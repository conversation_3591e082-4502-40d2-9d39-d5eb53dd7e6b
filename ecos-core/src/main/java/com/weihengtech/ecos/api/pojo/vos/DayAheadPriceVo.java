package com.weihengtech.ecos.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-21 15:29
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "电价查询入参")
public class DayAheadPriceVo {

    @ApiModelProperty(name = "dataSource", value = "数据源,目前仅支持Entsoe", required = true)
    @NotBlank(message = "err.not.blank")
    private String dataSource;

    @ApiModelProperty(name = "country", value = "国家")
    private String country;

    @ApiModelProperty(name = "region", value = "地区", required = true)
    @NotNull(message = "err.not.null")
    private List<String> region;

    @ApiModelProperty(name = "intervalSeconds", value = "间隔时间(单位秒，荷兰仅支持3600s，德国支持3600s和900s)", required = true)
    private Integer intervalSeconds;

    @ApiModelProperty(name = "type", value = "类型，Day-Ahead")
    private String type;

    @ApiModelProperty(name = "startTime", value = "开始时间(秒级时间戳)", required = true)
    @NotBlank(message = "err.not.blank")
    private String startTime;

    @ApiModelProperty(name = "endTime", value = "结束时间(秒级时间戳)", required = true)
    @NotBlank(message = "err.not.blank")
    private String endTime;

    @ApiModelProperty(name = "priceUnit", value = "价格单位：MWh|kWh|Wh")
    private String priceUnit;

}
