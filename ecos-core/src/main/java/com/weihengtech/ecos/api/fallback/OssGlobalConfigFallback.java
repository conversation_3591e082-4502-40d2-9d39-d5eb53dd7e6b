package com.weihengtech.ecos.api.fallback;

import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OssGlobalConfigFallback implements OssGlobalConfigApi {

	@Override
	public DataResponse<OssGlobalConfigBo> getGlobalConfig() {
		log.warn("com.weihengtech.api.fallback.OssGlobalConfigFallback.getGlobalConfig");
		DataResponse<OssGlobalConfigBo> response = new DataResponse<>();
		response.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
		response.setMessage("fail");
		response.setData(null);
		return response;
	}
}
