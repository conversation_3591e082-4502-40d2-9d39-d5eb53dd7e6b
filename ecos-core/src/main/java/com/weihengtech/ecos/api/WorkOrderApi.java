package com.weihengtech.ecos.api;

import com.weihengtech.ecos.api.config.WorkerOrderConfiguration;
import com.weihengtech.ecos.api.fallback.WorkerOrderFallback;
import com.weihengtech.ecos.api.pojo.base.WorkOrderResp;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.ecos.api.pojo.vos.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "WorkOrder", url = "${custom.url.order}", decode404 = true, fallback = WorkerOrderFallback.class, configuration = WorkerOrderConfiguration.class)
public interface WorkOrderApi {

	@PostMapping("/v1/order/{projectToken}/add")
	WorkOrderResp<Object> addWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderAddVo workOrderAddVo);

	@PostMapping("/v1/order/{projectToken}/quiz")
	WorkOrderResp<Object> quizWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderQuizVo workOrderQuizVo);

	@PostMapping("/v1/order/{projectToken}/close")
	WorkOrderResp<Object> closeWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderCloseVo workOrderCloseVo);

	@PostMapping("/v1/order/{projectToken}/page")
	WorkOrderResp<WorkOrderPageDto> pageWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderPageVo workOrderPageVo);

	@PostMapping("/v1/order/{projectToken}/list")
	WorkOrderResp<List<WorkOrderDetailDto>> listWorkOrder(@PathVariable("projectToken") String projectToken, @RequestBody WorkOrderListVo workOrderListVo);

}
