package com.weihengtech.ecos.api.pojo.dtos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "回复回参")
public class WorkOrderReplyDto {

	@ApiModelProperty(value = "回复内容")
	private String content;
	@ApiModelProperty(value = "创建时间")
	private Long createTime;
	@ApiModelProperty(value = "是否是提问者")
	private Boolean isQuizzer;
	@ApiModelProperty(value = "名字")
	private String name;
	@ApiModelProperty(value = "图片列表")
	private List<String> picList;
}
