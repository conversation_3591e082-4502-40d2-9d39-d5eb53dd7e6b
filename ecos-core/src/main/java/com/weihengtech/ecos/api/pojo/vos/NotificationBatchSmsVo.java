package com.weihengtech.ecos.api.pojo.vos;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class NotificationBatchSmsVo {
	private String phoneNumbers;
//	国内:
//	SMS_243391265 您重点关注的设备 [name] 离线了，快去看看吧！
//	SMS_203717191 尊敬的用户，当前您[siteName]站点的设备（SN[deviceSn]）出现[failMessage]故障，请开展检查。
//	SMS_203716924 尊敬的用户您好，江苏为恒智能科技有限公司通知您，您的产品[product]，SN: [sn]，发生了[event]，请及时关注。
//	SMS_172745021 验证码[code]，您正在进行身份验证，打死不要告诉别人哦！
//	SMS_172745020 验证码[code]，您正在登录，若非本人操作，请勿泄露。
//	SMS_172745019 验证码[code]，您正尝试异地登录，若非本人操作，请勿泄露。
//	SMS_172745018 验证码[code]，您正在注册成为新用户，感谢您的支持！
//	SMS_172745017 验证码[code]，您正在尝试修改登录密码，请妥善保管账户信息。
//	SMS_172745016 验证码[code]，您正在尝试变更重要信息，请妥善保管账户信息。
//	国际:
//	SMS_462020911: Register code [code] . The code will time out within [timeout] minutes
//	SMS_462015931: Cancel code [code] . Note that actions cannot be cancel.
//	SMS_461970875: Code [code] . Note that you are changing your password.
	private String templateCode;
	private String templateParam;
}
