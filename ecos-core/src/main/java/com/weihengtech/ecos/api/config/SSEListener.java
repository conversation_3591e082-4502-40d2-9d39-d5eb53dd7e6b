package com.weihengtech.ecos.api.config;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.weihengtech.ecos.api.pojo.vos.KnowledegeConversationVo;
import com.weihengtech.ecos.enums.ChatRoleEnum;
import com.weihengtech.ecos.consts.KnowledgeCommonConst;
import com.weihengtech.ecos.model.dos.ClientSessionDo;
import com.weihengtech.ecos.model.dos.ClientSessionMessageDo;
import com.weihengtech.ecos.service.app.ClientSessionMessageService;
import com.weihengtech.ecos.service.app.ClientSessionService;
import com.weihengtech.ecos.utils.ExecuteSSEUtil;
import com.weihengtech.ecos.utils.SSEListenerUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * @program: ecos-server
 * @description: SSE链接
 * @author: jiahao.jin
 * @create: 2024-05-09 19:58
 **/
@Slf4j
@Data
@DSTransactional
public class SSEListener extends EventSourceListener{

    private CountDownLatch countDownLatch = new CountDownLatch(1);

    private ClientSessionDo clientSessionDo;

    private KnowledegeConversationVo conversationItem;

    private HttpServletResponse rp;

    private StringBuffer output = new StringBuffer();

    private String apiHost;

    private String chatId;

    private ClientSessionService clientSessionService;

    private ClientSessionMessageService clientSessionMessageService;

    private SnowFlakeUtil snowFlakeUtil;

    private final SSEListenerUtil sseListenerUtil;

    public SSEListener(SSEListenerUtil sseListenerUtil, ClientSessionDo clientSessionDo, KnowledegeConversationVo conversationItem, HttpServletResponse response) {
        this.sseListenerUtil = sseListenerUtil;
        this.apiHost = sseListenerUtil.getApiHost();
        this.clientSessionService = sseListenerUtil.getClientSessionService();
        this.clientSessionMessageService = sseListenerUtil.getClientSessionMessageService();
        this.snowFlakeUtil = sseListenerUtil.getSnowFlakeUtil();
        this.clientSessionDo = clientSessionDo;
        this.conversationItem = conversationItem;
        this.chatId = "".equals(this.conversationItem.getChatId()) ? "0" : this.conversationItem.getChatId();
        this.rp = response;
    }
    @PostConstruct
    public void init() {
        // 在这里使用apiHost
        System.out.println("apiHost: " + apiHost);
    }


    /**
     * {@inheritDoc}
     * 建立sse连接
     */
    @Override
    public void onOpen(final EventSource eventSource, final Response
            response) {
        if (rp != null) {
            rp.setContentType("text/event-stream");
            rp.setCharacterEncoding("UTF-8");
            rp.setStatus(200);
            log.info("建立sse连接..." + JSON.toJSONString(conversationItem));
        } else {
            log.info("客户端非sse推送" + JSON.toJSONString(conversationItem));
        }
    }

    /**
     * 事件
     *
     * @param eventSource
     * @param id
     * @param type
     * @param data
     */
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            output.append(data + ",");
            if ("finish".equals(type)) {
                log.info("请求结束{} {}", this.chatId, output);

                // 添加content为null的结束标记
                rp.getWriter().write("event:" + type + "\n");
                rp.getWriter().write("id:" + this.chatId + "\n");
                rp.getWriter().write("data:null\n\n");
                rp.getWriter().flush();
            }
            if ("error".equals(type)) {
                log.info("{}: {}source {}", this.chatId, data, JSON.toJSONString(conversationItem));
            }
            if (rp != null) {
                if ("\n".equals(data)) {
                    rp.getWriter().write("event:" + type + "\n");
                    rp.getWriter().write("id:" + this.chatId + "\n");
                    rp.getWriter().write("data:\n\n");
                    rp.getWriter().flush();
                } else {
                    type = KnowledgeCommonConst.KNOWLEDGE_MESSAGE_TYPE;
                    String[] dataArr = data.split("\\n");
                    for (int i = 0; i < dataArr.length; i++) {
                        if ("[DONE]".equals(dataArr[i])) {
                            continue;
                        }
                        if (i == 0) {
                            rp.getWriter().write("event:" + type + "\n");
                            rp.getWriter().write("id:" + this.chatId + "\n");
                        }
                        if (i == dataArr.length - 1) {
                            rp.getWriter().write("data:" + dataArr[i] + "\n\n");
                            rp.getWriter().flush();
                        } else {
                            rp.getWriter().write("data:" + dataArr[i] + "\n");
                            rp.getWriter().flush();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("消息错误[" + JSON.toJSONString(conversationItem) + "]", e);
            countDownLatch.countDown();
            throw new RuntimeException(e);
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void onClosed(final EventSource eventSource) {
        // 添加content为null的结束标记
        try {
            rp.getWriter().write("event:" + KnowledgeCommonConst.END_MESSAGE_TYPE + "\n");
            rp.getWriter().write("id:" + this.chatId + "\n");
            rp.getWriter().write("data:null\n\n");
            rp.getWriter().flush();
        } catch (IOException e) {
            log.error("消息错误[" + JSON.toJSONString(conversationItem) + "]", e);
            countDownLatch.countDown();
            throw new RuntimeException(e);
        }
        log.info("sse连接关闭:{}", this.chatId);

        // 解析JSON字符串并获取拼接后的内容
        String concatenatedContent = getConcatenatedContent();

        // 保存结果到数据库
        log.info("结果输出:" + concatenatedContent);

        // 处理会话和消息的保存逻辑
        handleSessionAndMessages(concatenatedContent);

        countDownLatch.countDown();
    }

    private String getConcatenatedContent() {
        // 解析 JSON 字符串
        JSONArray jsonArray = JSONUtil.parseArray("[" + output.toString() + "]");

        StringBuilder concatenatedContent = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            if ("[\"DONE\"]".equals(jsonArray.get(i).toString())) {
                continue;
            }
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            // 提取 content 字段的值并拼接
            concatenatedContent.append(jsonObject.getStr("content"));
        }
        return concatenatedContent.toString();
    }

    private void handleSessionAndMessages(String concatenatedContent) {

        String title = "New Session";

        // 请求AI生成标题
        if ("".equals(conversationItem.getChatId())) {
            String token = SecurityUtil.generateAuthHeader(KnowledgeCommonConst.AUTH_HEADER);
            KnowledegeConversationVo knowledegeConversationVo1 = new KnowledegeConversationVo();
            knowledegeConversationVo1.setUserId(conversationItem.getUserId());
            knowledegeConversationVo1.setHistoryChatSize(0);
            knowledegeConversationVo1.setModel(KnowledgeCommonConst.ALI_MODEL);
            knowledegeConversationVo1.setChatId("");
            List<KnowledegeConversationVo.Message> messages = new ArrayList<>();
            KnowledegeConversationVo.Message message1 = new KnowledegeConversationVo.Message();
            message1.setRole(ChatRoleEnum.USER.getRole());
            message1.setContent(conversationItem.getMessages().get(2).getContent());
            messages.add(message1);
            KnowledegeConversationVo.Message message2 = new KnowledegeConversationVo.Message();
            message2.setRole(ChatRoleEnum.AI.getRole());
            message2.setContent(concatenatedContent);
            messages.add(message2);
            KnowledegeConversationVo.Message message3 = new KnowledegeConversationVo.Message();
            message3.setRole(ChatRoleEnum.USER.getRole());
            message3.setContent(KnowledgeCommonConst.TOPIC_GENERATE_PROMPT);
            messages.add(message3);
            knowledegeConversationVo1.setMessages(messages);


            String aiTitle = ExecuteSSEUtil.knowledgePostReq(apiHost + KnowledgeCommonConst.LLM_CHAT_URL, token, JSON.toJSONString(knowledegeConversationVo1));
            title = aiTitle == null ? title : aiTitle;
            int maxLength = 20;
            if (title.length() > maxLength) {
                title = title.substring(0, maxLength);
            }
        }

        // 处理会话的保存逻辑
        ClientSessionDo session = handleSession(title, concatenatedContent);

        // 保存问题和回答进数据库
        handleMessages(session, concatenatedContent);
    }

    private ClientSessionDo handleSession(String title, String concatenatedContent) {
        // 获取会话的chatId
        String chatId = getChatIdFromOutput();

        LocalDateTime now = LocalDateTime.now();
        if (clientSessionDo.getId() == null){
            // 第一次问答创建会话
            long id = snowFlakeUtil.generateId();
            clientSessionDo.setId(id);
            clientSessionDo.setChatId(chatId);
            clientSessionDo.setTitle(title);
            clientSessionDo.setUserId(conversationItem.getUserId());
            clientSessionDo.setDeleted(false);
            clientSessionDo.setUpdateTime(now);
            clientSessionDo.setCreateTime(now);
            clientSessionService.save(clientSessionDo);
        } else if (clientSessionDo.getChatId() == null || "".equals(clientSessionDo.getChatId())) {
            // 第一次问答AI更新会话的标题和chatId
            clientSessionDo.setTitle(title);
            clientSessionDo.setChatId(chatId);
            clientSessionDo.setUpdateTime(now);
            clientSessionService.updateById(clientSessionDo);
        }
        return clientSessionDo;
    }

    private void handleMessages(ClientSessionDo session, String concatenatedContent) {
        // 查询该会话的最后一条消息,得到消息的父ID
        ClientSessionMessageDo lastMessage = clientSessionMessageService.queryLastMessageBySessionId(session.getId());
        Long fatherMsgId = lastMessage != null ? lastMessage.getId() : 0L;

        LocalDateTime now = LocalDateTime.now();
        // 用户问题
        ClientSessionMessageDo userMessage = createMessage(session, conversationItem.getUserId(), ChatRoleEnum.USER, conversationItem.getMessages().get(2).getContent(), fatherMsgId, now);

        // AI回答
        ClientSessionMessageDo aiMessage = createMessage(session, conversationItem.getUserId(), ChatRoleEnum.AI, concatenatedContent, userMessage.getId(), now);

        clientSessionMessageService.saveBatch(Arrays.asList(userMessage, aiMessage));
        session.setUpdateTime(now);
        clientSessionService.updateById(session);
    }

    private ClientSessionMessageDo createMessage(ClientSessionDo session, Long userId, ChatRoleEnum role, String content, Long parentId, LocalDateTime timestamp) {
        ClientSessionMessageDo message = new ClientSessionMessageDo();
        message.setId(snowFlakeUtil.generateId());
        message.setSessionId(session.getId());
        message.setUserId(userId);
        message.setContent(content);
        message.setParentId(parentId);
        message.setType(role.getCode());
        message.setTimestamp(timestamp);
        return message;
    }

    private String getChatIdFromOutput() {
        // 解析JSON字符串
        JSONArray jsonArray = new JSONArray("[" + output.toString() + "]");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (jsonObject.containsKey("chatId")) {
                return jsonObject.get("chatId").toString();
            }
        }
        return "";
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onFailure(final EventSource eventSource, final Throwable t, final Response response) {
        log.error("使用事件源时出现异常... [响应：{}]...", conversationItem.getChatId());
        countDownLatch.countDown();
    }

    public CountDownLatch getCountDownLatch() {
        return this.countDownLatch;
    }
}