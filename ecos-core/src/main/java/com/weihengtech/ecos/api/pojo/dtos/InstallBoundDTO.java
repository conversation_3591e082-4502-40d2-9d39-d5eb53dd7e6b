package com.weihengtech.ecos.api.pojo.dtos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/11 15:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InstallBoundDTO {

    @ApiModelProperty("安装商id")
    private Long userId;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("保留时长：-1：不保留，999：永久")
    private Integer saveDeviceTime;


}
