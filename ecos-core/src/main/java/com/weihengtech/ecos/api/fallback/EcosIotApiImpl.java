package com.weihengtech.ecos.api.fallback;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.influxdb.query.FluxTable;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.pojo.dtos.DeviceBasicInfoDto;
import com.weihengtech.ecos.api.pojo.vos.TuyaDeviceSpeedupVo;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.TsdbQueryDTO;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.thirdpart.HomeMemberVo;
import com.weihengtech.ecos.model.vos.thirdpart.TuyaAddHomeMemberVo;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.utils.BeanUtil;
import com.weihengtech.ecos.utils.IotUtil;
import com.weihengtech.ecos.utils.ModbusParseUtil;
import com.weihengtech.sdk.iot.ecos.EcosIotClient;
import com.weihengtech.sdk.iot.ecos.model.EcosIotResponse;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpConfigurationResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.request.*;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDeviceDetailResponse;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDevicePropertyStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.response.CloudHomeMembersResponse;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.UndeclaredThrowableException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-10-13 17:35
 **/
@Slf4j
@Service
public class EcosIotApiImpl implements EcosIotApi {

    @Resource
    private EcosIotClient ecosIotClient;
    @Resource
    private StrategyService strategyService;

    @Override
    public List<CloudDevicePropertyStatusResponse> getDevicePropertyInfo(String deviceId, String cloud) {
        CloudSingleDevicePropertyRequest devicePropertyRequest = new CloudSingleDevicePropertyRequest(deviceId, cloud);
        return getIotCloudRes(devicePropertyRequest, ecosIotClient::queryCloudSingleDeviceProperty, "getDevicePropertyInfo");
    }

    @Override
    public List<Integer> readDevice(String wifiSn, Integer slaveId, Integer startAddress, Integer len, String cloud) {
        // TODO: 2023/10/16 等待上传设备的category和设备productId
        try {
            DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(wifiSn, cloud);
            BeanUtil.assertNotNull(basicInfoDto);

            CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
            int funcCode = 3;
            if (startAddress >= 30000 && startAddress <= 40000) {
                funcCode = 4;
            }
            EsTransportReadCommandRequest readCommandRequest = new EsTransportReadCommandRequest(
                    wifiSn, slaveId, startAddress, len, funcCode, cloud, categoryEnum, basicInfoDto.getProductId());
            EcosIotResponse<List<Integer>> listEcosIotResponse = ecosIotClient.sendEsTransportReadCommand(readCommandRequest);

            if (!listEcosIotResponse.getSuccess()) {
                log.warn("Warning readDevice (iot request cloud): {}-{}-{}-{}-{}", wifiSn, slaveId, startAddress, len, cloud);
                return new ArrayList<>();
            }

            return listEcosIotResponse.getResult();
        } catch (UndeclaredThrowableException e) {
            if ("timeout".equals(e.getUndeclaredThrowable().getMessage())) {
                return new ArrayList<>();
            }
            log.error("Error readDevice (ecos request iot): {}-{}-{}-{}-{}:{}", wifiSn, slaveId, startAddress, len, cloud, e.getMessage());
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
    }

    @Override
    public void writeDevice(String wifiSn, Integer slaveId, Integer startAddress, Integer len,
                            List<Integer> values, String cloud) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(wifiSn, cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EsTransportWriteCommandRequest writeCommandRequest = new EsTransportWriteCommandRequest(
                wifiSn, slaveId, startAddress, len, values, cloud, categoryEnum, basicInfoDto.getProductId());
        getIotCloudRes(writeCommandRequest, ecosIotClient::sendEsTransportWriteCommand, "writeDevice");
    }


    @Override
    public DeviceBasicInfoDto getDeviceBasicInfo(String deviceId, String cloud) {
        CloudSingleDeviceDetailRequest deviceDetailRequest = new CloudSingleDeviceDetailRequest(deviceId, cloud);
        CloudDeviceDetailResponse result = getIotCloudRes(deviceDetailRequest, ecosIotClient::queryCloudSingleDeviceDetail, "getDeviceBasicInfo");
        DeviceBasicInfoDto deviceBasicInfoDto = new DeviceBasicInfoDto();
        deviceBasicInfoDto.setId(result.getId());
        deviceBasicInfoDto.setUuid(result.getUuid());
        deviceBasicInfoDto.setIp(result.getIp());
        deviceBasicInfoDto.setLat(result.getLat());
        deviceBasicInfoDto.setLon(result.getLon());
        deviceBasicInfoDto.setCategory(result.getCategory());
        deviceBasicInfoDto.setProductId(result.getProductId());
        deviceBasicInfoDto.setOnline(result.getOnline());
        return deviceBasicInfoDto;
    }

    @Override
    public Boolean speedupDevice(TuyaDeviceSpeedupVo tuyaDeviceSpeedupVo, String cloud) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(tuyaDeviceSpeedupVo.getDeviceId(), cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EsSpeedupCommandRequest speedupCommandRequest = new EsSpeedupCommandRequest(basicInfoDto.getId(), cloud, categoryEnum, basicInfoDto.getProductId());
        return getIotCloudRes(speedupCommandRequest, ecosIotClient::sendEsSpeedupCommand, "speedupDevice");
    }

    @Override
    public Boolean resetDevice(String deviceId, String cloud) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(deviceId, cloud);
        BeanUtil.assertNotNull(basicInfoDto);

        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EsResetCommandRequest resetCommandRequest = new EsResetCommandRequest(deviceId, cloud, categoryEnum, basicInfoDto.getProductId());
        return getIotCloudRes(resetCommandRequest, ecosIotClient::sendEsResetCommand, "resetDevice");
    }

    @Override
    public Integer getDeviceStatus(HybridSinglePhaseDO hybridSinglePhaseDO) {
        if (hybridSinglePhaseDO.getFirstInstall() == 0) {
            return DeviceStatusEnum.UNKNOWN.getDbCode();
        }
        if (StrUtil.isBlank(hybridSinglePhaseDO.getWifiSn())) {
            return DeviceStatusEnum.OFFLINE.getDbCode();
        }
        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        Boolean deviceStatus = this.checkDeviceOnline(wifiSn, cloud);
        if (deviceStatus) {
            TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                    .chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
            Dict dict = timeSeriesDatabaseService.lastPoint(hybridSinglePhaseDO.getDeviceName(),
                    ListUtil.toList("sys_run_mode"), System.currentTimeMillis()
            );
            return new BigDecimal(String.valueOf(dict.get("sys_run_mode", "-1"))).intValue();
        }
        return DeviceStatusEnum.OFFLINE.getDbCode();
    }

    @Override
    public Boolean checkDeviceOnline(String deviceId, String cloud) {
        CloudDeviceOnlineStatusRequest onlineStatusRequest = new CloudDeviceOnlineStatusRequest(deviceId, cloud);
        Boolean res;
        try {
            res = getIotCloudRes(onlineStatusRequest, ecosIotClient::queryCloudDeviceOnlineStatus, "checkDeviceOnline");
        } catch (Exception e) {
            log.error(String.format("checkDeviceOnline failed: %s", deviceId), e);
            res = false;
        }
        return res;
    }

    @Override
    public String getDeviceSnByWifiSn(String wifiSn, String cloud) {
        try {
            List<Integer> integers = this.readDevice(wifiSn, 1, 30042, 10, cloud);
            return ModbusParseUtil.parseAscii(integers);
        } catch (Exception e) {
            log.error(String.format("tuyaIotClient getDeviceSnByWifiSn failed: %s", wifiSn), e);
            return StrUtil.EMPTY;
        }
    }

    @Override
    public String getDeviceIpByWifiSn(String deviceId, String cloud) {
        CloudDeviceIpRequest deviceIpRequest = new CloudDeviceIpRequest(deviceId, cloud);
        return getIotCloudRes(deviceIpRequest, ecosIotClient::queryCloudDeviceIp, "getDeviceIpByWifiSn");
    }

    @Override
    public Boolean addHomeMember(Long homeId, TuyaAddHomeMemberVo tuyaAddHomeMemberVo, String cloud) {
        HomeMemberVo member = tuyaAddHomeMemberVo.getMember();
        CloudAddHomeMemberCommandRequest memberCommandRequest = new CloudAddHomeMemberCommandRequest(
                StrUtil.toString(homeId),
                tuyaAddHomeMemberVo.getAppSchema(),
                member.getCountryCode(),
                member.getMemberAccount(),
                member.getAdmin(),
                member.getName(), cloud);
        return getIotCloudRes(memberCommandRequest, ecosIotClient::sendCloudAddHomeMemberCommand, "addHomeMember");
    }

    @Override
    public Boolean deleteHomeUser(Long homeId, String uid, String cloud) {
        CloudDeleteHomeMemberCommandRequest homeMemberCommandRequest = new CloudDeleteHomeMemberCommandRequest(StrUtil.toString(homeId), uid, cloud);
        return getIotCloudRes(homeMemberCommandRequest, ecosIotClient::sendCloudDeleteHomeMemberCommand, "deleteHomeUser");
    }

    @Override
    public List<CloudHomeMembersResponse> getHomeUserList(Long homeId, String cloud) {
        CloudHomeMembersRequest homeMembersRequest = new CloudHomeMembersRequest(StrUtil.toString(homeId), cloud);
        return getIotCloudRes(homeMembersRequest, ecosIotClient::queryCloudHomeMembers, "getHomeUserList");
    }

    @Override
    public String getHomeId(String deviceId, String cloud) {
        CloudHomeIdByCloudIdRequest idRequest = new CloudHomeIdByCloudIdRequest(deviceId, cloud);
        return getIotCloudRes(idRequest, ecosIotClient::queryCloudHomeIdByTuyaId, "getHomeId");
    }

    @Override
    public Boolean switchTuyaSocket(String cloudId, String cloud, Boolean switchStatus, Integer switchIndex) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(cloudId, cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EcoSwitchCommandRequest ecoSwitchCommandRequest = new EcoSwitchCommandRequest(cloudId, switchIndex, switchStatus, cloud, categoryEnum, basicInfoDto.getProductId());
        return getIotCloudRes(ecoSwitchCommandRequest, ecosIotClient::sendEcoSwitchCommand, "switchTuyaSocket");
    }

    @Override
    public Boolean updateTuyaSocketCountdown(String cloudId, String cloud, Integer second) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(cloudId, cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EcoCountdownCommandRequest request = new EcoCountdownCommandRequest(cloudId, 1, second, cloud, categoryEnum, basicInfoDto.getProductId());
        return getIotCloudRes(request, ecosIotClient :: sendEcoCountdownCommand, "updateTuyaSocketCountdown");
    }

    @Override
    public Boolean updateTuyaSocketRandomData(String cloudId, String cloud, String code) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(cloudId, cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EcoRandomTimeCommandRequest request = new EcoRandomTimeCommandRequest(cloudId, cloud, categoryEnum, basicInfoDto.getProductId(), code);
        return getIotCloudRes(request, ecosIotClient::sendEcoRandomTimeCommand, "updateTuyaSocketRandomData");
    }

    @Override
    public TuyaDeviceLogsResponse queryTuyaDeviceLogs(String cloudId, String cloud, String type, Long startTime, Long endTime, String codes, Integer queryType, Integer size, String startRowKey) {
        CloudTuyaDeviceLogsRequest request = new CloudTuyaDeviceLogsRequest(cloudId, cloud, type, startTime, endTime, size, codes, queryType, startRowKey);
        return getIotCloudRes(request, ecosIotClient :: queryCloudTuyaDeviceLogs, "queryTuyaDeviceLogs");
    }

    @Override
    public Boolean switchTuyaSocketOverCharge(String cloudId, String cloud, Boolean switchStatus) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(cloudId, cloud);
        BeanUtil.assertNotNull(basicInfoDto);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EcoOverchargeSwitchCommandRequest request = new EcoOverchargeSwitchCommandRequest(cloudId, switchStatus, cloud, categoryEnum, basicInfoDto.getProductId());
        return getIotCloudRes(request, ecosIotClient::sendEcoOverchargeSwitchCommand, "switchTuyaSocketOverCharge");
    }

    @Override
    public CpStatusResponse getChargeStationStatus(String cloudId, String cloud) {
        CpStatusRequest request = new CpStatusRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT);
        return getIotCloudRes(request, ecosIotClient::queryCpStatus, "getChargeStationStatus");
    }

    @Override
    public List<String> queryChargeStationCardList(String cloudId, String cloud) {
        CpCardsListRequest request = new CpCardsListRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT);
        return getIotCloudRes(request, ecosIotClient::queryCpCardList, "queryChargeStationCardList");
    }

    @Override
    public Boolean addChargeStationCard(String cloudId, String cloud, String cardId) {
        CpAddCardsRequest request = new CpAddCardsRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, cardId);
        return getIotCloudRes(request, ecosIotClient :: sendCpAddCardCommand, "sendCpAddCardCommand");
    }

    @Override
    public Boolean coverChargeStationCards(String cloudId, String cloud, List<String> cardIds) {
        CpCoverCardsRequest request = new CpCoverCardsRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, cardIds);
        return getIotCloudRes(request, ecosIotClient::sendCpCoverCardsCommand, "coverChargeStationCards");
    }

    @Override
    public Boolean removeChargeStationCards(String cloudId, String cloud, List<String> cardIds) {
        CpRemoveCardsRequest request = new CpRemoveCardsRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, cardIds);
        return getIotCloudRes(request, ecosIotClient::sendRemoveCardsCommand, "removeChargeStationCards");
    }

    @Override
    public CpInfoResponse queryChargeStationInfo(String cloudId, String cloud) {
        CpInfoRequest request = new CpInfoRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT);
        return getIotCloudRes(request, ecosIotClient::queryCpInfo, "queryChargeStationInfo");
    }

    @Override
    public Boolean setMaxChargeCurrent(String cloudId, String cloud, Integer power, Integer numberPhases) {
        CpMaxProfile cpMaxProfile = new CpMaxProfile(power, "W", numberPhases);
        CpMaxProfileRequest request = new CpMaxProfileRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, cpMaxProfile);
        return getIotCloudRes(request, ecosIotClient::sendCpMaxProfileCommand, "setMaxChargeCurrent");
    }

    @Override
    public Boolean startCharging(String cloudId, String cloud, Integer transactionId) {
        CpStartChargingRequest request = new CpStartChargingRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, transactionId);
        return getIotCloudRes(request, ecosIotClient:: sendCpStartChargingCommand, "startCharging");
    }

    @Override
    public Boolean stopCharging(String cloudId, String cloud, Integer transactionId) {
        CpStopChargingRequest request = new CpStopChargingRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, transactionId);
        return getIotCloudRes(request, ecosIotClient:: sendCpStopChargingCommand, "stopCharging");
    }

    @Override
    public CpConfigurationResponse queryConfiguration(String cloudId, String cloud, String keys) {
        CpConfigurationQueryRequest request = new CpConfigurationQueryRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, keys);
        return getIotCloudRes(request, ecosIotClient:: queryConfiguration, "queryConfiguration");
    }

    @Override
    public Boolean updConfiguration(String cloudId, String cloud, ConfigurationMap configMap) {
        CpConfigurationRequest request = new CpConfigurationRequest(cloudId, cloud, CloudCategoryEnum.CHARGING_POINT, configMap);
        return getIotCloudRes(request, ecosIotClient:: cpUpdConfiguration, "updConfiguration");
    }

    @Override
    public FluxTable queryIotLastPoint(TsdbQueryDTO req, String cloud) {
        CloudIotLastPointRequest request = new CloudIotLastPointRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(), "0");
        return getIotCloudRes(request, ecosIotClient::queryIotLastPoint, "queryIotLastPoint");
    }

    @Override
    public FluxTable queryIotDelta(TsdbQueryDTO req, String cloud) {
        CloudIotDeltaRequest request = new CloudIotDeltaRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(), req.getTimes(), "0");
        return getIotCloudRes(request, ecosIotClient::queryIotDelta, "queryIotDelta");
    }

    @Override
    public FluxTable queryIotWithSample(TsdbQueryDTO req, String cloud) {
        CloudIotWithSampleRequest request = new CloudIotWithSampleRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(), req.getTimes(), req.getAggregateFunction(), "0");
        return getIotCloudRes(request, ecosIotClient :: queryIotWithSample, "queryIotWithSample");
    }

    @Override
    public FluxTable queryIotWithoutSample(TsdbQueryDTO req, String cloud) {
        CloudIotWithoutSampleRequest request = new CloudIotWithoutSampleRequest(req.getCloudId(), cloud,
                req.getStartTime(), req.getEndTime(), req.getMetricList(), "0");
        return getIotCloudRes(request, ecosIotClient :: queryIotWithoutSample, "queryIotWithoutSample");
    }

    @Override
    public void sendEsResetCommand(String cloudId, String cloud) {
        DeviceBasicInfoDto basicInfoDto = this.getDeviceBasicInfo(cloudId, cloud);
        CloudCategoryEnum categoryEnum = IotUtil.getCategoryEnum(basicInfoDto.getCategory());
        EsResetCommandRequest request = new EsResetCommandRequest(cloudId, cloud,
                categoryEnum, basicInfoDto.getProductId());
        getIotCloudRes(request, ecosIotClient :: sendEsResetCommand, "sendEsResetCommand");
    }

    @Override
    public IotDeviceEventResponse queryCloudIotDeviceEvent(String cloudId, String cloud, HomeEventsFaultVo pageInfo) {
        CloudIotDeviceEventRequest request = new CloudIotDeviceEventRequest(cloudId, cloud, pageInfo.getType(),
                pageInfo.getStart() * 1000, pageInfo.getEnd() * 1000, pageInfo.getPageNum(), pageInfo.getPageSize(), StrUtil.EMPTY);
        return getIotCloudRes(request, ecosIotClient :: queryCloudIotDeviceEvent, "queryCloudIotDeviceEvent");
    }

    /**
     * 执行iot接口并获取结果数据
     *
     * @param t 入参
     * @param function 执行方法
     * @param <P> 入参类型
     * @param <T> 返参类型
     * @return 结果
     */
    private <P, T> T getIotCloudRes(P t, Function<P, EcosIotResponse<T>> function, String funcName) {
        EcosIotResponse<T> response = function.apply(t);
        if (!response.getSuccess()) {
            log.error("Error getIotCloudRes func is {}, param is : {}, res is {}", funcName, JSONUtil.toJsonStr(t), response.getResult());
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
        log.info("getIotCloudRes func is {}, param is : {}, res is {}",
                funcName, JSONUtil.toJsonStr(t), JSONUtil.toJsonStr(response.getResult()));
        return response.getResult();
    }

}
