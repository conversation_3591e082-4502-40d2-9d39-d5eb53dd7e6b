package com.weihengtech.ecos.api.fallback;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.WorkOrderApi;
import com.weihengtech.ecos.api.pojo.base.WorkOrderResp;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.ecos.api.pojo.vos.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkerOrderFallback implements WorkOrderApi {
	@Override
	public WorkOrderResp<Object> addWorkOrder(String projectToken, WorkOrderAddVo workOrderAddVo) {
		log.warn("projectToken {}", projectToken);
		log.warn("WorkerOrderFallback#addWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderAddVo));
		return failResp(null);
	}

	@Override
	public WorkOrderResp<Object> quizWorkOrder(String projectToken, WorkOrderQuizVo workOrderQuizVo) {
		log.warn("WorkerOrderFallback#quizWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderQuizVo));
		return failResp(null);
	}

	@Override
	public WorkOrderResp<Object> closeWorkOrder(String projectToken, WorkOrderCloseVo workOrderCloseVo) {
		log.warn("WorkerOrderFallback#closeWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderCloseVo));
		return failResp(null);
	}

	@Override
	public WorkOrderResp<WorkOrderPageDto> pageWorkOrder(String projectToken, WorkOrderPageVo workOrderPageVo) {
		log.warn("WorkerOrderFallback#pageWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderPageVo));
		return failResp(
				new WorkOrderPageDto()
						.setPages(0)
						.setCurrent(0)
						.setSize(0)
						.setTotal(0)
						.setRecords(ListUtil.empty())
		);
	}

	@Override
	public WorkOrderResp<List<WorkOrderDetailDto>> listWorkOrder(String projectToken, WorkOrderListVo workOrderListVo) {
		log.warn("WorkerOrderFallback#listWorkOrder ==> {}", JSONUtil.toJsonStr(workOrderListVo));
		return failResp(ListUtil.empty());
	}

	private <T> WorkOrderResp<T> failResp(T data) {
		return new WorkOrderResp<T>()
				.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
				.setT(System.currentTimeMillis())
				.setSuccess(Boolean.FALSE)
				.setMsg("FallbackError")
				.setData(data);
	}
}
