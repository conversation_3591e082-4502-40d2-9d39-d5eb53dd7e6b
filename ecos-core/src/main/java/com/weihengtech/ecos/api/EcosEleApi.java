package com.weihengtech.ecos.api;

import com.weihengtech.ecos.api.config.EcosEleConfiguration;
import com.weihengtech.ecos.api.fallback.EcosEleFallback;
import com.weihengtech.ecos.common.InResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "ElectricityPrice", url = "${custom.url.ele}", decode404 = true, configuration = EcosEleConfiguration.class, fallback = EcosEleFallback.class)
public interface EcosEleApi {

    @GetMapping("/data/v1/timezone")
    InResponse<Object> getEleTimeZone();

    @GetMapping("/data/v1/countryRegion")
    InResponse<Object> getEleCountryRegion(@RequestParam String dataSource);

    @GetMapping("/data/v1/dataSource")
    InResponse<Object> getEleDataSource();

    @GetMapping("/data/v1/dayAheadPrice")
    InResponse<Object> getEleDayAheadPrice(
            @RequestParam(name = "dataSource", required = true) String dataSource,
            @RequestParam(name = "country", required = false) String country,
            @RequestParam(name = "region", required = true) List<String> region,
            @RequestParam(name = "intervalSeconds", required = false) Integer intervalSeconds,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "startTime", required = true) String startTime,
            @RequestParam(name = "endTime", required = true) String endTime,
            @RequestParam(name = "priceUnit", required = true) String priceUnit
    );

}
