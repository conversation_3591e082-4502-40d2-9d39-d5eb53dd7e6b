package com.weihengtech.ecos.api;

import com.weihengtech.ecos.api.fallback.OssGlobalConfigFallback;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = "OssConfig", url = "${custom.url.config}", decode404 = true, fallback = OssGlobalConfigFallback.class)
public interface OssGlobalConfigApi {

	@GetMapping("/config.json")
    DataResponse<OssGlobalConfigBo> getGlobalConfig();
}
