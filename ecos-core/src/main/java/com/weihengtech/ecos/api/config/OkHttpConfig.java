package com.weihengtech.ecos.api.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;


/**
 * feign默认使用okhttp3客户端
 *
 * <AUTHOR>
 * @date 2025/3/24 16:23
 * @version 1.0
 */
@Configuration
public class OkHttpConfig {

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                // 连接超时
                .connectTimeout(10, TimeUnit.SECONDS)
                // 读取超时
                .readTimeout(30, TimeUnit.SECONDS)
                // 写入超时
                .writeTimeout(30, TimeUnit.SECONDS)
                // 自动重试
                .retryOnConnectionFailure(true)
                .build();
    }
}