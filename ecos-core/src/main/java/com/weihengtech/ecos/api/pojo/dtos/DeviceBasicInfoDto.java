package com.weihengtech.ecos.api.pojo.dtos;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceBasicInfoDto {

	private String category;

	private String id;

	private String uuid;

	private String ip;

	private String lat;

	private String lon;

	private Boolean online;

	@JSONField(name = "product_id")
	private String productId;

}
