package com.weihengtech.ecos.api;

import com.weihengtech.ecos.api.config.NotificationCenterConfiguration;
import com.weihengtech.ecos.api.fallback.NotificationCenterFallback;
import com.weihengtech.ecos.api.pojo.base.NotificationResponse;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchMailVo;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchSmsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(
		name = "NotificationCenter",
		url = "${custom.url.notify}",
		decode404 = true,
		fallback = NotificationCenterFallback.class,
		configuration = {NotificationCenterConfiguration.class}
)
public interface NotificationCenterApi {

	@PostMapping("/v1/async/mail")
	NotificationResponse<String> v1AsyncMail(@RequestBody NotificationBatchMailVo notificationBatchMailVo);

	@PostMapping("/v1/async/sms")
	NotificationResponse<String> v1AsyncSms(@RequestBody NotificationBatchSmsVo notificationBatchSmsVo);
}
