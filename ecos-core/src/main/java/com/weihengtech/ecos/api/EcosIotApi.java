package com.weihengtech.ecos.api;

import com.influxdb.query.FluxTable;
import com.weihengtech.ecos.api.pojo.dtos.DeviceBasicInfoDto;
import com.weihengtech.ecos.api.pojo.vos.TuyaDeviceSpeedupVo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.TsdbQueryDTO;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.thirdpart.TuyaAddHomeMemberVo;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpConfigurationResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpInfoResponse;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.request.ConfigurationMap;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDevicePropertyStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.response.CloudHomeMembersResponse;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EcosIotApi {

    /**
     * 根据WiFi SN获取设备属性详情
     *
     * @param deviceIds wifiSN拼接
     * @param cloud        平台
     * @return 设备属性列表
     */
    List<CloudDevicePropertyStatusResponse> getDevicePropertyInfo(String deviceIds, String cloud);

    /**
     * 发送透传读命令
     *
     * @param wifiSn       wifi标识
     * @param slaveId      从机号
     * @param startAddress 开始地址
     * @param len          长度
     * @param cloud        平台
     * @return 读取数据
     */
    List<Integer> readDevice(String wifiSn, Integer slaveId, Integer startAddress, Integer len, String cloud);

    /**
     * 发送透传写命令
     *
     * @param wifiSn       wifi标识
     * @param slaveId      从机号
     * @param startAddress 开始地址
     * @param len          长度
     * @param values       要写入的长度
     * @param cloud        平台
     */
    void writeDevice(String wifiSn, Integer slaveId, Integer startAddress, Integer len,
                           List<Integer> values, String cloud);

    /**
     * 根据WiFi SN获取设备详情
     *
     * @param deviceId      wifiSN
     * @param cloud         平台
     * @return 设备基础信息
     */
    DeviceBasicInfoDto getDeviceBasicInfo(String deviceId, String cloud);

    /**
     * 涂鸦绑定加速
     *
     * @param tuyaDeviceSpeedupVo       加速入参
     * @param cloud                     平台
     * @return 是否加速成功
     */
    Boolean speedupDevice(TuyaDeviceSpeedupVo tuyaDeviceSpeedupVo, String cloud);

    /**
     * 设备重置
     *
     * @param deviceId       wifiSn
     * @param cloud          平台
     * @return 是否重置成功
     */
    Boolean resetDevice(String deviceId, String cloud);

    /**
     * 获取设备在线状态码
     *
     * @param hybridSinglePhaseDO       设备详情
     * @return 设备状态码
     */
    Integer getDeviceStatus(HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 检查设备是否在线
     *
     * @param deviceId       wifiSn
     * @param cloud          平台
     * @return 是否在线
     */
    Boolean checkDeviceOnline(String deviceId, String cloud);

    /**
     * 根据设备wifiSn获取设备序列号SN
     *
     * @param deviceId       wifiSn
     * @param cloud          平台
     * @return 设备序列号SN
     */
    String getDeviceSnByWifiSn(String deviceId, String cloud);

    /**
     * 根据WiFi SN获取设备IP
     *
     * @param wifiSn WiFi SN
     * @param cloud        平台
     * @return IP
     */
    String getDeviceIpByWifiSn(String wifiSn, String cloud);

    /**
     * 添加家庭成员
     *
     * @param homeId                        家庭ID
     * @param tuyaAddHomeMemberVo           待添加的成员信息
     * @param cloud                         平台
     * @return 是否添加成功
     */
    Boolean addHomeMember(Long homeId,TuyaAddHomeMemberVo tuyaAddHomeMemberVo, String cloud);

    /**
     * 删除家庭成员
     *
     * @param homeId        家庭ID
     * @param uid           待删除的成员uid
     * @param cloud         平台
     * @return 是否删除成功
     */
    Boolean deleteHomeUser(Long homeId, String uid, String cloud);

    /**
     * 获取家庭成员列表
     *
     * @param homeId        家庭ID
     * @param cloud         平台
     * @return 家庭成员列表
     */
    List<CloudHomeMembersResponse> getHomeUserList(Long homeId, String cloud);

    /**
     * 根据wifiSn获取设备所在家庭ID
     *
     * @param deviceId      wifiSn
     * @param cloud         平台
     * @return 家庭ID
     */
    String getHomeId(String deviceId, String cloud);

    /**
     * 开关tuya插座
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param switchStatus         开关状态
     * @param switchIndex         开关编号
     */
    Boolean switchTuyaSocket(String cloudId, String cloud, Boolean switchStatus, Integer switchIndex);

    /**
     * 更新tuya插座倒计时
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param second         秒
     */
    Boolean updateTuyaSocketCountdown(String cloudId, String cloud, Integer second);

    /**
     * 更新tuya插座随机定时
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param code         base64加密后的字符串
     */
    Boolean updateTuyaSocketRandomData(String cloudId, String cloud, String code);

    /**
     * 查询Tuya设备日志
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param type         日志查询支持的类型。支持多个事件类型的查询，用半角逗号（,）隔开，必传
     * @param startTime         查询的开始时间戳
     * @param endTime         查询的结束时间戳
     * @param codes         设备支持的功能点。支持多个功能点的查询，用半角逗号（,）隔开，默认为空
     * @param queryType         查询类型:(0：免费，1：收费)
     * @param size         查询的日志数量大小。默认为 `20`。
     * @param startRowKey         下一页行键
     */
    TuyaDeviceLogsResponse queryTuyaDeviceLogs(String cloudId, String cloud, String type, Long startTime, Long endTime, String codes, Integer queryType, Integer size, String startRowKey);

    /**
     * 过冲保护开关
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param switchStatus         开关状态
     */
    Boolean switchTuyaSocketOverCharge(String cloudId, String cloud, Boolean switchStatus);

    /**
     * 获取充电桩状态
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     */
    CpStatusResponse getChargeStationStatus(String cloudId, String cloud);

    /**
     * 查询充电桩卡列表
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     */
    List<String> queryChargeStationCardList(String cloudId, String cloud);

    /**
     * 充电桩添加卡片
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param cardId         卡片ID
     */
    Boolean addChargeStationCard(String cloudId, String cloud, String cardId);

    /**
     * 充电桩覆盖卡片列表
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param cardIds         卡片ID列表
     */
    Boolean coverChargeStationCards(String cloudId, String cloud, List<String> cardIds);

    /**
     * 充电桩批量删除卡片
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param cardIds         卡片ID列表
     */
    Boolean removeChargeStationCards(String cloudId, String cloud, List<String> cardIds);

    /**
     * 查询充电桩详情
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     */
    CpInfoResponse queryChargeStationInfo(String cloudId, String cloud);

    /**
     * 充电桩设置最大充电电流
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param power         最大功率
     */
    Boolean setMaxChargeCurrent(String cloudId, String cloud, Integer power, Integer numberPhases);

    /**
     * 充电桩开始充电
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param transactionId         事务ID
     */
    Boolean startCharging(String cloudId, String cloud, Integer transactionId);

    /**
     * 充电桩停止充电
     *
     * @param cloudId      所在平台标识
     * @param cloud         平台
     * @param transactionId         事务ID
     */
    Boolean stopCharging(String cloudId, String cloud, Integer transactionId);

    /**
     * 查询充电桩全局配置
     *
     * @param cloudId
     * @param cloud
     * @param keys 配置key，传空默认查询所有
     * @return
     */
    CpConfigurationResponse queryConfiguration(String cloudId, String cloud, String keys);

    /**
     * 配置充电桩全局配置
     *
     * @param cloudId
     * @param cloud
     * @param configMap 配置明细key,value
     * @return
     */
    Boolean updConfiguration(String cloudId, String cloud, ConfigurationMap configMap);

    /**
     * 查询最新点位数据
     *
     * @param param tsdb查询参数
     * @param cloud 平台
     * @return influx结果
     */
    FluxTable queryIotLastPoint(TsdbQueryDTO param, String cloud);

    /**
     * 插值查询
     *
     * @param param tsdb查询参数
     * @param cloud 平台
     * @return influx结果
     */
    FluxTable queryIotDelta(TsdbQueryDTO param, String cloud);

    /**
     * 降采样查询
     *
     * @param param tsdb查询参数
     * @param cloud 平台
     * @return influx结果
     */
    FluxTable queryIotWithSample(TsdbQueryDTO param, String cloud);

    /**
     * 不降采样查询
     *
     * @param param tsdb查询参数
     * @param cloud 平台
     * @return influx结果
     */
    FluxTable queryIotWithoutSample(TsdbQueryDTO param, String cloud);

    /**
     * iot重置设备
     *
     * @param cloudId wifiSn
     */
    void sendEsResetCommand(String cloudId, String cloud);

    /**
     * iot事件分页接口
     *
     * @param cloudId wifiSn
     * @param pageInfo 分页信息
     * @return 事件信息
     */
    IotDeviceEventResponse queryCloudIotDeviceEvent(String cloudId, String cloud, HomeEventsFaultVo pageInfo);

}
