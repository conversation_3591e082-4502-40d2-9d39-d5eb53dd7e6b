package com.weihengtech.ecos.api.pojo.vos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/30 11:49
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价自动策略预览入参")
public class EleHomeStrategyPreviewVO {

    @ApiModelProperty(name = "deviceName", value = "设备sn", required = true)
    @NotNull(message = "err.not.null")
    private Long deviceId;

    @ApiModelProperty(name = "time", value = "查询时间，昨天：-1，今天：0，明天：1", required = true)
    @NotNull(message = "err.not.null")
    private Integer time;

    @ApiModelProperty(name = "time", value = "时区(默认用户时区)")
    private String timezone;

    @ApiModelProperty(name = "dayType", value = "日期类型，0：周末，1：工作日", required = true)
    private Integer dayType;

    @ApiModelProperty(name = "defChargePower", value = "自定义充电功率", required = true)
    private Integer defChargePower;

    @ApiModelProperty(name = "defDischargePower", value = "自定义放电功率", required = true)
    private Integer defDischargePower;


}
