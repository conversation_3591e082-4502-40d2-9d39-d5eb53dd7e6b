package com.weihengtech.ecos.api.pojo.vos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 知识库问答入参
 * @author: jiahao.jin
 * @create: 2024-05-09 17:23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledegeConversationVo {
    private String model;
    private List<Message> messages;
    private Long userId;
    private String chatId;
    private Integer historyChatSize;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Message {
        private String role;

        private String content;
    }
}
