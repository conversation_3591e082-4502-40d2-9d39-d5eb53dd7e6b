package com.weihengtech.ecos.api.fallback;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.EcosServiceApi;
import com.weihengtech.ecos.api.pojo.dtos.GlobalDeviceConfigDto;
import com.weihengtech.ecos.api.pojo.dtos.GuideAgreementVersionDto;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundDTO;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.api.pojo.vos.EcosDeviceConfigQueryVo;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.global.ResourceCategoryTreeDto;
import com.weihengtech.ecos.model.dtos.charger.V2ClientChargeRecordDto;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.thirdpart.SystemInfoUpdVO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordSaveVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordUpdateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EcosServiceFallback implements EcosServiceApi {

	@Override
	public EmptyResponse speedupOnce(String deviceId) {
		log.warn("com.weihengtech.api.fallback.EcosServiceFallback#speedupOnce: {}", deviceId);
		return EmptyResponse.fail();
	}

	@Override
	public DataResponse<GlobalDeviceConfigDto> queryDeviceConfig(EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo) {
		log.warn("com.weihengtech.api.fallback.EcosServiceFallback.queryDeviceConfig: {}", ecosDeviceConfigQueryVo.getDeviceSn());
		return DataResponse.fail(new GlobalDeviceConfigDto().withFeedback(Boolean.TRUE).withId(0L));
	}

	@Override
	public DataResponse<GuideAgreementVersionDto> getAgreementVersion() {
		log.warn("com.weihengtech.api.fallback.EcosServiceFallback.getAgreementVersion");
		GuideAgreementVersionDto guideAgreementVersionDto = new GuideAgreementVersionDto();
		guideAgreementVersionDto.setId(0);
		guideAgreementVersionDto.setVersion(0);
		return DataResponse.fail(guideAgreementVersionDto);
	}

	@Override
	public DataResponse<String> getAgentId(String deviceId) {
		log.warn("EcosServiceFallback#getAgentAccount: {}", deviceId);
		return DataResponse.fail("");
	}

	@Override
	public DataResponse<List<BindInfoDTO>> getAgentsByIds(List<Long> ids) {
		log.warn("EcosServiceFallback#getAgentsByIds: {}", ids);
		return DataResponse.fail(Collections.emptyList());
	}

	@Override
	public DataResponse<HybridSinglePhaseDO> queryByDeviceName(String deviceName) {
		log.warn("EcosServiceFallback#queryByDeviceName: {}", deviceName);
		return DataResponse.fail(new HybridSinglePhaseDO());
	}

	@Override
	public DataResponse<HybridSinglePhaseDO> queryById(Long deviceId) {
		log.warn("EcosServiceFallback#queryById: {}", deviceId);
		return DataResponse.fail(new HybridSinglePhaseDO());
	}

	@Override
	public EmptyResponse saveNewDevice(HybridSinglePhaseDO hybridSinglePhaseDO) {
		log.warn("EcosServiceFallback#saveNewDevice: {}", JSONUtil.toJsonStr(hybridSinglePhaseDO));
		return EmptyResponse.fail();
	}

	@Override
	public EmptyResponse updateById(HybridSinglePhaseDO hybridSinglePhaseDO) {
		log.warn("EcosServiceFallback#updateById: {}", JSONUtil.toJsonStr(hybridSinglePhaseDO));
		return EmptyResponse.fail();
	}

	@Override
	public DataResponse<List<HybridSinglePhaseDO>> nowBindDeviceList(String wifiSn) {
		log.warn("EcosServiceFallback#nowBindDeviceList: {}", wifiSn);
		return DataResponse.fail(ListUtil.empty());
	}

	@Override
	public DataResponse<List<HybridSinglePhaseDO>> listOtherBindDevice(String wifiSn, String deviceName) {
		log.warn("EcosServiceFallback#listOtherBindDevice: {}-{}", wifiSn, deviceName);
		return DataResponse.fail(ListUtil.empty());
	}

	@Override
	public DataResponse<GlobalVersionDto> ecosLatestVersion(String language) {
		log.warn("EcosServiceFallback#ecosLatestVersion {}", language);
		return DataResponse.fail(new GlobalVersionDto());
	}

	@Override
	public DataResponse<GlobalEnestLatestVersionDto> enestLatestVersion(String language) {
		log.warn("EcosServiceFallback#enestLatestVersion {}", language);
		return DataResponse.fail(new GlobalEnestLatestVersionDto());
	}

	@Override
	public EmptyResponse updSystemInfo(SystemInfoUpdVO updVO) {
		log.warn("EcosServiceFallback#updSystemInfo {}", JSONUtil.toJsonStr(updVO));
		return EmptyResponse.fail();
	}

	@Override
	public DataResponse<List<HybridSinglePhaseDO>> queryBatchById(Boolean isNeedExt, List<Long> ids) {
		log.warn("EcosServiceFallback#queryBatchById {}", ids);
		return DataResponse.fail(Collections.emptyList());
	}

	@Override
	public InResponse saveSocket(HybridSinglePhaseDO hybridSinglePhaseDO) {
		log.warn("EcosServiceFallback#saveSocket: {}", JSONUtil.toJsonStr(hybridSinglePhaseDO));
		return InResponse.fail();
	}

	@Override
	public InResponse updSocket(HybridSinglePhaseDO hybridSinglePhaseDO) {
		log.warn("EcosServiceFallback#updSocket: {}", JSONUtil.toJsonStr(hybridSinglePhaseDO));
		return InResponse.fail();
	}

	@Override
	public InResponse saveCharger(ChargerSaveVO chargerSaveVO) {
		log.warn("EcosServiceFallback#saveCharger: {}", JSONUtil.toJsonStr(chargerSaveVO));
		return InResponse.fail();
	}

	@Override
	public InResponse updCharger(ChargerSaveVO chargerSaveVO) {
		log.warn("EcosServiceFallback#updCharger: {}", JSONUtil.toJsonStr(chargerSaveVO));
		return InResponse.fail();
	}

    @Override
    public InResponse updChargerExtInfo(ExtInfoDto chargerSaveVO) {
		log.warn("EcosServiceFallback#updChargerExtInfo: {}", JSONUtil.toJsonStr(chargerSaveVO));
		return InResponse.fail();
    }

    @Override
	public DataResponse<List<ResourceCategoryTreeDto>> resourceCategoryTree() {
		log.warn("EcosServiceFallback#resourceCategoryTree");
		return DataResponse.fail(Collections.emptyList());
	}

	@Override
	public DataResponse<PageInfoDTO<V2ClientChargeRecordDto>> pageChargeRecord(V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo) {
		log.warn("EcosServiceFallback#pageChargeRecord：{}", JSONUtil.toJsonStr(v2ClientChargeRecordPageVo));
		return DataResponse.fail(new PageInfoDTO<>());
	}

	@Override
	public DataResponse<V2ClientChargeRecordDto> queryLastRecord(Long deviceId, Boolean isFilterCharing) {
		log.warn("EcosServiceFallback#queryLastRecord：{} {}", deviceId, isFilterCharing);
		return DataResponse.fail(new V2ClientChargeRecordDto());
	}

	@Override
	public EmptyResponse saveChargeRecord(V2ClientChargeRecordSaveVo v2ClientChargeRecordSaveVo) {
		log.warn("EcosServiceFallback#saveChargeRecord：{}", JSONUtil.toJsonStr(v2ClientChargeRecordSaveVo));
		return EmptyResponse.fail();
	}

	@Override
	public EmptyResponse updChargeRecord(V2ClientChargeRecordUpdateVo v2ClientChargeRecordUpdateVo) {
		log.warn("EcosServiceFallback#saveChargeRecord：{}", JSONUtil.toJsonStr(v2ClientChargeRecordUpdateVo));
		return EmptyResponse.fail();
	}

	@Override
	public EmptyResponse boundInstall(InstallBoundDTO item) {
		log.warn("EcosServiceFallback#boundInstall：{}", JSONUtil.toJsonStr(item));
		return EmptyResponse.fail();
	}

    @Override
    public DataResponse<InstallBoundInfoDTO> getBindInstallInfo(String deviceId) {
		log.warn("EcosServiceFallback#getBindInstallInfo：{}", deviceId);
		return DataResponse.fail(new InstallBoundInfoDTO());
    }

	@Override
	public DataResponse<DynamicExportDTO> dynamicExport(String deviceName) {
		log.warn("EcosServiceFallback#dynamicExport：{}", deviceName);
		return DataResponse.fail(new DynamicExportDTO());
	}

	@Override
	public DataResponse<DynamicDesignDTO> designInfo(String deviceName) {
		log.warn("EcosServiceFallback#designInfo：{}", deviceName);
		return DataResponse.fail(new DynamicDesignDTO());
	}

	@Override
	public EmptyResponse dynamicSave(DynamicSaveVO param) {
		log.warn("EcosServiceFallback#dynamicSave：{}", JSONUtil.toJsonStr(param));
		return EmptyResponse.fail();
	}

	@Override
	public DataResponse<DynamicTestDTO> dynamicTest(String deviceName) {
		log.warn("EcosServiceFallback#dynamicTest：{}", deviceName);
		return DataResponse.fail(new DynamicTestDTO());
	}

	@Override
	public EmptyResponse dynamicSwitch(DynamicSwitchVO param) {
		log.warn("EcosServiceFallback#dynamicTest：{}", JSONUtil.toJsonStr(param));
		return EmptyResponse.fail();
	}
}
