package com.weihengtech.ecos.api.config;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableMap;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class NotificationCenterConfiguration {

	@Value("${custom.notify.token}")
	private String token;

	@Bean
	public RequestInterceptor headerInterceptor() {
		return requestTemplate -> {
			List<String> contentTypeList = ListUtil.toList("application/json;charset=utf-8");
			Map<String, Collection<String>> headers = ImmutableMap.of(
					"Authorization", ListUtil.toList(token),
					"Content-Type", contentTypeList
			);
			requestTemplate.headers(headers);
		};
	}
}
