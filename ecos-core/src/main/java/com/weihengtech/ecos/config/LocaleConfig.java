package com.weihengtech.ecos.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Configuration
@Profile({"dev", "prod"})
public class LocaleConfig {

	@Bean
	public LocaleResolver localeResolver() {
		return new CustomLocaleResolver();
	}

	private static class CustomLocaleResolver implements LocaleResolver {

		@Override
		@NonNull
		public Locale resolveLocale(HttpServletRequest request) {
			String lang = request.getHeader("Language");
			Locale locale;
			try {
				String[] localeArr = lang.split("_");
				locale = new Locale(localeArr[0], localeArr[1]);
			} catch (Exception e) {
				locale = Locale.US;
			}
			return locale;
		}

		@Override
		public void setLocale(@NonNull HttpServletRequest request, HttpServletResponse response, Locale locale) {
			throw new UnsupportedOperationException(
					"Cannot change HTTP accept header - use a different locale resolution strategy");
		}
	}
}
