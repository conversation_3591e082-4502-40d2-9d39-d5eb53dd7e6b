package com.weihengtech.ecos.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/27 17:37
 */
@Configuration
public class ThreadPoolConfig {

    @Value("${thread.pool.core.size:4}")
    private Integer coreSize;

    @Value("${thread.pool.max.size:16}")
    private Integer maxSize;

    @Value("${thread.pool.name.prefix:hub-executor-}")
    private String namePrefix;

    @Value("${thread.pool.queue.capacity:64}")
    private Integer queueCapacity;

    @Value("${thread.pool.keepalive:60}")
    private Integer keepAlive;

    @Bean
    @Primary
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // IO密集型，CPU核心数*2
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setThreadNamePrefix(namePrefix);
        // 阻塞队列长度
        executor.setQueueCapacity(queueCapacity);
        // 当线程数大于核心线程数时，多余的空闲线程存活的最长时间
        executor.setKeepAliveSeconds(keepAlive);
        // 拒绝策略：直接丢弃
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
