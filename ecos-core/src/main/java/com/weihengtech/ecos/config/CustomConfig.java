package com.weihengtech.ecos.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Profile({"dev", "prod"})
@Configuration
@ConfigurationProperties(prefix = "custom")
public class CustomConfig {

	@Getter
	@Setter
	private Avatar avatar = new Avatar();

	@Getter
	@Setter
	private Token token = new Token();

	@Getter
	@Setter
	public static class Avatar {

		private String location;
	}

	@Getter
	@Setter
	public static class Token {

		private long accessExpire;
		private long refreshExpire;
		private String headerKey;
		private String typePrefix;
	}

	@Getter
	@Setter
	public static class Datacenter {
		private String name;
	}
}
