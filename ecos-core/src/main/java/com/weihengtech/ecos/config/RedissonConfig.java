package com.weihengtech.ecos.config;

import cn.hutool.core.util.StrUtil;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 */
@Configuration
@Profile({"dev", "prod"})
public class RedissonConfig {

	@Value("${spring.redis.host}")
	private String host;

	@Value("${spring.redis.port}")
	private String port;

	@Value("${spring.redis.password}")
	private String password;

	@Value("${spring.redis.database}")
	private Integer database;

	@Bean
	public RedissonClient redissonClient() {
		Config config = new Config();
		String redisAddress = "redis://" + host + ":" + port;
		if (StrUtil.isBlank(password)) {
			config.useSingleServer().setAddress(redisAddress).setDatabase(database);
		} else {
			config.useSingleServer().setAddress(redisAddress)
					.setPassword(password)
					.setDatabase(database);
		}
		return Redisson.create(config);
	}
}
