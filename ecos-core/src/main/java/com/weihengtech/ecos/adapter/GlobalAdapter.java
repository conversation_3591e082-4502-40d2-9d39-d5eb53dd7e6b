package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.global.GlobalConfigDto;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalReduceCarbonEmissionsDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.vos.global.TuyaLoginLogVo;

/**
 * <AUTHOR>
 */
public interface GlobalAdapter {

	/**
	 * 减少碳排放量统计
	 *
	 * @return 统计结果 碳减排量(节约1度电=减排0.997千克“二氧化碳”)
	 */
	GlobalReduceCarbonEmissionsDto reduceCarbonEmissionsStatistics();

	/**
	 * 获取全局配置
	 *
	 * @return 全局配置参数
	 */
	GlobalConfigDto getGlobalConfig();

	/**
	 * 获取最新的版本信息
	 *
	 * @return com.weihengtech.pojo.dtos.GlobalVersionDto
	 */
	GlobalVersionDto getGlobalVersion();

	/**
	 * 获取单设备碳减排
	 *
	 * @param deviceId 设备id
	 * @return 单设备碳减排统计
	 */
	GlobalReduceCarbonEmissionsDto deviceReduceCarbonEmissionsStatistics(String deviceId);

	/**
	 * 获取enest最新版本信息
	 *
	 * @return com.weihengtech.pojo.dtos.GlobalEnestLatestVersionDto
	 */
	GlobalEnestLatestVersionDto getEnestLatestVersion();

	void saveTuyaLoginLog(ClientUserDo clientUserDo, TuyaLoginLogVo tuyaLoginLogVo);
}
