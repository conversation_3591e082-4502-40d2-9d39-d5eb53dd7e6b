package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.jd.platform.async.executor.Async;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.adapter.HomeAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.GeoApi;
import com.weihengtech.ecos.api.WeatherApi;
import com.weihengtech.ecos.consts.*;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceSketchDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryBatteryDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryGridDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryHomeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistorySolarDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceStatisticsDto;
import com.weihengtech.ecos.model.dtos.thirdpart.NowWeatherDto;
import com.weihengtech.ecos.model.dtos.thirdpart.QueryCityDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.thirdpart.DailyWeatherDto;
import com.weihengtech.ecos.model.dtos.thirdpart.HourWeatherDto;
import com.weihengtech.ecos.model.vos.app.home.HomeClientUserBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.HomeDeviceCityUpdateVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeHistoryVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceStatisticsVo;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.utils.*;
import com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo;
import com.weihengtech.ecos.model.bos.app.HistoryHomeBo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.service.ecos.EventService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.worker.database.DatabaseDeltaQueryOneHourPointWorker;
import com.weihengtech.ecos.worker.database.DatabaseLastPointWorker;
import com.weihengtech.ecos.worker.hub.HubServiceGetAgentsByIdsWork;
import com.weihengtech.ecos.worker.hub.HubServiceGetBatchByIdWork;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HomeAdapterImpl implements HomeAdapter {

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private EventService eventService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private RetryService retryService;
	@Resource
	private EcosIotApi ecosIotApi;
	@Resource
	private RedissonClient redissonClient;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private HubService hubService;
	@Resource
	private WeatherApi weatherApi;
	@Resource
	private GeoApi geoApi;
	@Resource
	private V2HomeAdapter v2HomeAdapter;


	@Override
	public void bindClientUserDevice(HomeClientUserBindDeviceVo homeClientUserBindDeviceVo, ClientUserDo clientUserDo) {
		Integer type = homeClientUserBindDeviceVo.getType();
		String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
		String redisKey;
		Double lon = homeClientUserBindDeviceVo.getLon();
		Double lat = homeClientUserBindDeviceVo.getLat();
		if (lon != null && lat != null) {
			if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
				log.warn("lon或lat值不正确");
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
			}
		}

		if (StrUtil.isNotBlank(wifiSn)) {
			redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
			if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
				String bindStatus = stringRedisTemplate.opsForValue().get(redisKey);
				if (DeviceBindStatus.OK.equals(bindStatus)) {
					return;
				}
			}
			log.info("开始绑定 {}", JSONUtil.toJsonStr(homeClientUserBindDeviceVo));
			stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.ING, 3, TimeUnit.MINUTES);

			List<HybridSinglePhaseDO> nowBindDeviceList = hubService.nowBindDeviceList(wifiSn);
			if (CollUtil.isNotEmpty(nowBindDeviceList)) {
				for (HybridSinglePhaseDO hybridSinglePhaseDO : nowBindDeviceList) {
					MiddleClientUserDeviceDo nowBindMiddle = middleClientUserDeviceService
							.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
									.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
									.eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId()));
					if (nowBindMiddle != null) {
						stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
						return;
					}
				}
			}
		} else {
			log.warn("wifiSn为空");
			throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
		}

//		if (1 == type) {
//			TuyaDeviceSpeedupVo tuyaDeviceSpeedupVo = new TuyaDeviceSpeedupVo();
//			tuyaDeviceSpeedupVo.setDeviceId(wifiSn);
//			ecosIotApi.speedupDevice(tuyaDeviceSpeedupVo,"1");
//		}
//
//		// 根据WiFi Sn查询设备IP
//		String ip = ecosIotApi.getDeviceIpByWifiSn(wifiSn, String.valueOf(type));

		try {
			Pair<String, String> pair = retryService.getDeviceSn(type, wifiSn);
			bindDeviceAction(pair.getKey(), clientUserDo, homeClientUserBindDeviceVo, redisKey, pair.getValue());
		} catch (Exception e) {
			log.warn(e.getMessage());
			throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
		}
	}

	@Override
	public Integer checkDeviceBindStatus(String wifiSn) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
		return Integer.parseInt(Optional.ofNullable(stringRedisTemplate.opsForValue().get(redisKey)).orElse(DeviceBindStatus.MISS));
	}

	@Override
	public NowWeatherDto getNowWeather(String key, String location, String lang, String unit) {
		JSONObject nowWeather = weatherApi.nowWeather(key, location, lang, unit);
		if (!("200".equals(nowWeather.get("code")))) {
			return new NowWeatherDto();
		}
		return JSONUtil.toBean(StrUtil.toString(nowWeather.get("now")), NowWeatherDto.class);
	}

	@Override
	public List<DailyWeatherDto> get3dWeather(String key, String location, String lang, String unit) {
		JSONObject nowWeather = weatherApi.threeDayWeather(key, location, lang, unit);
		if (!("200".equals(nowWeather.get("code")))) {
			return Collections.emptyList();
		}
		JSONArray dailyArray = nowWeather.getJSONArray("daily");
		return JSONUtil.toList(dailyArray, DailyWeatherDto.class);
	}

	@Override
	public List<DailyWeatherDto> get7dWeather(String key, String location, String lang, String unit) {
		JSONObject nowWeather = weatherApi.sevenDayWeather(key, location, lang, unit);
		if (!("200".equals(nowWeather.get("code")))) {
			return Collections.emptyList();
		}
		JSONArray dailyArray = nowWeather.getJSONArray("daily");
		return JSONUtil.toList(dailyArray, DailyWeatherDto.class);
	}

	@Override
	public List<HourWeatherDto> get24hWeather(String key, String location, String lang, String unit) {
		JSONObject nowWeather = weatherApi.roundTheClockWeather(key, location, lang, unit);
		if (!("200".equals(nowWeather.get("code")))) {
			return Collections.emptyList();
		}
		JSONArray hourlyArray = nowWeather.getJSONArray("hourly");
		return JSONUtil.toList(hourlyArray, HourWeatherDto.class);
	}

	@Override
	public List<QueryCityDto> queryCity(String key, String location, String lang) {
		String adm = null;
		String range = null;
		String number = "20";
		JSONObject nowWeather = geoApi.queryCity(key, location,adm,range,number,lang);
		if (!("200".equals(nowWeather.get("code")))) {
			return Collections.emptyList();
		}
		JSONArray cityArray = nowWeather.getJSONArray("location");
		return JSONUtil.toList(cityArray, QueryCityDto.class);
	}

	@Override
	public void updateDeviceCity(HomeDeviceCityUpdateVo homeDeviceCityUpdateVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		getOptionalBindDevice(homeDeviceCityUpdateVo.getDeviceId(), clientUserDo.getId())
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> {
					HybridSinglePhaseDO hybridSinglePhaseDO = new HybridSinglePhaseDO();
					Double lon = homeDeviceCityUpdateVo.getLon();
					Double lat = homeDeviceCityUpdateVo.getLat();
					if (lon != null && lat != null) {
						if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
							log.warn("lon或lat值不正确");
							throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
						}
					}
					hybridSinglePhaseDO.setId(Long.valueOf(homeDeviceCityUpdateVo.getDeviceId()));
					hybridSinglePhaseDO.setLongitude(lon);
					hybridSinglePhaseDO.setLatitude(lat);
					hubService.updateById(hybridSinglePhaseDO);
				}, () -> {
					log.warn("未绑定设备");
					return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
	}

	/**
	 * 根据设备ID获取设备所在家庭ID
	 *
	 * @param deviceId 设备id
	 * @return 家庭ID
	 */
	@Override
	public String getTuyaDeviceHomeId(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, deviceId);
		String homeId = ecosIotApi.getHomeId(pair.getValue().getWifiSn(), "1");
		if (homeId != null) {
			return homeId;
		} else {
			// 处理获取家庭设备信息失败的情况
			log.error("获取家庭设备信息失败");
			return null;
		}
	}


	private Long saveDeviceInfo(String deviceSn, String wifiSn, HomeClientUserBindDeviceVo homeClientUserBindDeviceVo, Integer datacenterId) {
		Long deviceId = snowFlakeUtil.generateId();
		HybridSinglePhaseDO hybridSinglePhaseDO = new HybridSinglePhaseDO();
		hybridSinglePhaseDO.setId(deviceId);
		hybridSinglePhaseDO.setDeviceName(deviceSn);
		hybridSinglePhaseDO.setDeviceSn(deviceSn);
		hybridSinglePhaseDO.setWifiSn(wifiSn);
		hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
		hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getType() == 0 ? 2 : 1);
		hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
		hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
		hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
		hybridSinglePhaseDO.setDatacenterId(datacenterId);
		initHybridSinglePhase(hybridSinglePhaseDO);
		hubService.save(hybridSinglePhaseDO);
		return deviceId;
	}

	private void updateDeviceInfo(HybridSinglePhaseDO hybridSinglePhaseDO, HomeClientUserBindDeviceVo homeClientUserBindDeviceVo, String wifiSn, String deviceSn) {
		String beforeWifiSn = hybridSinglePhaseDO.getWifiSn();
		stringRedisTemplate.opsForValue().set("NEW-BIND:" + beforeWifiSn, "1", 10, TimeUnit.MINUTES);
		hybridSinglePhaseDO.setWifiSn(wifiSn);
		Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
		if (firstInstall == null || firstInstall < 100) {
			hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
		}
		hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
		hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getType() == 0 ? 2 : 1);
		hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
		hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
		hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
		hubService.updateById(hybridSinglePhaseDO);

		List<HybridSinglePhaseDO> beforeWifiBindDevice = hubService.listOtherBindDevice(wifiSn, deviceSn);

		if (CollUtil.isNotEmpty(beforeWifiBindDevice)) {
			beforeWifiBindDevice.forEach(deviceDo -> {
				deviceDo.setWifiSn("");
				deviceDo.setUpdateTime(LocalDateTime.now());
				hubService.updateById(deviceDo);
			});
		}
	}

	@DSTransactional
	private void bindDeviceAction(
			String deviceSn, ClientUserDo clientUserDo,
			HomeClientUserBindDeviceVo homeClientUserBindDeviceVo,
			String redisKey,
			String ip
	) {

		String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
		RLock lock = redissonClient.getLock(wifiSn);
		try {
			if (lock.tryLock(100, TimeUnit.MILLISECONDS)) {
				HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getByDeviceName(deviceSn);
				Long deviceId;
				if (null == hybridSinglePhaseDO) {
					deviceId = saveDeviceInfo(deviceSn, wifiSn, homeClientUserBindDeviceVo, clientUserDo.getDatacenterId());
				} else {
					deviceId = hybridSinglePhaseDO.getId();
					MiddleClientUserDeviceDo masterDevice = middleClientUserDeviceService.getOne(Wrappers
							.<MiddleClientUserDeviceDo>lambdaQuery().eq(
									MiddleClientUserDeviceDo::getDeviceId,
									deviceId
							)
							.eq(MiddleClientUserDeviceDo::getMaster, 1));

					if (masterDevice != null) {
						if (!masterDevice.getUserId().equals(clientUserDo.getId())) {
							log.warn("从账号绑定错误");
							stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FORBIDDEN, 1, TimeUnit.MINUTES);
							throw new EcosException(EcosExceptionEnum.SLAVE_BIND_ERROR);
						}
					}
					hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
					hybridSinglePhaseDO.setIp(ip);
					updateDeviceInfo(hybridSinglePhaseDO, homeClientUserBindDeviceVo, wifiSn, deviceSn);

					if (masterDevice != null) {
						if (masterDevice.getUserId().equals(clientUserDo.getId())) {
							log.warn("{} 已经绑定了该设备：{}", clientUserDo.getEmail(), deviceSn);
							stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
							throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
						}
					}
				}

				long middleId = snowFlakeUtil.generateId();
				MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
				middleClientUserDeviceDo.setId(middleId);
				middleClientUserDeviceDo.setUserId(clientUserDo.getId());
				middleClientUserDeviceDo.setDeviceId(deviceId);
				middleClientUserDeviceDo.setCreateTime(System.currentTimeMillis());
				middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
				middleClientUserDeviceDo.setWeight(0);
				middleClientUserDeviceDo.setName(StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName())
						? deviceSn
						: homeClientUserBindDeviceVo.getDeviceAliasName());
				middleClientUserDeviceDo.setMaster(1);
				ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(middleClientUserDeviceDo));
				stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
			}
		} catch (InterruptedException e) {
			log.warn(e.getMessage());
			stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FAIL, 1, TimeUnit.MINUTES);
			throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		} finally {
			lock.unlock();
		}
	}


	@Override
	public void unbindClientUserDevice(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService.getOne(
				Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
						.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId()));

		if (null == middleClientUserDeviceDo) {
			log.warn("无权操作设备");
			throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
		int master = middleClientUserDeviceDo.getMaster();
		if (1 == master) {
			middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
					.eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId)));
		} else {
			middleClientUserDeviceService.removeById(middleClientUserDeviceDo.getId());
		}
	}

	@Override
	public List<HomeDeviceListDto> queryUserDeviceList(String userId) {
		List<MiddleClientUserDeviceDo> middleList = middleClientUserDeviceService.list(Wrappers
				.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(
						MiddleClientUserDeviceDo::getUserId,
						userId
				)
				.orderByAsc(
						MiddleClientUserDeviceDo::getWeight));

		// 提取设备ID列表
		List<Long> deviceIdList = middleList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());

		HubServiceGetBatchByIdWork hubServiceGetBatchByIdWork = new HubServiceGetBatchByIdWork(false, deviceIdList, hubService);
		HubServiceGetAgentsByIdsWork hubServiceGetAgentsByIdsWork = new HubServiceGetAgentsByIdsWork(deviceIdList, hubService);

		// 通过异步任务获取设备信息
		WorkerWrapper<String, List<HybridSinglePhaseDO>> getBatchByIdWork = new WorkerWrapper.Builder<String, List<HybridSinglePhaseDO>>()
				.worker(hubServiceGetBatchByIdWork)
				.callback(hubServiceGetBatchByIdWork)
				.param("1")
				.build();

		// 通过异步任务获取代理信息
		WorkerWrapper<String, List<BindInfoDTO>> getAgentsByIdsWork = new WorkerWrapper.Builder<String, List<BindInfoDTO>>()
				.worker(hubServiceGetAgentsByIdsWork)
				.callback(hubServiceGetAgentsByIdsWork)
				.param("1")
				.build();

		try {
			Async.beginWork(RequestConstants.HUB_REQUEST_MILLISECOND_TIMES, getBatchByIdWork, getAgentsByIdsWork);
		} catch (ExecutionException | InterruptedException e) {
			log.error(e.getMessage());
			return new ArrayList<>();
		}
		Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDOMap = getBatchByIdWork.getWorkResult().getResult().stream()
				.collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

		Map<String, String> agentIdMap = getAgentsByIdsWork.getWorkResult().getResult()
				.stream()
				.collect(Collectors.toMap(
						BindInfoDTO::getResourceId,
						BindInfoDTO::getUserId,
						(existing, replacement) -> existing // 保留第一个agentId，忽略后续的
				));

		return middleList.stream().map(middle -> {
					HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDOMap.get(middle.getDeviceId());

					// 如果 hybridSinglePhaseDO 为 null，意味着设备不存在或获取设备时发生错误
					if (hybridSinglePhaseDO == null) {
						log.warn("设备不存在或无法获取: deviceId=" + middle.getDeviceId());
						return null;
					}

					HomeDeviceListDto homeDeviceListDto = new HomeDeviceListDto();
					homeDeviceListDto.setDeviceAliasName(middle.getName());
					homeDeviceListDto.setWifiSn(hybridSinglePhaseDO.getWifiSn());
					homeDeviceListDto.setState(hybridSinglePhaseDO.getState());
					homeDeviceListDto.setDeviceId(String.valueOf(middle.getDeviceId()));
					homeDeviceListDto.setWeight(middle.getWeight());
					homeDeviceListDto.setVpp(hybridSinglePhaseDO.getVppMode());
					homeDeviceListDto.setMaster(middle.getMaster());
					homeDeviceListDto.setType(hybridSinglePhaseDO.getDataSource());
					homeDeviceListDto.setCategory(hybridSinglePhaseDO.getCategory());
					homeDeviceListDto.setModel(hybridSinglePhaseDO.getModel());
					homeDeviceListDto.setDeviceType(hybridSinglePhaseDO.getType());
					homeDeviceListDto.setDeviceSn(hybridSinglePhaseDO.getDeviceName());
					homeDeviceListDto.setAgentId(agentIdMap.getOrDefault(hybridSinglePhaseDO.getId(), ""));
					homeDeviceListDto.setLon(hybridSinglePhaseDO.getLongitude());
					homeDeviceListDto.setLat(hybridSinglePhaseDO.getLatitude());

					return homeDeviceListDto;
				})
				.filter(Objects::nonNull) // 过滤掉所有的 null 值
				.collect(Collectors.toList());
	}


	@Override
	public Integer queryUserBindDeviceCount() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		List<MiddleClientUserDeviceDo> middleList = middleClientUserDeviceService.list(Wrappers
				.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(
						MiddleClientUserDeviceDo::getUserId,
						clientUserDo.getId()
				));

		// 提取设备ID列表
		List<Long> deviceIdList = middleList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());

		// 一次性获取所有设备详情
		List<HybridSinglePhaseDO> hybridSinglePhaseDOList = hubService.getBatchById(false, deviceIdList);

		return hybridSinglePhaseDOList != null ? hybridSinglePhaseDOList.size() : 0;

	}

	@Override
	public void orderDeviceList(List<String> deviceIdList) {

		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		List<MiddleClientUserDeviceDo> middleList = Optional.ofNullable(middleClientUserDeviceService.list(Wrappers
						.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())))
				.orElse(new ArrayList<>());

		// 提取设备ID列表
		List<Long> deviceIdLists = middleList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());

		// 一次性获取所有设备详情
		List<HybridSinglePhaseDO> hybridSinglePhaseDOList = hubService.getBatchById(false, deviceIdLists);

		if (hybridSinglePhaseDOList.size() != deviceIdList.size()) {
			log.info("排序设备数量不匹配");
			throw new EcosException(EcosExceptionEnum.MISS_DEVICE_COUNT);
		}

		Map<String, MiddleClientUserDeviceDo> middleMap = middleList.stream()
				.collect(Collectors.toMap(
						middleClientUserDeviceDo -> String.valueOf(middleClientUserDeviceDo.getDeviceId()),
						middleClientUserDeviceDo -> middleClientUserDeviceDo
				));
		List<MiddleClientUserDeviceDo> devicesToUpdate = new ArrayList<>();
		for (int i = 0; i < deviceIdList.size(); i++) {
			MiddleClientUserDeviceDo middleClientUserDeviceDo = middleMap.get(deviceIdList.get(i));
			if (middleClientUserDeviceDo == null) {
				log.info("Device not found for the given ID: " + deviceIdList.get(i));
				continue;
			}
			middleClientUserDeviceDo.setWeight(i);
			middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
			devicesToUpdate.add(middleClientUserDeviceDo);
		}

		// Update all devices in a single batch operation
		ActionFlagUtil.assertTrue(middleClientUserDeviceService.updateBatchById(devicesToUpdate));
	}


	@Override
	public PageInfoDTO<HomeEventsFaultDto> pageEventFault(HomeEventsFaultVo homeEventsFaultVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String deviceId = homeEventsFaultVo.getDeviceId();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, deviceId);
		HybridSinglePhaseDO deviceInfo = pair.getValue();
		if (DeviceTypeInfoEnum.WH.getDatasource() == deviceInfo.getDataSource()) {
			// iot事件分页
			return eventService.pageEventListIot(deviceInfo.getWifiSn(), homeEventsFaultVo);
		} else {
			// 本地事件
			return eventService.pageEventList(deviceInfo.getDeviceName(), homeEventsFaultVo);
		}
	}

	@Override
	public HistoryHomeBo queryHistoryHomeBo(HomeHistoryVo homeHistoryVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		HistoryHomeBo historyHomeBo = new HistoryHomeBo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, homeHistoryVo.getDeviceId());
		Pair<Long, Long> timePair = pairStartTimeAndEndTimeForHomeHistory(homeHistoryVo, pair.getKey().getTimeZone());
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(pair.getValue());

		LinkedHashMap<Long, BigDecimal> homeEnergyDps = new LinkedHashMap<>(1024);

		if (timePair.getKey() > timePair.getValue()) {
			return historyHomeBo;
		}

		List<String> metricList = buildMetricListByGraph(CommonConstants.GRAPH_HOME);
		DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), timePair.getKey());
		DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), timePair.getValue());
		DatabaseDeltaQueryOneHourPointWorker deltaQueryOneHourPointWorker = new DatabaseDeltaQueryOneHourPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(), metricList, timePair.getKey(), timePair.getValue());

		WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker1)
				.callback(lastPointWorker1)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker2)
				.callback(lastPointWorker2)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Map<String, LinkedHashMap<Long, Object>>> deltaQueryOneHourPointWorkerWrapper = new WorkerWrapper.Builder<String, Map<String, LinkedHashMap<Long, Object>>>()
				.worker(deltaQueryOneHourPointWorker)
				.callback(deltaQueryOneHourPointWorker)
				.param("DatabaseDeltaQueryOneHourPointWorker")
				.build();

		try {
			Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, lastPointWorker1Wrapper, lastPointWorker2Wrapper, deltaQueryOneHourPointWorkerWrapper);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		try {
			historyHomeBo.setCycleTimes(computeBatteryCycleTimes(lastPointWorker1Wrapper.getWorkResult().getResult(), lastPointWorker2Wrapper.getWorkResult().getResult()));
		} catch (Exception e) {
			log.warn(e.getMessage());
		}


		Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(
				deltaQueryOneHourPointWorkerWrapper.getWorkResult().getResult(),
				homeHistoryVo.getPeriodType(),
				pair.getKey().getTimeZone()
		);

		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
			LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
			LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
			LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
			LinkedHashMap<Long, Object> toBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
			LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);

			if (fromSolarMap2 != null) {
				for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
					Long time = entry.getKey();
					Object value = entry.getValue();
					if (fromSolarMap.containsKey(time)) {
						// 如果键已存在，则将值相加
						BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
						BigDecimal additionalValue = new BigDecimal(value.toString());
						BigDecimal sum = originalValue.add(additionalValue);
						fromSolarMap.put(time, sum);
					} else {
						// 如果键不存在，则添加键值对
						fromSolarMap.put(time, value);
					}
				}
			}

			historyHomeBo.setFromBatteryDps(fromBatteryMap);
			historyHomeBo.setToBatteryDps(toBatteryMap);
			historyHomeBo.setFromGridDps(fromGridMap);
			historyHomeBo.setToGridDps(toGridMap);
			historyHomeBo.setFromSolarDps(fromSolarMap);

			List<Long> keyList = null == fromSolarMap
					? ListUtil.empty()
					: fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
					.collect(Collectors.toList());

			BigDecimal totalSolar = BigDecimal.ZERO;
			BigDecimal totalFromGrid = BigDecimal.ZERO;
			BigDecimal totalToGrid = BigDecimal.ZERO;
			BigDecimal totalFromBattery = BigDecimal.ZERO;
			BigDecimal totalToBattery = BigDecimal.ZERO;
			BigDecimal totalHomeEnergy = BigDecimal.ZERO;

			if (CollUtil.isNotEmpty(fromSolarMap)) {
				for (Long time : keyList) {
					BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
					fromSolar = fromSolar.signum() == -1 ? new BigDecimal("0") : fromSolar;

					BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());
					BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());

					BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal homeEnergy = NumberUtil.round(
							NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid, fromBattery), toGrid, toBattery), 2,
							RoundingMode.HALF_UP
					);

					homeEnergyDps.put(time, homeEnergy);

					totalSolar = NumberUtil.add(totalSolar, fromSolar);
					totalFromGrid = NumberUtil.add(totalFromGrid, fromGrid);
					totalToGrid = NumberUtil.add(totalToGrid, toGrid);
					totalFromBattery = NumberUtil.add(totalFromBattery, fromBattery);
					totalToBattery = NumberUtil.add(totalToBattery, toBattery);
					totalHomeEnergy = NumberUtil.add(totalHomeEnergy, homeEnergy);
				}
			}

			historyHomeBo.setHomeEnergyDps(homeEnergyDps);
			historyHomeBo.setFromBattery(totalFromBattery);
			historyHomeBo.setToBattery(totalToBattery);
			historyHomeBo.setFromGrid(totalFromGrid);
			historyHomeBo.setToGrid(totalToGrid);
			historyHomeBo.setFromSolar(totalSolar);
			historyHomeBo.setHomeEnergy(totalHomeEnergy);
			if (totalSolar.compareTo(BigDecimal.ZERO) == 0 || totalHomeEnergy.compareTo(BigDecimal.ZERO) == 0) {
				historyHomeBo.setSelfPowered(BigDecimal.ZERO);
			} else {
				BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(totalSolar, totalHomeEnergy), 2, RoundingMode.HALF_UP);
				BigDecimal percentData = NumberUtil.mul(
						solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
						new BigDecimal(100)
				);
				historyHomeBo.setSelfPowered(percentData);
			}
		}
		return historyHomeBo;
	}

	/**
	 * 0: 自选月; 1: 本周; 2: 本月; 3: 本季度; 4: 本年
	 *
	 * @param homeHistoryVo 查询时间段
	 * @return 统计结果
	 */
	@Override
	public HomeHistoryHomeDto queryHistoryHome(HomeHistoryVo homeHistoryVo) {
		HistoryHomeBo historyHomeBo = queryHistoryHomeBo(homeHistoryVo);
		HomeHistoryHomeDto historyHomeDto = new HomeHistoryHomeDto();
		historyHomeDto.setEnergyConsumption(historyHomeBo.getHomeEnergy());
		historyHomeDto.setSolarPercent(historyHomeBo.getSelfPowered());
		historyHomeDto.setHomeEnergyDps(historyHomeBo.getHomeEnergyDps());
		return historyHomeDto;
	}

	@Override
	public HomeHistorySolarDto queryHistorySolar(HomeHistoryVo homeHistoryVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, homeHistoryVo.getDeviceId());
		Pair<Long, Long> timePair = pairStartTimeAndEndTimeForHomeHistory(homeHistoryVo, pair.getKey().getTimeZone());

		LinkedHashMap<String, BigDecimal> fromSolarLinkedMap = new LinkedHashMap<>(1024);
		HomeHistorySolarDto homeHistorySolarDto = new HomeHistorySolarDto();
		homeHistorySolarDto.setCarbonReduction(new BigDecimal("0"));
		homeHistorySolarDto.setFromSolar(new BigDecimal("0"));
		homeHistorySolarDto.setFromSolarDps(fromSolarLinkedMap);

		if (timePair.getKey() > timePair.getValue()) {
			return homeHistorySolarDto;
		}

		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(pair.getValue());
		List<String> metricList = buildMetricListByGraph(CommonConstants.GRAPH_SOLAR);
		Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(
				pair.getValue().getDeviceSn(),
				metricList,
				timePair.getKey(),
				timePair.getValue(),
				TsdbSampleEnum.ONE_HOUR_NONE_POINT
		);
		Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(
				result,
				homeHistoryVo.getPeriodType(),
				pair.getKey().getTimeZone()
		);

		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
			LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
			if (fromSolarMap2 != null) {
				for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
					Long time = entry.getKey();
					Object value = entry.getValue();
					if (fromSolarMap.containsKey(time)) {
						// 如果键已存在，则将值相加
						BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
						BigDecimal additionalValue = new BigDecimal(value.toString());
						BigDecimal sum = originalValue.add(additionalValue);
						fromSolarMap.put(time, sum);
					} else {
						// 如果键不存在，则添加键值对
						fromSolarMap.put(time, value);
					}
				}
			}

			BigDecimal totalSolar = BigDecimal.ZERO;

			if (CollUtil.isNotEmpty(fromSolarMap)) {
				for (Long time : fromSolarMap.keySet()) {
					BigDecimal solar = NumberUtil.round(
							new BigDecimal(fromSolarMap.getOrDefault(time, BigDecimal.ZERO).toString()), 2,
							RoundingMode.HALF_UP
					);
					fromSolarLinkedMap.put(String.valueOf(time), solar);
					totalSolar = NumberUtil.add(totalSolar, solar);
				}
				homeHistorySolarDto.setFromSolar(totalSolar);
				homeHistorySolarDto.setFromSolarDps(fromSolarLinkedMap);
				homeHistorySolarDto.setCarbonReduction(
						NumberUtil.round(NumberUtil.mul(totalSolar, new BigDecimal("0.997")), 2, RoundingMode.HALF_UP));
			}
		}
		return homeHistorySolarDto;
	}

	@Override
	public HomeHistoryBatteryDto queryHistoryBattery(HomeHistoryVo homeHistoryVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, homeHistoryVo.getDeviceId());
		Pair<Long, Long> timePair = pairStartTimeAndEndTimeForHomeHistory(homeHistoryVo, pair.getKey().getTimeZone());

		HomeHistoryBatteryDto homeHistoryBatteryDto = new HomeHistoryBatteryDto();
		homeHistoryBatteryDto.setToBattery(new BigDecimal("0"));
		homeHistoryBatteryDto.setFromBattery(new BigDecimal("0"));
		homeHistoryBatteryDto.setFromBatteryDps(Maps.newHashMap());
		homeHistoryBatteryDto.setToBatteryDps(Maps.newHashMap());
		homeHistoryBatteryDto.setBatterySoh(new BigDecimal("100"));

		if (timePair.getKey() > timePair.getValue()) {
			return homeHistoryBatteryDto;
		}
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(pair.getValue());
		List<String> metricList = buildMetricListByGraph(CommonConstants.GRAPH_BATTERY);
		DatabaseLastPointWorker lastPointWorker = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_SOH), timePair.getValue());
		DatabaseDeltaQueryOneHourPointWorker deltaQueryOneHourPointWorker = new DatabaseDeltaQueryOneHourPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(), metricList, timePair.getKey(), timePair.getValue());

		WorkerWrapper<String, Map<String, LinkedHashMap<Long, Object>>> deltaQueryOneHourPointWorkerWrapper = new WorkerWrapper.Builder<String, Map<String, LinkedHashMap<Long, Object>>>()
				.worker(deltaQueryOneHourPointWorker)
				.callback(deltaQueryOneHourPointWorker)
				.param("DatabaseDeltaQueryOneHourPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorkerWrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker)
				.callback(lastPointWorker)
				.param("DatabaseLastPointWorker")
				.build();

		try {
			Async.beginWork(
					RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES,
					lastPointWorkerWrapper,
					deltaQueryOneHourPointWorkerWrapper);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(
				deltaQueryOneHourPointWorkerWrapper.getWorkResult().getResult(),
				homeHistoryVo.getPeriodType(),
				pair.getKey().getTimeZone()
		);

		LinkedHashMap<String, BigDecimal> fromBatteryLinkedMap = new LinkedHashMap<>(1024);
		LinkedHashMap<String, BigDecimal> toBatteryLinkedMap = new LinkedHashMap<>(1024);

		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> toBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
			LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);

			BigDecimal totalToBattery = BigDecimal.ZERO;
			BigDecimal totalFromBattery = BigDecimal.ZERO;

			if (CollUtil.isNotEmpty(toBatteryMap)) {
				for (Long time : toBatteryMap.keySet()) {
					BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());

					totalToBattery = NumberUtil.add(toBattery, totalToBattery);
					totalFromBattery = NumberUtil.add(fromBattery, totalFromBattery);
					toBatteryLinkedMap.put(String.valueOf(time), NumberUtil.round(toBattery, 2, RoundingMode.HALF_UP));
					fromBatteryLinkedMap.put(String.valueOf(time), NumberUtil.round(fromBattery, 2, RoundingMode.HALF_UP));
				}
				homeHistoryBatteryDto.setToBattery(NumberUtil.round(totalToBattery, 2, RoundingMode.HALF_UP));
				homeHistoryBatteryDto.setFromBattery(NumberUtil.round(totalFromBattery, 2, RoundingMode.HALF_UP));
				homeHistoryBatteryDto.setFromBatteryDps(fromBatteryLinkedMap);
				homeHistoryBatteryDto.setToBatteryDps(toBatteryLinkedMap);
			}
		}
		Dict dict = lastPointWorkerWrapper.getWorkResult().getResult();
		homeHistoryBatteryDto.setBatterySoh(NumberUtil.round(
				new BigDecimal(dict.getOrDefault(TsdbMetricsConstants.BAT_SOH, BigDecimal.ZERO).toString()), 2,
				RoundingMode.HALF_UP
		));
		return homeHistoryBatteryDto;
	}

	@Override
	public BigDecimal queryHistoryBatteryStatistics(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> clientUserDoStringPair = validateDeviceOwner(clientUserDo, deviceId);
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(clientUserDoStringPair.getValue());
		Dict lastPointDict = timeSeriesDatabaseService.lastPoint(
				clientUserDoStringPair.getValue().getDeviceSn(),
				ListUtil.toList(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE),
				System.currentTimeMillis()
		);
		BigDecimal totalDischarge = BigDecimal.ZERO;
		totalDischarge = totalDischarge
				.add(new BigDecimal(lastPointDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE)));
		return NumberUtil.round(totalDischarge, 2, RoundingMode.HALF_UP);
	}

	@Override
	public HomeHistoryGridDto queryHistoryGrid(HomeHistoryVo homeHistoryVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, homeHistoryVo.getDeviceId());
		Pair<Long, Long> timePair = pairStartTimeAndEndTimeForHomeHistory(homeHistoryVo, pair.getKey().getTimeZone());

		HomeHistoryGridDto homeHistoryGridDto = new HomeHistoryGridDto();
		homeHistoryGridDto.setFromGrid(new BigDecimal("0"));
		homeHistoryGridDto.setToGrid(new BigDecimal("0"));

		if (timePair.getKey() > timePair.getValue()) {
			return homeHistoryGridDto;
		}

		List<String> metricList = buildMetricListByGraph(CommonConstants.GRAPH_GRID);
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(pair.getValue());
		Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(
				pair.getValue().getDeviceSn(),
				metricList,
				timePair.getKey(),
				timePair.getValue(),
				TsdbSampleEnum.ONE_HOUR_NONE_POINT
		);
		Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(
				result,
				homeHistoryVo.getPeriodType(),
				pair.getKey().getTimeZone()
		);

		Map<String, BigDecimal> fromGridMap = new LinkedHashMap<>();
		Map<String, BigDecimal> toGridMap = new LinkedHashMap<>();
		BigDecimal totalFromGrid = BigDecimal.ZERO;
		BigDecimal totalToGrid = BigDecimal.ZERO;

		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> fromGridLinkedMap = metricDataMap
					.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID, new LinkedHashMap<>());
			LinkedHashMap<Long, Object> toGridLinkedMap = metricDataMap
					.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID, new LinkedHashMap<>());
			if (CollUtil.isNotEmpty(fromGridLinkedMap)) {
				for (Long time : fromGridLinkedMap.keySet()) {
					BigDecimal fromGrid = new BigDecimal(fromGridLinkedMap.getOrDefault(time, "0").toString());
					BigDecimal toGrid = new BigDecimal(toGridLinkedMap.getOrDefault(time, "0").toString());
					totalFromGrid = NumberUtil.add(totalFromGrid, fromGrid);
					totalToGrid = NumberUtil.add(totalToGrid, toGrid);
					fromGridMap.put(String.valueOf(time), NumberUtil.round(fromGrid, 2, RoundingMode.HALF_UP));
					toGridMap.put(String.valueOf(time), NumberUtil.round(toGrid, 2, RoundingMode.HALF_UP));
				}
				homeHistoryGridDto.setFromGrid(NumberUtil.round(totalFromGrid, 2, RoundingMode.HALF_UP));
				homeHistoryGridDto.setToGrid(NumberUtil.round(totalToGrid, 2, RoundingMode.HALF_UP));
			}
		}

		homeHistoryGridDto.setFromGridDps(fromGridMap);
		homeHistoryGridDto.setToGridDps(toGridMap);
		return homeHistoryGridDto;
	}

	@Override
	public HomeNowDeviceStatisticsDto queryNowDeviceStatistics(HomeNowDeviceStatisticsVo homeNowDeviceStatisticsVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = validateDeviceOwner(clientUserDo, homeNowDeviceStatisticsVo.getDeviceId());
		Pair<Long, Long> timePair = pairStartTimeAndEndTimeForStatistics(
				homeNowDeviceStatisticsVo.getPeriodType(),
				pair.getKey().getTimeZone()
		);
		HomeNowDeviceStatisticsDto homeNowDeviceStatisticsDto = new HomeNowDeviceStatisticsDto();
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(pair.getValue());

		homeNowDeviceStatisticsDto.setBackup(eventService.countBackupMode(pair.getValue().getDeviceSn()));

		DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), timePair.getKey());
		DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), timePair.getValue());
		DatabaseLastPointWorker lastPointWorker3 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_SOH), timePair.getValue());
		DatabaseDeltaQueryOneHourPointWorker deltaQueryOneHourPointWorker = new DatabaseDeltaQueryOneHourPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(), CommonConstants.METRIC_LIST_V2, timePair.getKey(), timePair.getValue());

		WorkerWrapper<String, Map<String, LinkedHashMap<Long, Object>>> deltaQueryOneHourPointWorkerWrapper = new WorkerWrapper.Builder<String, Map<String, LinkedHashMap<Long, Object>>>()
				.worker(deltaQueryOneHourPointWorker)
				.callback(deltaQueryOneHourPointWorker)
				.param("DatabaseDeltaQueryOneHourPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker1)
				.callback(lastPointWorker1)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker2)
				.callback(lastPointWorker2)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorker3Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker3)
				.callback(lastPointWorker3)
				.param("DatabaseLastPointWorker")
				.build();

		try {
			Async.beginWork(
					RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES,
					lastPointWorker1Wrapper,
					lastPointWorker2Wrapper,
					lastPointWorker3Wrapper,
					deltaQueryOneHourPointWorkerWrapper);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		setHomeEnergyRefStatistics(
				lastPointWorker3Wrapper.getWorkResult().getResult(),
				deltaQueryOneHourPointWorkerWrapper.getWorkResult().getResult(),
				homeNowDeviceStatisticsDto
		);

		homeNowDeviceStatisticsDto.setRecycleTimes(computeBatteryCycleTimes(lastPointWorker1Wrapper.getWorkResult().getResult(), lastPointWorker2Wrapper.getWorkResult().getResult()));
		return homeNowDeviceStatisticsDto;
	}

	@Override
	public HomeNowDeviceRealtimeDto queryNowDeviceRealtime(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> userPair = validateDeviceOwner(clientUserDo, deviceId);
		String offset = userPair.getKey().getTimeZone();

		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(userPair.getValue());

		String deviceName = userPair.getValue().getDeviceSn();
		Map<String, LinkedHashMap<Long, Object>> metricMap;
		if (deviceName.startsWith("TH")) {
			metricMap = timeSeriesDatabaseService.graphQuery(
					deviceName,
					CommonConstants.TH_REALTIME_POWER_V2,
					TimeUtil.getDayStart(0, offset),
					TimeUtil.getCurrentTime(offset), false
			);
		} else {
			metricMap = timeSeriesDatabaseService.graphQuery(
					deviceName,
					CommonConstants.SH_REALTIME_POWER_V2,
					TimeUtil.getDayStart(0, offset),
					TimeUtil.getCurrentTime(offset), false
			);
		}
		return packageNowDeviceRealtimeResult(metricMap);
	}

	@Override
	public HomeNowDeviceRealtimeDto packageNowDeviceRealtimeResult(
			Map<String, LinkedHashMap<Long, Object>> metricMap
	) {
		HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = new HomeNowDeviceRealtimeDto();

		List<Long> sortedTimeList = new ArrayList<>();
		List<Object> meterValueList = new ArrayList<>();
		List<Object> solarValueList1 = new ArrayList<>();
		List<Object> solarValueList2 = new ArrayList<>();
		List<Object> batteryValueList = new ArrayList<>();
		List<Object> epsRList = new ArrayList<>();
		List<Object> epsSList = new ArrayList<>();
		List<Object> epsTList = new ArrayList<>();

		for (String metric : metricMap.keySet()) {
			LinkedHashMap<Long, Object> resultMap = metricMap.get(metric);
			switch (metric) {
				case TsdbMetricsConstants.METER_P_PV:
					solarValueList1 = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.METER_PV_P:
					solarValueList2 = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.AC_P:
					homeNowDeviceRealtimeDto.setGridPowerDps(resultMap);
					break;
				case TsdbMetricsConstants.BAT_P:
					batteryValueList = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.METER_P:
					sortedTimeList = resultMap.keySet().stream().sorted().collect(Collectors.toList());
					meterValueList = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.EPS_P:
					epsRList = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.EPS_PS:
					epsSList = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				case TsdbMetricsConstants.EPS_PT:
					epsTList = resultMap.keySet().stream().sorted().map(resultMap::get)
							.collect(Collectors.toList());
					break;
				default:
			}
		}

		if (CollUtil.isEmpty(sortedTimeList)
				|| CollUtil.isEmpty(meterValueList)
				|| CollUtil.isEmpty(solarValueList1)
				|| CollUtil.isEmpty(batteryValueList)
				|| CollUtil.isEmpty(epsRList)
		) {
			return homeNowDeviceRealtimeDto;
		}

		Map<Long, Object> homePowerDps = new LinkedHashMap<>();
		Map<Long, Object> meterPowerDps = new LinkedHashMap<>();
		Map<Long, Object> solarPowerDps = new LinkedHashMap<>();
		Map<Long, Object> batteryPowerDps = new LinkedHashMap<>();
		Map<Long, Object> epsPowerDps = new LinkedHashMap<>();

		// 家庭用电功率:meter_p(电网) + meter_p_pv(光伏) - eps_p(应急负载) - bat_p(电池)
		for (int i = 0; i < sortedTimeList.size(); i++) {
			Long time = sortedTimeList.get(i);
			BigDecimal meterValue = parseListValue(i, meterValueList);
			BigDecimal solarValue1 = parseListValue(i, solarValueList1);
			BigDecimal solarValue2 = parseListValue(i, solarValueList2);
			BigDecimal solarValue = NumberUtil.add(solarValue1,solarValue2);
			BigDecimal batteryValue = parseListValue(i, batteryValueList);

			BigDecimal epsRValue = parseListValue(i, epsRList);
			BigDecimal epsSValue = parseListValue(i, epsSList);
			BigDecimal epsTValue = parseListValue(i, epsTList);

			meterPowerDps.put(time, meterValue);
			solarPowerDps.put(time, solarValue);
			batteryPowerDps.put(time, batteryValue);

			BigDecimal epsValue = NumberUtil.add(epsRValue, epsSValue, epsTValue);
			epsPowerDps.put(time, epsValue);

			homePowerDps.put(time, NumberUtil.sub(NumberUtil.add(meterValue, solarValue), batteryValue, epsValue));
		}

		homeNowDeviceRealtimeDto.setHomePowerDps(homePowerDps);
		homeNowDeviceRealtimeDto.setSolarPowerDps(solarPowerDps);
		homeNowDeviceRealtimeDto.setBatteryPowerDps(batteryPowerDps);
		homeNowDeviceRealtimeDto.setMeterPowerDps(meterPowerDps);
		homeNowDeviceRealtimeDto.setEpsPowerDps(epsPowerDps);
		return homeNowDeviceRealtimeDto;
	}

	private BigDecimal parseListValue(Integer i, List<Object> list) {
		return new BigDecimal(i > list.size() - 1
				? "0"
				: Optional.ofNullable(list.get(i)).orElse("0").toString());
	}

	@Override
	public HomeNowDeviceRunDataDto queryNowDeviceRunData(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> userPair = validateDeviceOwner(clientUserDo, deviceId);

		HomeNowDeviceRunDataDto homeNowDeviceRunDataDto = new HomeNowDeviceRunDataDto();

		HybridSinglePhaseDO hybridSinglePhaseDO = userPair.getValue();
		Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);
		homeNowDeviceRunDataDto.setSysRunMode(sysRunMode);

		if (sysRunMode.equals(DeviceStatusEnum.OFFLINE.getDbCode())) {
			log.info("设备id： " + deviceId + " 离线");
			return homeNowDeviceRunDataDto;
		}

		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(userPair.getValue());
		Dict lastPointDict = timeSeriesDatabaseService.lastPoint(userPair.getValue().getDeviceSn(), CommonConstants.NOW_RUN_DATA, System.currentTimeMillis());

		if (((int) lastPointDict.values().stream().filter(Objects::isNull).count()) > 0) {
			log.warn("首页数据存在Null: {}", JSONUtil.toJsonStr(lastPointDict));
			throw new EcosException(EcosExceptionEnum.INVALID_DATA);
		}

		String solarPower = StrUtil.toString(NumberUtil.add(lastPointDict.getStr(TsdbMetricsConstants.METER_P_PV), lastPointDict.getStr(TsdbMetricsConstants.METER_PV_P)));
		reflectSetRunData("setSolarPower", solarPower, homeNowDeviceRunDataDto);
		reflectSetRunData("setGridPower", lastPointDict.getStr(TsdbMetricsConstants.AC_P), homeNowDeviceRunDataDto);
		reflectSetRunData("setBatteryPower", lastPointDict.getStr(TsdbMetricsConstants.BAT_P), homeNowDeviceRunDataDto);
		reflectSetRunData("setMeterPower", lastPointDict.getStr(TsdbMetricsConstants.METER_P), homeNowDeviceRunDataDto);
		reflectSetRunData("setEpsPower", lastPointDict.getStr(TsdbMetricsConstants.EPS_P_1), homeNowDeviceRunDataDto);
		setDeviceRunDataBatterySoc(lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC), homeNowDeviceRunDataDto);
		val sysPowerConfig = lastPointDict.getOrDefault(TsdbMetricsConstants.SYS_POWER_CONFIG, "-1").toString();
		homeNowDeviceRunDataDto.setSysPowerConfig(new BigDecimal(sysPowerConfig).intValue());

		homeNowDeviceRunDataDto.setHomePower(NumberUtil.round(
				NumberUtil.sub(NumberUtil.sub(NumberUtil.add(
						homeNowDeviceRunDataDto.getSolarPower(),
						homeNowDeviceRunDataDto.getMeterPower()
				), homeNowDeviceRunDataDto.getBatteryPower()), homeNowDeviceRunDataDto.getEpsPower()),
				0, RoundingMode.HALF_UP
		));

		return homeNowDeviceRunDataDto;
	}

	@Override
	public void nowDataIncreaseRefresh(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		validateDeviceOwner(clientUserDo, deviceId);
		hubService.speedupOnce(deviceId);
	}

	@Override
	public PageInfoDTO<HomeEventsBackupPageDto> pageBackup(HomeEventsBackupPageVo homeEventsBackupPageVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> userPair = validateDeviceOwner(clientUserDo, homeEventsBackupPageVo.getDeviceId());
		List<EcosEpsEventBo> ecosEpsEventBos = eventService.listEpsEventByCondition(
				userPair.getValue().getDeviceSn(),
				homeEventsBackupPageVo.getStartTime(),
				homeEventsBackupPageVo.getEndTime()
		);

		Integer pageNum = homeEventsBackupPageVo.getPageNum();
		Integer pageSize = homeEventsBackupPageVo.getPageSize();
		int count = ecosEpsEventBos.size();

		PageInfoDTO<HomeEventsBackupPageDto> pageInfoDto = new PageInfoDTO<>();
		pageInfoDto.setTotalPages(count == 0 ? 0 : count / pageSize + 1);
		pageInfoDto.setTotalCount((long) count);

		List<List<EcosEpsEventBo>> splitList = ListUtil.split(ecosEpsEventBos, pageSize);
		if (pageNum > splitList.size()) {
			pageInfoDto.setData(ListUtil.empty());
		} else {
			List<EcosEpsEventBo> pageData = splitList.get(pageNum - 1);
			List<HomeEventsBackupPageDto> pageDtoList = new ArrayList<>();
			// 将列表转换为数组
			WorkerWrapper<String, Dict>[] wrappers = new WorkerWrapper[0];
			TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
					.chooseTimeSeriesDatabaseService(userPair.getValue());
			for (EcosEpsEventBo epsEventBo : pageData) {
				String deviceName = epsEventBo.getDeviceName();
				EcosEpsEventBo notEpsEventBo = eventService.findNearlyNotEpsEvent(deviceName, epsEventBo.getId());
				long start = epsEventBo.getUploadTime();
				long end = notEpsEventBo == null ? System.currentTimeMillis() / 1000 : notEpsEventBo.getUploadTime();

				HomeEventsBackupPageDto homeEventsBackupPageDto = new HomeEventsBackupPageDto();
				homeEventsBackupPageDto.setStartTime(
						TimeUtil.longTimestampToSerialStringOffsetGMT8(start * 1000L, userPair.getKey().getTimeZone()));

				DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceName,
						ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
						start * 1000);
				DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceName,
						ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
						end * 1000);

				WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
						.worker(lastPointWorker1)
						.callback(lastPointWorker1)
						.param("DatabaseLastPointWorker")
						.build();
				WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
						.worker(lastPointWorker2)
						.callback(lastPointWorker2)
						.param("DatabaseLastPointWorker")
						.build();
				// 创建一个更大的数组，并将原数组中的元素复制到新数组中
				wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
				// 将新元素添加到数组的末尾
				wrappers[wrappers.length - 1] = lastPointWorker1Wrapper;
				wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
				wrappers[wrappers.length - 1] = lastPointWorker2Wrapper;
				pageDtoList.add(homeEventsBackupPageDto);
			}

			try {
				Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
			} catch (ExecutionException | InterruptedException e) {
				log.warn(e.getMessage());
			}

			for (EcosEpsEventBo epsEventBo : pageData) {
				String deviceName = epsEventBo.getDeviceName();
				EcosEpsEventBo notEpsEventBo = eventService.findNearlyNotEpsEvent(deviceName, epsEventBo.getId());
				long start = epsEventBo.getUploadTime();
				long end = notEpsEventBo == null ? System.currentTimeMillis() / 1000 : notEpsEventBo.getUploadTime();
				int i = pageData.indexOf(epsEventBo);
				HomeEventsBackupPageDto homeEventsBackupPageDto = pageDtoList.get(i);

				Dict startResult = wrappers[2 * i].getWorkResult().getResult();
				Dict endResult = wrappers[2 * i + 1].getWorkResult().getResult();

				homeEventsBackupPageDto
						.setMinutes(NumberUtil.round(new BigDecimal((end - start) / 60), 2, RoundingMode.HALF_UP));
				BigDecimal deltaEnergy = NumberUtil.round(NumberUtil.sub(
								new BigDecimal(
										endResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString()),
								new BigDecimal(
										startResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString())
						),
						2, RoundingMode.HALF_UP
				);
				homeEventsBackupPageDto
						.setEnergy(deltaEnergy.compareTo(BigDecimal.ZERO) > 0 ? deltaEnergy : BigDecimal.ZERO);
			}

			pageInfoDto.setData(pageDtoList);
		}
		return pageInfoDto;
	}

	@Override
	public HomeEventsBackupStatisticsDto backupStatistics(HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> userPair = validateDeviceOwner(clientUserDo,
				homeEventsBackupStatisticsVo.getDeviceId());
		List<EcosEpsEventBo> ecosEpsEventBos = eventService.listEpsEventByCondition(
				userPair.getValue().getDeviceSn(),
				homeEventsBackupStatisticsVo.getStartTime(),
				homeEventsBackupStatisticsVo.getEndTime()
		);

		BigDecimal totalEnergy = BigDecimal.ZERO;
		BigDecimal totalDuration = BigDecimal.ZERO;

		WorkerWrapper<String, Dict>[] wrappers = new WorkerWrapper[0];
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(userPair.getValue());
		for (EcosEpsEventBo ecosEpsEventBo : ecosEpsEventBos) {
			String deviceName = ecosEpsEventBo.getDeviceName();
			EcosEpsEventBo notEpsEventBo = eventService.findNearlyNotEpsEvent(deviceName, ecosEpsEventBo.getId());
			long start = ecosEpsEventBo.getUploadTime();
			long end = notEpsEventBo == null ? System.currentTimeMillis() / 1000 : notEpsEventBo.getUploadTime();

			totalDuration = NumberUtil.add(totalDuration, new BigDecimal(end - start));

			DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceName,
					ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
					start * 1000);
			DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceName,
					ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
					end * 1000);

			WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker1)
					.callback(lastPointWorker1)
					.param("DatabaseLastPointWorker")
					.build();
			WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker2)
					.callback(lastPointWorker2)
					.param("DatabaseLastPointWorker")
					.build();

			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker1Wrapper;
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker2Wrapper;
		}

		try {
			Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		for (EcosEpsEventBo ecosEpsEventBo : ecosEpsEventBos) {
			int i = ecosEpsEventBos.indexOf(ecosEpsEventBo);

			Dict startResult = wrappers[2 * i].getWorkResult().getResult();
			Dict endResult = wrappers[2 * i + 1].getWorkResult().getResult();

			totalEnergy = NumberUtil.add(NumberUtil.round(NumberUtil.sub(
							new BigDecimal(
									endResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString()),
							new BigDecimal(
									startResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString())
					),
					2, RoundingMode.HALF_UP
			), totalEnergy);
		}

		HomeEventsBackupStatisticsDto homeEventsBackupStatisticsDto = new HomeEventsBackupStatisticsDto();
		homeEventsBackupStatisticsDto.setBackupCount(ecosEpsEventBos.size());
		homeEventsBackupStatisticsDto
				.setBackupEnergy(totalEnergy.compareTo(BigDecimal.ZERO) > 0 ? totalEnergy : BigDecimal.ZERO);
		homeEventsBackupStatisticsDto.setBackupDuration(
				NumberUtil.round(NumberUtil.div(totalDuration, new BigDecimal("3600")), 2, RoundingMode.HALF_UP));
		return homeEventsBackupStatisticsDto;
	}

	@Override
	public HomeDeviceSketchDto getDeviceSketch() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String offset = clientUserDo.getTimeZone();
		List<MiddleClientUserDeviceDo> middleClientUserDeviceDoList = middleClientUserDeviceService.list(Wrappers
				.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(
						MiddleClientUserDeviceDo::getUserId,
						clientUserDo.getId()
				));
		HomeDeviceSketchDto homeDeviceSketchDto = new HomeDeviceSketchDto();

		BigDecimal currentSolarPower = BigDecimal.ZERO;
		BigDecimal currentDayGeneratedEnergy = BigDecimal.ZERO;
		BigDecimal currentMonthGeneratedEnergy = BigDecimal.ZERO;
		BigDecimal currentYearGeneratedEnergy = BigDecimal.ZERO;
		BigDecimal totalEnergy = BigDecimal.ZERO;

		BigDecimal dailyDischargingEnergy = BigDecimal.ZERO;
		BigDecimal monthlyDischargingEnergy = BigDecimal.ZERO;
		BigDecimal yearlyDischargingEnergy = BigDecimal.ZERO;
		BigDecimal totalDischargingEnergy = BigDecimal.ZERO;

		BigDecimal dailyChargingEnergy = BigDecimal.ZERO;
		BigDecimal monthlyChargingEnergy = BigDecimal.ZERO;
		BigDecimal yearlyChargingEnergy = BigDecimal.ZERO;
		BigDecimal totalChargingEnergy = BigDecimal.ZERO;

		List<Long> deviceIds = middleClientUserDeviceDoList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());
		List<HybridSinglePhaseDO> hybridSinglePhaseDOList = hubService.getBatchById(false, deviceIds);
		WorkerWrapper<String, Dict>[] wrappers = new WorkerWrapper[0];

		for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {

			String deviceFlag = hybridSinglePhaseDO.getDeviceName();
			TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
					.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

			DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceFlag, CommonConstants.HOME_DEVICE_SKETCH_V1, System.currentTimeMillis());
			DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceFlag, CommonConstants.HOME_DEVICE_SKETCH_V2, TimeUtil.getDayStart(0, offset));
			DatabaseLastPointWorker lastPointWorker3 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceFlag, CommonConstants.HOME_DEVICE_SKETCH_V2, TimeUtil.getCurrentMonthStart(offset));
			DatabaseLastPointWorker lastPointWorker4 = new DatabaseLastPointWorker(timeSeriesDatabaseService, deviceFlag, CommonConstants.HOME_DEVICE_SKETCH_V2, TimeUtil.getCurrentYearStart(offset));

			WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker1)
					.callback(lastPointWorker1)
					.param("DatabaseLastPointWorker")
					.build();
			WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker2)
					.callback(lastPointWorker2)
					.param("DatabaseLastPointWorker")
					.build();
			WorkerWrapper<String, Dict> lastPointWorker3Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker3)
					.callback(lastPointWorker3)
					.param("DatabaseLastPointWorker")
					.build();
			WorkerWrapper<String, Dict> lastPointWorker4Wrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker4)
					.callback(lastPointWorker4)
					.param("DatabaseLastPointWorker")
					.build();
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker1Wrapper;
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker2Wrapper;
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker3Wrapper;
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			wrappers[wrappers.length - 1] = lastPointWorker4Wrapper;
		}

		try {
			Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {
			Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);
			int i = hybridSinglePhaseDOList.indexOf(hybridSinglePhaseDO);

			Dict currentDict = wrappers[4 * i].getWorkResult().getResult();

			if (sysRunMode >= 0) {
				currentSolarPower = setAndAdd(currentDict.getStr(TsdbMetricsConstants.METER_P_PV), currentSolarPower);
				currentSolarPower = setAndAdd(currentDict.getStr(TsdbMetricsConstants.METER_PV_P), currentSolarPower);
			}

			totalEnergy = setAndAdd(currentDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV), totalEnergy);
			totalEnergy = setAndAdd(currentDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID), totalEnergy);

			Dict todayStartDict = wrappers[4 * i + 1].getWorkResult().getResult();

			currentDayGeneratedEnergy = setAndAdd(
					todayStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV),
					currentDayGeneratedEnergy
			);
			currentDayGeneratedEnergy = setAndAdd(
					todayStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID),
					currentDayGeneratedEnergy
			);

			Dict currentMonthStartDict = wrappers[4 * i + 2].getWorkResult().getResult();
			currentMonthGeneratedEnergy = setAndAdd(
					currentMonthStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV),
					currentMonthGeneratedEnergy
			);
			currentMonthGeneratedEnergy = setAndAdd(
					currentMonthStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID),
					currentMonthGeneratedEnergy
			);

			Dict currentYearStartDict = wrappers[4 * i + 3].getWorkResult().getResult();
			currentYearGeneratedEnergy = setAndAdd(
					currentYearStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV),
					currentYearGeneratedEnergy
			);
			currentYearGeneratedEnergy = setAndAdd(
					currentYearStartDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID),
					currentYearGeneratedEnergy
			);

			dailyDischargingEnergy = setAndAdd(
					todayStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE),
					dailyDischargingEnergy
			);
			monthlyDischargingEnergy = setAndAdd(
					currentMonthStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE),
					monthlyDischargingEnergy
			);
			yearlyDischargingEnergy = setAndAdd(
					currentYearStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE),
					yearlyDischargingEnergy
			);
			totalDischargingEnergy = setAndAdd(
					currentDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE),
					totalDischargingEnergy
			);

			dailyChargingEnergy = setAndAdd(
					todayStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE),
					dailyChargingEnergy
			);
			monthlyChargingEnergy = setAndAdd(
					currentMonthStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE),
					monthlyChargingEnergy
			);
			yearlyChargingEnergy = setAndAdd(
					currentYearStartDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE),
					yearlyChargingEnergy
			);
			totalChargingEnergy = setAndAdd(
					currentDict.getStr(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE),
					totalChargingEnergy
			);
		}

		homeDeviceSketchDto.setCurrentSolarPower(NumberUtil.round(currentSolarPower, 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setCurrentDayGeneratedEnergy(
				NumberUtil.round(NumberUtil.sub(totalEnergy, currentDayGeneratedEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setCurrentMonthGeneratedEnergy(
				NumberUtil.round(NumberUtil.sub(totalEnergy, currentMonthGeneratedEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setCurrentYearGeneratedEnergy(
				NumberUtil.round(NumberUtil.sub(totalEnergy, currentYearGeneratedEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setTotalEnergy(NumberUtil.round(totalEnergy, 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setTotalReduceCarbonEmission(
				NumberUtil.round(NumberUtil.mul(totalEnergy, new BigDecimal("0.997")), 2, RoundingMode.HALF_UP));

		homeDeviceSketchDto.setDailyDischargingEnergy(NumberUtil
				.round(
						NumberUtil.sub(totalDischargingEnergy, dailyDischargingEnergy), 2,
						RoundingMode.HALF_UP
				));
		homeDeviceSketchDto.setMonthlyDischargingEnergy(NumberUtil
				.round(
						NumberUtil.sub(totalDischargingEnergy, monthlyDischargingEnergy), 2,
						RoundingMode.HALF_UP
				));
		homeDeviceSketchDto.setYearlyDischargingEnergy(NumberUtil
				.round(
						NumberUtil.sub(totalDischargingEnergy, yearlyDischargingEnergy), 2,
						RoundingMode.HALF_UP
				));
		homeDeviceSketchDto
				.setTotalDischargingEnergy(NumberUtil.round(totalDischargingEnergy, 2, RoundingMode.HALF_UP));

		homeDeviceSketchDto.setDailyChargingEnergy(
				NumberUtil.round(NumberUtil.sub(totalChargingEnergy, dailyChargingEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setMonthlyChargingEnergy(
				NumberUtil.round(NumberUtil.sub(totalChargingEnergy, monthlyChargingEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setYearlyChargingEnergy(
				NumberUtil.round(NumberUtil.sub(totalChargingEnergy, yearlyChargingEnergy), 2, RoundingMode.HALF_UP));
		homeDeviceSketchDto.setTotalChargingEnergy(NumberUtil.round(totalChargingEnergy, 2, RoundingMode.HALF_UP));

		return homeDeviceSketchDto;
	}

	private BigDecimal setAndAdd(String val, BigDecimal data) {
		BigDecimal result = data;
		if (StrUtil.isNotBlank(val)) {
			result = NumberUtil.add(result, new BigDecimal(val));
		}
		return result;
	}

	private void setDeviceRunDataBatterySoc(String val, HomeNowDeviceRunDataDto homeNowDeviceRunDataDto) {
		if (StrUtil.isNotBlank(val)) {
			BigDecimal soc = NumberUtil.div(new BigDecimal(val), new BigDecimal("100"), 2, RoundingMode.HALF_UP);
			homeNowDeviceRunDataDto.setBatterySoc(soc);
		} else {
			homeNowDeviceRunDataDto.setBatterySoc(new BigDecimal("0"));
		}
	}

	private void reflectSetRunData(String method, String val, HomeNowDeviceRunDataDto homeNowDeviceRunDataDto) {
		log.info("{} {}", method, val);
		if (StrUtil.isNotBlank(val)) {
			BigDecimal data = NumberUtil.round(new BigDecimal(val), 0, RoundingMode.HALF_UP);
			ReflectUtil.invoke(homeNowDeviceRunDataDto, method,
					(Math.abs(data.doubleValue()) < 10L) ? BigDecimal.ZERO : data
			);
		} else {
			ReflectUtil.invoke(homeNowDeviceRunDataDto, method, BigDecimal.ZERO);
		}
	}

	private void setHomeEnergyRefStatistics(
			Dict endValDict, Map<String, LinkedHashMap<Long, Object>> metricDataMap,
			HomeNowDeviceStatisticsDto homeNowDeviceStatisticsDto
	) {
		homeNowDeviceStatisticsDto.setBatterySoh(NumberUtil.round(
				new BigDecimal(endValDict.getOrDefault(TsdbMetricsConstants.BAT_SOH, BigDecimal.ZERO).toString()), 2,
				RoundingMode.HALF_UP
		));
		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
					.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
			LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
					.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
			LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
			LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
			LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
			LinkedHashMap<Long, Object> fromGridMap = metricDataMap
					.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);

			BigDecimal totalToBattery = BigDecimal.ZERO;
			BigDecimal totalFromBattery = BigDecimal.ZERO;
			BigDecimal totalFromSolar = BigDecimal.ZERO;
			BigDecimal totalToGrid = BigDecimal.ZERO;
			BigDecimal totalFromGrid = BigDecimal.ZERO;

			if (CollUtil.isNotEmpty(fromSolarMap)) {
				for (Long time : fromSolarMap.keySet()) {
					BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
					BigDecimal fromSolar2 = new BigDecimal(fromSolarMap2.getOrDefault(time, "0").toString());
					BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
					BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());

					totalToBattery = NumberUtil.add(toBattery, totalToBattery);
					totalFromBattery = NumberUtil.add(fromBattery, totalFromBattery);
					totalFromSolar = NumberUtil.add(fromSolar, totalFromSolar);
					totalFromSolar = NumberUtil.add(fromSolar2, totalFromSolar);
					totalToGrid = NumberUtil.add(toGrid, totalToGrid);
					totalFromGrid = NumberUtil.add(fromGrid, totalFromGrid);
				}
			}

			BigDecimal totalConsumption = NumberUtil.add(totalFromSolar, totalFromBattery, totalFromGrid);
			homeNowDeviceStatisticsDto.setConsumptionEnergy(NumberUtil
					.round(
							NumberUtil.sub(totalConsumption, totalToBattery, totalToGrid), 2,
							RoundingMode.HALF_UP
					));
			homeNowDeviceStatisticsDto.setFromBattery(NumberUtil.round(totalFromBattery, 2, RoundingMode.HALF_UP));
			homeNowDeviceStatisticsDto.setToBattery(NumberUtil.round(totalToBattery, 2, RoundingMode.HALF_UP));
			homeNowDeviceStatisticsDto.setFromGrid(NumberUtil.round(totalFromGrid, 2, RoundingMode.HALF_UP));
			homeNowDeviceStatisticsDto.setToGrid(NumberUtil.round(totalToGrid, 2, RoundingMode.HALF_UP));
			homeNowDeviceStatisticsDto.setFromSolar(NumberUtil.round(totalFromSolar, 2, RoundingMode.HALF_UP));

			if (totalConsumption.compareTo(BigDecimal.ZERO) > 0) {
				homeNowDeviceStatisticsDto.setFromGridPercent(
						NumberUtil.round(NumberUtil.div(totalFromGrid, totalConsumption), 4, RoundingMode.HALF_UP));
				homeNowDeviceStatisticsDto.setFromSolarPercent(
						NumberUtil.round(NumberUtil.div(totalFromSolar, totalConsumption), 4, RoundingMode.HALF_UP));
				homeNowDeviceStatisticsDto.setFromBatteryPercent(
						NumberUtil.round(NumberUtil.div(totalFromBattery, totalConsumption), 4, RoundingMode.HALF_UP));
			} else {
				homeNowDeviceStatisticsDto.setFromGridPercent(BigDecimal.ZERO);
				homeNowDeviceStatisticsDto.setFromSolarPercent(BigDecimal.ZERO);
				homeNowDeviceStatisticsDto.setFromBatteryPercent(BigDecimal.ZERO);
			}
		}
	}

	@Override
	public Integer computeBatteryCycleTimes(
			Dict startCycleTimes, Dict endCycleTimes
	) {
		BigDecimal before = new BigDecimal(
				startCycleTimes.getOrDefault(TsdbMetricsConstants.BAT_CYCLE_TIME, "0").toString());
		BigDecimal after = new BigDecimal(
				endCycleTimes.getOrDefault(TsdbMetricsConstants.BAT_CYCLE_TIME, "0").toString());

		int times;
		try {
			times = NumberUtil.sub(after, before).intValue();
		} catch (Exception e) {
			times = 0;
		}
		return Math.max(times, 0);
	}

	private Pair<Long, Long> pairStartTimeAndEndTimeForStatistics(int periodType, String offset) {
		Long startTime;
		Long endTime;
		switch (periodType) {
			case CommonConstants.HOME_DEVICE_PERIOD_TODAY:
				startTime = TimeUtil.getDayStart(0, offset);
				endTime = TimeUtil.getCurrentTime(offset);
				break;
			case CommonConstants.HOME_DEVICE_PERIOD_YESTERDAY:
				startTime = TimeUtil.getLastDayStart(offset);
				endTime = TimeUtil.getLastDayEnd(offset);
				break;
			case CommonConstants.HOME_DEVICE_PERIOD_WEEK:
				startTime = TimeUtil.getCurrentWeekStart(offset);
				endTime = TimeUtil.getCurrentTime(offset);
				break;
			case CommonConstants.HOME_DEVICE_PERIOD_MONTH:
				startTime = TimeUtil.getCurrentMonthStart(offset);
				endTime = TimeUtil.getCurrentTime(offset);
				break;
			default:
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		return Pair.of(startTime, endTime);
	}

	private Pair<Long, Long> pairStartTimeAndEndTimeForHomeHistory(HomeHistoryVo homeHistoryVo, String offset) {
		Integer periodType = homeHistoryVo.getPeriodType();
		long startTime;
		long endTime = TimeUtil.getCurrentTime(offset);
		switch (periodType) {
			case CommonConstants.PERIOD_WEEK:
				startTime = TimeUtil.getCurrentWeekStart(offset);
				break;
			case CommonConstants.PERIOD_MONTH:
				startTime = TimeUtil.getCurrentMonthStart(offset);
				break;
			case CommonConstants.PERIOD_SEASON:
				startTime = TimeUtil.getCurrentSeasonStart(offset);
				break;
			case CommonConstants.PERIOD_YEAR:
				startTime = TimeUtil.getCurrentYearStart(offset);
				break;
			default:
				startTime = TimeUtil.getAssignMonthStart(homeHistoryVo.getTimestamp() * 1000, offset, 0);
				endTime = TimeUtil.getAssignMonthEnd(homeHistoryVo.getTimestamp() * 1000, offset);
		}
		return Pair.of(startTime, endTime);
	}

	private List<String> buildMetricListByGraph(Integer graphType) {
		List<String> metricList = new ArrayList<>();
		switch (graphType) {
			case CommonConstants.GRAPH_HOME:
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
				metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
				metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
				break;
			case CommonConstants.GRAPH_SOLAR:
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
				break;
			case CommonConstants.GRAPH_SOLAR_2:
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
				break;
			case CommonConstants.GRAPH_BATTERY:
				metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
				metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
				break;
			case CommonConstants.GRAPH_GRID:
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
				metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
				break;
			default:
		}
		return metricList;
	}

	@Override
	public Pair<ClientUserDo, HybridSinglePhaseDO> validateDeviceOwner(ClientUserDo clientUserDo, String deviceId) {
		AtomicReference<HybridSinglePhaseDO> hybridSinglePhaseDO = new AtomicReference<>(new HybridSinglePhaseDO());

		// 如果家庭中有这台设备就不做校验
		Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, deviceId);
		if (checkUserHasDevice) {
			hybridSinglePhaseDO.set(hubService.getById(Long.parseLong(deviceId)));
			return Pair.of(clientUserDo, hybridSinglePhaseDO.get());
		}
		OperationUtil
				.of(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
						.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())))
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> {
					hybridSinglePhaseDO.set(hubService.getById(Long.parseLong(deviceId)));
				}, () -> {
					log.warn("未绑定设备");
					return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
		return Pair.of(clientUserDo, hybridSinglePhaseDO.get());
	}

	static void initHybridSinglePhase(HybridSinglePhaseDO hybridSinglePhaseDO) {
		hybridSinglePhaseDO.setAlias("");
		hybridSinglePhaseDO.setDeviceModel("");
		hybridSinglePhaseDO.setBrand("");
		hybridSinglePhaseDO.setFactory("");
		hybridSinglePhaseDO.setPowerBoardHardwareVersion("");
		hybridSinglePhaseDO.setDsp1SoftwareVersion("");
		hybridSinglePhaseDO.setDsp2SoftwareVersion("");
		hybridSinglePhaseDO.setEmsSoftwareVersion("");
		hybridSinglePhaseDO.setEmsHardwareVersion("");
		hybridSinglePhaseDO.setBmsGaugeVersion("");
		hybridSinglePhaseDO.setBmsSn("");
		hybridSinglePhaseDO.setBmsVendor("");
		hybridSinglePhaseDO.setBmsSoftwareVersion("");
		hybridSinglePhaseDO.setBmsHardwareVersion("");
		hybridSinglePhaseDO.setState(0);
		hybridSinglePhaseDO.setLongitude(0.0D);
		hybridSinglePhaseDO.setLatitude(0.0D);
		hybridSinglePhaseDO.setVppMode(false);
		hybridSinglePhaseDO.setCreateTime(LocalDateTime.now());
		hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
	}

	private Map<String, LinkedHashMap<Long, Object>> deltaQueryTSDB(
			Map<String, LinkedHashMap<Long, Object>> result, Integer periodType, String offset
	) {
		if (4 == periodType) {
			return TSDBAggUtil.aggregateDeltaQueryDayToMonth(result, offset);
		} else {
			return TSDBAggUtil.aggregateDeltaQueryHourToDay(result, offset);
		}
	}

	private OperationUtil<MiddleClientUserDeviceDo> getOptionalBindDevice(String deviceId, Long userId) {
		return OperationUtil.of(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
				.eq(MiddleClientUserDeviceDo::getUserId, userId)));
	}

}
