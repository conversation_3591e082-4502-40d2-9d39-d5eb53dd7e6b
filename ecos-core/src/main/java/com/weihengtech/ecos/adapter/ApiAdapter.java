package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.thirdpart.AccountBindInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ApiAdapter {

	/**
	 * 根据设备id获取主账号信息
	 *
	 * @param deviceId 设备id
	 * @return 主账号
	 */
	ClientUserDo getDeviceMasterAccountDo(Long deviceId);

	/**
	 * 根据设备id获取主账号和子账号信息列表
	 *
	 * @param deviceId 设备id
	 * @return 主账号和子账号信息
	 */
	Map<String, List<String>> getDeviceMasterAndSubAccount(Long deviceId);


	/**
	 * 绑定主账号
	 *
	 * @param username    账号
	 * @param device 设备bean
	 * @return 0: 绑定失败 1: 绑定成功 2: 已有主账号
	 */
	Integer bindMasterAccount(HybridSinglePhaseDO device, String username);

	/**
	 * 更新自定义配置页
	 *
	 * @param param 自定义配置信息
	 */
    void updateCustomize(ClientCustomizeDo param);

	/**
	 * 解绑指定设备id的账号
	 *
	 * @param deviceIdList 设备id列表
	 */
    void unbindAccount(List<Long> deviceIdList);

	/**
	 * 根据账号获取绑定的各类设备
	 *
	 * @param account 账号
	 * @return 设备信息
	 */
	AccountBindInfoDTO getDevicesByAccount(String account);
}
