package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.platform.async.executor.Async;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.adapter.GlobalAdapter;
import com.weihengtech.ecos.consts.RequestConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.model.dos.*;
import com.weihengtech.ecos.model.dtos.global.GlobalConfigDto;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalReduceCarbonEmissionsDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.vos.global.TuyaLoginLogVo;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.ClientGlobalConfigService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.global.TuyaLoginLogService;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.worker.database.DatabaseLastPointWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class GlobalAdapterImpl implements GlobalAdapter {

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;

	@Resource
	private ClientGlobalConfigService clientGlobalConfigService;

	@Resource
	private StrategyService strategyService;

	@Resource
	private HubService hubService;

	@Resource
	private TuyaLoginLogService tuyaLoginLogService;

	@Override
	public GlobalReduceCarbonEmissionsDto reduceCarbonEmissionsStatistics() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		List<MiddleClientUserDeviceDo> bindList = middleClientUserDeviceService.list(Wrappers
				.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId()));

		BigDecimal result = BigDecimal.ZERO;
		List<Long> deviceIds = bindList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());
		List<HybridSinglePhaseDO> hybridSinglePhaseDOList = hubService.getBatchById(false, deviceIds);

		WorkerWrapper<String, Dict>[] wrappers = new WorkerWrapper[0];
		for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {
			TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
					.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

			DatabaseLastPointWorker lastPointWorker = new DatabaseLastPointWorker(timeSeriesDatabaseService,
					hybridSinglePhaseDO.getDeviceName(),
					ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID),
					System.currentTimeMillis());

			WorkerWrapper<String, Dict> lastPointWorkerWrapper = new WorkerWrapper.Builder<String, Dict>()
					.worker(lastPointWorker)
					.callback(lastPointWorker)
					.param("DatabaseLastPointWorker")
					.build();

			// 创建一个更大的数组，并将原数组中的元素复制到新数组中
			wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
			// 将新元素添加到数组的末尾
			wrappers[wrappers.length - 1] = lastPointWorkerWrapper;
		}

		try {
			Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {
			int i = hybridSinglePhaseDOList.indexOf(hybridSinglePhaseDO);
			Dict lastPointDict = wrappers[i].getWorkResult().getResult();


			result = NumberUtil.add(result, new BigDecimal(
					String.valueOf(lastPointDict.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV, "0"))));
			result = NumberUtil.add(result, new BigDecimal(
					String.valueOf(lastPointDict.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID, "0"))));
		}
		GlobalReduceCarbonEmissionsDto globalReduceCarbonEmissionsDto = new GlobalReduceCarbonEmissionsDto();
		globalReduceCarbonEmissionsDto.setReduceCarbonEmission(NumberUtil
				.round(NumberUtil.mul(result, new BigDecimal("0.997")), 2, RoundingMode.HALF_UP)
				.toPlainString());
		globalReduceCarbonEmissionsDto.setSaveStandardCoal(NumberUtil
				.round(NumberUtil.mul(result, new BigDecimal("0.404")), 2, RoundingMode.HALF_UP)
				.toPlainString());
		globalReduceCarbonEmissionsDto.setLevel(1);
		return globalReduceCarbonEmissionsDto;
	}

	@Override
	public GlobalConfigDto getGlobalConfig() {
		ClientGlobalConfigDo clientGlobalConfigDo = clientGlobalConfigService.getById(1);
		GlobalConfigDto globalConfigDto = new GlobalConfigDto();
		CglibUtil.copy(clientGlobalConfigDo, globalConfigDto);
		return globalConfigDto;
	}

	@Override
	public GlobalVersionDto getGlobalVersion() {
		return hubService.ecosLatestVersion();
	}

	@Override
	public GlobalEnestLatestVersionDto getEnestLatestVersion() {
		return hubService.enestLatestVersion();
	}

	@Override
	public void saveTuyaLoginLog(ClientUserDo clientUserDo, TuyaLoginLogVo tuyaLoginLogVo) {
		TuyaLoginLogDo tuyaLoginLogDo = new TuyaLoginLogDo();
		CglibUtil.copy(tuyaLoginLogVo, tuyaLoginLogDo);
		tuyaLoginLogDo.setEcosAccount(clientUserDo.getUsername());
		tuyaLoginLogDo.setLoginTime(LocalDateTime.now());
		tuyaLoginLogService.save(tuyaLoginLogDo);
	}

	@Override
	public GlobalReduceCarbonEmissionsDto deviceReduceCarbonEmissionsStatistics(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		MiddleClientUserDeviceDo middleClientUserDeviceDo = Optional
				.ofNullable(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
						.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)))
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE));
		BigDecimal result = BigDecimal.ZERO;
		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(middleClientUserDeviceDo.getDeviceId());
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

		GlobalReduceCarbonEmissionsDto globalDeviceReduceCarbonEmissionsDto = new GlobalReduceCarbonEmissionsDto();
		globalDeviceReduceCarbonEmissionsDto.setReduceCarbonEmission("0");
		globalDeviceReduceCarbonEmissionsDto.setLevel(1);

		Dict lastPointDict = timeSeriesDatabaseService.lastPoint(hybridSinglePhaseDO.getDeviceName(),
				ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID), System.currentTimeMillis()
		);
		String totalPv = lastPointDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
		String totalPv2 = lastPointDict.getStr(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
		totalPv = StrUtil.isBlank(totalPv) ? "0" : totalPv;
		totalPv2 = StrUtil.isBlank(totalPv2) ? "0" : totalPv2;
		result = NumberUtil.add(
				result,
				new BigDecimal(totalPv)
		);
		result = NumberUtil.add(
				result,
				new BigDecimal(totalPv2)
		);
		globalDeviceReduceCarbonEmissionsDto.setReduceCarbonEmission(NumberUtil
				.round(NumberUtil.mul(result, new BigDecimal("0.997")), 2, RoundingMode.HALF_UP)
				.toPlainString());
		globalDeviceReduceCarbonEmissionsDto.setSaveStandardCoal(NumberUtil
				.round(NumberUtil.mul(result, new BigDecimal("0.404")), 2, RoundingMode.HALF_UP)
				.toPlainString());
		return globalDeviceReduceCarbonEmissionsDto;
	}
}
