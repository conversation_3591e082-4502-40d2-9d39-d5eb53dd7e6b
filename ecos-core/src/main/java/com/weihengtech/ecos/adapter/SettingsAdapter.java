package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dtos.settings.SettingsBindDeviceListDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsDeviceDetailDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsUserInfoDto;
import com.weihengtech.ecos.model.vos.settings.SettingHelpEmailVo;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.settings.SettingTransferMainVo;
import com.weihengtech.ecos.model.vos.settings.SettingsDeviceDetailUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserPasswordUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserUpdateVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SettingsAdapter {

	/**
	 * 更新用户个人信息
	 *
	 * @param settingsUserUpdateVo 用户要更新的信息
	 */
	void updateUserInfo(SettingsUserUpdateVo settingsUserUpdateVo);

	/**
	 * 查询用户信息
	 *
	 * @return optional 用户信息
	 */
	SettingsUserInfoDto queryUserInfo();

	/**
	 * 更新用户密码
	 *
	 * @param settingsUserPasswordUpdateVo 密码校验参数
	 */
	void updateUserPassword(SettingsUserPasswordUpdateVo settingsUserPasswordUpdateVo);

	/**
	 * 查询个人绑定设备列表
	 *
	 * @return 设备列表
	 */
	List<SettingsBindDeviceListDto> queryBindDeviceList();

	/**
	 * 查询设备详情
	 *
	 * @param deviceId 设备id
	 * @return 设备绑定时间线
	 */
	SettingsDeviceDetailDto queryDeviceDetail(String deviceId);

	/**
	 * 更新设备详情
	 *
	 * @param settingsDeviceDetailUpdateVo 设备详情更新入参
	 */
	void updateDeviceDetail(SettingsDeviceDetailUpdateVo settingsDeviceDetailUpdateVo);

	/**
	 * 移除设备绑定账号
	 *
	 * @param settingRemoveDeviceBindAccountVo 移除设备绑定账号
	 */
	void removeDeviceBindAccount(SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo);

	/**
	 * 生成二维码需要的密文
	 *
	 * @param deviceId 设备id
	 * @return 密文
	 */
	String qrCodeEncryption(String deviceId);

	/**
	 * 转移设备主账号，生成二维码需要密文
	 *
	 * @param dto 二维码入参
	 * @return 密文
	 */
	String qrCodeEncryptionTransferMain(SettingTransferMainVo dto);

	/**
	 * 绑定从账号
	 *
	 * @param code 密文
	 */
	void bindSlaveAccount(String code);

	/**
	 * 发送注销邮件
	 */
	void delAccountEmail();

	/**
	 * 添加注销记录
	 *
	 * @param code 验证码
	 */
	void delAccount(String code);

	/**
	 * 发送售后邮件
	 *
	 * @param settingHelpEmailVo 邮件内容
	 */
	void sendHelpEmail(SettingHelpEmailVo settingHelpEmailVo);

	/**
	 * 恢复出厂设置
	 *
	 * @param deviceId 设备id
	 */
	void resetDevice(String deviceId);

	/**
	 * 注销短信
	 */
	void delAccountPhone();

	/**
	 * 绑定邮箱 验证码
	 */
    void sendBindEmail(String email);

	/**
	 * 绑定手机 验证码
	 */
	void sendBindPhone(String phone);

	/**
	 * 账号绑定邮箱
	 */
	void userBindEmail(String email, String code);

	/**
	 * 账号绑定手机
	 */
	void userBindPhone(String phone, String code);

	/**
	 * 账号设备转移
	 *
	 * @param code 二维码数据
	 */
    void deviceTransfer(String code);
}
