package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.GuideAdapter;
import com.weihengtech.ecos.enums.global.SmsEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.LoginLimitException;
import com.weihengtech.ecos.enums.global.AccountClearRecordStateEnum;
import com.weihengtech.ecos.enums.global.ClientTypeEnum;
import com.weihengtech.ecos.enums.global.MailModelEnum;
import com.weihengtech.ecos.consts.RegexConstants;
import com.weihengtech.ecos.model.dos.*;
import com.weihengtech.ecos.model.dtos.global.GuideCountryDto;
import com.weihengtech.ecos.model.dtos.global.GuideDatacenterListDto;
import com.weihengtech.ecos.model.dtos.global.GuideTokenDto;
import com.weihengtech.ecos.model.dtos.global.TimezoneDto;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideEmailPasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideForgetPasswordVo;
import com.weihengtech.ecos.model.vos.guide.GuidePhonePasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidateEmailCodeVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidatePhoneCodeVo;
import com.weihengtech.ecos.service.global.CacheService;
import com.weihengtech.ecos.service.global.ClientAccountClearRecordService;
import com.weihengtech.ecos.service.global.EcosCountryService;
import com.weihengtech.ecos.service.thirdpart.MessageService;
import com.weihengtech.ecos.service.global.TimezoneService;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.utils.*;
import com.weihengtech.ecos.model.bos.user.ClientUserDetails;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GuideAdapterImpl implements GuideAdapter {

	private static final String CACHE_CLIENT_REGISTER = "{}:CACHE:CLIENT_REGISTER:{}";
	private static final String CODE_CLIENT_REGISTER = "{}:CODE:CLIENT_REGISTER:{}";
	private static final String CACHE_CLIENT_FORGET_PASSWORD = "{}:CACHE:FORGET_PASSWORD:{}";
	private static final String CODE_CLIENT_FORGET_PASSWORD = "{}:CODE:FORGET_PASSWORD:{}";

	@Resource
	private CacheService cacheService;
	@Resource
	private ClientUserService clientUserService;
	@Resource
	private TimezoneService timezoneService;
	@Resource
	private EcosCountryService ecosCountryService;
	@Resource
	private MessageService messageService;
	@Resource
	private PasswordEncoder passwordEncoder;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private ClientJwtUtil clientJwtUtil;
	@Resource
	private ClientUserRoleService clientUserRoleService;
	@Resource
	private ClientAccountClearRecordService clientAccountClearRecordService;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;

	@Override
	public GuideTokenDto registerEmail(GuideClientUserRegisterEmailVo guideClientUserRegisterVo) {
		log.info("registerEmail:{}", JSONUtil.toJsonStr(guideClientUserRegisterVo));
		String email = guideClientUserRegisterVo.getEmail();
		checkEmailAccount(email);
		checkEmailRegisterCode(email, guideClientUserRegisterVo.getCode());
		return register(
				guideClientUserRegisterVo.getPassword(),
				guideClientUserRegisterVo.getEmail(),
				guideClientUserRegisterVo.getEmail(),
				null,
				guideClientUserRegisterVo.getTimeZoneId(),
				guideClientUserRegisterVo.getDatacenterId()
		);
	}

	@Override
	public GuideTokenDto registerPhone(GuideClientUserRegisterPhoneVo guideClientUserRegisterPhoneVo) {
		log.info("registerPhone:{}", JSONUtil.toJsonStr(guideClientUserRegisterPhoneVo));
		String phone = guideClientUserRegisterPhoneVo.getPhone();
		checkPhoneAccount(phone);
		checkPhoneRegisterCode(phone, guideClientUserRegisterPhoneVo.getCode());
		return register(
				guideClientUserRegisterPhoneVo.getPassword(),
				guideClientUserRegisterPhoneVo.getPhone(),
				null,
				guideClientUserRegisterPhoneVo.getPhone(),
				guideClientUserRegisterPhoneVo.getTimeZoneId(),
				guideClientUserRegisterPhoneVo.getDatacenterId()
		);
	}


	@Override
	public List<GuideDatacenterListDto> listDatacenter() {
		return tuyaDatacenterService.list()
				.stream()
				.map(tuyaDatacenterDo -> {
					GuideDatacenterListDto guideDatacenterListDto = new GuideDatacenterListDto();
					CglibUtil.copy(tuyaDatacenterDo, guideDatacenterListDto);
					guideDatacenterListDto.setCountry(ReflectUtil.invoke(tuyaDatacenterDo, LocaleUtil.mapLocaleToDatabaseGetMethod()));
					return guideDatacenterListDto;
				})
				.collect(Collectors.toList());
	}

	@DSTransactional
	GuideTokenDto register(String password, String username, String email, String phone, Integer timezoneId, Integer datacenterId) {
		checkPasswordStrength(password);
		long userId = snowFlakeUtil.generateId();
		long timestamp = System.currentTimeMillis();
		checkPasswordStrength(password);

		ClientUserDo clientUserDo = new ClientUserDo();
		clientUserDo.setClientType(ClientTypeEnum.IOS);

		clientUserDo.setId(userId);
		clientUserDo.setUsername(username);
		clientUserDo.setNickname(RandomUtil.generateNickname());
		clientUserDo.setPassword(passwordEncoder.encode(password));
		clientUserDo.setEmail(email);
		clientUserDo.setPhone(phone);

		TimezoneDo timezoneDo = timezoneService.getById(timezoneId);
		OperationUtil.of(timezoneDo).ifPresentOrElseThrow(timeZoneDo -> {
			clientUserDo.setTimeZoneId(timeZoneDo.getId());
			clientUserDo.setTimeZone(timeZoneDo.getTimezone());
		}, () -> {
			log.warn("时区id不存在");
			return new EcosException(EcosExceptionEnum.INVALID_PARAM);
		});

		OperationUtil.of(datacenterId)
				.ifPresentOrElseThrow(di -> {
					if (tuyaDatacenterService.exist(di)) {
						clientUserDo.setDatacenterId(di);
					} else {
						throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
					}
				}, () -> new EcosException(EcosExceptionEnum.INVALID_PARAM));

		clientUserDo.setEnabled(true);
		clientUserDo.setAccountNonExpired(true);
		clientUserDo.setAccountNonLocked(true);
		clientUserDo.setCredentialsNonExpired(true);
		clientUserDo.setCreateTime(timestamp);
		clientUserDo.setUpdateTime(timestamp);
		ActionFlagUtil.assertTrue(clientUserService.save(clientUserDo));

		ClientUserRoleDo clientUserRoleDo = new ClientUserRoleDo();
		clientUserRoleDo.setId(snowFlakeUtil.generateId());
		clientUserRoleDo.setClientUserId(userId);
		clientUserRoleDo.setRoleName("ROLE_USER");
		ActionFlagUtil.assertTrue(clientUserRoleService.save(clientUserRoleDo));

		ClientUserDetails clientUserDetails = new ClientUserDetails();
		CglibUtil.copy(clientUserDo, clientUserDetails);
		clientUserDetails.setAuthorities(ListUtil.toLinkedList("ROLE_USER"));
		return new GuideTokenDto().withAccessToken(clientJwtUtil.generateAccessToken(clientUserDetails))
				.withRefreshToken(clientJwtUtil.generateRefreshToken(clientUserDetails));
	}

	@Override
	public Optional<List<TimezoneDto>> queryTimeZoneLocale() {
		return Optional.ofNullable(timezoneService.allTimezoneWithLocale());
	}

	@Override
	public void checkEmailRegisterCode(String email, String code) {
		checkRegisterCode("EMAIL", email, code);
	}

	@Override
	public void checkPhoneRegisterCode(String phone, String code) {
		checkRegisterCode("PHONE", phone, code);
	}

	void checkRegisterCode(String source, String contact, String code) {
		String codeKey = StrUtil.format(CODE_CLIENT_REGISTER, source, contact);
		Optional<String> opCode = cacheService.getStr(codeKey);
		if (opCode.isPresent()) {
			if (!opCode.get().equals(code)) {
				log.warn("注册验证码错误");
				throw new EcosException(EcosExceptionEnum.INVALID_CODE);
			}
		} else {
			log.warn("注册验证码不存在");
			throw new EcosException(EcosExceptionEnum.INVALID_CODE);
		}
	}

	@Override
	public Optional<List<GuideCountryDto>> queryCountryList() {
		List<EcosCountryDO> list = ecosCountryService.list(Wrappers.<EcosCountryDO>lambdaQuery().orderByAsc(EcosCountryDO::getId));
		List<GuideCountryDto> guideCountryList = new ArrayList<>();
		String language = LocaleUtil.getLanguage();
		Optional.of(list).ifPresent(doList -> {
			for (EcosCountryDO ecosCountryDO : doList) {
				Integer id = ecosCountryDO.getId();
				String strId = String.valueOf(id);
				GuideCountryDto guideCountryDto = new GuideCountryDto();
				guideCountryDto.setId(id);
				guideCountryDto.setCnName(InitUtil.COUNTRY_SETTING.get("zh_CN", strId));
				guideCountryDto.setEnName(InitUtil.COUNTRY_SETTING.get("en_US", strId));
				if (language.equals("zh_CN")) {
					guideCountryDto.setName(InitUtil.COUNTRY_SETTING.get("zh_CN", strId));
				} else {
					guideCountryDto.setName(InitUtil.COUNTRY_SETTING.get("en_US", strId));
				}
				guideCountryList.add(guideCountryDto);
			}
		});
		return Optional.of(guideCountryList);
	}

	@Override
	public void checkEmailAccount(String email) {
		Optional<ClientUserDo> optionalClientUserDo = clientUserService.queryOptionalUserByEmail(email);
		if (optionalClientUserDo.isPresent()) {
			log.warn("账号已存在 {}", email);
			throw new EcosException(EcosExceptionEnum.ACCOUNT_ALREADY_EXIST);
		}
	}

	@Override
	public void checkPhoneAccount(String phone) {
		Optional<ClientUserDo> optionalClientUserDo = clientUserService.queryOptionalUserByPhone(phone);
		if (optionalClientUserDo.isPresent()) {
			log.warn("账号已存在 {}", phone);
			throw new EcosException(EcosExceptionEnum.ACCOUNT_ALREADY_EXIST);
		}
	}

	@Override
	public void checkPasswordStrength(String password) {
		if (!ReUtil.isMatch(RegexConstants.PASSWORD_STRENGTH, password)) {
			log.warn("密码强度校验不通过");
			throw new EcosException(EcosExceptionEnum.INVALID_PASSWORD);
		}
	}

	@Override
	public void sendForgetPasswordEmail(GuideSendEmailVo guideSendEmailVo) {
		String toEmail = guideSendEmailVo.getEmail();
		Optional.ofNullable(clientUserService.getOne(Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getEmail, toEmail)))
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.MAIL_NOT_EXISTED));
		String cacheKey = StrUtil.format(CACHE_CLIENT_FORGET_PASSWORD, "EMAIL", toEmail);
		String codeKey = StrUtil.format(CODE_CLIENT_FORGET_PASSWORD, "EMAIL", toEmail);
		messageService.sendEmailWithCache(toEmail, codeKey, cacheKey, MailModelEnum.CLIENT_FORGET_PASSWORD);
	}

	@Override
	public void sendRegisterEmail(GuideSendEmailVo guideSendEmailVo) {
		String toEmail = guideSendEmailVo.getEmail();
		clientUserService.queryOptionalUserByEmail(toEmail).ifPresent(user -> {throw new EcosException(EcosExceptionEnum.ACCOUNT_ALREADY_EXIST);});
		String cacheKey = StrUtil.format(CACHE_CLIENT_REGISTER, "EMAIL", toEmail);
		String codeKey = StrUtil.format(CODE_CLIENT_REGISTER, "EMAIL", toEmail);
		messageService.sendEmailWithCache(toEmail, codeKey, cacheKey, MailModelEnum.CLIENT_REGISTER);
	}

	@Override
	public void sendRegisterPhone(GuideSendPhoneVo guideSendPhoneVo) {
		String phone = guideSendPhoneVo.getPhone();
		clientUserService.queryOptionalUserByPhone(phone).ifPresent(user -> {throw new EcosException(EcosExceptionEnum.ACCOUNT_ALREADY_EXIST);});
		String cacheKey = StrUtil.format(CACHE_CLIENT_REGISTER, "PHONE", phone);
		String codeKey = StrUtil.format(CODE_CLIENT_REGISTER, "PHONE", phone);
		messageService.sendPhoneWithCache(SmsEnum.REGISTER, phone, codeKey, cacheKey);
	}

	@Override
	public void sendForgetPasswordPhone(GuideSendPhoneVo guideSendPhoneVo) {
		String phone = guideSendPhoneVo.getPhone();
		clientUserService.queryOptionalUserByPhone(phone).orElseThrow(() -> new EcosException(EcosExceptionEnum.PHONE_NOT_BOUND));
		String cacheKey = StrUtil.format(CACHE_CLIENT_FORGET_PASSWORD, "PHONE", phone);
		String codeKey = StrUtil.format(CODE_CLIENT_FORGET_PASSWORD, "PHONE", phone);
		messageService.sendPhoneWithCache(SmsEnum.FORGET_PASSWORD, phone, codeKey, cacheKey);
	}

	@Override
	@DSTransactional
	public void resetPassword(GuideForgetPasswordVo guideForgetPasswordVo) {
		String token = guideForgetPasswordVo.getToken();
		Claims claims = clientJwtUtil.parseAccessToken(token).orElseThrow(() -> {
			log.warn("token校验失败");
			return new EcosException(EcosExceptionEnum.INVALID_TOKEN);
		});
		checkPasswordStrength(guideForgetPasswordVo.getPassword());
		clientUserService.resetPassword(
				claims.getSubject(),
				passwordEncoder.encode(guideForgetPasswordVo.getPassword())
		);
	}

	@Override
	@DSTransactional
	public GuideTokenDto emailPasswordLogin(GuideEmailPasswordLoginVo guideEmailPasswordLoginVo) {
		val loginLimit = "LOGIN:LIMIT:" + guideEmailPasswordLoginVo.getEmail();
		return loginWithLimit(
				clientUserService.queryOptionalUserByEmail(guideEmailPasswordLoginVo.getEmail()),
				guideEmailPasswordLoginVo.getPassword(),
				loginLimit,
				guideEmailPasswordLoginVo.getClientType(),
				guideEmailPasswordLoginVo.getClientVersion()
		);
	}

	@Override
	public GuideTokenDto phonePasswordLogin(GuidePhonePasswordLoginVo guidePhonePasswordLoginVo) {
		val loginLimit = "LOGIN:LIMIT:" + guidePhonePasswordLoginVo.getPhone();
		return loginWithLimit(
				clientUserService.queryOptionalUserByPhone(guidePhonePasswordLoginVo.getPhone()),
				guidePhonePasswordLoginVo.getPassword(),
				loginLimit,
				guidePhonePasswordLoginVo.getClientType(),
				guidePhonePasswordLoginVo.getClientVersion()
		);
	}

	GuideTokenDto loginWithLimit(Optional<ClientUserDo> op, String password, String limitKey, String clientType, String clientVersion) {
		long now = System.currentTimeMillis();
		return op.filter(clientUserDo -> {
					val matches = passwordEncoder.matches(password, clientUserDo.getPassword());
					val optionalS = cacheService.getStr(limitKey);

					if (optionalS.isPresent()) {
						val value = optionalS.get();
						val valueArray = value.split("_");
						int times = Integer.parseInt(valueArray[0]);
						long timestamps = Long.parseLong(valueArray[1]);

						if (times < 5) {
							if (!matches) {
								cacheService.setStrWithSecond(limitKey, ++times + "_" + timestamps, 24 * 60 * 60);
								throw new LoginLimitException("", 5 - times, 30 - TimeUtil.convertMinuteBetweenTimestamp(timestamps, now));
							}
						} else if (times < 10 && ((now - timestamps) < 1000 * 60 * 30)) {
							throw new LoginLimitException("", 0, 30);
						} else if (times < 10 && ((now - timestamps) >= 1000 * 60 * 30)) {
							if (!matches) {
								cacheService.setStrWithSecond(limitKey, ++times + "_" + timestamps, 24 * 60 * 60);
								throw new LoginLimitException("", 10 - times, 60 * 24 - TimeUtil.convertMinuteBetweenTimestamp(timestamps, now));
							}
						} else {
							throw new LoginLimitException("", 0, 60 * 24 - TimeUtil.convertMinuteBetweenTimestamp(timestamps, now));
						}
					} else {
						if (!matches) {
							cacheService.setStrWithSecond(limitKey, "1_" + now, 30 * 60);
							throw new LoginLimitException("", 4, 30);
						}
					}

					return true;
				})
				.map(clientUserDo -> {
					List<ClientTypeEnum> typeEnums = Arrays.stream(ClientTypeEnum.values())
							.filter(clientTypeEnum -> clientTypeEnum.name().equals(clientType))
							.collect(Collectors.toList());
					if (typeEnums.size() != 1) {
						log.warn("客户端类型不存在");
						throw new EcosException(EcosExceptionEnum.INVALID_CLIENT_TYPE);
					}
					ActionFlagUtil.assertTrue(
							clientUserService.updateById(
									clientUserDo.withClientType(typeEnums.get(0))
											.withClientVersion(clientVersion)
							)
					);

					ClientUserDetails clientUserDetails = new ClientUserDetails();
					CglibUtil.copy(clientUserDo, clientUserDetails);
					clientUserDetails.setAuthorities(clientUserRoleService.queryRoleListByClientUserId(clientUserDo.getId()));

					// 清除注销状态
					List<ClientAccountClearRecordDo> list = clientAccountClearRecordService.list(
							Wrappers.<ClientAccountClearRecordDo>lambdaQuery()
									.eq(ClientAccountClearRecordDo::getUserId, clientUserDo.getId())
									.eq(ClientAccountClearRecordDo::getState, AccountClearRecordStateEnum.ING.getCode())
					);
					list.forEach(clientAccountClearRecordDo -> {
						clientAccountClearRecordDo.setState(AccountClearRecordStateEnum.CANCELLED.getCode());
						clientAccountClearRecordService.updateById(clientAccountClearRecordDo);
					});

					cacheService.delKey(limitKey);

					if (Objects.equals(clientUserDo.getNickname(), "")) {
						clientUserDo.setNickname(RandomUtil.generateNickname());
						clientUserDo.setUpdateTime(System.currentTimeMillis());
						ActionFlagUtil.assertTrue(clientUserService.updateById(clientUserDo));
					}

					return new GuideTokenDto().withAccessToken(clientJwtUtil.generateAccessToken(clientUserDetails))
							.withRefreshToken(clientJwtUtil.generateRefreshToken(clientUserDetails));
				}).orElseThrow(() -> {
					log.warn("账号名密码错误");
					return new EcosException(EcosExceptionEnum.INVALID_USERNAME_OR_PASSWORD);
				});
	}

	@Override
	public String validateForgetPasswordEmailCode(GuideValidateEmailCodeVo guideValidateEmailCodeVo) {
		verifyCode("EMAIL", guideValidateEmailCodeVo.getEmail(), guideValidateEmailCodeVo.getCode());
		return userLogin(clientUserService.queryOptionalUserByEmail(guideValidateEmailCodeVo.getEmail()));
	}

	@Override
	public String validateForgetPasswordPhoneCode(GuideValidatePhoneCodeVo guideValidatePhoneCodeVo) {
		verifyCode("PHONE", guideValidatePhoneCodeVo.getPhone(), guideValidatePhoneCodeVo.getCode());
		return userLogin(clientUserService.queryOptionalUserByPhone(guideValidatePhoneCodeVo.getPhone()));
	}

	String userLogin(Optional<ClientUserDo> op) {
		return op.map(clientUserDo -> {
			ClientUserDetails clientUserDetails = new ClientUserDetails();
			CglibUtil.copy(clientUserDo, clientUserDetails);
			clientUserDetails.setAuthorities(clientUserRoleService.queryRoleListByClientUserId(clientUserDo.getId()));
			return clientJwtUtil.generateAccessToken(clientUserDetails);
		}).orElseThrow(() -> {
			log.warn("登录失败");
			return new EcosException(EcosExceptionEnum.INVALID_DATA);
		});
	}

	private void verifyCode(String source, String contact, String code) {
		Optional<String> optionalCode = cacheService.getStr(StrUtil.format(CODE_CLIENT_FORGET_PASSWORD, source, contact));
		if (optionalCode.isPresent()) {
			if (!optionalCode.get().equals(code)) {
				log.warn("验证码校验错误");
				throw new EcosException(EcosExceptionEnum.INVALID_CODE);
			}
		} else {
			log.warn("验证码不存在");
			throw new EcosException(EcosExceptionEnum.INVALID_CODE);
		}
	}
}
