package com.weihengtech.ecos.adapter.impl;

import com.weihengtech.ecos.adapter.EnestAdapter;
import com.weihengtech.ecos.model.vos.bind.EnestBindDeviceVO;
import com.weihengtech.ecos.service.bind.EnestBindService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Enest相关操作实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6 16:33
 */
@Service
public class EnestAdapterImpl implements EnestAdapter {

    @Resource
    private EnestBindService enestBindService;

    @Override
    public Long deviceBind(EnestBindDeviceVO bindParam) {
        return enestBindService.netDeviceBind(bindParam);
    }
}
