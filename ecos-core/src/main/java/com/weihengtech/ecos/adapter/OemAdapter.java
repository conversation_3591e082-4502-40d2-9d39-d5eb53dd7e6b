package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;

/**
 * <AUTHOR>
 */
public interface OemAdapter {

	/**
	 * 计算Oem energy数
	 *
	 * @param oemEnergyVo 时间与id
	 * @return 计算数据
	 */
	OemEnergyDto computeOemEnergy(OemEnergyVo oemEnergyVo);

	/**
	 * Oem 实时功率曲线
	 *
	 * @param homeNowDeviceRealtimeVo 设备
	 */
	OemRealTimePowerDto oemRealTimePower(HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo);

	/**
	 * Oem 历史能量
	 *
	 * @param oemHistoryEnergyVo 设备
	 */
	OemHistoryEnergyDto oemHistoryEnergy(OemHistoryEnergyVo oemHistoryEnergyVo);

}
