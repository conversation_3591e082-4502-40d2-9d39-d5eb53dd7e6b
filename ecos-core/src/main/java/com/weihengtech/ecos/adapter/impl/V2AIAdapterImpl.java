package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.V2AIAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.config.SSEListener;
import com.weihengtech.ecos.api.pojo.vos.KnowledegeConversationVo;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.KnowledgeCommonConst;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.enums.ChatRoleEnum;
import com.weihengtech.ecos.enums.thirdpart.KnowledgePresetspromptEnum;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientSessionDo;
import com.weihengtech.ecos.model.dos.ClientSessionMessageDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.app.V2AISessionDto;
import com.weihengtech.ecos.model.dtos.app.V2AISessionMessagesDto;
import com.weihengtech.ecos.model.vos.app.V2AIChatCompletionsVo;
import com.weihengtech.ecos.model.vos.app.V2AIChatMessagesPageVo;
import com.weihengtech.ecos.service.app.ClientSessionMessageService;
import com.weihengtech.ecos.service.app.ClientSessionService;
import com.weihengtech.ecos.utils.ExecuteSSEUtil;
import com.weihengtech.ecos.utils.LocaleUtil;
import com.weihengtech.ecos.utils.SSEListenerUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: AI问答相关方法实现类
 * @author: jiahao.jin
 * @create: 2024-05-09 15:01
 **/
@Slf4j
@Service
@DSTransactional
public class V2AIAdapterImpl implements V2AIAdapter {

    private static final String FIRST_INPUT_SESSION = "0";

    @Value("${custom.url.knowledge.api}")
    private String apiHost;

    @Value("${custom.knowledge.model}")
    private String model;

    @Value("${custom.knowledge.chat.size:20}")
    private int chatSize;


    @Resource
    private ClientSessionService clientSessionService;
    @Resource
    private ClientSessionMessageService clientSessionMessageService;
    @Resource
    private SSEListenerUtil sseListenerUtil;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private V2HomeAdapter v2HomeAdapter;


    @Override
    public void chatCompletions(V2AIChatCompletionsVo chatContent, ClientUserDo clientUserDo, HttpServletResponse rp,
                                Locale locale) {
        try {
            // 校验文字是否超过限制
            if (chatContent.getContent().length() > KnowledgeCommonConst.DEFAULT_CONTENT_TOKEN_SIZE) {
                throw new EcosException(EcosExceptionEnum.CHAT_CONTENT_OVER_LIMIT);
            }
            String userId = String.valueOf(clientUserDo.getId());
            // 校验家庭Id是否存在
            ClientHomeDo clientHomeDo = v2HomeAdapter.checkHomeDefault(userId, chatContent.getHomeId());
            // 校验会话Id是否存在
            String chatId = "";
            ClientSessionDo sessionItem = new ClientSessionDo();
            if (!FIRST_INPUT_SESSION.equals(chatContent.getSessionId())) {
                // 查找是否有该会话
                sessionItem = clientSessionService.getById(chatContent.getSessionId());
                if (sessionItem == null) {
                    log.warn("会话不存在：{}", chatContent.getSessionId());
                    throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
                }
                chatId = sessionItem.getChatId();
            }
            // 处理系统预设问题
            if (chatContent.getRole() == ChatRoleEnum.SYSTEM.getCode()) {
                log.info("系统预设问题: {}", JSONUtil.toJsonStr(chatContent));
                handleSystemPresetQuestion(chatContent.getContent(), clientHomeDo, rp, chatId, clientUserDo, sessionItem,
                        chatContent, locale);
                return;
            }
            // 处理AI回答
            if (chatContent.getRole() == ChatRoleEnum.AI.getCode()) {
                log.error("AI回答问题");
                return;
            }
            // 检查用户24小时内请求次数
            checkChatNumLimit(userId);
            // 生成预设请求体
            KnowledegeConversationVo conversationItem = createKnowledgeConversationVo(chatContent, clientUserDo, chatId);
            // 执行SSE
            executeSSE(conversationItem, rp, sessionItem);
        } catch (EcosException e) {
            sendSseEvent(rp, KnowledgeCommonConst.ERROR_MESSAGE_TYPE, LocaleUtil.getMessage(e.getMessage())
                    .orElse(KnowledgeCommonConst.ERROR_MESSAGE_TYPE), e.getCode());
        }
    }

    @Override
    public List<V2AISessionDto> chatConversations(ClientUserDo clientUserDo) {
        // 查询用户当前历史会话
        List<ClientSessionDo> clientSessionDos = clientSessionService.queryClientSessionByUserId(clientUserDo.getId());
        List<V2AISessionDto> v2AISessionDtoList = new ArrayList<>();
        for (ClientSessionDo clientSessionDo : clientSessionDos) {
            V2AISessionDto v2AISessionDto = new V2AISessionDto();
            v2AISessionDto.setId(String.valueOf(clientSessionDo.getId()));
            v2AISessionDto.setTitle(clientSessionDo.getTitle());
            v2AISessionDto.setCreateTime(clientSessionDo.getCreateTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
            v2AISessionDto.setUpdateTime(clientSessionDo.getCreateTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
            v2AISessionDtoList.add(v2AISessionDto);
        }
        return v2AISessionDtoList;
    }

    @Override
    public V2AISessionMessagesDto chatConversationMessages(ClientUserDo clientUserDo, V2AIChatMessagesPageVo historyParam, Locale locale) {
        V2AISessionMessagesDto result = new V2AISessionMessagesDto();
        List<V2AISessionMessagesDto.V2AISessionMessages> messagesList = new ArrayList<>();
        Long sessionId = historyParam.getSessionId() == null ? 0 : Long.parseLong(historyParam.getSessionId());
        Long messageId = historyParam.getMessageId() == null ? 0 : Long.parseLong(historyParam.getMessageId());
        int size = historyParam.getSize() == null ? 10 : historyParam.getSize();
        result.setPromptList(KnowledgePresetspromptEnum.getAllPromptsByLocale(locale));
        try {
            if (sessionId == 0) {
                List<ClientSessionDo> clientSessionDos = clientSessionService.queryClientSessionByUserId(clientUserDo.getId());
                if (clientSessionDos.isEmpty()) {
                    result.setHasNext(false);
                    result.setSessionId(String.valueOf(sessionId));
                    result.setMessages(messagesList);
                    return result;
                }
                sessionId = clientSessionDos.get(0).getId();
            }

            // 查询sessionId某个messageID的上面10条数据
            List<ClientSessionMessageDo> clientSessionMessageDoList = clientSessionMessageService.list(Wrappers.<ClientSessionMessageDo>lambdaQuery()
                    .eq(ClientSessionMessageDo::getSessionId, sessionId)
                    .lt(messageId > 0, ClientSessionMessageDo::getId, messageId)  // 如果 messageId > 0，则添加 lt 条件
                    .orderByDesc(ClientSessionMessageDo::getTimestamp)
                    .last("LIMIT " + (size + 1)));  // 取 size + 1 条记录以判断是否有下一页

            // 判断是否有下一页
            boolean hasNext = clientSessionMessageDoList.size() > size;
            result.setHasNext(hasNext);

            // 转换数据
            messagesList = clientSessionMessageDoList.stream()
                    .sorted(Comparator.comparing(ClientSessionMessageDo::getParentId, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .map(clientSessionMessageDo -> {
                        V2AISessionMessagesDto.V2AISessionMessages v2AISessionMessages = new V2AISessionMessagesDto.V2AISessionMessages();
                        v2AISessionMessages.setId(String.valueOf(clientSessionMessageDo.getId()));
                        v2AISessionMessages.setContent(clientSessionMessageDo.getContent());
                        v2AISessionMessages.setRole(ChatRoleEnum.getRoleByCode(clientSessionMessageDo.getType()));
                        v2AISessionMessages.setTimestamp(clientSessionMessageDo.getTimestamp().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
                        return v2AISessionMessages;
                    })
                    .collect(Collectors.toList());

            // 如果有多于 size 条记录，移除多余的记录
            if (hasNext) {
                messagesList.remove(0);
            }
            // 反转顺序
            Collections.reverse(messagesList);
            result.setSessionId(String.valueOf(sessionId));
            result.setMessages(messagesList);
        } catch (Exception e) {
            // Log the error and handle appropriately
            System.err.println("Error retrieving session messages: " + e.getMessage());
            result.setHasNext(false);
            result.setSessionId(String.valueOf(sessionId));
            result.setMessages(messagesList);
        }
        return result;
    }


    public void sendSseEvent(HttpServletResponse response, String eventName, String data, int code) {
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("UTF-8");

        try {
            Map<String, Object> map = new HashMap<>();
            map.put("content", data);
            map.put("chatId", "");
            map.put("code", code);
            response.getWriter().write("event: " + eventName + "\n");
            response.getWriter().write("data: " + JSONUtil.toJsonStr(map) + "\n\n");
            response.getWriter().flush();
            // 添加content为null的结束标记
            response.getWriter().write("event:" + KnowledgeCommonConst.END_MESSAGE_TYPE + "\n");
            response.getWriter().write("data:null\n\n");
            response.getWriter().flush();
        } catch (IOException e) {
            log.error("发送SSE事件时发生错误", e);
        }
    }

    private void handleSystemPresetQuestion(String content, ClientHomeDo clientHomeDo, HttpServletResponse rp, String chatId, ClientUserDo clientUserDo, ClientSessionDo clientSessionDo, V2AIChatCompletionsVo v2AIChatCompletionsVo, Locale language) {
        KnowledgePresetspromptEnum promptEnum = KnowledgePresetspromptEnum.OTHER_PROMPT;
        for (KnowledgePresetspromptEnum enumValue : KnowledgePresetspromptEnum.values()) {
            if (enumValue.getPrompt(language).equals(content)) {
                promptEnum = enumValue;
                break;
            }
        }

        switch (promptEnum) {
            case ENERGY_CONSUMPTION_MONTH:
                // 上月用电量
                InsightDeviceDataDto insightDeviceDataDto1 = v2HomeAdapter.queryHomeDeviceInsightData(String.valueOf(clientHomeDo.getId()), 2, System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000, clientUserDo);

                if (insightDeviceDataDto1 == null || insightDeviceDataDto1.getDeviceStatisticsDto() == null || insightDeviceDataDto1.getDeviceStatisticsDto().getConsumptionEnergy() == null) {
                    // 当前家庭还未添加储能设备，暂无相关统计数据
                    throw new EcosException(EcosExceptionEnum.NO_ENERGY_DEVICE);
                }
                BigDecimal consumptionEnergy = insightDeviceDataDto1.getDeviceStatisticsDto().getConsumptionEnergy();
                String answer1 = KnowledgePresetspromptEnum.ENERGY_CONSUMPTION_MONTH.getTemplate(language).replace("homeName", clientHomeDo.getHomeName()).replace("Number", consumptionEnergy.toString());
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer1, 200);
                log.info(answer1);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer1, clientSessionDo);
                break;
            case SELF_CONSUMPTION_RATE:
                // 本月自发自用率
                InsightDeviceDataDto insightDeviceDataDto2 = v2HomeAdapter.queryHomeDeviceInsightData(String.valueOf(clientHomeDo.getId()), 2, System.currentTimeMillis(), clientUserDo);

                if (insightDeviceDataDto2 == null || insightDeviceDataDto2.getSelfPowered() == null) {
                    // 当前家庭还未添加储能设备，暂无相关统计数据
                    throw new EcosException(EcosExceptionEnum.NO_ENERGY_DEVICE);
                }
                BigDecimal selfPowered = insightDeviceDataDto2.getSelfPowered();
                String answer2 = KnowledgePresetspromptEnum.SELF_CONSUMPTION_RATE.getTemplate(language).replace("homeName", clientHomeDo.getHomeName()).replace("Number", selfPowered.toString());
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer2,200);
                log.info(answer2);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer2, clientSessionDo);
                break;
            case GRID_CONSUMPTION_TODAY:
                // Handle GRID_CONSUMPTION_TODAY case
                InsightDeviceDataDto insightDeviceDataDto3 = v2HomeAdapter.queryHomeDeviceInsightData(String.valueOf(clientHomeDo.getId()), 2, System.currentTimeMillis(), clientUserDo);

                if (insightDeviceDataDto3 == null || insightDeviceDataDto3.getDeviceStatisticsDto() == null || insightDeviceDataDto3.getDeviceStatisticsDto().getFromGrid() == null) {
                    // 当前家庭还未添加储能设备，暂无相关统计数据
                    throw new EcosException(EcosExceptionEnum.NO_ENERGY_DEVICE);
                }
                BigDecimal fromGrid = insightDeviceDataDto3.getDeviceStatisticsDto().getFromGrid();
                String answer3 = KnowledgePresetspromptEnum.GRID_CONSUMPTION_TODAY.getTemplate(language).replace("homeName", clientHomeDo.getHomeName()).replace("Number", fromGrid.toString());
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer3, 200);
                log.info(answer3);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer3, clientSessionDo);
                break;
            case PV_GENERATION_YEAR:
                // Handle PV_GENERATION_YEAR case
                InsightDeviceDataDto insightDeviceDataDto4 = v2HomeAdapter.queryHomeDeviceInsightData(String.valueOf(clientHomeDo.getId()), 4, System.currentTimeMillis(), clientUserDo);

                if (insightDeviceDataDto4 == null || insightDeviceDataDto4.getDeviceStatisticsDto() == null || insightDeviceDataDto4.getDeviceStatisticsDto().getFromSolar() == null) {
                    // 当前家庭还未添加储能设备，暂无相关统计数据
                    throw new EcosException(EcosExceptionEnum.NO_ENERGY_DEVICE);
                }
                BigDecimal fromSolar = insightDeviceDataDto4.getDeviceStatisticsDto().getFromSolar();
                String answer4 = KnowledgePresetspromptEnum.PV_GENERATION_YEAR.getTemplate(language).replace("homeName", clientHomeDo.getHomeName()).replace("Number", fromSolar.toString());
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer4, 200);
                log.info(answer4);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer4, clientSessionDo);
                break;
            case ELECTRICITY_CONSUMPTION_WEEK:
                // Handle ELECTRICITY_CONSUMPTION_WEEK case
                HomeDeviceEnergyStatisticsDto homeDeviceEnergyStatisticsDto = v2HomeAdapter.queryHomeSolarAndGridEnergyData(String.valueOf(clientHomeDo.getId()), clientUserDo);

                if (homeDeviceEnergyStatisticsDto == null || homeDeviceEnergyStatisticsDto.getWeekEnergy() == null) {
                    // 当前家庭还未添加储能设备，暂无相关统计数据
                    throw new EcosException(EcosExceptionEnum.NO_ENERGY_DEVICE);
                }
                Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> weekEnergy = homeDeviceEnergyStatisticsDto.getWeekEnergy();
                String answer5 = KnowledgePresetspromptEnum.ELECTRICITY_CONSUMPTION_WEEK.getTemplate(language)
                        .replace("homeName", clientHomeDo.getHomeName())
                        .replace("Week1", weekEnergy.get(1).getHomeEnergy().toString())
                        .replace("Week2", weekEnergy.get(2).getHomeEnergy().toString())
                        .replace("Week3", weekEnergy.get(3).getHomeEnergy().toString())
                        .replace("Week4", weekEnergy.get(4).getHomeEnergy().toString())
                        .replace("Week5", weekEnergy.get(5).getHomeEnergy().toString())
                        .replace("Week6", weekEnergy.get(6).getHomeEnergy().toString())
                        .replace("Week7", weekEnergy.get(7).getHomeEnergy().toString());
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer5, 200);
                log.info(answer5);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer5, clientSessionDo);
                break;
            case DELETE_HOME:
                // Handle DELETE_HOME case
                String answer6 = KnowledgePresetspromptEnum.DELETE_HOME.getTemplate(language);
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer6, 200);
                log.info(answer6);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer6, clientSessionDo);
                break;
            case SET_HOME_WEATHER:
                // Handle SET_HOME_WEATHER case
                String answer7 = KnowledgePresetspromptEnum.SET_HOME_WEATHER.getTemplate(language);
                sendSseEvent(rp, KnowledgeCommonConst.SYSTEM_MESSAGE_TYPE, answer7, 200);
                log.info(answer7);
                handleSessionAndMessages(chatId, clientUserDo.getId(), content, answer7, clientSessionDo);
                break;
            default:
                v2AIChatCompletionsVo.setRole(ChatRoleEnum.USER.getCode());
                chatCompletions(v2AIChatCompletionsVo, clientUserDo, rp, language);
                break;
        }
    }

    private void checkChatNumLimit(String userId) {
        String redisKey = RedisRefConstants.buildAiChatKey(userId);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String chatNum = stringRedisTemplate.opsForValue().get(redisKey);
            int chatNumInt = 0;
            if (chatNum != null) {
                chatNumInt = Integer.parseInt(chatNum);
            }
            if (chatNumInt >= chatSize) {
                throw new EcosException(EcosExceptionEnum.CHAT_NUM_OVER_LIMIT);
            }
            stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(chatNumInt + 1), 24, TimeUnit.HOURS);
        } else {
            stringRedisTemplate.opsForValue().set(redisKey, "1", 24, TimeUnit.HOURS);
        }
    }

    private KnowledegeConversationVo createKnowledgeConversationVo(V2AIChatCompletionsVo chatContent, ClientUserDo clientUserDo, String chatId) {
        KnowledegeConversationVo knowledegeConversationVo = new KnowledegeConversationVo();
        // 假设model是一个已定义的变量
        knowledegeConversationVo.setModel(model);
        List<KnowledegeConversationVo.Message> messages = new ArrayList<>();
        messages.add(KnowledegeConversationVo.Message.builder()
                .role(ChatRoleEnum.USER.getRole())
                .content(KnowledgeCommonConst.PROMPT)
                .build());
        messages.add(KnowledegeConversationVo.Message.builder()
                .role(ChatRoleEnum.AI.getRole())
                .content(KnowledgeCommonConst.AI_ANSWER)
                .build());
        messages.add(KnowledegeConversationVo.Message.builder()
                .role(ChatRoleEnum.USER.getRole())
                .content(chatContent.getContent())
                .build());
        knowledegeConversationVo.setMessages(messages);
        knowledegeConversationVo.setUserId(clientUserDo.getId());
        knowledegeConversationVo.setHistoryChatSize(KnowledgeCommonConst.DEFAULT_HISTORY_CHAT_SIZE);
        knowledegeConversationVo.setChatId(chatId);
        return knowledegeConversationVo;
    }

    private void executeSSE(KnowledegeConversationVo conversationItem, HttpServletResponse rp, ClientSessionDo clientSessionDo) {
        try {
            String token = SecurityUtil.generateAuthHeader(KnowledgeCommonConst.AUTH_HEADER);
            SSEListener sseListener = new SSEListener(sseListenerUtil, clientSessionDo, conversationItem, rp);
            ExecuteSSEUtil.executeSSE(apiHost + KnowledgeCommonConst.LLM_STREAM_CHAT_URL, token, sseListener, JSON.toJSONString(conversationItem));
        } catch (Exception e) {
            log.error("请求SSE错误处理", e);
        }
    }

    private void handleSessionAndMessages(String chatId, Long userId, String message, String concatenatedContent, ClientSessionDo clientSessionDo) {
        String title = "New Session";
        // 请求AI生成标题
        if (StrUtil.isBlank(chatId)) {
            String token = SecurityUtil.generateAuthHeader(KnowledgeCommonConst.AUTH_HEADER);
            KnowledegeConversationVo conversationItem = new KnowledegeConversationVo();
            conversationItem.setUserId(userId);
            conversationItem.setHistoryChatSize(0);
            conversationItem.setModel(KnowledgeCommonConst.ALI_MODEL);
            conversationItem.setChatId("");
            List<KnowledegeConversationVo.Message> messages = new ArrayList<>();
            messages.add(KnowledegeConversationVo.Message.builder()
                    .role(ChatRoleEnum.USER.getRole())
                    .content(message)
                    .build());
            messages.add(KnowledegeConversationVo.Message.builder()
                    .role(ChatRoleEnum.AI.getRole())
                    .content(concatenatedContent)
                    .build());
            messages.add(KnowledegeConversationVo.Message.builder()
                    .role(ChatRoleEnum.USER.getRole())
                    .content(KnowledgeCommonConst.TOPIC_GENERATE_PROMPT)
                    .build());
            conversationItem.setMessages(messages);

            String aiTitle = ExecuteSSEUtil.knowledgePostReq(apiHost + KnowledgeCommonConst.LLM_CHAT_URL, token, JSON.toJSONString(conversationItem));
            title = aiTitle == null ? title : aiTitle;
            int maxLength = 20;
            if (title.length() > maxLength) {
                title = title.substring(0, maxLength);
            }
        }
        // 处理会话的保存逻辑
        ClientSessionDo session = clientSessionService.handleSession(title, userId, clientSessionDo);
        // 保存问题和回答进数据库
        handleMessages(session, concatenatedContent, userId, message);
    }

    private void handleMessages(ClientSessionDo session, String concatenatedContent, Long userId, String message) {
        // 查询该会话的最后一条消息,得到消息的父ID
        ClientSessionMessageDo lastMessage = clientSessionMessageService.queryLastMessageBySessionId(session.getId());
        Long fatherMsgId = lastMessage != null ? lastMessage.getId() : 0L;
        LocalDateTime now = LocalDateTime.now();
        // 问题
        ClientSessionMessageDo userMessage = clientSessionMessageService.createMessage(session, userId, ChatRoleEnum.USER, message, fatherMsgId, now);
        // 系统预设回答
        ClientSessionMessageDo aiMessage = clientSessionMessageService.createMessage(session, userId, ChatRoleEnum.SYSTEM, concatenatedContent, userMessage.getId(), now);
        clientSessionMessageService.saveBatch(Arrays.asList(userMessage, aiMessage));
        session.setUpdateTime(now);
        clientSessionService.updateById(session);
    }
}
