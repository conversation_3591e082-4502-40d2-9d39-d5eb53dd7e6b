package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dtos.global.GuideCountryDto;
import com.weihengtech.ecos.model.dtos.global.GuideDatacenterListDto;
import com.weihengtech.ecos.model.dtos.global.GuideTokenDto;
import com.weihengtech.ecos.model.dtos.global.TimezoneDto;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideEmailPasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideForgetPasswordVo;
import com.weihengtech.ecos.model.vos.guide.GuidePhonePasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidateEmailCodeVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidatePhoneCodeVo;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface GuideAdapter {

	/**
	 * 注册
	 *
	 * @param guideClientUserRegisterVo 注册相关信息
	 * @return 登录信息
	 */
	GuideTokenDto registerEmail(GuideClientUserRegisterEmailVo guideClientUserRegisterVo);


	/**
	 * 国家列表
	 *
	 * @return Optional国家列表
	 */
	Optional<List<GuideCountryDto>> queryCountryList();

	/**
	 * 检查邮箱账号是否存在
	 *
	 * @param email 检查的邮箱
	 */
	void checkEmailAccount(String email);

	/**
	 * 检查手机账号是否存在
	 *
	 * @param phone 检查的手机
	 */
	void checkPhoneAccount(String phone);


	/**
	 * 检查密码强度
	 *
	 * @param password 密码
	 */
	void checkPasswordStrength(String password);

	/**
	 * 发送邮件
	 *
	 * @param guideSendEmailVo 邮箱
	 */
	void sendForgetPasswordEmail(GuideSendEmailVo guideSendEmailVo);

	/**
	 * 发送注册邮件
	 *
	 * @param guideSendEmailVo 邮箱
	 */
	void sendRegisterEmail(GuideSendEmailVo guideSendEmailVo);

	/**
	 * 重置密码
	 *
	 * @param guideForgetPasswordVo 密码 验证码 账号
	 */
	void resetPassword(GuideForgetPasswordVo guideForgetPasswordVo);

	/**
	 * 登录
	 *
	 * @param guideEmailPasswordLoginVo 账号密码客户端
	 * @return token
	 */
	GuideTokenDto emailPasswordLogin(GuideEmailPasswordLoginVo guideEmailPasswordLoginVo);

	/**
	 * 校验邮箱验证码
	 *
	 * @param guideValidateEmailCodeVo 邮箱验证码入参
	 * @return token
	 */
	String validateForgetPasswordEmailCode(GuideValidateEmailCodeVo guideValidateEmailCodeVo);

	/**
	 * 校验忘记密码手机验证码
	 */
	String validateForgetPasswordPhoneCode(GuideValidatePhoneCodeVo guideValidatePhoneCodeVo);

	/**
	 * 获取国际化时区
	 *
	 * @return Optional国际化时区
	 */
	Optional<List<TimezoneDto>> queryTimeZoneLocale();


	/**
	 * 检查注册验证码
	 *
	 * @param email 邮箱
	 * @param code  验证码
	 */
	void checkEmailRegisterCode(String email, String code);

	/**
	 * 检查注册验证码
	 *
	 * @param phone 手机号
	 * @param code  验证码
	 */
	void checkPhoneRegisterCode(String phone, String code);

	/**
	 * 获取数据中心国家列表
	 *
	 * @return 数据中心国家列表
	 */
	List<GuideDatacenterListDto> listDatacenter();

	/**
	 * 手机号注册
	 */
	GuideTokenDto registerPhone(GuideClientUserRegisterPhoneVo guideClientUserRegisterPhoneVo);

	/**
	 * 发送手机注册验证码
	 */
	void sendRegisterPhone(GuideSendPhoneVo guideSendPhoneVo);

	/**
	 * 发送手机忘记密码验证码
	 */
	void sendForgetPasswordPhone(GuideSendPhoneVo guideSendPhoneVo);

	/**
	 * 手机账号密码登录
	 */
	GuideTokenDto phonePasswordLogin(GuidePhonePasswordLoginVo guidePhonePasswordLoginVo);
}
