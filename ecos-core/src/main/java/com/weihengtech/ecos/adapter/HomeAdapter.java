package com.weihengtech.ecos.adapter;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceSketchDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryBatteryDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryGridDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryHomeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistorySolarDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceStatisticsDto;
import com.weihengtech.ecos.model.dtos.thirdpart.NowWeatherDto;
import com.weihengtech.ecos.model.dtos.thirdpart.QueryCityDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.thirdpart.DailyWeatherDto;
import com.weihengtech.ecos.model.dtos.thirdpart.HourWeatherDto;
import com.weihengtech.ecos.model.bos.app.HistoryHomeBo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.vos.app.home.HomeClientUserBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.HomeDeviceCityUpdateVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeHistoryVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceStatisticsVo;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface HomeAdapter {

	/**
	 * 用户绑定设备
	 *
	 * @param homeClientUserBindDeviceVo 设备信息
	 * @param clientUserDo               用户信息
	 */
	void bindClientUserDevice(HomeClientUserBindDeviceVo homeClientUserBindDeviceVo, ClientUserDo clientUserDo);

	/**
	 * 查询用户的设备列表
	 *
	 * @return 设备列表
	 */
	List<HomeDeviceListDto> queryUserDeviceList(String userId);

	/**
	 * 查询用户绑定设备数量
	 *
	 * @return 绑定设备数量
	 */
	Integer queryUserBindDeviceCount();

	/**
	 * 设置设备排序
	 *
	 * @param deviceIdList 设备排序id列表
	 */
	void orderDeviceList(List<String> deviceIdList);

	/**
	 * 分页查询event-fault
	 *
	 * @param homeEventsFaultVo 事件相关参数
	 * @return 分页后的数据
	 */
	PageInfoDTO<HomeEventsFaultDto> pageEventFault(HomeEventsFaultVo homeEventsFaultVo);

	/**
	 * 查询家庭能耗数据
	 */
	HistoryHomeBo queryHistoryHomeBo(HomeHistoryVo homeHistoryVo);

	/**
	 * 查询历史家庭耗能
	 *
	 * @param homeHistoryVo 查询时间段
	 * @return com.weihengtech.pojo.dtos.HomeHistoryHomeDto
	 */
	HomeHistoryHomeDto queryHistoryHome(HomeHistoryVo homeHistoryVo);

	/**
	 * 查询历史光伏
	 *
	 * @param homeHistoryVo 查询时间段
	 * @return com.weihengtech.pojo.dtos.HomeHistorySolarDto
	 */
	HomeHistorySolarDto queryHistorySolar(HomeHistoryVo homeHistoryVo);

	/**
	 * 查询历史电池
	 *
	 * @param homeHistoryVo 查询时间段
	 * @return com.weihengtech.pojo.dtos.HomeHistoryBatteryDto
	 */
	HomeHistoryBatteryDto queryHistoryBattery(HomeHistoryVo homeHistoryVo);

	/**
	 * 查询历史电网
	 *
	 * @param homeHistoryVo 查询时间段
	 * @return com.weihengtech.pojo.dtos.HomeHistoryGridDto
	 */
	HomeHistoryGridDto queryHistoryGrid(HomeHistoryVo homeHistoryVo);

	/**
	 * 查询首页设备统计信息
	 *
	 * @param homeNowDeviceStatisticsVo 设备统计信息入参
	 * @return 统计结果
	 */
	HomeNowDeviceStatisticsDto queryNowDeviceStatistics(HomeNowDeviceStatisticsVo homeNowDeviceStatisticsVo);

	/**
	 * 首页实时能量曲线
	 *
	 * @param deviceId 设备id
	 * @return 能量曲线数据
	 */
	HomeNowDeviceRealtimeDto queryNowDeviceRealtime(String deviceId);

	/**
	 * 首页实时能量曲线
	 *
	 * @param result 查询结果
	 * @return 能量曲线数据
	 */
	HomeNowDeviceRealtimeDto packageNowDeviceRealtimeResult(Map<String, LinkedHashMap<Long, Object>> result);

	/**
	 * 首页设备实时数据
	 *
	 * @param deviceId 设备id
	 * @return 实时数据
	 */
	HomeNowDeviceRunDataDto queryNowDeviceRunData(String deviceId);

	/**
	 * 提高设备的采样频率
	 *
	 * @param deviceId 设备id
	 */
	void nowDataIncreaseRefresh(String deviceId);

	/**
	 * 分页查询备电信息
	 *
	 * @param homeEventsBackupPageVo 备电参数
	 * @return 分页结果
	 */
	PageInfoDTO<HomeEventsBackupPageDto> pageBackup(HomeEventsBackupPageVo homeEventsBackupPageVo);

	/**
	 * 备电统计信息
	 *
	 * @param homeEventsBackupStatisticsVo 统计参数
	 * @return 统计结果
	 */
	HomeEventsBackupStatisticsDto backupStatistics(HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo);

	/**
	 * 家庭设备概况
	 *
	 * @return 发电统计信息
	 */
	HomeDeviceSketchDto getDeviceSketch();

	/**
	 * 用户解绑设备
	 *
	 * @param deviceId 设备id
	 */
	void unbindClientUserDevice(String deviceId);

	/**
	 * 设备历史放电量统计
	 *
	 * @param deviceId 设备id
	 * @return 统计放电量
	 */
	BigDecimal queryHistoryBatteryStatistics(String deviceId);

	/**
	 * 校验是否为设备的所有者
	 *
	 * @param deviceId 设备id
	 * @return 用户信息与设备信息
	 */
	Pair<ClientUserDo, HybridSinglePhaseDO> validateDeviceOwner(ClientUserDo clientUserDo, String deviceId);

	/**
	 * 计算电池循环次数
	 *
	 * @param startCycleTimes                     开始时间
	 * @param endCycleTimes                       结束时间
	 * @return 次数
	 */
	Integer computeBatteryCycleTimes(
			Dict startCycleTimes, Dict endCycleTimes
	);

	/**
	 * 检测wifi棒是否绑定设备成功
	 *
	 * @param wifiSn wifi棒序列号
	 * @return 0 失败 1 成功 2 进行中 3 未进行绑定  4 已被其他账户绑定
	 */
	Integer checkDeviceBindStatus(String wifiSn);

	/**
	 * 获取实时天气
	 *
	 * @param key (必选)用户认证key，请参考如何获取你的KEY。支持数字签名方式进行认证。例如 key=123456789ABC
	 * @param location (必选)需要查询地区的LocationID或以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位），LocationID可通过城市搜索服务获取。例如 location=101010100 或 location=116.41,39.92
	 * @param lang 多语言设置，更多语言可选值参考语言代码。当数据不匹配你设置的语言时，将返回英文或其本地语言结果。
	 * @param unit 数据单位设置，可选值包括unit=m（公制单位，默认）和unit=i（英制单位）。更多选项和说明参考度量衡单位。
	 * @return 实时天气数据
	 */
	NowWeatherDto getNowWeather(String key, String location, String lang, String unit);

	/**
	 * 获取3天天气
	 *
	 * @param key (必选)用户认证key，请参考如何获取你的KEY。支持数字签名方式进行认证。例如 key=123456789ABC
	 * @param location (必选)需要查询地区的LocationID或以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位），LocationID可通过城市搜索服务获取。例如 location=101010100 或 location=116.41,39.92
	 * @param lang 多语言设置，更多语言可选值参考语言代码。当数据不匹配你设置的语言时，将返回英文或其本地语言结果。
	 * @param unit 数据单位设置，可选值包括unit=m（公制单位，默认）和unit=i（英制单位）。更多选项和说明参考度量衡单位。
	 * @return 未来3天天气
	 */
	List<DailyWeatherDto> get3dWeather(String key, String location, String lang, String unit);

	/**
	 * 获取7天天气
	 *
	 * @param key (必选)用户认证key，请参考如何获取你的KEY。支持数字签名方式进行认证。例如 key=123456789ABC
	 * @param location (必选)需要查询地区的LocationID或以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位），LocationID可通过城市搜索服务获取。例如 location=101010100 或 location=116.41,39.92
	 * @param lang 多语言设置，更多语言可选值参考语言代码。当数据不匹配你设置的语言时，将返回英文或其本地语言结果。
	 * @param unit 数据单位设置，可选值包括unit=m（公制单位，默认）和unit=i（英制单位）。更多选项和说明参考度量衡单位。
	 * @return 未来7天天气
	 */
	List<DailyWeatherDto> get7dWeather(String key,String location,String lang, String unit);

	/**
	 * 获取未来24小时天气
	 *
	 * @param key (必选)用户认证key，请参考如何获取你的KEY。支持数字签名方式进行认证。例如 key=123456789ABC
	 * @param location (必选)需要查询地区的LocationID或以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位），LocationID可通过城市搜索服务获取。例如 location=101010100 或 location=116.41,39.92
	 * @param lang 多语言设置，更多语言可选值参考语言代码。当数据不匹配你设置的语言时，将返回英文或其本地语言结果。
	 * @param unit 数据单位设置，可选值包括unit=m（公制单位，默认）和unit=i（英制单位）。更多选项和说明参考度量衡单位。
	 * @return 实时天气数据
	 */
	List<HourWeatherDto> get24hWeather(String key, String location, String lang, String unit);


	/**
	 * 城市搜索
	 *
	 * @param key (必选)用户认证key，请参考如何获取你的KEY。支持数字签名方式进行认证。例如 key=123456789ABC
	 * @param location (必选)需要查询地区的名称，支持文字、以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位）、LocationID或Adcode（仅限中国城市）。例如 location=北京 或 location=116.41,39.92
	 * @param lang 多语言设置，更多语言可选值参考语言代码。当数据不匹配你设置的语言时，将返回英文或其本地语言结果。
	 * @return 相关城市信息列表
	 */
	List<QueryCityDto> queryCity(String key, String location, String lang);

	/**
	 * 更新设备经纬度
	 *
	 * @param homeDeviceCityUpdateVo 更新信息入参
	 */
	void updateDeviceCity(HomeDeviceCityUpdateVo homeDeviceCityUpdateVo);

	/**
	 * 通过设备ID获取家庭ID
	 *
	 * @param deviceId 设备ID
	 */
	String getTuyaDeviceHomeId(String deviceId);
}
