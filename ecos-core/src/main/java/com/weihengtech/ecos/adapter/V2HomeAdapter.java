package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.EcosAccountDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceBindStatusDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceModeDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoAllDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.vos.bind.WhBindDeviceVO;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeCreateHomeVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeUpdateHomeVo;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.settings.V2SettingTransferVo;
import com.weihengtech.ecos.model.vos.thirdpart.InstallBoundVO;

import java.util.List;

/**
 * @program: ecos-server
 * @description: V2家庭相关方法
 * @author: jiahao.jin
 * @create: 2024-01-21 11:40
 **/
public interface V2HomeAdapter {

    /**
     * 创建家庭
     *
     * @param v2HomeCreateHomeVo 家庭信息
     * @param homeType 家庭类型
     * @return 家庭详情
     */
    ClientHomeDo createHome(V2HomeCreateHomeVo v2HomeCreateHomeVo, Integer homeType);

    /**
     * 删除家庭
     *
     * @param homeId 家庭ID
     */
    void deleteHome(String homeId);

    /**
     * 查询家庭列表
     *
     * @return 家庭列表
     */
    List<V2HomeInfoDto> queryHomeList(String userId);

    /**
     * 查询家庭信息
     *
     * @param homeId 家庭id
     * @return 家庭详细详情
     */
    V2HomeInfoAllDto queryHomeInfo(String homeId);

    /**
     * 更新家庭信息
     *
     * @param v2HomeUpdateHomeVo 家庭信息
     * @return 更新后的家庭详情
     */
    V2HomeInfoDto updateHomeInfo(V2HomeUpdateHomeVo v2HomeUpdateHomeVo);

    /**
     * 加入家庭
     *
     * @param code 二维码密文
     */
    void joinHome(String code);

    /**
     * 生成邀请家庭成员二维码所需要的密文
     *
     * @param homeId 家庭id
     * @return 密文
     */
    String inviteMemberQrCode(String homeId);

    /**
     * 删除家庭成员
     *
     * @param homeId 家庭id
     * @param userIds 要删除的成员列表
     */
    void deleteHomeMember(String homeId, List<String> userIds);

    /**
     * 家庭成员退出所在家庭
     *
     * @param homeId 家庭id
     */
    void memberExitHome(String homeId);

    /**
     * 家庭添加设备
     *
     * @param v2ClientHomeBindDeviceVo 用户绑定设备入参
     */
    void bindClientUserAndHomeDevice(V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo, ClientUserDo clientUserDo, String ip);

    /**
     * 检测wifi棒是否绑定设备成功
     *
     * @param wifiSn wifi棒序列号
     * @return 0 失败 1 成功 2 进行中 3 未进行绑定  4 已被其他账户绑定
     */
    V2HomeDeviceBindStatusDto checkDeviceBindStatus(String wifiSn);

    /**
     * 批量删除家庭的设备
     *
     * @param homeId 家庭ID
     * @param deviceIds 设备ID列表
     */
    void unbindClientUserAndHomeDevice(String homeId, List<Long> deviceIds);

    /**
     * 家庭绑定设备
     *
     * @param clientUserDo 操作人信息
     * @param homeId 家庭id
     * @param deviceId 设备ID
     * @param deviceName 设备名
     */
    void homeBindDevice(ClientUserDo clientUserDo, String homeId, String deviceId, String deviceName);

    /**
     * 家庭绑定设备(自研棒子)
     *
     * @param bindParam bindParam
     */
    Long iotDeviceBind(WhBindDeviceVO bindParam);

    /**
     * 查询家庭中的设备
     *
     * @param homeId 家庭ID
     * @return 家庭设备信息列表
     */
    List<V2HomeDeviceListDto> queryHomeDeviceList(ClientUserDo clientUserDo, String homeId);

    /**
     * 校验用户是否有设备操作权限
     *
     * @param userId 用户信息
     * @param homeId 家庭ID
     * @return 家庭信息
     */
    ClientHomeDo checkHomeAndUser(String userId, String homeId);

    /**
     * 校验用户是否有家庭操作权限
     *
     * @param userId 用户信息
     * @param homeId 家庭ID
     * @return 家庭信息
     */
    ClientHomeDo checkHomeDefault(String userId, String homeId);

    /**
     * 校验用户是否有设备操作权限
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 家庭设备信息
     */
    ClientHomeDeviceDo checkUserAndDevice(String userId, String deviceId);

    // 判断当前操作的用户下是否有家庭，家庭中是否有该设备
    Boolean checkUserHasDevice(ClientUserDo clientUserDo, String deviceId);

    /**
     * 校验设备是否存在、设备是否在该家庭中
     *
     * @param userId 用户Id
     * @param deviceId 设备ID
     * @return 家庭信息
     */
    HybridSinglePhaseDO checkHomeAndDevice(String userId, String deviceId);


    /**
     * 查询家庭中设备的运行功率
     *
     * @param homeId 家庭ID
     * @return 家庭设备运行功率
     */
    V2HomeNowDeviceRunDataDto queryHomeDeviceRunData(String homeId, ClientUserDo clientUserDo);

    /**
     * 更改家庭中设备的别名
     *
     * @param deviceId 设备ID
     * @param remark 要更改的备注
     * @return 家庭设备运行功率
     */
    void updateDeviceRemark(String deviceId, String remark);

    /**
     * 查询家庭中设备的mode
     *
     * @param homeId 家庭ID
     * @return 家庭设备Mode
     */
    List<V2HomeDeviceModeDto> queryHomeDeviceMode(String homeId);

    /**
     * 家庭获取最近一周光伏、电网、碳减排、节约标准煤数据
     *
     * @param homeId 家庭ID
     * @return 家庭能耗数据
     */
    HomeDeviceEnergyStatisticsDto queryHomeSolarAndGridEnergyData(String homeId, ClientUserDo clientUserDo);

    /**
     * 获取Insight页面家庭所有储能能耗数据
     *
     * @param homeId 家庭ID
     * @return Insight页面能耗数据
     */
    InsightDeviceDataDto queryHomeDeviceInsightData(String homeId, Integer periodType, Long timestamp, ClientUserDo clientUserDo);

    /**
     * 转移设备的家庭
     *
     * @param homeId 要转移的家庭ID
     * @param deviceId 设备ID
     * @return 家庭能耗数据
     */
    void transferDeviceFamily(String deviceId, String homeId);

    /**
     * 生成二维码需要的密文
     *
     * @param deviceId 设备id
     * @return 密文
     */
    String shareDeviceQRCodeClient(String deviceId, Integer saveDeviceTime);

    /**
     * 绑定从账号
     *
     * @param code 密文
     */
    void bindSlaveAccount(String code);

    /**
     * 移除设备绑定账号
     *
     * @param settingRemoveDeviceBindAccountVo 移除设备绑定账号
     */
    void removeDeviceBindAccount(SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo);

    /**
     * 账号设备转移
     *
     * @param req 转移参数
     */
    void deviceTransfer(V2SettingTransferVo req);

    /**
     * 提高设备的采集率
     *
     * @param homeId 家庭ID
     */
    void nowDataIncreaseRefresh(String homeId, ClientUserDo clientUserDo);

    /**
     * 更新安装商绑定设备保留时长
     *
     * @param req 参数
     */
    void installUpdSaveTime(InstallBoundVO req);

    /**
     * 获取wifi棒是否在线
     *
     * @param wifiSn wifiSn
     * @return 是否在线
     */
    Boolean iotIsOnline(String wifiSn);

    /**
     * 查询设备绑定的账号信息
     *
     * @param deviceSn 设备sn
     * @return EcosAccountDTO
     */
    EcosAccountDTO deviceBindAccountInfo(String deviceSn);

    /**
     * iot平台重置设备
     *
     * @param wifiSn wifiSn
     */
    void iotResetDevice(String wifiSn);

    /**
     * 获取指定设备详情
     *
     * @param deviceSn 设备sn
     * @return 设备
     */
    HybridSinglePhaseDO deviceInfo(String deviceSn);

    /**
     * 获取家庭电价信息
     *
     * @param homeId 家庭ID
     * @return
     */
    HomeElePriceDTO familyPriceInfo(String homeId);

    /**
     * 计算家庭节省成本
     *
     * @param homeId
     * @return
     */
    HomeCostSavingDTO costInfo(String homeId);

    /**
     * 计算家庭节省成本
     *
     * @param homeId
     * @return
     */
    void clearHistoryCostInfo(String homeId);
}
