package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.platform.async.executor.Async;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.adapter.ApiAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundDTO;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.DeviceBindStatus;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.consts.RequestConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.app.EcosAccountDTO;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationRunDataDto;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceBatterySocDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceBindStatusDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceModeDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoAllDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeMemberInfoDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightConsumptionDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceStatisticsDto;
import com.weihengtech.ecos.model.dtos.settings.SettingTransferDTO;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketBaseInfoDto;
import com.weihengtech.ecos.model.vos.socket.SocketInfoVo;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.enums.thirdpart.DeviceSaveTimeEnum;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientHomeUserDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDTO;
import com.weihengtech.ecos.model.vos.app.InsightDeviceDataVo;
import com.weihengtech.ecos.model.vos.thirdpart.InstallBoundVO;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeCreateHomeVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeUpdateHomeVo;
import com.weihengtech.ecos.model.vos.settings.V2SettingTransferVo;
import com.weihengtech.ecos.model.vos.bind.WhBindDeviceVO;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.bind.WhIotBindService;
import com.weihengtech.ecos.service.ele.ClientHomeSaveCostService;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.thirdpart.TuyaService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.OperationUtil;
import com.weihengtech.ecos.utils.RotUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.utils.TimezoneUtil;
import com.weihengtech.ecos.worker.device.chargeStation.GetChargeStationStatusWorker;
import com.weihengtech.ecos.worker.device.chargeStation.RunDataWorker;
import com.weihengtech.ecos.worker.device.energyStorage.GetLastWeekSolarAndGridEnergyDataWorker;
import com.weihengtech.ecos.worker.device.energyStorage.GetSocWorker;
import com.weihengtech.ecos.worker.device.energyStorage.QueryDeviceInsightDataWorker;
import com.weihengtech.ecos.worker.device.energyStorage.QueryNowDeviceRunDataWorker;
import com.weihengtech.ecos.worker.device.tuyaSocket.QueryTuyaSocketInfoWorker;
import com.weihengtech.ecos.worker.hub.HubServiceGetAgentsByIdsWork;
import com.weihengtech.ecos.worker.hub.HubServiceGetBatchByIdWork;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: V2家庭相关方法实现类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:41
 **/
@Slf4j
@Service
public class V2HomeAdapterImpl implements V2HomeAdapter {

    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private ClientUserService clientUserService;
    @Resource
    private ClientHomeService clientHomeService;
    @Resource
    private ClientHomeUserService clientHomeUserService;
    @Resource
    private ClientHomeDeviceService clientHomeDeviceService;
    @Resource
    private HomeAdapterImpl homeAdapter;
    @Resource
    private SinglePhaseService singlePhaseService;
    @Resource
    private HubService hubService;
    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;
    @Resource
    private SinglePlugSocketService singlePlugSocketService;
    @Resource
    private ChargeStationService chargeStationService;
    @Resource
    private ClientCustomizeService clientCustomizeService;
    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;
    @Resource
    private TuyaService tuyaService;
    @Resource
    private WhIotBindService whIotBindService;
    @Resource
    private ApiAdapter apiAdapter;
    @Resource
    private ClientHomeSaveCostService clientHomeSaveCostService;

    @DSTransactional
    @Override
    public ClientHomeDo createHome(V2HomeCreateHomeVo v2HomeCreateHomeVo, Integer homeType) {

        Double lon = v2HomeCreateHomeVo.getLongitude();
        Double lat = v2HomeCreateHomeVo.getLatitude();
        if (lon != null && lat != null) {
            if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                log.warn("lon或lat值不正确");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        // 查询用户相关的家庭
        List<ClientHomeUserDo> clientHomeUserDoList = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId, clientUserDo.getId()));
        // 如果用户已经拥有100个家庭，则家庭创建失败
        if (clientHomeUserDoList.size() > 100) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }

        long homeId = snowFlakeUtil.generateId();
        long homeUserId = snowFlakeUtil.generateId();
        ClientHomeDo clientHomeDo = new ClientHomeDo();
        clientHomeDo.setId(homeId);
        clientHomeDo.setHomeName(v2HomeCreateHomeVo.getHomeName());
        clientHomeDo.setHomeType(homeType);
        clientHomeDo.setLongitude(v2HomeCreateHomeVo.getLongitude());
        clientHomeDo.setLatitude(v2HomeCreateHomeVo.getLatitude());
        clientHomeDo.setCreateTime(System.currentTimeMillis());
        clientHomeDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientHomeService.save(clientHomeDo));

        ClientHomeUserDo clientHomeUserDo = new ClientHomeUserDo();
        clientHomeUserDo.setId(homeUserId);
        clientHomeUserDo.setUserId(clientUserDo.getId());
        clientHomeUserDo.setHomeId(homeId);
        clientHomeUserDo.setInviteUserId(null);
        clientHomeUserDo.setRelationType(CommonConstants.HOME_OWNER);
        clientHomeUserDo.setCreateTime(System.currentTimeMillis());
        clientHomeUserDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientHomeUserService.save(clientHomeUserDo));

        return clientHomeDo;

    }

    @DSTransactional
    @Override
    public void deleteHome(String homeId) {

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        // 校验家庭与操作人的关系
        checkHomeAndUser(String.valueOf(clientUserDo.getId()), homeId);

        // 校验家庭是否是最后一个普通家庭家庭
        List<ClientHomeUserDo> clientHomeUserDos = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId, clientUserDo.getId())
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));
        if (clientHomeUserDos.size() <= 2) {
            throw new EcosException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
        }

        // 查询家庭下绑定的设备
        Map<Long, ClientHomeDeviceDo> clientHomeDeviceDoMap = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId))
                .stream().collect(Collectors.toMap(ClientHomeDeviceDo::getDeviceId, Function.identity(), (existing, replacement) -> existing));


        // 清除设备的配置、清除设备的定时任务
        if (!clientHomeDeviceDoMap.isEmpty()) {

            List<Long> deviceIds = new ArrayList<>(clientHomeDeviceDoMap.keySet());
            Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(false, deviceIds)
                    .stream()
                    .collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));
            List<Long> chargeStationIds = hybridSinglePhaseDoMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getResourceSeriesId() == 104)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            if (chargeStationIds.size() > 0) {
                chargeStationService.deleteDevicesConfigAndTask(chargeStationIds);
            }
            List<HybridSinglePhaseDO> sockets = hybridSinglePhaseDoMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getResourceSeriesId() == 105)
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
            if (sockets.size() > 0) {
                singlePlugSocketService.deleteDevicesConfigAndTask(sockets);
            }

            // 解除设备与用户之间的绑定关系
            ActionFlagUtil.assertTrue(middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                    .in(MiddleClientUserDeviceDo::getDeviceId, clientHomeDeviceDoMap.keySet())));

            // 清除家庭-设备关系表数据
            ActionFlagUtil.assertTrue(clientHomeDeviceService.remove(Wrappers.<ClientHomeDeviceDo>lambdaQuery().eq(ClientHomeDeviceDo::getHomeId, homeId)));
        }

        // 清除家庭-用户关系表数据
        ActionFlagUtil.assertTrue(clientHomeUserService.remove(Wrappers.<ClientHomeUserDo>lambdaQuery().eq(ClientHomeUserDo::getHomeId, homeId)));
        // 清除家庭表数据
        ActionFlagUtil.assertTrue(clientHomeService.remove(Wrappers.<ClientHomeDo>lambdaQuery().eq(ClientHomeDo::getId, homeId)));

    }

    @DSTransactional
    @Override
    public List<V2HomeInfoDto> queryHomeList(String userId) {
        // 查询用户相关的家庭
        List<ClientHomeUserDo> clientHomeUserDoList = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId, userId));

        // 初始化家庭
        if (clientHomeUserDoList.isEmpty()) {
            // 创建一个普通家庭，并把账号下所有自己是master的设备添加进去
            // 如果还是某些设备的子账号，就创建一个被分享家庭，将这些子设备添加进去
            ClientUserDo userDo = clientUserService.getById(userId);
            if (userDo == null) {
                throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
            }
            return initHome(userId, userDo.getNickname());
        }

        List<Long> homeIds = clientHomeUserDoList.stream()
                .map(ClientHomeUserDo::getHomeId)
                .collect(Collectors.toList());
        List<ClientHomeDo> clientHomeDoList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
                .in(ClientHomeDo::getId, homeIds)
                .orderByDesc(ClientHomeDo::getUpdateTime));
        List<ClientHomeDeviceDo> clientHomeDeviceDoList = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .in(ClientHomeDeviceDo::getHomeId, homeIds));

        Map<Long, ClientHomeUserDo> homeUserMap = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                        .in(ClientHomeUserDo::getHomeId, homeIds)
                        .eq(ClientHomeUserDo::getUserId, userId))
                .stream().collect(Collectors.toMap(ClientHomeUserDo::getHomeId, Function.identity(), (existing, replacement) -> existing));

        List<V2HomeInfoDto> v2HomeInfoDtoList = clientHomeDoList.stream().map(clientHomeDo -> {
            V2HomeInfoDto v2HomeInfoDto = new V2HomeInfoDto();
            CglibUtil.copy(clientHomeDo, v2HomeInfoDto);
            v2HomeInfoDto.setHomeId(String.valueOf(clientHomeDo.getId()));
            ClientHomeUserDo clientHomeUserDo = homeUserMap.get(clientHomeDo.getId());
            if (clientHomeUserDo != null) {
                v2HomeInfoDto.setRelationType(clientHomeUserDo.getRelationType());
            }
            v2HomeInfoDto.setHomeDeviceNumber((int) clientHomeDeviceDoList.stream().filter(clientHomeDeviceDo -> clientHomeDeviceDo.getHomeId().equals(clientHomeDo.getId())).count());
            return v2HomeInfoDto;
        }).sorted(Comparator.comparing(V2HomeInfoDto::getHomeDeviceNumber, Comparator.reverseOrder())).collect(Collectors.toList()); // 先按updateTime降序排序

        // 分割列表：普通家庭和被分享家庭
        List<V2HomeInfoDto> normalHomes = v2HomeInfoDtoList.stream()
                .filter(h -> h.getHomeType() == CommonConstants.HOME_COMMON) // 假设homeType为0表示被分享家庭
                .collect(Collectors.toList());
        int normalHomeDevices = v2HomeInfoDtoList.stream()
                .filter(h -> h.getHomeType() == CommonConstants.HOME_COMMON)
                .mapToInt(V2HomeInfoDto::getHomeDeviceNumber)
                .sum();
        List<V2HomeInfoDto> sharedHomes = v2HomeInfoDtoList.stream()
                .filter(h -> h.getHomeType() == CommonConstants.HOME_SHARED && h.getHomeDeviceNumber() > 0) // 假设homeType为0表示被分享家庭
                .collect(Collectors.toList());
        int sharedHomeDevices = v2HomeInfoDtoList.stream()
                .filter(h -> h.getHomeType() == CommonConstants.HOME_SHARED)
                .mapToInt(V2HomeInfoDto::getHomeDeviceNumber)
                .sum();

        // 先添加普通家庭，再添加被分享家庭
        List<V2HomeInfoDto> sortedList = new ArrayList<>();

        // 如果普通家庭和被分享家庭都有设备，先显示普通家庭；
        // 如果普通家庭没有设备，被分享家庭有设备，先显示被分享家庭
        if (normalHomeDevices == 0 && sharedHomeDevices > 0) {
            sortedList.addAll(sharedHomes);
            sortedList.addAll(normalHomes);
        } else {
            sortedList.addAll(normalHomes);
            sortedList.addAll(sharedHomes);
        }

        return sortedList;

    }

    @Override
    public V2HomeInfoAllDto queryHomeInfo(String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        V2HomeInfoAllDto v2HomeInfoAllDto = new V2HomeInfoAllDto();

        // 家庭与操作人关系校验
        ClientHomeDo clientHomeDo = checkHomeDefault(String.valueOf(clientUserDo.getId()), homeId);
        CglibUtil.copy(clientHomeDo, v2HomeInfoAllDto);
        List<ClientHomeDeviceDo> clientHomeDeviceDos = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, clientHomeDo.getId()));
        v2HomeInfoAllDto.setHomeDeviceNumber(clientHomeDeviceDos.size());
        v2HomeInfoAllDto.setHomeId(String.valueOf(clientHomeDo.getId()));

        // 查询家庭相关的用户id列表
        Map<Long, ClientHomeUserDo> clientHomeUserDoMap = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                        .eq(ClientHomeUserDo::getHomeId, homeId))
                .stream().collect(Collectors.toMap(ClientHomeUserDo::getUserId, Function.identity(), (existing, replacement) -> existing));
        v2HomeInfoAllDto.setHomeMemberNumber(clientHomeUserDoMap.size());

        // 获取家庭相关用户信息列表
        Set<Long> userIds = clientHomeUserDoMap.keySet();
        Map<Long, ClientUserDo> clientUserDoMap = clientUserService.list(Wrappers.<ClientUserDo>lambdaQuery()
                        .in(ClientUserDo::getId, userIds))
                .stream().collect(Collectors.toMap(ClientUserDo::getId, Function.identity(), (existing, replacement) -> existing));

        // 获取邀请人信息列表
        List<Long> inviteUserIds = clientHomeUserDoMap.values().stream().map(ClientHomeUserDo::getInviteUserId).collect(Collectors.toList());
        Map<Long, ClientUserDo> inviteClientUserDoMap = clientUserService.list(Wrappers.<ClientUserDo>lambdaQuery()
                        .in(ClientUserDo::getId, inviteUserIds))
                .stream().collect(Collectors.toMap(ClientUserDo::getId, Function.identity(), (existing, replacement) -> existing));

        List<V2HomeMemberInfoDto> v2HomeMemberInfoDtos = new ArrayList<>();
        clientHomeUserDoMap.forEach((userId,clientHomeUserDo) -> {
            V2HomeMemberInfoDto v2HomeMemberInfoDto = new V2HomeMemberInfoDto();
            ClientUserDo userDo = clientUserDoMap.get(userId);
            v2HomeMemberInfoDto.setId(String.valueOf(userId));
            v2HomeMemberInfoDto.setNickname(userDo.getNickname());
            if (userDo.getEmail() != null) {
                v2HomeMemberInfoDto.setContactInfo(userDo.getEmail());
            } else {
                v2HomeMemberInfoDto.setContactInfo(userDo.getPhone());
            }
            v2HomeMemberInfoDto.setRelationType(clientHomeUserDo.getRelationType());
            String inviteTime = TimeUtil.longTimestampToSerialStringOffsetGMT8(clientHomeUserDo.getCreateTime(), clientUserDo.getTimeZone());
            v2HomeMemberInfoDto.setInviteTime(inviteTime);

            // 如果是家庭所有者
            if (clientHomeUserDo.getInviteUserId() == null) {
                v2HomeMemberInfoDto.setInviteNickname(null);
                v2HomeMemberInfoDtos.add(v2HomeMemberInfoDto);
                return;
            }

            // 如果是被分享者
            ClientUserDo inviteUserDo = inviteClientUserDoMap.get(clientHomeUserDo.getInviteUserId());
            v2HomeMemberInfoDto.setInviteNickname(inviteUserDo.getNickname());
            v2HomeMemberInfoDtos.add(v2HomeMemberInfoDto);
        });
        v2HomeInfoAllDto.setV2HomeMemberInfoDtoList(v2HomeMemberInfoDtos);

        return v2HomeInfoAllDto;
    }

    @DSTransactional
    @Override
    public V2HomeInfoDto updateHomeInfo(V2HomeUpdateHomeVo v2HomeUpdateHomeVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        String homeId = v2HomeUpdateHomeVo.getId();
        // 校验家庭与操作人的关系
        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn("找不到该家庭");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }

        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId,clientUserDo.getId())
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));
        if (clientHomeUserDo == null) {
            log.warn("不是所有者，无权操作该家庭");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_HOME);
        }

        // 校验经纬度是否正确
        Double lon = v2HomeUpdateHomeVo.getLongitude();
        Double lat = v2HomeUpdateHomeVo.getLatitude();
        if (lon != null && lat != null) {
            if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                log.warn("lon或lat值不正确");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
            clientHomeDo.setLongitude(lon);
            clientHomeDo.setLatitude(lat);
        }

        if (v2HomeUpdateHomeVo.getHomeName() != null) {
            if (clientHomeDo.getHomeType().equals(CommonConstants.HOME_SHARED)) {
                log.warn("被分享设备家庭无法被操作");
                throw new EcosException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
            }
            clientHomeDo.setHomeName(v2HomeUpdateHomeVo.getHomeName());
        }
        if (clientHomeDo.getCurrency() != null && v2HomeUpdateHomeVo.getCurrency() != null &&
                !clientHomeDo.getCurrency().equals(v2HomeUpdateHomeVo.getCurrency())) {
            // 切换币种时，会清空历史成本节省统计数据
            clearHistoryCostInfo(v2HomeUpdateHomeVo.getId());
        }
        clientHomeDo.setCurrency(v2HomeUpdateHomeVo.getCurrency());
        clientHomeDo.setElePriceType(v2HomeUpdateHomeVo.getElePriceType());
        clientHomeDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientHomeService.updateById(clientHomeDo));
        V2HomeInfoDto v2HomeInfoDto = new V2HomeInfoDto();
        CglibUtil.copy(clientHomeDo, v2HomeInfoDto);
        v2HomeInfoDto.setHomeId(String.valueOf(clientHomeDo.getId()));
        return v2HomeInfoDto;
    }

    @DSTransactional
    @Override
    public void joinHome(String code) {
        // 获取被邀请者信息
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 获取homeId
        String homeIdWithRandomStr = RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
                .orElseThrow(() -> {
                    log.warn("二维码解密失败");
                    return new EcosException(EcosExceptionEnum.INVALID_PARAM);
                });
        String homeId;
        String minute;
        try {
            minute = homeIdWithRandomStr.substring(0, 8);
            homeId = homeIdWithRandomStr.substring(8);
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        long currMinute = System.currentTimeMillis() / 1000 / 60;
        if ((currMinute - Long.parseLong(minute)) > 30) {
            log.warn("超过加入家庭时效");
            throw new EcosException(EcosExceptionEnum.QR_CODE_EXPIRED);
        }

        // 校验家庭
        ClientHomeDo clientHomeDo = checkHomeAndUserByJoin(clientUserDo, homeId);

        // 查询邀请人信息
        ClientHomeUserDo inviteUser = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));
        // 加入家庭
        ClientHomeUserDo clientHomeUserDo = new ClientHomeUserDo();
        long homeUserId = snowFlakeUtil.generateId();
        long currentTimeMillis = System.currentTimeMillis();
        clientHomeUserDo.setId(homeUserId);
        clientHomeUserDo.setHomeId(clientHomeDo.getId());
        clientHomeUserDo.setUserId(clientUserDo.getId());
        clientHomeUserDo.setInviteUserId(inviteUser.getUserId());
        clientHomeUserDo.setRelationType(CommonConstants.HOME_MEMBER);
        clientHomeUserDo.setCreateTime(currentTimeMillis);
        clientHomeUserDo.setUpdateTime(currentTimeMillis);
        ActionFlagUtil.assertTrue(clientHomeUserService.save(clientHomeUserDo));
    }

    @Override
    public String inviteMemberQrCode(String homeId) {

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        // 校验家庭与操作人的关系
        checkHomeAndUser(String.valueOf(clientUserDo.getId()), homeId);

        // 生成二维码所需code
        long timestamp = System.currentTimeMillis() / 1000 / 60;
        int offset = new Random().nextInt(9);
        return offset + RotUtil.encode(timestamp + homeId, offset);
    }

    @DSTransactional
    @Override
    public void deleteHomeMember(String homeId, List<String> userIds) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 校验家庭与操作人的关系
        checkHomeAndUser(String.valueOf(clientUserDo.getId()), homeId);

        // 校验用户Id是否都与homeId有关系
        Map<Long, ClientHomeUserDo> clientHomeUserDoMap = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .in(ClientHomeUserDo::getUserId, userIds))
                .stream().collect(Collectors.toMap(ClientHomeUserDo::getUserId, Function.identity(), (existing, replacement) -> existing));
        List<String> notInHomeUserId = new ArrayList<>();
        userIds.forEach(userId -> {
            if (clientHomeUserDoMap.get(Long.valueOf(userId)) == null) {
                notInHomeUserId.add(userId);
            }
        });
        if (notInHomeUserId.size() > 0) {
            log.warn("以下用户不在该家庭中：" + notInHomeUserId);
            throw new EcosException(EcosExceptionEnum.USER_NOT_IN_HOME);
        }

        // 删除成员
        ActionFlagUtil.assertTrue(clientHomeUserService.remove(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .in(ClientHomeUserDo::getUserId, userIds)));
        log.info("{} 家庭删除 {} 成员成功", homeId, userIds);
    }

    @Override
    public void memberExitHome(String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn("找不到该家庭");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }

        if (clientHomeDo.getHomeType().equals(CommonConstants.HOME_SHARED)) {
            log.warn("被分享设备家庭无法被操作");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
        }

        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId,clientUserDo.getId())
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_MEMBER));
        if (clientHomeUserDo == null) {
            log.warn("不是成员，无权退出该家庭");
            throw new UnauthorizedException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
        }

        ActionFlagUtil.assertTrue(clientHomeUserService.removeById(clientHomeUserDo.getId()));
    }

    @DSTransactional
    @Override
    public void bindClientUserAndHomeDevice(V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo, ClientUserDo clientUserDo, String ip) {
        switch (v2ClientHomeBindDeviceVo.getTypeInfo()){
            case (CommonConstants.DEVICE_ELINK_CN):
                // 易联储能设备配网绑定
                singlePhaseService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo);
                break;
            case (CommonConstants.DEVICE_TUYA_CN):
                // 涂鸦储能设备配网绑定
                singlePhaseService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo);
                break;
            case (CommonConstants.DEVICE_TUYA_DC):
                // 涂鸦单插设备配网绑定
                singlePlugSocketService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo);
                break;
            case (CommonConstants.DEVICE_EN_CDZ):
                // EN+充电桩配网绑定
                chargeStationService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo, ip);
                break;
        }
        log.info("{}向{}家庭中添加设备(wifiSn)：{}",clientUserDo.getId(),v2ClientHomeBindDeviceVo.getHomeId(),v2ClientHomeBindDeviceVo.getWifiSn());

    }

    @Override
    public V2HomeDeviceBindStatusDto checkDeviceBindStatus(String wifiSn) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
        V2HomeDeviceBindStatusDto v2HomeDeviceBindStatusDto = new V2HomeDeviceBindStatusDto();
        Integer bindStatus = Integer.parseInt(Optional.ofNullable(stringRedisTemplate.opsForValue().get(redisKey)).orElse(DeviceBindStatus.MISS));
        v2HomeDeviceBindStatusDto.setBindStatus(bindStatus);

        // 说明已经跟当前用户绑定了
        if (bindStatus == 1) {
            List<HybridSinglePhaseDO> nowBindDeviceList = hubService.nowBindDeviceList(wifiSn);
            if (CollUtil.isNotEmpty(nowBindDeviceList)) {
                for (HybridSinglePhaseDO hybridSinglePhaseDO : nowBindDeviceList) {
                    MiddleClientUserDeviceDo nowBindMiddle = middleClientUserDeviceService
                            .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                    .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                                    .eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId()));
                    if (nowBindMiddle != null) {
                        v2HomeDeviceBindStatusDto.setDeviceId(String.valueOf(hybridSinglePhaseDO.getId()));
                    }
                }
            }
        }

        return v2HomeDeviceBindStatusDto;
    }

    @DSTransactional
    @Override
    public void unbindClientUserAndHomeDevice(String homeId, List<Long> deviceIds) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 校验操作人与家庭的关系
        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn("找不到该家庭");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }

        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId,clientUserDo.getId())
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));
        if (clientHomeUserDo == null) {
            log.warn("不是所有者，无权操作该家庭");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_HOME);
        }

        // 校验是否有这个设备
        Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(false, deviceIds)
                .stream()
                .collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

        // 查询这些人是否绑定了这些设备
        Map<Long, MiddleClientUserDeviceDo> middleClientUserDeviceDoMap = middleClientUserDeviceService.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                        .in(MiddleClientUserDeviceDo::getDeviceId, deviceIds))
                .stream().collect(Collectors.toMap(MiddleClientUserDeviceDo::getDeviceId, Function.identity(), (existing, replacement) -> existing));

        // 校验设备是否在这个家庭中
        Map<Long, ClientHomeDeviceDo> clientHomeDeviceDoMap = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                        .eq(ClientHomeDeviceDo::getHomeId, homeId)
                        .in(ClientHomeDeviceDo::getDeviceId, deviceIds))
                .stream().collect(Collectors.toMap(ClientHomeDeviceDo::getDeviceId, Function.identity(), (existing, replacement) -> existing));

        // 使用Set优化查找性能
        Set<Long> deviceIdSet = new HashSet<>(deviceIds);

        // 使用统一的方法来检查设备是否存在，并收集不存在的设备ID
        Set<Long> notInDeviceIds = new HashSet<>();
        notInDeviceIds.addAll(checkDevicesExistence(deviceIdSet, hybridSinglePhaseDoMap));
        notInDeviceIds.addAll(checkDevicesExistence(deviceIdSet, middleClientUserDeviceDoMap));
        notInDeviceIds.addAll(checkDevicesExistence(deviceIdSet, clientHomeDeviceDoMap));

        if (!notInDeviceIds.isEmpty()) {
            log.error("查询不到这些设备：" + notInDeviceIds); // 使用error级别
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }

        // 解绑人和设备的关系
        if (clientHomeDo.getHomeType() ==  1) {

            // 清除设备的配置、清除设备的定时任务
            List<Long> chargeStationIds = hybridSinglePhaseDoMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getResourceSeriesId() == 104)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            List<HybridSinglePhaseDO> sockets = hybridSinglePhaseDoMap.values().stream()
                    .filter(hybridSinglePhaseDO -> hybridSinglePhaseDO.getResourceSeriesId() == 105)
                    .collect(Collectors.toList());
            List<Long> storageIds = hybridSinglePhaseDoMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getResourceSeriesId() == 101 || entry.getValue().getResourceSeriesId() == 102)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chargeStationIds)) {
                chargeStationService.deleteDevicesConfigAndTask(chargeStationIds);
            }
            if (CollUtil.isNotEmpty(sockets)) {
                singlePlugSocketService.deleteDevicesConfigAndTask(sockets);
            }
            if (CollUtil.isNotEmpty(storageIds)) {
                List<ClientCustomizeDo> list = clientCustomizeService.list(Wrappers.<ClientCustomizeDo>lambdaQuery()
                        .in(ClientCustomizeDo::getDeviceId, storageIds));
                if (CollUtil.isNotEmpty(list)) {
                    list.forEach(i -> {
                        i.setAutoStrategy(0);
                        i.setRegion(null);
                    });
                    clientCustomizeService.updateBatchById(list);
                }
            }

            ActionFlagUtil.assertTrue(middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                    .in(MiddleClientUserDeviceDo::getDeviceId, deviceIds)));
        }
        if (clientHomeDo.getHomeType() ==  0) {
            ActionFlagUtil.assertTrue(middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                    .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                    .in(MiddleClientUserDeviceDo::getDeviceId, deviceIds)));
        }

        // 解绑家庭和设备的关系
        ActionFlagUtil.assertTrue(clientHomeDeviceService.remove(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId)
                .in(ClientHomeDeviceDo::getDeviceId, deviceIds)));

        log.info("{}成功删除{}家庭中的设备：{}",clientUserDo.getId(),homeId,deviceIds);

    }

    // 设备跟家庭做绑定
    @DSTransactional
    @Override
    public void homeBindDevice(ClientUserDo clientUserDo, String homeId, String deviceId, String deviceName) {

        // 校验操作人与家庭的关系
        checkHomeAndUser(String.valueOf(clientUserDo.getId()), homeId);
        ActionFlagUtil.assertTrue(clientHomeDeviceService.saveRelation(homeId, deviceId, deviceName));
    }

    @Override
    public Long iotDeviceBind(WhBindDeviceVO bindParam) {
        return whIotBindService.netDeviceBind(bindParam);
    }

    @Override
    public List<V2HomeDeviceListDto> queryHomeDeviceList(ClientUserDo clientUserDo, String homeId) {
        String userId = String.valueOf(clientUserDo.getId());
        checkHomeDefault(userId, homeId);

        List<ClientHomeDeviceDo> clientHomeDeviceDos = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId)
                .orderByAsc(
                        ClientHomeDeviceDo::getCreateTime));

        // 家庭没设备就返回空数组
        if (clientHomeDeviceDos.size() == 0) {
            return new ArrayList<>();
        }

        List<Long> deviceIdList = clientHomeDeviceDos.stream()
                .map(ClientHomeDeviceDo::getDeviceId) // 从ClientHomeDeviceDo对象中提取deviceId
                .collect(Collectors.toList()); // 收集结果到List

        // 筛选设备是否是该该家庭主人的
        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getRelationType, 1));
        List<MiddleClientUserDeviceDo> middleList = middleClientUserDeviceService.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .in(MiddleClientUserDeviceDo::getDeviceId, deviceIdList)
                .eq(MiddleClientUserDeviceDo::getUserId, clientHomeUserDo.getUserId()));
        List<Long> list = middleList.stream()
                .map(MiddleClientUserDeviceDo::getDeviceId) // 从ClientHomeDeviceDo对象中提取deviceId
                .collect(Collectors.toList());
        Map<Long, Integer> masterMap = middleList.stream()
                .collect(Collectors.toMap(MiddleClientUserDeviceDo::getDeviceId, MiddleClientUserDeviceDo::getMaster));

        HubServiceGetBatchByIdWork hubServiceGetBatchByIdWork = new HubServiceGetBatchByIdWork(true, list, hubService);
        HubServiceGetAgentsByIdsWork hubServiceGetAgentsByIdsWork = new HubServiceGetAgentsByIdsWork(list, hubService);

        // 通过异步任务获取设备信息
        WorkerWrapper<String, List<HybridSinglePhaseDO>> getBatchByIdWork = new WorkerWrapper.Builder<String, List<HybridSinglePhaseDO>>()
                .worker(hubServiceGetBatchByIdWork)
                .callback(hubServiceGetBatchByIdWork)
                .param("1")
                .build();

        // 通过异步任务获取代理信息
        WorkerWrapper<String, List<BindInfoDTO>> getAgentsByIdsWork = new WorkerWrapper.Builder<String, List<BindInfoDTO>>()
                .worker(hubServiceGetAgentsByIdsWork)
                .callback(hubServiceGetAgentsByIdsWork)
                .param("1")
                .build();

        try {
            Async.beginWork(RequestConstants.HUB_REQUEST_MILLISECOND_TIMES, getBatchByIdWork, getAgentsByIdsWork);
        } catch (ExecutionException | InterruptedException e) {
            log.error(e.getMessage());
            return new ArrayList<>();
        }
        Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = getBatchByIdWork.getWorkResult().getResult().stream()
                .collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

        Map<String, String> agentIdMap = getAgentsByIdsWork.getWorkResult().getResult()
                .stream()
                .collect(Collectors.toMap(
                        BindInfoDTO::getResourceId,
                        BindInfoDTO::getUserId,
                        (existing, replacement) -> existing // 保留第一个agentId，忽略后续的
                ));

        Map<V2HomeDeviceListDto, WorkerWrapper> map = new HashMap<>();
        WorkerWrapper[] wrappers = new WorkerWrapper[0];

        List<V2HomeDeviceListDto> homeDeviceListDtos = clientHomeDeviceDos.stream().map(clientHomeDeviceDo -> {
                    Long deviceId = clientHomeDeviceDo.getDeviceId();
                    HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoMap.get(deviceId);

                    // 如果 hybridSinglePhaseDO 为 null，意味着设备不存在或获取设备时发生错误
                    if (hybridSinglePhaseDO == null) {
                        log.warn("设备不存在或无法获取: deviceId=" + deviceId);
                        return null;
                    }

                    V2HomeDeviceListDto homeDeviceListDto = new V2HomeDeviceListDto();
                    String deviceName = clientHomeDeviceDo.getDeviceName();
                    if (Objects.equals(deviceName, "")) {
                        deviceName = hybridSinglePhaseDO.getDeviceModel();
                    }
                    homeDeviceListDto.setDeviceAliasName(deviceName);
                    homeDeviceListDto.setState(hybridSinglePhaseDO.getState());
                    homeDeviceListDto.setDeviceId(String.valueOf(deviceId));
                    homeDeviceListDto.setVpp(hybridSinglePhaseDO.getVppMode());
                    homeDeviceListDto.setType(hybridSinglePhaseDO.getDataSource());
                    homeDeviceListDto.setDeviceSn(hybridSinglePhaseDO.getDeviceName());
                    homeDeviceListDto.setAgentId(agentIdMap.getOrDefault(String.valueOf(deviceId), ""));
                    homeDeviceListDto.setLon(hybridSinglePhaseDO.getLongitude());
                    homeDeviceListDto.setLat(hybridSinglePhaseDO.getLatitude());
                    homeDeviceListDto.setDeviceType(hybridSinglePhaseDO.getDeviceModel());
                    homeDeviceListDto.setResourceSeriesId(hybridSinglePhaseDO.getResourceSeriesId());
                    homeDeviceListDto.setResourceTypeId(hybridSinglePhaseDO.getResourceTypeId());
                    homeDeviceListDto.setBatterySoc(new BigDecimal(0));
                    homeDeviceListDto.setBatteryPower(new BigDecimal(0));
                    homeDeviceListDto.setMaster(masterMap.get(deviceId));
                    homeDeviceListDto.setEmsSoftwareVersion(hybridSinglePhaseDO.getEmsSoftwareVersion());
                    homeDeviceListDto.setDsp1SoftwareVersion(hybridSinglePhaseDO.getDsp1SoftwareVersion());
                    homeDeviceListDto.setFirstInstall(hybridSinglePhaseDO.getFirstInstall());
                    homeDeviceListDto.setIsMain(hybridSinglePhaseDO.getIsMain());
                    return homeDeviceListDto;
                }).filter(Objects::nonNull) // 过滤掉所有的 null 值
                .collect(Collectors.toList());

        for (V2HomeDeviceListDto homeDeviceListDto : homeDeviceListDtos) {
            Long deviceId = Long.valueOf(homeDeviceListDto.getDeviceId());
            HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoMap.get(deviceId);

            // 单插开关状态
            if (hybridSinglePhaseDO.getResourceSeriesId() == 105) {
                SocketInfoVo socketInfoVo = new SocketInfoVo();
                socketInfoVo.setDeviceId(String.valueOf(deviceId));

                QueryTuyaSocketInfoWorker queryTuyaSocketInfoWorker = new QueryTuyaSocketInfoWorker(singlePlugSocketService, clientUserDo, socketInfoVo);
                WorkerWrapper<String, TuyaSocketBaseInfoDto> queryTuyaSocketInfoWork = new WorkerWrapper.Builder<String, TuyaSocketBaseInfoDto>()
                        .worker(queryTuyaSocketInfoWorker)
                        .callback(queryTuyaSocketInfoWorker)
                        .param("QueryTuyaSocketInfoWorker")
                        .build();
                map.put(homeDeviceListDto, queryTuyaSocketInfoWork);
                wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                wrappers[wrappers.length - 1] = queryTuyaSocketInfoWork;
            }

            // 充电桩专用
            if (hybridSinglePhaseDO.getResourceSeriesId() == 104) {
                ExtInfoDto extInfoDto = BeanUtil.toBean(hybridSinglePhaseDO.getExtInfo(), ExtInfoDto.class);
                if (extInfoDto.getMode() != 0) {
                    GetChargeStationStatusWorker getChargeStationStatusWorker = new GetChargeStationStatusWorker(chargeStationService, userId, String.valueOf(deviceId));
                    WorkerWrapper<String, Integer> getChargeStationStatusWork = new WorkerWrapper.Builder<String, Integer>()
                            .worker(getChargeStationStatusWorker)
                            .callback(getChargeStationStatusWorker)
                            .param("GetChargeStationStatusWorker")
                            .build();
                    map.put(homeDeviceListDto, getChargeStationStatusWork);
                    wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                    wrappers[wrappers.length - 1] = getChargeStationStatusWork;
                }
                homeDeviceListDto.setChargeStationMode(extInfoDto.getMode());
            }

            // 单相机和三相机储能才有
            if (hybridSinglePhaseDO.getResourceSeriesId() == 101 || hybridSinglePhaseDO.getResourceSeriesId() == 102 ||
                    hybridSinglePhaseDO.getResourceSeriesId() == 103) {
                Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);
                homeDeviceListDto.setState(sysRunMode);

                if (sysRunMode.equals(DeviceStatusEnum.OFFLINE.getDbCode())) {
                    log.info("设备id： " + deviceId + " 离线");
                }
                GetSocWorker getSocWorker = new GetSocWorker(singlePhaseService, userId, hybridSinglePhaseDO);
                WorkerWrapper<String, Map<String, BigDecimal>> getSocWork = new WorkerWrapper.Builder<String, Map<String, BigDecimal>>()
                        .worker(getSocWorker)
                        .callback(getSocWorker)
                        .param("GetSocWorker")
                        .build();
                map.put(homeDeviceListDto, getSocWork);
                wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                wrappers[wrappers.length - 1] = getSocWork;
            }
        }

        try {
            Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
        } catch (ExecutionException | InterruptedException e) {
            log.warn(e.getMessage());
        }

        for (V2HomeDeviceListDto homeDeviceListDto : homeDeviceListDtos) {
            Long deviceId = Long.valueOf(homeDeviceListDto.getDeviceId());
            HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoMap.get(deviceId);

            // 单插开关状态
            if (hybridSinglePhaseDO.getResourceSeriesId() == 105) {
                WorkerWrapper workerWrapper = map.get(homeDeviceListDto);
                if (workerWrapper != null) {
                    TuyaSocketBaseInfoDto tuyaSocketInfo = (TuyaSocketBaseInfoDto) workerWrapper.getWorkResult().getResult();
                    homeDeviceListDto.setState(tuyaSocketInfo.getSysRunMode());
                    homeDeviceListDto.setSocketSwitch(tuyaSocketInfo.getSwitch1());
                }

            }

            // 充电桩专用
            if (hybridSinglePhaseDO.getResourceSeriesId() == 104) {
                WorkerWrapper workerWrapper = map.get(homeDeviceListDto);
                if (workerWrapper != null) {
                    Integer stationStatus = (Integer) workerWrapper.getWorkResult().getResult();
                    homeDeviceListDto.setState(stationStatus);
                }
            }

            // 单相机和三相机储能才有
            if (hybridSinglePhaseDO.getResourceSeriesId() == 101 || hybridSinglePhaseDO.getResourceSeriesId() == 102 ||
                    hybridSinglePhaseDO.getResourceSeriesId() == 103) {
                WorkerWrapper workerWrapper = map.get(homeDeviceListDto);
                if (workerWrapper != null) {
                    Map<String, BigDecimal> soc = (Map<String, BigDecimal>) workerWrapper.getWorkResult().getResult();
                    homeDeviceListDto.setBatterySoc(soc.get(TsdbMetricsConstants.BAT_SOC));
                    homeDeviceListDto.setBatteryPower(soc.get(TsdbMetricsConstants.BAT_P));
                }
            }

        }

        return homeDeviceListDtos;
    }

    public List<V2HomeInfoDto> initHome(String userId, String userName){
        List<ClientHomeDo> clientHomeDoList =  new ArrayList<>();
        List<HomeDeviceListDto> homeDeviceListDtos = homeAdapter.queryUserDeviceList(userId);
        ArrayList<ClientHomeDeviceDo> masterClientHomeDeviceDos = new ArrayList<>();
        ArrayList<ClientHomeDeviceDo> slaveClientHomeDeviceDos = new ArrayList<>();
        homeDeviceListDtos.forEach(homeDeviceListDto -> {
            long homeDeviceId = snowFlakeUtil.generateId();
            long currentTimeMillis = System.currentTimeMillis();
            ClientHomeDeviceDo clientHomeDeviceDo = new ClientHomeDeviceDo();
            clientHomeDeviceDo.setId(homeDeviceId);
            clientHomeDeviceDo.setDeviceId(Long.valueOf(homeDeviceListDto.getDeviceId()));
            clientHomeDeviceDo.setDeviceName(homeDeviceListDto.getDeviceAliasName());
            clientHomeDeviceDo.setCreateTime(currentTimeMillis);
            clientHomeDeviceDo.setUpdateTime(currentTimeMillis);
            if (homeDeviceListDto.getMaster().equals(CommonConstants.DEVICE_SALVE)) {
                slaveClientHomeDeviceDos.add(clientHomeDeviceDo);
            }
            if (homeDeviceListDto.getMaster().equals(CommonConstants.DEVICE_MASTER)) {
                masterClientHomeDeviceDos.add(clientHomeDeviceDo);
            }
        });

        V2HomeCreateHomeVo masterV2HomeCreateHomeVo = new V2HomeCreateHomeVo();
        masterV2HomeCreateHomeVo.setHomeName(userName);
        ClientHomeDo masterHome = createHome(masterV2HomeCreateHomeVo, CommonConstants.HOME_COMMON);

        if (masterClientHomeDeviceDos.size() > 0) {
            ArrayList<ClientHomeDeviceDo> newClientHomeDeviceDos = new ArrayList<>();
            masterClientHomeDeviceDos.forEach(clientHomeDeviceDo -> {
                clientHomeDeviceDo.setHomeId(masterHome.getId());
                newClientHomeDeviceDos.add(clientHomeDeviceDo);
            });
            ActionFlagUtil.assertTrue(clientHomeDeviceService.saveBatch(newClientHomeDeviceDos));
        }
        clientHomeDoList.add(masterHome);
        V2HomeCreateHomeVo v2HomeCreateHomeVo = new V2HomeCreateHomeVo();
        v2HomeCreateHomeVo.setHomeName("被分享设备");
        ClientHomeDo shareHome = createHome(v2HomeCreateHomeVo, CommonConstants.HOME_SHARED);

        if (slaveClientHomeDeviceDos.size() > 0) {
            ArrayList<ClientHomeDeviceDo> newClientHomeDeviceDos = new ArrayList<>();
            slaveClientHomeDeviceDos.forEach(clientHomeDeviceDo -> {
                clientHomeDeviceDo.setHomeId(shareHome.getId());
                newClientHomeDeviceDos.add(clientHomeDeviceDo);
            });
            ActionFlagUtil.assertTrue(clientHomeDeviceService.saveBatch(newClientHomeDeviceDos));
            clientHomeDoList.add(shareHome);
        }

        List<V2HomeInfoDto> v2HomeInfoDtoArrayList = new ArrayList<>();
        clientHomeDoList.forEach(clientHomeDo -> {
            V2HomeInfoDto v2HomeInfoDto = new V2HomeInfoDto();
            CglibUtil.copy(clientHomeDo, v2HomeInfoDto);
            v2HomeInfoDto.setHomeId(String.valueOf(clientHomeDo.getId()));
            if (clientHomeDo.getHomeType() == CommonConstants.HOME_COMMON) {
                v2HomeInfoDto.setHomeDeviceNumber(masterClientHomeDeviceDos.size());
            }
            if (clientHomeDo.getHomeType() == CommonConstants.HOME_SHARED) {
                v2HomeInfoDto.setHomeDeviceNumber(slaveClientHomeDeviceDos.size());
            }
            v2HomeInfoDto.setRelationType(CommonConstants.HOME_OWNER);
            v2HomeInfoDtoArrayList.add(v2HomeInfoDto);
        });

        return v2HomeInfoDtoArrayList;
    }

    // 检测家庭与用户的关系（不是所有者，不能操作）
    @Override
    public ClientHomeDo checkHomeAndUser(String userId, String homeId) {
        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn("找不到该家庭");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }

        if (clientHomeDo.getHomeType().equals(CommonConstants.HOME_SHARED)) {
            log.warn("被分享设备家庭无法被操作");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
        }

        clientHomeUserService.checkOwner(userId, homeId);
        return clientHomeDo;
    }

    // 检测家庭与用户的关系（加入家庭检测）
    public ClientHomeDo checkHomeAndUserByJoin(ClientUserDo clientUserDo, String homeId) {
        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn("找不到该家庭");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }

        if (clientHomeDo.getHomeType().equals(CommonConstants.HOME_SHARED)) {
            log.warn("被分享设备家庭无法被操作");
            throw new EcosException(EcosExceptionEnum.HOME_NOT_ALLOWED_OPERATE);
        }

        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId,clientUserDo.getId()));
        if (clientHomeUserDo != null) {
            log.warn("你已经加入该家庭，无法重复加入");
            throw new UnauthorizedException(EcosExceptionEnum.DONT_BIND_HOME_TWICE);
        }

        return clientHomeDo;
    }

    @Override
    public ClientHomeDo checkHomeDefault(String userId, String homeId) {
        ClientHomeDo clientHomeDo = clientHomeService.getById(homeId);
        if (clientHomeDo == null) {
            log.warn(String.format("家庭信息不存在 homeId: %s", homeId));
            throw new EcosException(EcosExceptionEnum.HOME_NOT_FIND);
        }
        ClientHomeUserDo clientHomeUserDo = clientHomeUserService.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId,userId));
        if (clientHomeUserDo == null) {
            log.warn(String.format( "用户[%s]非法访问家庭[%s]", userId, homeId));
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_HOME);
        }
        return clientHomeDo;
    }

    @Override
    public ClientHomeDeviceDo checkUserAndDevice(String userId, String deviceId) {
        ClientHomeDeviceDo clientHomeDeviceDo = new ClientHomeDeviceDo();
        List<ClientHomeUserDo> clientHomeUserDos = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId,userId));
        List<Long> homeIdList = clientHomeUserDos.stream().map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());

        if (clientHomeUserDos.size() == 0) {
            List<V2HomeInfoDto> v2HomeInfoDtos = queryHomeList(userId);
            homeIdList = v2HomeInfoDtos.stream().map(v2HomeInfoDto -> Long.valueOf(v2HomeInfoDto.getHomeId())).collect(Collectors.toList());
        }

        List<ClientHomeDeviceDo> clientHomeDeviceDoList = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .in(ClientHomeDeviceDo::getHomeId, homeIdList)
                .eq(ClientHomeDeviceDo::getDeviceId, deviceId));

        if (clientHomeDeviceDoList.size() == 0) {
            throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }

        for (ClientHomeUserDo clientHomeUserDo : clientHomeUserDos) {
            if (clientHomeUserDo.getRelationType() == 1) {
                List<ClientHomeDeviceDo> collect = clientHomeDeviceDoList.stream().filter(homeDeviceDo -> Objects.equals(homeDeviceDo.getHomeId(), clientHomeUserDo.getHomeId())).collect(Collectors.toList());
                if (collect.size() > 0) {
                    return collect.get(0);
                }
            }
        }

        for (ClientHomeUserDo clientHomeUserDo : clientHomeUserDos) {
            if (clientHomeUserDo.getRelationType() == 0) {
                List<ClientHomeDeviceDo> collect = clientHomeDeviceDoList.stream().filter(homeDeviceDo -> Objects.equals(homeDeviceDo.getHomeId(), clientHomeUserDo.getHomeId())).collect(Collectors.toList());
                if (collect.size() > 0) {
                    return collect.get(0);
                }
            }
        }

        return clientHomeDeviceDo;
    }

    @Override
    public Boolean checkUserHasDevice(ClientUserDo clientUserDo, String deviceId) {
        List<ClientHomeUserDo> clientHomeUserDos = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId,clientUserDo.getId()));

        if (clientHomeUserDos.size() == 0) {
            return false;
        }
        List<Long> homeIdList = clientHomeUserDos.stream().map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());
        List<ClientHomeDeviceDo> clientHomeDeviceDoList = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .in(ClientHomeDeviceDo::getHomeId, homeIdList)
                .eq(ClientHomeDeviceDo::getDeviceId, deviceId));
        if (clientHomeDeviceDoList.size() == 0) {
            return false;
        }
        return true;
    }

    @Override
    public HybridSinglePhaseDO checkHomeAndDevice(String userId, String deviceId) {

        List<HybridSinglePhaseDO> hybridSinglePhaseDoList = hubService.getBatchById(true, Collections.singletonList(Long.valueOf(deviceId)));

        if (hybridSinglePhaseDoList.size() != 1) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }

        // 校验用户是否是设备的成员
        ClientHomeDeviceDo clientHomeDeviceDo = checkUserAndDevice(userId, deviceId);

        HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoList.get(0);
        hybridSinglePhaseDO.setAlias(clientHomeDeviceDo.getDeviceName() == null ? "" : clientHomeDeviceDo.getDeviceName());

        return hybridSinglePhaseDO;
    }

    @Override
    public V2HomeNowDeviceRunDataDto queryHomeDeviceRunData(String homeId, ClientUserDo clientUserDo) {
        checkHomeDefault(String.valueOf(clientUserDo.getId()), homeId);

        List<ClientHomeDeviceDo> clientHomeDeviceDos = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId)
                .orderByAsc(
                        ClientHomeDeviceDo::getCreateTime));

        List<Long> deviceIdList = clientHomeDeviceDos.stream()
                .map(ClientHomeDeviceDo::getDeviceId) // 从ClientHomeDeviceDo对象中提取deviceId
                .collect(Collectors.toList()); // 收集结果到List

        // 一次性获取所有设备详情
        Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(false, deviceIdList)
                .stream()
                .collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

        List<HybridSinglePhaseDO> hybridSinglePhaseDOList = clientHomeDeviceDos.stream().map(clientHomeDeviceDo -> {
                    Long deviceId = clientHomeDeviceDo.getDeviceId();
                    HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoMap.get(deviceId);

                    // 如果 hybridSinglePhaseDO 为 null，意味着设备不存在或获取设备时发生错误
                    if (hybridSinglePhaseDO == null) {
                        log.warn("设备不存在或无法获取: deviceId=" + deviceId);
                        return null;
                    }

                    // 过滤非储能设备
                    if (hybridSinglePhaseDO.getResourceSeriesId() > 103 && hybridSinglePhaseDO.getResourceSeriesId() != 104) {
                        return null;
                    }
                    hybridSinglePhaseDO.setDeviceName(clientHomeDeviceDo.getDeviceName());
                    return hybridSinglePhaseDO;
                })
                .filter(Objects::nonNull) // 过滤掉所有的 null 值
                .collect(Collectors.toList());

        V2HomeNowDeviceRunDataDto v2HomeNowDeviceRunDataDto = new V2HomeNowDeviceRunDataDto();
        List<V2HomeDeviceBatterySocDto> v2HomeDeviceBatterySocDtoList = new ArrayList<>();

        WorkerWrapper[] wrappers = new WorkerWrapper[0];
        for (HybridSinglePhaseDO phaseDO : hybridSinglePhaseDOList) {
            if(phaseDO.getResourceSeriesId() == 104) {
                RunDataWorker runDataWorker = new RunDataWorker(chargeStationService, clientUserDo, String.valueOf(phaseDO.getId()));
                WorkerWrapper<String, ENPlusChargeStationRunDataDto> runDataWork = new WorkerWrapper.Builder<String, ENPlusChargeStationRunDataDto>()
                        .worker(runDataWorker)
                        .callback(runDataWorker)
                        .param("RunDataWorker")
                        .build();
                wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                wrappers[wrappers.length - 1] = runDataWork;
            } else {
                QueryNowDeviceRunDataWorker queryNowDeviceRunDataWorker = new QueryNowDeviceRunDataWorker(singlePhaseService, clientUserDo, String.valueOf(phaseDO.getId()));
                WorkerWrapper<String, HomeNowDeviceRunDataDto> queryNowDeviceRunDataWork = new WorkerWrapper.Builder<String, HomeNowDeviceRunDataDto>()
                        .worker(queryNowDeviceRunDataWorker)
                        .callback(queryNowDeviceRunDataWorker)
                        .param("QueryNowDeviceRunDataWorker")
                        .build();
                wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                wrappers[wrappers.length - 1] = queryNowDeviceRunDataWork;
            }
        }

        try {
            Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
        } catch (ExecutionException | InterruptedException e) {
            log.warn(e.getMessage());
        }

        for (HybridSinglePhaseDO phaseDO : hybridSinglePhaseDOList) {
            int i = hybridSinglePhaseDOList.indexOf(phaseDO);

            if(phaseDO.getResourceSeriesId() == 104) {
                ENPlusChargeStationRunDataDto chargeStationRunDataDto = (ENPlusChargeStationRunDataDto) wrappers[i].getWorkResult().getResult();
                v2HomeNowDeviceRunDataDto.setChargePower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getChargePower(), chargeStationRunDataDto.getPower()));
            } else {
                HomeNowDeviceRunDataDto runDataDto = (HomeNowDeviceRunDataDto) wrappers[i].getWorkResult().getResult();
                V2HomeDeviceBatterySocDto v2HomeDeviceBatterySocDto = new V2HomeDeviceBatterySocDto();
                v2HomeDeviceBatterySocDto.setDeviceSn(phaseDO.getDeviceSn());
                v2HomeDeviceBatterySocDto.setBatterySoc(runDataDto.getBatterySoc());
                v2HomeDeviceBatterySocDto.setIsExistSolar(runDataDto.getIsExistSolar());
                v2HomeDeviceBatterySocDto.setSysRunMode(runDataDto.getSysRunMode());
                v2HomeDeviceBatterySocDto.setSysPowerConfig(runDataDto.getSysPowerConfig());
                v2HomeDeviceBatterySocDtoList.add(v2HomeDeviceBatterySocDto);

                v2HomeNowDeviceRunDataDto.setBatteryPower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getBatteryPower(), runDataDto.getBatteryPower()));
                v2HomeNowDeviceRunDataDto.setHomePower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getHomePower(), runDataDto.getHomePower()));
                v2HomeNowDeviceRunDataDto.setEpsPower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getEpsPower(), runDataDto.getEpsPower()));
                v2HomeNowDeviceRunDataDto.setMeterPower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getMeterPower(), runDataDto.getMeterPower()));
                v2HomeNowDeviceRunDataDto.setGridPower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getGridPower(), runDataDto.getGridPower()));
                v2HomeNowDeviceRunDataDto.setSolarPower(NumberUtil.add(v2HomeNowDeviceRunDataDto.getSolarPower(), runDataDto.getSolarPower()));
            }
        }

        v2HomeNowDeviceRunDataDto.setHomePower(NumberUtil.round(
                NumberUtil.add(v2HomeNowDeviceRunDataDto.getHomePower(), v2HomeNowDeviceRunDataDto.getEpsPower()),
                0, RoundingMode.HALF_UP
        ));
        v2HomeNowDeviceRunDataDto.setBatterySocList(v2HomeDeviceBatterySocDtoList);

        return v2HomeNowDeviceRunDataDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDeviceRemark(String deviceId, String remark) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        List<HybridSinglePhaseDO> hybridSinglePhaseDoList = hubService.getBatchById(false, Collections.singletonList(Long.valueOf(deviceId)));

        if (hybridSinglePhaseDoList.size() != 1) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }

        // 校验用户是否是设备的成员
        ClientHomeDeviceDo clientHomeDeviceDo = checkUserAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        // 更新家庭设备的别名
        clientHomeDeviceDo.setDeviceName(remark);
        clientHomeDeviceDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientHomeDeviceService.updateById(clientHomeDeviceDo));

        // 更新用户设备对的别名
        MiddleClientUserDeviceDo middleClientUserDeviceServiceOne = middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId));
        middleClientUserDeviceServiceOne.setName(remark);
        middleClientUserDeviceServiceOne.setUpdateTime(System.currentTimeMillis());
        try {
            middleClientUserDeviceService.updateById(middleClientUserDeviceServiceOne);
        } catch (Exception e) {
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }
    }

    @Override
    public List<V2HomeDeviceModeDto> queryHomeDeviceMode(String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        List<HybridSinglePhaseDO> hybridSinglePhaseDOList = queryHomeEnergyDeviceList(homeId, clientUserDo);

        List<V2HomeDeviceModeDto> v2HomeDeviceModeDtos = new ArrayList<>();
        for (HybridSinglePhaseDO phaseDO : hybridSinglePhaseDOList) {
            V2HomeDeviceModeDto v2HomeDeviceModeDto = new V2HomeDeviceModeDto();
            CustomizeInfoDto customizeInfoDto = new CustomizeInfoDto();
            OperationUtil
                    .of(clientCustomizeService
                            .getOne(Wrappers.<ClientCustomizeDo>lambdaQuery().eq(ClientCustomizeDo::getDeviceId, phaseDO.getId())))
                    .ifPresentOrElse(
                            clientCustomizeDo -> this.queryDataFromDb(clientCustomizeDo, customizeInfoDto),
                            () -> this.queryFromDevice(phaseDO, customizeInfoDto)
                    );
            TimezoneUtil.convertGMTToUserTimezone(customizeInfoDto, clientUserDo.getTimeZone());
            v2HomeDeviceModeDto.setDeviceName(!Objects.equals(phaseDO.getDeviceName(), "") ? phaseDO.getDeviceName() : phaseDO.getDeviceSn());
            v2HomeDeviceModeDto.setChargeUseMode(customizeInfoDto.getChargeUseMode() == null ? 0 : customizeInfoDto.getChargeUseMode());
            v2HomeDeviceModeDto.setIsMain(phaseDO.getIsMain());
            // 如果为vpp模式，则设为3
            if (phaseDO.getVppMode()) {
                v2HomeDeviceModeDto.setChargeUseMode(3);
            }
            v2HomeDeviceModeDtos.add(v2HomeDeviceModeDto);
        }
        return v2HomeDeviceModeDtos;
    }

    @Override
    public HomeDeviceEnergyStatisticsDto queryHomeSolarAndGridEnergyData(String homeId, ClientUserDo clientUserDo) {
        List<HybridSinglePhaseDO> hybridSinglePhaseDOList = queryHomeEnergyDeviceList(homeId , clientUserDo);

        HomeDeviceEnergyStatisticsDto homeDeviceEnergyStatisticsDto = new HomeDeviceEnergyStatisticsDto();
        WorkerWrapper[] wrappers = new WorkerWrapper[0];
        for (HybridSinglePhaseDO phaseDO : hybridSinglePhaseDOList) {
            String deviceId = String.valueOf(phaseDO.getId());
            GetLastWeekSolarAndGridEnergyDataWorker getLastWeekSolarAndGridEnergyDataWorker = new GetLastWeekSolarAndGridEnergyDataWorker(singlePhaseService, clientUserDo, deviceId, -7, null);
            WorkerWrapper<String, HomeDeviceEnergyStatisticsDto> getLastWeekSolarAndGridEnergyDataWork = new WorkerWrapper.Builder<String, HomeDeviceEnergyStatisticsDto>()
                    .worker(getLastWeekSolarAndGridEnergyDataWorker)
                    .callback(getLastWeekSolarAndGridEnergyDataWorker)
                    .param("GetLastWeekSolarAndGridEnergyDataWorker")
                    .build();
            wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
            wrappers[wrappers.length - 1] = getLastWeekSolarAndGridEnergyDataWork;
        }

        try {
            Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
        } catch (ExecutionException | InterruptedException e) {
            log.warn(e.getMessage());
        }

        for (HybridSinglePhaseDO phaseDO : hybridSinglePhaseDOList) {
            int i = hybridSinglePhaseDOList.indexOf(phaseDO);
            HomeDeviceEnergyStatisticsDto lastWeekSolarAndGridEnergyData = (HomeDeviceEnergyStatisticsDto) wrappers[i].getWorkResult().getResult();

            homeDeviceEnergyStatisticsDto.setLastWeekTotalGrid(NumberUtil.add(homeDeviceEnergyStatisticsDto.getLastWeekTotalGrid(),lastWeekSolarAndGridEnergyData.getLastWeekTotalGrid()));
            homeDeviceEnergyStatisticsDto.setLastWeekTotalSolar(NumberUtil.add(homeDeviceEnergyStatisticsDto.getLastWeekTotalSolar(),lastWeekSolarAndGridEnergyData.getLastWeekTotalSolar()));
            homeDeviceEnergyStatisticsDto.setLastWeekTotalCarbonEmissions(NumberUtil.add(homeDeviceEnergyStatisticsDto.getLastWeekTotalCarbonEmissions(),lastWeekSolarAndGridEnergyData.getLastWeekTotalCarbonEmissions()));
            homeDeviceEnergyStatisticsDto.setLastWeekTotalSaveStandardCoal(NumberUtil.add(homeDeviceEnergyStatisticsDto.getLastWeekTotalSaveStandardCoal(),lastWeekSolarAndGridEnergyData.getLastWeekTotalSaveStandardCoal()));

            if (hybridSinglePhaseDOList.indexOf(phaseDO) == 0) {
                homeDeviceEnergyStatisticsDto.setWeekEnergy(lastWeekSolarAndGridEnergyData.getWeekEnergy());
                homeDeviceEnergyStatisticsDto.setCarbonEmissionsWeekEnergy(lastWeekSolarAndGridEnergyData.getCarbonEmissionsWeekEnergy());
                homeDeviceEnergyStatisticsDto.setSaveStandardCoalWeekEnergy(lastWeekSolarAndGridEnergyData.getSaveStandardCoalWeekEnergy());
                homeDeviceEnergyStatisticsDto.setToday(lastWeekSolarAndGridEnergyData.getToday());
            } else {
                Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> map1 = new HashMap<>(Math.abs(-7));
                Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> map2 = new HashMap<>(Math.abs(-7));
                Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> map3 = new HashMap<>(Math.abs(-7));
                Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> weekEnergy1 = homeDeviceEnergyStatisticsDto.getWeekEnergy();
                Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> carbonEmissionsWeekEnergy1 = homeDeviceEnergyStatisticsDto.getCarbonEmissionsWeekEnergy();
                Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> saveStandardCoalWeekEnergy1 = homeDeviceEnergyStatisticsDto.getSaveStandardCoalWeekEnergy();

                Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> weekEnergy2 = lastWeekSolarAndGridEnergyData.getWeekEnergy();
                Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> carbonEmissionsWeekEnergy2 = lastWeekSolarAndGridEnergyData.getCarbonEmissionsWeekEnergy();
                Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> saveStandardCoalWeekEnergy2 = lastWeekSolarAndGridEnergyData.getSaveStandardCoalWeekEnergy();

                weekEnergy1.forEach((week, deviceEnergy) -> {
                    HomeDeviceEnergyStatisticsDto.DeviceEnergy deviceEnergy1 = weekEnergy1.get(week);
                    HomeDeviceEnergyStatisticsDto.DeviceEnergy deviceEnergy2 = weekEnergy2.get(week);
                    deviceEnergy1.setGridEnergy(NumberUtil.add(deviceEnergy1.getGridEnergy(), deviceEnergy2.getGridEnergy()));
                    deviceEnergy1.setHomeEnergy(NumberUtil.add(deviceEnergy1.getHomeEnergy(), deviceEnergy2.getHomeEnergy()));
                    deviceEnergy1.setSolarEnergy(NumberUtil.add(deviceEnergy1.getSolarEnergy(), deviceEnergy2.getSolarEnergy()));
                    deviceEnergy1.setToGrid(NumberUtil.add(deviceEnergy1.getToGrid(), deviceEnergy2.getToGrid()));

                    if (deviceEnergy1.getSolarEnergy().compareTo(BigDecimal.ZERO) == 0 || deviceEnergy1.getHomeEnergy().compareTo(BigDecimal.ZERO) == 0) {
                        deviceEnergy1.setSelfPowered(BigDecimal.ZERO);
                    } else {
                        BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(deviceEnergy1.getSolarEnergy(), deviceEnergy1.getToGrid()), deviceEnergy1.getHomeEnergy()), 2, RoundingMode.HALF_UP);
                        // 如果solarPercent小于等于0，则将其设置为0
                        if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                            solarPercent = BigDecimal.ZERO;
                        }
                        BigDecimal percentData = NumberUtil.mul(
                                solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                                new BigDecimal(100)
                        );
                        deviceEnergy1.setSelfPowered(percentData);
                    }
                    map1.put(week, deviceEnergy1);
                });
                carbonEmissionsWeekEnergy1.forEach((week, deviceEnergy) -> {
                    HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy deviceEnergy1 = carbonEmissionsWeekEnergy1.get(week);
                    HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy deviceEnergy2 = carbonEmissionsWeekEnergy2.get(week);
                    deviceEnergy1.setCarbonEmissions(NumberUtil.add(deviceEnergy1.getCarbonEmissions(), deviceEnergy2.getCarbonEmissions()));
                    map2.put(week,deviceEnergy1);
                });
                saveStandardCoalWeekEnergy1.forEach((week, deviceEnergy) -> {
                    HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy deviceEnergy1 = saveStandardCoalWeekEnergy1.get(week);
                    HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy deviceEnergy2 = saveStandardCoalWeekEnergy2.get(week);
                    deviceEnergy1.setSaveStandardCoal(NumberUtil.add(deviceEnergy1.getSaveStandardCoal(), deviceEnergy2.getSaveStandardCoal()));
                    map3.put(week,deviceEnergy1);
                });
                homeDeviceEnergyStatisticsDto.setWeekEnergy(map1);
                homeDeviceEnergyStatisticsDto.setCarbonEmissionsWeekEnergy(map2);
                homeDeviceEnergyStatisticsDto.setSaveStandardCoalWeekEnergy(map3);
            }

        }
        return homeDeviceEnergyStatisticsDto;
    }

    @Override
    public InsightDeviceDataDto queryHomeDeviceInsightData(String homeId, Integer periodType, Long timestamp, ClientUserDo clientUserDo) {
        List<HybridSinglePhaseDO> hybridSinglePhaseDOList = queryHomeEnergyDeviceList(homeId , clientUserDo);
        InsightDeviceDataDto insightDeviceDataDto = new InsightDeviceDataDto();
        HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = new HomeNowDeviceRealtimeDto();
        InsightDeviceStatisticsDto insightDeviceStatisticsDto = new InsightDeviceStatisticsDto();
        InsightConsumptionDataDto insightConsumptionDataDto = new InsightConsumptionDataDto();
        insightDeviceDataDto.setInsightConsumptionDataDto(insightConsumptionDataDto);
        insightDeviceDataDto.setDeviceRealtimeDto(homeNowDeviceRealtimeDto);
        insightDeviceDataDto.setDeviceStatisticsDto(insightDeviceStatisticsDto);

        if (hybridSinglePhaseDOList.size() == 0) {
            return insightDeviceDataDto;
        }
        WorkerWrapper[] wrappers = new WorkerWrapper[0];
        Map<InsightDeviceDataVo, WorkerWrapper> map = new HashMap<>();
        for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {
            InsightDeviceDataVo insightDeviceDataVo = new InsightDeviceDataVo();
            insightDeviceDataVo.setDeviceId(String.valueOf(hybridSinglePhaseDO.getId()));
            insightDeviceDataVo.setPeriodType(periodType);
            insightDeviceDataVo.setTimestamp(timestamp);
            insightDeviceDataVo.checkParams();
            if (hybridSinglePhaseDO.getResourceSeriesId() == 101 || hybridSinglePhaseDO.getResourceSeriesId() == 102 ||
                    hybridSinglePhaseDO.getResourceSeriesId() == 103) {
                QueryDeviceInsightDataWorker queryDeviceInsightDataWorker = new QueryDeviceInsightDataWorker(singlePhaseService, clientUserDo, insightDeviceDataVo);
                WorkerWrapper<String, InsightDeviceDataDto> queryDeviceInsightDataWork = new WorkerWrapper.Builder<String, InsightDeviceDataDto>()
                        .worker(queryDeviceInsightDataWorker)
                        .callback(queryDeviceInsightDataWorker)
                        .param("QueryDeviceInsightDataWorker")
                        .build();
                wrappers = Arrays.copyOf(wrappers, wrappers.length + 1);
                wrappers[wrappers.length - 1] = queryDeviceInsightDataWork;
                map.put(insightDeviceDataVo, queryDeviceInsightDataWork);
            }
        }
        try {
            Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, wrappers);
        } catch (ExecutionException | InterruptedException e) {
            log.warn(e.getMessage());
        }

        map.forEach((insightDeviceDataVo, workerWrapper) -> {
            InsightDeviceDataDto insightDeviceDataDto1 = (InsightDeviceDataDto) workerWrapper.getWorkResult().getResult();
            addInsightDeviceDataDto(insightDeviceDataDto, insightDeviceDataDto1, insightDeviceDataVo);
        });
        // soc求平均
        calAverageSoc(insightDeviceDataDto, map.size());
        return insightDeviceDataDto;
    }

    /** 计算soc平均值 */
    private void calAverageSoc(InsightDeviceDataDto sumData, int deviceCount) {
        Map<Long, Object> socDps = sumData.getDeviceRealtimeDto().getSocDps();
        if (CollUtil.isNotEmpty(socDps)) {
            socDps.replaceAll((time, value) -> ((BigDecimal)value).divide(new BigDecimal(deviceCount),
                    2, RoundingMode.HALF_UP));
        }
        InsightDeviceStatisticsDto statisticsData = sumData.getDeviceStatisticsDto();
        statisticsData.setSoc(statisticsData.getSoc().divide(new BigDecimal(deviceCount), 2, RoundingMode.HALF_UP));
    }

    @Override
    @Transactional
    public void transferDeviceFamily(String deviceId, String homeId) {

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 检查这台设备存在不存在
        List<HybridSinglePhaseDO> hybridSinglePhaseDoList = hubService.getBatchById(false, Collections.singletonList(Long.valueOf(deviceId)));

        if (hybridSinglePhaseDoList.size() != 1) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }

        // 校验用户是否是设备所在家庭的主人,是不是要转移家庭的主人
        ClientHomeDeviceDo clientHomeDeviceDo = checkUserAndDevice(String.valueOf(clientUserDo.getId()), deviceId);
        // 自己转移自己不用转移
        if (Objects.equals(String.valueOf(clientHomeDeviceDo.getHomeId()), homeId)) {
            return;
        }

        ClientHomeDo clientHomeDoPast = checkHomeAndUser(String.valueOf(clientUserDo.getId()), String.valueOf(clientHomeDeviceDo.getHomeId()));
        ClientHomeDo clientHomeDoNow = checkHomeAndUser(String.valueOf(clientUserDo.getId()), homeId);

        // 开始转移
        clientHomeDeviceDo.setHomeId(Long.valueOf(homeId));
        clientHomeDeviceDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientHomeDeviceService.updateById(clientHomeDeviceDo));

    }

    @Override
    public String shareDeviceQRCodeClient(String deviceId, Integer saveDeviceTime) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Long userId = clientUserDo.getId();
        Long deviceLongId = Long.parseLong(deviceId);

        MiddleClientUserDeviceDo middleDo = middleClientUserDeviceService
                .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getUserId, userId)
                        .eq(MiddleClientUserDeviceDo::getDeviceId, deviceLongId));

        if (null == middleDo || 1 != middleDo.getMaster()) {
            log.warn("非主账号");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }
        long timestamp = System.currentTimeMillis() / 1000 / 60;
        int offset = new Random().nextInt(9);
        String rawStr = saveDeviceTime == null ? timestamp + deviceId : timestamp + deviceId + Constants.COMMA + saveDeviceTime;
        return offset + RotUtil.encode(rawStr, offset);
    }

    @Override
    public void bindSlaveAccount(String code) {
        String deviceIdWithRandomStr = RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
                .orElseThrow(() -> {
                    log.warn("二维码解密失败");
                    return new EcosException(EcosExceptionEnum.INVALID_PARAM);
                });
        String deviceId;
        String minute;
        try {
            minute = deviceIdWithRandomStr.substring(0, 8);
            deviceId = deviceIdWithRandomStr.substring(8);
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        long currMinute = System.currentTimeMillis() / 1000 / 60;
        if ((currMinute - Long.parseLong(minute)) > 30) {
            log.warn("超过绑定时效");
            throw new EcosException(EcosExceptionEnum.QR_CODE_EXPIRED);
        }

        HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        if (!tuyaDatacenterService.isSameDatacenter(clientUserDo.getDatacenterId(), hybridSinglePhaseDO.getDatacenterId())) {
            throw new EcosException(EcosExceptionEnum.INVALID_DATACENTER);
        }

        MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService
                .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId))
                        .eq(MiddleClientUserDeviceDo::getMaster, 1));
        if (null == middleClientUserDeviceDo) {
            log.warn("未绑定设备");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }
        MiddleClientUserDeviceDo boundDeviceDo = middleClientUserDeviceService
                .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId))
                        .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId()));
        if (null != boundDeviceDo) {
            log.warn("已经绑定过设备");
            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
        }
        MiddleClientUserDeviceDo newMiddleDo = new MiddleClientUserDeviceDo();
        newMiddleDo.setId(snowFlakeUtil.generateId());
        newMiddleDo.setUserId(clientUserDo.getId());
        newMiddleDo.setDeviceId(Long.parseLong(deviceId));
        newMiddleDo.setCreateTime(System.currentTimeMillis());
        newMiddleDo.setUpdateTime(System.currentTimeMillis());
        newMiddleDo.setWeight(0);
        newMiddleDo.setName(hybridSinglePhaseDO.getDeviceName());
        newMiddleDo.setMaster(0);
        ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(newMiddleDo));

        // 添加进被分享设备的家庭中
        List<ClientHomeUserDo> homeUserDoList = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getUserId, clientUserDo.getId())
                .eq(ClientHomeUserDo::getRelationType, 1));
        List<Long> homeList = homeUserDoList.stream().map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());

        if (homeUserDoList.size() > 0) {
            ClientHomeDo clientHomeDo = clientHomeService.getOne(Wrappers.<ClientHomeDo>lambdaQuery()
                    .eq(ClientHomeDo::getHomeType, 0)
                    .in(ClientHomeDo::getId, homeList));

            // 如果没有默认创建一个
            if (clientHomeDo == null) {
                V2HomeCreateHomeVo v2HomeCreateHomeVo = new V2HomeCreateHomeVo();
                v2HomeCreateHomeVo.setHomeName("被分享设备");
                clientHomeDo = createHome(v2HomeCreateHomeVo, CommonConstants.HOME_SHARED);
            }

            ClientHomeDeviceDo clientHomeDeviceDo = new ClientHomeDeviceDo();
            long homeDeviceId = snowFlakeUtil.generateId();
            long currentTimeMillis = System.currentTimeMillis();
            clientHomeDeviceDo.setId(homeDeviceId);
            clientHomeDeviceDo.setHomeId(clientHomeDo.getId());
            clientHomeDeviceDo.setDeviceId(Long.valueOf(deviceId));
            clientHomeDeviceDo.setDeviceName(hybridSinglePhaseDO.getDeviceName());
            clientHomeDeviceDo.setCreateTime(currentTimeMillis);
            clientHomeDeviceDo.setUpdateTime(currentTimeMillis);

            ActionFlagUtil.assertTrue(clientHomeDeviceService.save(clientHomeDeviceDo));

        }
    }

    @Override
    @DSTransactional
    public void removeDeviceBindAccount(SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo) {
        // 移除安装商绑定
        if (settingRemoveDeviceBindAccountVo.getIsInstaller() != null && settingRemoveDeviceBindAccountVo.getIsInstaller()) {
            hubService.boundInstall(InstallBoundDTO.builder()
                    .userId(Long.parseLong(settingRemoveDeviceBindAccountVo.getAccountId()))
                    .deviceId(Long.parseLong(settingRemoveDeviceBindAccountVo.getDeviceId()))
                    .saveDeviceTime(DeviceSaveTimeEnum.NEVER.getCode())
                    .build());
            return;
        }
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Long handlerUserId = clientUserDo.getId();
        Long wannaDelAccountId = Long.parseLong(settingRemoveDeviceBindAccountVo.getAccountId());

        getOptionalBindDevice(settingRemoveDeviceBindAccountVo.getDeviceId(), handlerUserId)
                .ifPresentOrElseThrow(middleClientUserDeviceDo -> {
                    int master = middleClientUserDeviceDo.getMaster();
                    if (1 == master) {
                        if (Objects.equals(handlerUserId, wannaDelAccountId)) {
                            // 清除设备的配置、清除设备的定时任务
                            HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.valueOf(settingRemoveDeviceBindAccountVo.getDeviceId()));
                            if (hybridSinglePhaseDO.getResourceSeriesId() == 104 ) {
                                chargeStationService.deleteDevicesConfigAndTask(Collections.singletonList(Long.valueOf(settingRemoveDeviceBindAccountVo.getDeviceId())));
                            }

                            if (hybridSinglePhaseDO.getResourceSeriesId() == 105) {
                                singlePlugSocketService.deleteDevicesConfigAndTask(Collections.singletonList(hybridSinglePhaseDO));
                            }

                            middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(
                                    MiddleClientUserDeviceDo::getDeviceId,
                                    Long.parseLong(settingRemoveDeviceBindAccountVo.getDeviceId())
                            ));

                            // 清除所有设备和家庭的关系
                            ActionFlagUtil.assertTrue(clientHomeDeviceService.remove(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                                    .eq(ClientHomeDeviceDo::getDeviceId, settingRemoveDeviceBindAccountVo.getDeviceId())));

                        } else {
                            middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                    .eq(
                                            MiddleClientUserDeviceDo::getDeviceId,
                                            Long.parseLong(settingRemoveDeviceBindAccountVo.getDeviceId())
                                    )
                                    .eq(MiddleClientUserDeviceDo::getUserId, wannaDelAccountId));

                            // 清除被清除人被分享家庭和设备的关系
                            List<ClientHomeUserDo> homeUserDoList = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
                                    .eq(ClientHomeUserDo::getUserId, wannaDelAccountId)
                                    .eq(ClientHomeUserDo::getRelationType, 1));
                            List<Long> homeList = homeUserDoList.stream().map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());

                            if (homeUserDoList.size() > 0) {
                                ClientHomeDo clientHomeDo = clientHomeService.getOne(Wrappers.<ClientHomeDo>lambdaQuery()
                                        .eq(ClientHomeDo::getHomeType, 0)
                                        .in(ClientHomeDo::getId, homeList));

                                if (clientHomeDo != null) {
                                    ActionFlagUtil.assertTrue(clientHomeDeviceService.remove(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                                            .eq(ClientHomeDeviceDo::getDeviceId, settingRemoveDeviceBindAccountVo.getDeviceId())
                                            .eq(ClientHomeDeviceDo::getHomeId, clientHomeDo.getId())));
                                }

                            }

                        }
                    } else {
                        throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
                    }
                }, () -> {
                    log.warn("未绑定设备");
                    return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deviceTransfer(V2SettingTransferVo req) {
        // 校验二维码合法
        String codeStr = checkQrCode(req.getCode());
        // 校验二维码是否过期
        String paramStr = checkQrTime(codeStr, 10);
        // 解析二维码数据
        SettingTransferDTO param = JSONObject.parseObject(paramStr, SettingTransferDTO.class);
        if (CollUtil.isEmpty(param.getDeviceIds())) {
            log.info("deviceIds is empty, param is {}", JSONUtil.toJsonStr(param));
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        List<HybridSinglePhaseDO> deviceInfoList = hubService.getBatchById(true, param.getDeviceIds());
        if (CollUtil.isEmpty(deviceInfoList)) {
            log.info("deviceInfo is empty, param is {}", JSONUtil.toJsonStr(param));
            throw new EcosException(EcosExceptionEnum.CUR_DATACENTER_NO_DEVICE);
        }
        ClientUserDo userInfo = SecurityUtil.getClientUserDo();
        // 校验是否在同一数据中心
        checkSameDatacenter(userInfo, deviceInfoList);
        // 校验是否已经绑定过
        List<MiddleClientUserDeviceDo> dealList = checkAlreadyBind(deviceInfoList, userInfo.getId());
        // 执行绑定过程
        doBindAction(userInfo, dealList, deviceInfoList, param, req);
    }

    @Override
    public void nowDataIncreaseRefresh(String homeId, ClientUserDo clientUserDo) {
        List<HybridSinglePhaseDO> hybridSinglePhaseDOList = queryHomeEnergyDeviceList(homeId , clientUserDo);

        for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOList) {
            hubService.speedupOnce(String.valueOf(hybridSinglePhaseDO.getId()));
        }

    }

    @Override
    public void installUpdSaveTime(InstallBoundVO req) {
        hubService.boundInstall(InstallBoundDTO.builder()
                .userId(req.getUserId())
                .deviceId(req.getDeviceId())
                .saveDeviceTime(req.getSaveDeviceTime())
                .build());
    }

    @Override
    public Boolean iotIsOnline(String wifiSn) {
        return ecosIotApi.checkDeviceOnline(wifiSn, String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
    }

    @Override
    public EcosAccountDTO deviceBindAccountInfo(String deviceSn) {
        HybridSinglePhaseDO deviceInfo = hubService.getByDeviceName(deviceSn);
        Map<String, List<String>> map = apiAdapter.getDeviceMasterAndSubAccount(deviceInfo.getId());
        return JSONUtil.toBean(JSONUtil.toJsonStr(map), EcosAccountDTO.class);
    }

    @Override
    public void iotResetDevice(String wifiSn) {
        ecosIotApi.sendEsResetCommand(wifiSn, String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
    }

    @Override
    public HybridSinglePhaseDO deviceInfo(String deviceSn) {
        return hubService.getByDeviceName(deviceSn);
    }

    @Override
    public HomeElePriceDTO familyPriceInfo(String homeId) {
        return clientHomeService.familyPriceInfo(homeId);
    }

    @Override
    public HomeCostSavingDTO costInfo(String homeId) {
        return clientHomeSaveCostService.calHomeCostInfo(Long.parseLong(homeId));
    }

    @Override
    public void clearHistoryCostInfo(String homeId) {
        clientHomeSaveCostService.clearHistoryCostInfo(Long.parseLong(homeId));
    }

    private List<Long> checkDevicesExistence(Set<Long> deviceIds, Map<Long, ?> deviceMap) {
        return deviceIds.stream()
                .filter(deviceId -> !deviceMap.containsKey(deviceId))
                .collect(Collectors.toList());
    }

    private List<HybridSinglePhaseDO> queryHomeEnergyDeviceList(String homeId, ClientUserDo clientUserDo) {
        checkHomeDefault(String.valueOf(clientUserDo.getId()), homeId);

        List<ClientHomeDeviceDo> clientHomeDeviceDos = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId)
                .orderByAsc(
                        ClientHomeDeviceDo::getCreateTime));

        List<Long> deviceIdList = clientHomeDeviceDos.stream()
                .map(ClientHomeDeviceDo::getDeviceId) // 从ClientHomeDeviceDo对象中提取deviceId
                .collect(Collectors.toList()); // 收集结果到List

        // 一次性获取所有设备详情
        Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(true, deviceIdList)
                .stream()
                .collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

        return clientHomeDeviceDos.stream().map(clientHomeDeviceDo -> {
                    Long deviceId = clientHomeDeviceDo.getDeviceId();
                    HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDoMap.get(deviceId);

                    // 如果 hybridSinglePhaseDO 为 null，意味着设备不存在或获取设备时发生错误
                    if (hybridSinglePhaseDO == null) {
                        log.warn("设备不存在或无法获取: deviceId=" + deviceId);
                        return null;
                    }

                    // 过滤非储能设备
                    if (hybridSinglePhaseDO.getResourceSeriesId() > 103) {
                        return null;
                    }
                    hybridSinglePhaseDO.setDeviceName(clientHomeDeviceDo.getDeviceName());
                    return hybridSinglePhaseDO;
                })
                .filter(Objects::nonNull) // 过滤掉所有的 null 值
                .collect(Collectors.toList());
    }

    private void queryDataFromDb(ClientCustomizeDo clientCustomizeDo, CustomizeInfoDto customizeInfoDto) {
        customizeInfoDto.setMinCapacity(clientCustomizeDo.getBatteryMin());
        customizeInfoDto.setChargeUseMode(clientCustomizeDo.getChargeMode());
        customizeInfoDto.setCharge1StartTimeHour(clientCustomizeDo.getChargeStartHour1());
        customizeInfoDto.setCharge1StartTimeMinute(clientCustomizeDo.getChargeStartMinute1());
        customizeInfoDto.setCharge1EndTimeHour(clientCustomizeDo.getChargeEndHour1());
        customizeInfoDto.setCharge1EndTimeMinute(clientCustomizeDo.getChargeEndMinute1());
        customizeInfoDto.setCharge2StartTimeHour(clientCustomizeDo.getChargeStartHour2());
        customizeInfoDto.setCharge2StartTimeMinute(clientCustomizeDo.getChargeStartMinute2());
        customizeInfoDto.setCharge2EndTimeHour(clientCustomizeDo.getChargeEndHour2());
        customizeInfoDto.setCharge2EndTimeMinute(clientCustomizeDo.getChargeEndMinute2());
        customizeInfoDto.setDischarge1StartTimeHour(clientCustomizeDo.getDischargeStartHour1());
        customizeInfoDto.setDischarge1StartTimeMinute(clientCustomizeDo.getDischargeStartMinute1());
        customizeInfoDto.setDischarge1EndTimeHour(clientCustomizeDo.getDischargeEndHour1());
        customizeInfoDto.setDischarge1EndTimeMinute(clientCustomizeDo.getDischargeEndMinute1());
        customizeInfoDto.setDischarge2StartTimeHour(clientCustomizeDo.getDischargeStartHour2());
        customizeInfoDto.setDischarge2StartTimeMinute(clientCustomizeDo.getDischargeStartMinute2());
        customizeInfoDto.setDischarge2EndTimeHour(clientCustomizeDo.getDischargeEndHour2());
        customizeInfoDto.setDischarge2EndTimeMinute(clientCustomizeDo.getDischargeEndMinute2());
        customizeInfoDto.setMaxFeedIn(clientCustomizeDo.getMaxFeedIn());
        customizeInfoDto.setEpsBatteryMin(clientCustomizeDo.getEpsBatteryMin());
        customizeInfoDto.setDischargeToGridFlag(clientCustomizeDo.getDischargeToGridFlag());
        customizeInfoDto.setSelfSoc(clientCustomizeDo.getSelfSoc());
        customizeInfoDto.setSelfFeedIn(clientCustomizeDo.getSelfFeedIn());
        customizeInfoDto.setRegularSoc(clientCustomizeDo.getRegularSoc());
        customizeInfoDto.setRegularFeedIn(clientCustomizeDo.getRegularFeedIn());
        customizeInfoDto.setBackupSoc(clientCustomizeDo.getBackupSoc());
        customizeInfoDto.setBackupFeedIn(clientCustomizeDo.getBackupFeedIn());
    }

    @DSTransactional
    private void queryFromDevice(HybridSinglePhaseDO hybridSinglePhaseDO, CustomizeInfoDto customizeInfoDto) {
        Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);

        if (sysRunMode.equals(DeviceStatusEnum.OFFLINE.getDbCode())) {
            log.info("设备id： " + hybridSinglePhaseDO.getId() + " 离线");
            return;
        }
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        List<Integer> valList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41001, 27, cloud);
        if (CollUtil.isEmpty(valList)) {
            log.warn("读取设备长度为0");
            throw new EcosException(EcosExceptionEnum.READ_DEVICE_CONFIG_ERROR);
        }

        setCustomizeInfoDto(customizeInfoDto, valList);

        ClientCustomizeDo clientCustomizeDo = new ClientCustomizeDo();
        clientCustomizeDo.setId(snowFlakeUtil.generateId());
        clientCustomizeDo.setDeviceId(hybridSinglePhaseDO.getId());

        clientCustomizeDo.setBatteryMin(valList.get(1));
        clientCustomizeDo.setChargeMode(valList.get(2));
        clientCustomizeDo.setChargeStartHour1(valList.get(7));
        clientCustomizeDo.setChargeStartMinute1(valList.get(8));
        clientCustomizeDo.setChargeEndHour1(valList.get(9));
        clientCustomizeDo.setChargeEndMinute1(valList.get(10));

        clientCustomizeDo.setDischargeStartHour1(valList.get(12));
        clientCustomizeDo.setDischargeStartMinute1(valList.get(13));
        clientCustomizeDo.setDischargeEndHour1(valList.get(14));
        clientCustomizeDo.setDischargeEndMinute1(valList.get(15));

        clientCustomizeDo.setChargeStartHour2(valList.get(17));
        clientCustomizeDo.setChargeStartMinute2(valList.get(18));
        clientCustomizeDo.setChargeEndHour2(valList.get(19));
        clientCustomizeDo.setChargeEndMinute2(valList.get(20));

        clientCustomizeDo.setDischargeStartHour2(valList.get(22));
        clientCustomizeDo.setDischargeStartMinute2(valList.get(23));
        clientCustomizeDo.setDischargeEndHour2(valList.get(24));
        clientCustomizeDo.setDischargeEndMinute2(valList.get(25));

        List<Integer> feedInList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41037, 1, cloud);
        List<Integer> epsBatteryMinList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41042, 1, cloud);
        List<Integer> dischargeToGridFlagList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 40046, 1, cloud);

        if (feedInList.size() > 0) {
            customizeInfoDto.setMaxFeedIn(feedInList.get(0));
            clientCustomizeDo.setMaxFeedIn(feedInList.get(0));
        }

        if (epsBatteryMinList.size() > 0) {
            customizeInfoDto.setEpsBatteryMin(epsBatteryMinList.get(0));
            clientCustomizeDo.setEpsBatteryMin(epsBatteryMinList.get(0));
        }

        if (dischargeToGridFlagList.size() > 0) {
            customizeInfoDto.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
            clientCustomizeDo.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
        }

        ClientCustomizeDo clientCustomizeDo2 = convertClientCustomizeDo(clientCustomizeDo);

        ActionFlagUtil.assertTrue(clientCustomizeService.save(clientCustomizeDo2));
    }

    private void setCustomizeInfoDto(CustomizeInfoDto customizeInfoDto, List<Integer> valList) {
        val resLen = 27;
        if (valList.size() != resLen) {
            log.warn("参数长度错误");
            throw new EcosException(EcosExceptionEnum.INVALID_RESULT_LEN);
        }
        customizeInfoDto.setMinCapacity(valList.get(1));
        customizeInfoDto.setChargeUseMode(valList.get(2));

        customizeInfoDto.setCharge1StartTimeHour(valList.get(7));
        customizeInfoDto.setCharge1StartTimeMinute(valList.get(8));
        customizeInfoDto.setCharge1EndTimeHour(valList.get(9));
        customizeInfoDto.setCharge1EndTimeMinute(valList.get(10));

        customizeInfoDto.setDischarge1StartTimeHour(valList.get(12));
        customizeInfoDto.setDischarge1StartTimeMinute(valList.get(13));
        customizeInfoDto.setDischarge1EndTimeHour(valList.get(14));
        customizeInfoDto.setDischarge1EndTimeMinute(valList.get(15));

        customizeInfoDto.setCharge2StartTimeHour(valList.get(17));
        customizeInfoDto.setCharge2StartTimeMinute(valList.get(18));
        customizeInfoDto.setCharge2EndTimeHour(valList.get(19));
        customizeInfoDto.setCharge2EndTimeMinute(valList.get(20));

        customizeInfoDto.setDischarge2StartTimeHour(valList.get(22));
        customizeInfoDto.setDischarge2StartTimeMinute(valList.get(23));
        customizeInfoDto.setDischarge2EndTimeHour(valList.get(24));
        customizeInfoDto.setDischarge2EndTimeMinute(valList.get(25));
    }

    private ClientCustomizeDo convertClientCustomizeDo(ClientCustomizeDo clientCustomizeDo) {

        ClientCustomizeDo clientCustomizeDo2 = new ClientCustomizeDo();
        CglibUtil.copy(clientCustomizeDo,clientCustomizeDo2);
        Integer mode = clientCustomizeDo.getChargeMode();
        val selfPowered = 0;
        val loadShifting = 1;
        val backup = 2;

        switch (mode) {
            case selfPowered:
                clientCustomizeDo2.setSelfSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setSelfFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            case loadShifting:
                clientCustomizeDo2.setRegularSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setRegularFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            case backup:
                clientCustomizeDo2.setBackupSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setBackupFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            default:
                log.warn("参数错误");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }


        return clientCustomizeDo2;
    }

    private OperationUtil<MiddleClientUserDeviceDo> getOptionalBindDevice(String deviceId, Long userId) {
        return OperationUtil.of(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                .eq(MiddleClientUserDeviceDo::getUserId, userId)));
    }

    /**
     * 执行转移过程
     *
     * @param userInfo 用户
     * @param boundList 已绑定主账号列表
     * @param deviceList 设备列表
     * @param param 入参
     */
    @DSTransactional
    private void doBindAction(ClientUserDo userInfo, List<MiddleClientUserDeviceDo> boundList,
                              List<HybridSinglePhaseDO> deviceList,
                              SettingTransferDTO param, V2SettingTransferVo req) {

        // 检查用户是否是这个家庭的主人
        ClientHomeDo clientHomeDo = checkHomeAndUser(String.valueOf(userInfo.getId()), req.getHomeId());
        int tuyaDevice = 0;
        // 只需新增尚未绑定主账号关系的设备
        Set<Long> existsSet = boundList == null ? new HashSet<>() : boundList.stream()
                .map(MiddleClientUserDeviceDo::getDeviceId)
                .collect(Collectors.toSet());
        List<MiddleClientUserDeviceDo> itemList = deviceList.stream()
                .filter(i -> !existsSet.contains(i.getId()))
                .map(i -> MiddleClientUserDeviceDo.builder()
                        .id(snowFlakeUtil.generateId())
                        .userId(userInfo.getId())
                        .deviceId(i.getId())
                        .createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .weight(1000)
                        .name(i.getDeviceName())
                        .master(1)
                        .build())
                .collect(Collectors.toList());
        ActionFlagUtil.assertTrue(middleClientUserDeviceService.saveBatch(itemList));

        List<Long> collect = itemList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());
        if (collect.size() > 0) {
            List<HybridSinglePhaseDO> hybridSinglePhaseDOS = hubService.getBatchById(true, collect);
            if (hybridSinglePhaseDOS.size() == 0) {
                throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
            }
            List<ClientHomeDeviceDo> clientHomeDeviceDoList = new ArrayList<>();
            for (HybridSinglePhaseDO hybridSinglePhaseDO : hybridSinglePhaseDOS) {
                ClientHomeDeviceDo clientHomeDeviceDo = new ClientHomeDeviceDo();
                long homeDeviceId = snowFlakeUtil.generateId();
                long currentTimeMillis = System.currentTimeMillis();
                clientHomeDeviceDo.setId(homeDeviceId);
                clientHomeDeviceDo.setHomeId(clientHomeDo.getId());
                clientHomeDeviceDo.setDeviceId(hybridSinglePhaseDO.getId());
                clientHomeDeviceDo.setDeviceName(hybridSinglePhaseDO.getDeviceName());
                clientHomeDeviceDo.setCreateTime(currentTimeMillis);
                clientHomeDeviceDo.setUpdateTime(currentTimeMillis);
                clientHomeDeviceDoList.add(clientHomeDeviceDo);

                if (hybridSinglePhaseDO.getDataSource() == 1) {
                    tuyaDevice = tuyaDevice + 1;
                }
            }

            if (clientHomeDeviceDoList.size() > 0) {
                ActionFlagUtil.assertTrue(clientHomeDeviceService.saveBatch(clientHomeDeviceDoList));
            }
        }

        // 2、转移tuya云的家庭
        if (tuyaDevice > 0) {
            tuyaService.transTuyaHome(userInfo, param);
        }
        // 3、更新系统转移时间
        hubService.updateSysTransTime(param.getSystemInfoId(), req.getSaveDeviceTime());
    }

    /**
     * 校验二维码合法性
     *
     * @param code 二维码
     * @return 解析结果
     */
    private String checkQrCode(String code) {
        return RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
                .orElseThrow(() -> {
                    log.warn("二维码解密失败");
                    return new EcosException(EcosExceptionEnum.INVALID_PARAM);
                });
    }

    /**
     * 校验二维码是否过期
     *
     * @param codeStr 二维码解析结果
     * @param expiredTime 过期时间
     * @return 反序列化结果
     */
    private String checkQrTime(String codeStr, int expiredTime) {
        try {
            String minute = codeStr.substring(0, 8);
            long currMinute = System.currentTimeMillis() / 1000 / 60;
            if ((currMinute - Long.parseLong(minute)) > expiredTime) {
                log.warn("超过转移时效");
//                throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
            }
            return codeStr.substring(8);
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
    }

    /**
     * 校验是否是同一数据中心
     *
     * @param userInfo 用户信息
     * @param deviceInfoList 设备信息
     */
    private void checkSameDatacenter(ClientUserDo userInfo, List<HybridSinglePhaseDO> deviceInfoList) {
        for (HybridSinglePhaseDO deviceInfo : deviceInfoList) {
            if (!tuyaDatacenterService.isSameDatacenter(userInfo.getDatacenterId(), deviceInfo.getDatacenterId())) {
                throw new EcosException(EcosExceptionEnum.INVALID_DATACENTER);
            }
        }
    }

    /**
     * 校验是否已绑定过
     *
     * @param deviceInfoList 设备列表
     * @param userId 用户id
     * @return 已绑定的主账号关系
     */
    private List<MiddleClientUserDeviceDo> checkAlreadyBind(List<HybridSinglePhaseDO> deviceInfoList, Long userId) {
        List<Long> deviceIds = deviceInfoList.stream()
                .map(HybridSinglePhaseDO::getId)
                .collect(Collectors.toList());
        List<MiddleClientUserDeviceDo> boundDeviceList = middleClientUserDeviceService
                .list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .in(MiddleClientUserDeviceDo::getDeviceId, deviceIds)
                        .eq(MiddleClientUserDeviceDo::getMaster, 1));
        if (CollUtil.isEmpty(boundDeviceList)) {
            return boundDeviceList;
        }
        // 存在其他主账号已绑定其中设备，直接抛出异常
        boolean existsOtherMaster = boundDeviceList.stream()
                .anyMatch(i -> !userId.equals(i.getUserId()));
        if (existsOtherMaster) {
            throw new EcosException(EcosExceptionEnum.DEVICE_ALREADY_BOUND);
        }
        return boundDeviceList;
    }

    public void addInsightDeviceDataDto(InsightDeviceDataDto insightDeviceDataDto, InsightDeviceDataDto insightDeviceDataDto1, InsightDeviceDataVo insightDeviceDataVo) {

        if (insightDeviceDataVo.getPeriodType() == CommonConstants.PERIOD_DAY) {
            HomeNowDeviceRealtimeDto deviceRealtimeDto = insightDeviceDataDto.getDeviceRealtimeDto();
            HomeNowDeviceRealtimeDto deviceRealtimeDto1 = insightDeviceDataDto1.getDeviceRealtimeDto();

            Map<Long, Object> solarPowerDps = deviceRealtimeDto.getSolarPowerDps();
            Map<Long, Object> solarPowerDps1 = deviceRealtimeDto1.getSolarPowerDps();
            for (Map.Entry<Long, Object> entry : solarPowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                solarPowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setSolarPowerDps(solarPowerDps);

            Map<Long, Object> batteryPowerDps = deviceRealtimeDto.getBatteryPowerDps();
            Map<Long, Object> batteryPowerDps1 = deviceRealtimeDto1.getBatteryPowerDps();
            for (Map.Entry<Long, Object> entry : batteryPowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                batteryPowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setBatteryPowerDps(batteryPowerDps);

            Map<Long, Object> gridPowerDps = deviceRealtimeDto.getGridPowerDps();
            Map<Long, Object> gridPowerDps1 = deviceRealtimeDto1.getGridPowerDps();
            for (Map.Entry<Long, Object> entry : gridPowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                gridPowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setGridPowerDps(gridPowerDps);

            Map<Long, Object> homePowerDps = deviceRealtimeDto.getHomePowerDps();
            Map<Long, Object> homePowerDps1 = deviceRealtimeDto1.getHomePowerDps();
            for (Map.Entry<Long, Object> entry : homePowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                homePowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setHomePowerDps(homePowerDps);

            Map<Long, Object> epsPowerDps = deviceRealtimeDto.getEpsPowerDps();
            Map<Long, Object> epsPowerDps1 = deviceRealtimeDto1.getEpsPowerDps();
            for (Map.Entry<Long, Object> entry : epsPowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                epsPowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setEpsPowerDps(epsPowerDps);

            Map<Long, Object> meterPowerDps = deviceRealtimeDto.getMeterPowerDps();
            Map<Long, Object> meterPowerDps1 = deviceRealtimeDto1.getMeterPowerDps();
            for (Map.Entry<Long, Object> entry : meterPowerDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                meterPowerDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setMeterPowerDps(meterPowerDps);

            Map<Long, Object> socDps = deviceRealtimeDto.getSocDps();
            Map<Long, Object> socDps1 = deviceRealtimeDto1.getSocDps();
            for (Map.Entry<Long, Object> entry : socDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                socDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            deviceRealtimeDto.setSocDps(socDps);
            insightDeviceDataDto.setDeviceRealtimeDto(deviceRealtimeDto);
        } else {
            InsightConsumptionDataDto insightConsumptionDataDto = insightDeviceDataDto.getInsightConsumptionDataDto();
            InsightConsumptionDataDto insightConsumptionDataDto1 = insightDeviceDataDto1.getInsightConsumptionDataDto();

            Map<Long, Object> homeEnergyDps = insightConsumptionDataDto.getHomeEnergyDps();
            Map<Long, Object> homeEnergyDps1 = insightConsumptionDataDto1.getHomeEnergyDps();
            for (Map.Entry<Long, Object> entry : homeEnergyDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                homeEnergyDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setHomeEnergyDps(homeEnergyDps);

            Map<Long, Object> solarEnergyDps = insightConsumptionDataDto.getFromSolarDps();
            Map<Long, Object> solarEnergyDps1 = insightConsumptionDataDto1.getFromSolarDps();
            for (Map.Entry<Long, Object> entry : solarEnergyDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                solarEnergyDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setFromSolarDps(solarEnergyDps);

            Map<Long, Object> gridEnergyDps = insightConsumptionDataDto.getFromGridDps();
            Map<Long, Object> gridEnergyDps1 = insightConsumptionDataDto1.getFromGridDps();
            for (Map.Entry<Long, Object> entry : gridEnergyDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                gridEnergyDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setFromGridDps(gridEnergyDps);

            Map<Long, Object> toGridDps = insightConsumptionDataDto.getToGridDps();
            Map<Long, Object> toGridDps1 = insightConsumptionDataDto1.getToGridDps();
            for (Map.Entry<Long, Object> entry : toGridDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                toGridDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setToGridDps(toGridDps);

            Map<Long, Object> batteryEnergyDps = insightConsumptionDataDto.getFromBatteryDps();
            Map<Long, Object> batteryEnergyDps1 = insightConsumptionDataDto1.getFromBatteryDps();
            for (Map.Entry<Long, Object> entry : batteryEnergyDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                batteryEnergyDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setFromBatteryDps(batteryEnergyDps);

            Map<Long, Object> batteryDps = insightConsumptionDataDto.getToBatteryDps();
            Map<Long, Object> batteryDps1 = insightConsumptionDataDto1.getToBatteryDps();
            for (Map.Entry<Long, Object> entry : batteryDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                batteryDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setToBatteryDps(batteryDps);

            Map<Long, Object> epsEnergyDps = insightConsumptionDataDto.getEpsDps();
            Map<Long, Object> epsEnergyDps1 = insightConsumptionDataDto1.getEpsDps();
            for (Map.Entry<Long, Object> entry : epsEnergyDps1.entrySet()) {
                if (entry.getValue() ==  null) {
                    continue;
                }
                epsEnergyDps.merge(entry.getKey(), entry.getValue(), (v1, v2) -> {
                    BigDecimal v1BigDecimal = new BigDecimal(v1.toString());
                    BigDecimal v2BigDecimal = new BigDecimal(v2.toString());
                    return v1BigDecimal.add(v2BigDecimal);
                });
            }
            insightConsumptionDataDto.setEpsDps(epsEnergyDps);

            Map<Long, Object> selfPoweredDps = getSelfPoweredDps(insightConsumptionDataDto);
            insightConsumptionDataDto.setSelfPoweredDps(selfPoweredDps);

            insightDeviceDataDto.setInsightConsumptionDataDto(insightConsumptionDataDto);
        }

        InsightDeviceStatisticsDto deviceStatisticsDto = insightDeviceDataDto.getDeviceStatisticsDto();
        InsightDeviceStatisticsDto deviceStatisticsDto1 = insightDeviceDataDto1.getDeviceStatisticsDto();

        deviceStatisticsDto.setConsumptionEnergy(NumberUtil.add(deviceStatisticsDto.getConsumptionEnergy(), deviceStatisticsDto1.getConsumptionEnergy()));
        deviceStatisticsDto.setFromSolar(NumberUtil.add(deviceStatisticsDto.getFromSolar(), deviceStatisticsDto1.getFromSolar()));
        deviceStatisticsDto.setFromGrid(NumberUtil.add(deviceStatisticsDto.getFromGrid(), deviceStatisticsDto1.getFromGrid()));
        deviceStatisticsDto.setToGrid(NumberUtil.add(deviceStatisticsDto.getToGrid(), deviceStatisticsDto1.getToGrid()));
        deviceStatisticsDto.setFromBattery(NumberUtil.add(deviceStatisticsDto.getFromBattery(), deviceStatisticsDto1.getFromBattery()));
        deviceStatisticsDto.setToBattery(NumberUtil.add(deviceStatisticsDto.getToBattery(), deviceStatisticsDto1.getToBattery()));
        deviceStatisticsDto.setEps(NumberUtil.add(deviceStatisticsDto.getEps(), deviceStatisticsDto1.getEps()));
        deviceStatisticsDto.setSoc(NumberUtil.add(deviceStatisticsDto.getSoc(), deviceStatisticsDto1.getSoc()));

        insightDeviceDataDto.setDeviceStatisticsDto(deviceStatisticsDto);

        if (deviceStatisticsDto.getFromSolar().compareTo(BigDecimal.ZERO) == 0 || deviceStatisticsDto.getConsumptionEnergy().compareTo(BigDecimal.ZERO) == 0) {
            insightDeviceDataDto.setSelfPowered(BigDecimal.ZERO);
        } else {
            BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(deviceStatisticsDto.getFromSolar(), deviceStatisticsDto.getToGrid()), deviceStatisticsDto.getConsumptionEnergy()), 2, RoundingMode.HALF_UP);
            // 如果solarPercent小于等于0，则将其设置为0
            if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                solarPercent = BigDecimal.ZERO;
            }
            BigDecimal percentData = NumberUtil.mul(
                    solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                    new BigDecimal(100)
            );
            insightDeviceDataDto.setSelfPowered(percentData);
        }
    }

    public Map<Long, Object> getSelfPoweredDps(InsightConsumptionDataDto consumptionDataDto) {
        LinkedHashMap<Long, Object> selfPoweredDps = new LinkedHashMap<>(1024);
        Map<Long, Object> fromSolarDps = consumptionDataDto.getFromSolarDps();
        Map<Long, Object> toGridDps = consumptionDataDto.getToGridDps();
        Map<Long, Object> homeEnergyDps = consumptionDataDto.getHomeEnergyDps();

        List<Long> keyList = null == fromSolarDps
                ? ListUtil.empty()
                : fromSolarDps.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(fromSolarDps)) {
            for (Long time : keyList) {
                BigDecimal fromSolar = new BigDecimal(fromSolarDps.getOrDefault(time, "0").toString());
                BigDecimal toGrid = new BigDecimal(toGridDps.getOrDefault(time, "0").toString());
                BigDecimal homeEnergy = new BigDecimal(homeEnergyDps.getOrDefault(time, "0").toString());

                if (fromSolar.compareTo(BigDecimal.ZERO) == 0 || homeEnergy.compareTo(BigDecimal.ZERO) == 0) {
                    selfPoweredDps.put(time, BigDecimal.ZERO);
                } else {
                    BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(fromSolar, toGrid), homeEnergy), 2, RoundingMode.HALF_UP);
                    // 如果solarPercent小于等于0，则将其设置为0
                    if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                        solarPercent = BigDecimal.ZERO;
                    }
                    BigDecimal percentData = NumberUtil.mul(
                            solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                            new BigDecimal(100)
                    );
                    selfPoweredDps.put(time, percentData);
                }

            }
        }

        return selfPoweredDps;
    }

}
