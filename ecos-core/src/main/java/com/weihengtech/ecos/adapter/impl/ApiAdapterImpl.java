package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.ApiAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.enums.BindTypeEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.dao.ClientChargeRecordMapper;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientHomeUserDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.thirdpart.AccountBindInfoDTO;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.OperationUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ApiAdapterImpl implements ApiAdapter {

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;
	@Resource
	private ClientUserService clientUserService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private OssGlobalConfigApi ossGlobalConfigApi;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private ClientCustomizeService clientCustomizeService;
	@Resource
	private V2HomeAdapter v2HomeAdapter;
	@Resource
	private ClientChargeRecordMapper clientChargeRecordMapper;
	@Resource
	private ClientHomeDeviceService clientHomeDeviceService;
	@Resource
	private HubService hubService;
	@Resource
	private ChargeStationService chargeStationService;
	@Resource
	private SinglePlugSocketService singlePlugSocketService;
	@Resource
	private ClientHomeUserService clientHomeUserService;
	@Resource
	private ClientHomeService clientHomeService;

	@Override
	public ClientUserDo getDeviceMasterAccountDo(Long deviceId) {
		MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService.getOne(
				Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
						.eq(MiddleClientUserDeviceDo::getMaster, 1));

		if (middleClientUserDeviceDo != null) {
			return clientUserService.getById(middleClientUserDeviceDo.getUserId());
		}
		return null;
	}

	@Override
	public Map<String, List<String>> getDeviceMasterAndSubAccount(Long deviceId) {

		// Step 1: 创建 Map
		Map<String, List<String>> result = new HashMap<>();

		// Step 2: 准备主账号和子账号的用户名列表
		List<String> masterUsernames = new ArrayList<>();
		List<String> subUsernames = new ArrayList<>();

		// Step 3: 获取 MiddleClientUserDeviceDo 列表
		List<MiddleClientUserDeviceDo> middleClientUserDeviceDoList = middleClientUserDeviceService.list(
				Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getDeviceId, deviceId));

		if (middleClientUserDeviceDoList.size() == 0) {
			result.put("master", masterUsernames);
			result.put("sub", subUsernames);
			return result;
		}

		// Step 4: 收集 userIds
		Set<Long> userIds = middleClientUserDeviceDoList.stream()
				.map(MiddleClientUserDeviceDo::getUserId)
				.collect(Collectors.toSet());

		// Step 5: 一次性查询所有相关的 ClientUserDo
		Map<Long, ClientUserDo> usersMap = clientUserService.listByIds(userIds)
				.stream()
				.collect(Collectors.toMap(ClientUserDo::getId, Function.identity()));

		// Step 6: 填充用户名列表
		for (MiddleClientUserDeviceDo userDevice : middleClientUserDeviceDoList) {
			ClientUserDo user = usersMap.get(userDevice.getUserId());
			if (user != null) {
				if (userDevice.getMaster() == 1) {
					masterUsernames.add(user.getUsername());
				} else {
					subUsernames.add(user.getUsername());
				}
			}
		}

		// Step 7: 返回结果 Map
		result.put("master", masterUsernames);
		result.put("sub", subUsernames);

		return result;
	}



	@Override
	@DSTransactional
	public Integer bindMasterAccount(HybridSinglePhaseDO device, String username) {
		Optional<ClientUserDo> clientUserDo = clientUserService.queryOptionalUserByUsername(username);
		if (!clientUserDo.isPresent()) {
			// 账号不存在
			return 3;
		}
		if (null == device) {
			// 绑定失败
			return 0;
		}
		if (!tuyaDatacenterService.isSameDatacenter(clientUserDo.get().getDatacenterId(), device.getDatacenterId())) {
			return 4;
		}
		OssGlobalConfigBo configBo = ossGlobalConfigApi.getGlobalConfig().getData();
		if (null == configBo) {
			// 数据中心错误
			return 4;
		}
		if (configBo.getAftersales().contains(username)) {
			Boolean success = bindAfterSaleAccount(device.getId(), device.getDeviceName(), username);
			return success ? 1 : 0;
		} else {
			List<MiddleClientUserDeviceDo> bindList = middleClientUserDeviceService.list(
					Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
							.eq(MiddleClientUserDeviceDo::getDeviceId, device.getId())
			);
			// 已有主账号
			if (bindList.parallelStream().anyMatch(b -> b.getMaster() == 1)) return 2;
			// 成功
			if (
					bindList.parallelStream().anyMatch(b -> Objects.equals(b.getUserId(), clientUserDo.get().getId())) ||
					bindUserAndDevice(clientUserDo.get().getId(), device.getId(), 1, device.getDeviceName())
			) return 1;
		}
		return 0;
	}

	@Override
	public synchronized void updateCustomize(ClientCustomizeDo param) {
		doUpdateCustomize(param);
	}

	@Override
	@DSTransactional
	public void unbindAccount(List<Long> deviceIdList) {
		if (CollUtil.isNotEmpty(deviceIdList)) {
			// 校验是否有这个设备
			Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDoMap = hubService.getBatchById(false,deviceIdList)
					.stream()
					.collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

			// 清除设备的配置、清除设备的定时任务
			List<Long> chargeStationIds = hybridSinglePhaseDoMap.entrySet().stream()
					.filter(entry -> entry.getValue().getResourceSeriesId() == 104)
					.map(Map.Entry::getKey)
					.collect(Collectors.toList());
			List<HybridSinglePhaseDO> sockets = hybridSinglePhaseDoMap.values().stream()
					.filter(hybridSinglePhaseDO -> hybridSinglePhaseDO.getResourceSeriesId() == 105)
					.collect(Collectors.toList());
			List<Long> storageIds = hybridSinglePhaseDoMap.entrySet().stream()
					.filter(entry -> entry.getValue().getResourceSeriesId() == 101 || entry.getValue().getResourceSeriesId() == 102)
					.map(Map.Entry::getKey)
					.collect(Collectors.toList());
			if (CollUtil.isNotEmpty(chargeStationIds)) {
				chargeStationService.deleteDevicesConfigAndTask(chargeStationIds);
			}

			if (CollUtil.isNotEmpty(sockets)) {
				singlePlugSocketService.deleteDevicesConfigAndTask(sockets);
			}
			if (CollUtil.isNotEmpty(storageIds)) {
				List<ClientCustomizeDo> list = clientCustomizeService.list(Wrappers.<ClientCustomizeDo>lambdaQuery()
						.in(ClientCustomizeDo::getDeviceId, storageIds));
				if (CollUtil.isNotEmpty(list)) {
					list.forEach(i -> {
						i.setAutoStrategy(0);
						i.setRegion(null);
					});
					clientCustomizeService.updateBatchById(list);
				}
			}

			middleClientUserDeviceService.remove(
					Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().in(MiddleClientUserDeviceDo::getDeviceId, deviceIdList)
			);

			clientHomeDeviceService.remove(
					Wrappers.<ClientHomeDeviceDo>lambdaQuery().in(ClientHomeDeviceDo::getDeviceId, deviceIdList)
			);

		}
	}

    @Override
    public AccountBindInfoDTO getDevicesByAccount(String account) {
		Optional<ClientUserDo> optional = clientUserService.queryOptionalUserByUsername(account);
		if (!optional.isPresent()) {
			return AccountBindInfoDTO.builder().build();
		}
		// 获取绑定设备
		ClientUserDo clientUserDo = optional.get();
		LambdaQueryWrapper<MiddleClientUserDeviceDo> wrapper = Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId());
		List<MiddleClientUserDeviceDo> list = middleClientUserDeviceService.list(wrapper);
		if (CollUtil.isEmpty(list)) {
			return AccountBindInfoDTO.builder().build();
		}
		// 过滤主账号
		List<String> masterList = list.stream()
				.filter(i -> BindTypeEnum.MASTER.getCode().equals(i.getMaster()))
				.map(MiddleClientUserDeviceDo::getDeviceId)
				.map(String :: valueOf)
				.distinct()
				.collect(Collectors.toList());
		// 过滤子账号
		List<String> subList = list.stream()
				.filter(i -> BindTypeEnum.SUB.getCode().equals(i.getMaster()))
				.map(MiddleClientUserDeviceDo::getDeviceId)
				.map(String :: valueOf)
				.distinct()
				.collect(Collectors.toList());
		// 过滤家庭设备
		List<Long> idList = list.stream()
				.map(MiddleClientUserDeviceDo::getDeviceId)
				.collect(Collectors.toList());
		List<ClientHomeDeviceDo> homeDeviceDoList = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
				.in(ClientHomeDeviceDo::getDeviceId, idList));
		List<String> homeDeviceList = homeDeviceDoList.stream()
				.map(ClientHomeDeviceDo::getDeviceId)
				.map(String :: valueOf)
				.distinct()
				.collect(Collectors.toList());
		return AccountBindInfoDTO.builder()
				.masterDeviceList(masterList)
				.subDeviceList(subList)
				.homeDeviceList(homeDeviceList)
				.build();
    }


    @DSTransactional
	private void doUpdateCustomize(ClientCustomizeDo param) {
		ClientCustomizeDo data = clientCustomizeService.lambdaQuery().eq(ClientCustomizeDo::getDeviceId, param.getDeviceId()).one();
		if (null != data) {
			OperationUtil.of(param.getChargeMode()).then(data::setChargeMode);
			OperationUtil.of(param.getBatteryMin()).then(data::setBatteryMin);
			OperationUtil.of(param.getEpsBatteryMin()).then(data::setEpsBatteryMin);
			OperationUtil.of(param.getDischargeToGridFlag()).then(data::setDischargeToGridFlag);
			OperationUtil.of(param.getMaxFeedIn()).then(data::setMaxFeedIn);

			Integer mode = data.getChargeMode();
			val selfPowered = 0;
			val loadShifting = 1;
			val backup = 2;

			switch (mode) {
				case selfPowered:
					OperationUtil.of(param.getBatteryMin()).then(data::setSelfSoc);
					OperationUtil.of(param.getMaxFeedIn()).then(data::setSelfFeedIn);
					break;
				case loadShifting:
					OperationUtil.of(param.getBatteryMin()).then(data::setRegularSoc);
					OperationUtil.of(param.getMaxFeedIn()).then(data::setRegularFeedIn);
					break;
				case backup:
					OperationUtil.of(param.getBatteryMin()).then(data::setBackupSoc);
					OperationUtil.of(param.getMaxFeedIn()).then(data::setBackupFeedIn);
					break;
				default:
					throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
			}

			OperationUtil.of(param.getChargeStartHour1()).then(data::setChargeStartHour1);
			OperationUtil.of(param.getChargeStartMinute1()).then(data::setChargeStartMinute1);
			OperationUtil.of(param.getChargeStartHour2()).then(data::setChargeStartHour2);
			OperationUtil.of(param.getChargeStartMinute2()).then(data::setChargeStartMinute2);
			OperationUtil.of(param.getChargeStartHour3()).then(data::setChargeStartHour3);
			OperationUtil.of(param.getChargeStartMinute3()).then(data::setChargeStartMinute3);
			OperationUtil.of(param.getChargeStartHour4()).then(data::setChargeStartHour4);
			OperationUtil.of(param.getChargeStartMinute4()).then(data::setChargeStartMinute4);
			OperationUtil.of(param.getChargeStartHour5()).then(data::setChargeStartHour5);
			OperationUtil.of(param.getChargeStartMinute5()).then(data::setChargeStartMinute5);
			OperationUtil.of(param.getChargeStartHour6()).then(data::setChargeStartHour6);
			OperationUtil.of(param.getChargeStartMinute6()).then(data::setChargeStartMinute6);
			OperationUtil.of(param.getChargeStartHour7()).then(data::setChargeStartHour7);
			OperationUtil.of(param.getChargeStartMinute7()).then(data::setChargeStartMinute7);
			OperationUtil.of(param.getChargeStartHour8()).then(data::setChargeStartHour8);
			OperationUtil.of(param.getChargeStartMinute8()).then(data::setChargeStartMinute8);
			OperationUtil.of(param.getChargeStartHour9()).then(data::setChargeStartHour9);
			OperationUtil.of(param.getChargeStartMinute9()).then(data::setChargeStartMinute9);
			OperationUtil.of(param.getChargeStartHour10()).then(data::setChargeStartHour10);
			OperationUtil.of(param.getChargeStartMinute10()).then(data::setChargeStartMinute10);
			OperationUtil.of(param.getChargeStartHour11()).then(data::setChargeStartHour11);
			OperationUtil.of(param.getChargeStartMinute11()).then(data::setChargeStartMinute11);
			OperationUtil.of(param.getChargeStartHour12()).then(data::setChargeStartHour12);
			OperationUtil.of(param.getChargeStartMinute12()).then(data::setChargeStartMinute12);

			OperationUtil.of(param.getChargeEndHour1()).then(data::setChargeEndHour1);
			OperationUtil.of(param.getChargeEndMinute1()).then(data::setChargeEndMinute1);
			OperationUtil.of(param.getChargeEndHour2()).then(data::setChargeEndHour2);
			OperationUtil.of(param.getChargeEndMinute2()).then(data::setChargeEndMinute2);
			OperationUtil.of(param.getChargeEndHour3()).then(data::setChargeEndHour3);
			OperationUtil.of(param.getChargeEndMinute3()).then(data::setChargeEndMinute3);
			OperationUtil.of(param.getChargeEndHour4()).then(data::setChargeEndHour4);
			OperationUtil.of(param.getChargeEndMinute4()).then(data::setChargeEndMinute4);
			OperationUtil.of(param.getChargeEndHour5()).then(data::setChargeEndHour5);
			OperationUtil.of(param.getChargeEndMinute5()).then(data::setChargeEndMinute5);
			OperationUtil.of(param.getChargeEndHour6()).then(data::setChargeEndHour6);
			OperationUtil.of(param.getChargeEndMinute6()).then(data::setChargeEndMinute6);
			OperationUtil.of(param.getChargeEndHour7()).then(data::setChargeEndHour7);
			OperationUtil.of(param.getChargeEndMinute7()).then(data::setChargeEndMinute7);
			OperationUtil.of(param.getChargeEndHour8()).then(data::setChargeEndHour8);
			OperationUtil.of(param.getChargeEndMinute8()).then(data::setChargeEndMinute8);
			OperationUtil.of(param.getChargeEndHour9()).then(data::setChargeEndHour9);
			OperationUtil.of(param.getChargeEndMinute9()).then(data::setChargeEndMinute9);
			OperationUtil.of(param.getChargeEndHour10()).then(data::setChargeEndHour10);
			OperationUtil.of(param.getChargeEndMinute10()).then(data::setChargeEndMinute10);
			OperationUtil.of(param.getChargeEndHour11()).then(data::setChargeEndHour11);
			OperationUtil.of(param.getChargeEndMinute11()).then(data::setChargeEndMinute11);
			OperationUtil.of(param.getChargeEndHour12()).then(data::setChargeEndHour12);
			OperationUtil.of(param.getChargeEndMinute12()).then(data::setChargeEndMinute12);

			OperationUtil.of(param.getDischargeStartHour1()).then(data::setDischargeStartHour1);
			OperationUtil.of(param.getDischargeStartMinute1()).then(data::setDischargeStartMinute1);
			OperationUtil.of(param.getDischargeStartHour2()).then(data::setDischargeStartHour2);
			OperationUtil.of(param.getDischargeStartMinute2()).then(data::setDischargeStartMinute2);
			OperationUtil.of(param.getDischargeStartHour3()).then(data::setDischargeStartHour3);
			OperationUtil.of(param.getDischargeStartMinute3()).then(data::setDischargeStartMinute3);
			OperationUtil.of(param.getDischargeStartHour4()).then(data::setDischargeStartHour4);
			OperationUtil.of(param.getDischargeStartMinute4()).then(data::setDischargeStartMinute4);
			OperationUtil.of(param.getDischargeStartHour5()).then(data::setDischargeStartHour5);
			OperationUtil.of(param.getDischargeStartMinute5()).then(data::setDischargeStartMinute5);
			OperationUtil.of(param.getDischargeStartHour6()).then(data::setDischargeStartHour6);
			OperationUtil.of(param.getDischargeStartMinute6()).then(data::setDischargeStartMinute6);
			OperationUtil.of(param.getDischargeStartHour7()).then(data::setDischargeStartHour7);
			OperationUtil.of(param.getDischargeStartMinute7()).then(data::setDischargeStartMinute7);
			OperationUtil.of(param.getDischargeStartHour8()).then(data::setDischargeStartHour8);
			OperationUtil.of(param.getDischargeStartMinute8()).then(data::setDischargeStartMinute8);
			OperationUtil.of(param.getDischargeStartHour9()).then(data::setDischargeStartHour9);
			OperationUtil.of(param.getDischargeStartMinute9()).then(data::setDischargeStartMinute9);
			OperationUtil.of(param.getDischargeStartHour10()).then(data::setDischargeStartHour10);
			OperationUtil.of(param.getDischargeStartMinute10()).then(data::setDischargeStartMinute10);
			OperationUtil.of(param.getDischargeStartHour11()).then(data::setDischargeStartHour11);
			OperationUtil.of(param.getDischargeStartMinute11()).then(data::setDischargeStartMinute11);
			OperationUtil.of(param.getDischargeStartHour12()).then(data::setDischargeStartHour12);
			OperationUtil.of(param.getDischargeStartMinute12()).then(data::setDischargeStartMinute12);

			OperationUtil.of(param.getDischargeEndHour1()).then(data::setDischargeEndHour1);
			OperationUtil.of(param.getDischargeEndMinute1()).then(data::setDischargeEndMinute1);
			OperationUtil.of(param.getDischargeEndHour2()).then(data::setDischargeEndHour2);
			OperationUtil.of(param.getDischargeEndMinute2()).then(data::setDischargeEndMinute2);
			OperationUtil.of(param.getDischargeEndHour3()).then(data::setDischargeEndHour3);
			OperationUtil.of(param.getDischargeEndMinute3()).then(data::setDischargeEndMinute3);
			OperationUtil.of(param.getDischargeEndHour4()).then(data::setDischargeEndHour4);
			OperationUtil.of(param.getDischargeEndMinute4()).then(data::setDischargeEndMinute4);
			OperationUtil.of(param.getDischargeEndHour5()).then(data::setDischargeEndHour5);
			OperationUtil.of(param.getDischargeEndMinute5()).then(data::setDischargeEndMinute5);
			OperationUtil.of(param.getDischargeEndHour6()).then(data::setDischargeEndHour6);
			OperationUtil.of(param.getDischargeEndMinute6()).then(data::setDischargeEndMinute6);
			OperationUtil.of(param.getDischargeEndHour7()).then(data::setDischargeEndHour7);
			OperationUtil.of(param.getDischargeEndMinute7()).then(data::setDischargeEndMinute7);
			OperationUtil.of(param.getDischargeEndHour8()).then(data::setDischargeEndHour8);
			OperationUtil.of(param.getDischargeEndMinute8()).then(data::setDischargeEndMinute8);
			OperationUtil.of(param.getDischargeEndHour9()).then(data::setDischargeEndHour9);
			OperationUtil.of(param.getDischargeEndMinute9()).then(data::setDischargeEndMinute9);
			OperationUtil.of(param.getDischargeEndHour10()).then(data::setDischargeEndHour10);
			OperationUtil.of(param.getDischargeEndMinute10()).then(data::setDischargeEndMinute10);
			OperationUtil.of(param.getDischargeEndHour11()).then(data::setDischargeEndHour11);
			OperationUtil.of(param.getDischargeEndMinute11()).then(data::setDischargeEndMinute11);
			OperationUtil.of(param.getDischargeEndHour12()).then(data::setDischargeEndHour12);
			OperationUtil.of(param.getDischargeEndMinute12()).then(data::setDischargeEndMinute12);

			OperationUtil.of(param.getChargePower1()).then(data::setChargePower1);
			OperationUtil.of(param.getChargePower2()).then(data::setChargePower2);
			OperationUtil.of(param.getChargePower3()).then(data::setChargePower3);
			OperationUtil.of(param.getChargePower4()).then(data::setChargePower4);
			OperationUtil.of(param.getChargePower5()).then(data::setChargePower5);
			OperationUtil.of(param.getChargePower6()).then(data::setChargePower6);
			OperationUtil.of(param.getChargePower7()).then(data::setChargePower7);
			OperationUtil.of(param.getChargePower8()).then(data::setChargePower8);
			OperationUtil.of(param.getChargePower9()).then(data::setChargePower9);
			OperationUtil.of(param.getChargePower10()).then(data::setChargePower10);
			OperationUtil.of(param.getChargePower11()).then(data::setChargePower11);
			OperationUtil.of(param.getChargePower12()).then(data::setChargePower12);

			OperationUtil.of(param.getDischargePower1()).then(data::setDischargePower1);
			OperationUtil.of(param.getDischargePower2()).then(data::setDischargePower2);
			OperationUtil.of(param.getDischargePower3()).then(data::setDischargePower3);
			OperationUtil.of(param.getDischargePower4()).then(data::setDischargePower4);
			OperationUtil.of(param.getDischargePower5()).then(data::setDischargePower5);
			OperationUtil.of(param.getDischargePower6()).then(data::setDischargePower6);
			OperationUtil.of(param.getDischargePower7()).then(data::setDischargePower7);
			OperationUtil.of(param.getDischargePower8()).then(data::setDischargePower8);
			OperationUtil.of(param.getDischargePower9()).then(data::setDischargePower9);
			OperationUtil.of(param.getDischargePower10()).then(data::setDischargePower10);
			OperationUtil.of(param.getDischargePower11()).then(data::setDischargePower11);
			OperationUtil.of(param.getDischargePower12()).then(data::setDischargePower12);

			OperationUtil.of(param.getChargeAbandonPv1()).then(data :: setChargeAbandonPv1);
			OperationUtil.of(param.getChargeAbandonPv2()).then(data :: setChargeAbandonPv2);
			OperationUtil.of(param.getChargeAbandonPv3()).then(data :: setChargeAbandonPv3);
			OperationUtil.of(param.getChargeAbandonPv4()).then(data :: setChargeAbandonPv4);
			OperationUtil.of(param.getChargeAbandonPv5()).then(data :: setChargeAbandonPv5);
			OperationUtil.of(param.getChargeAbandonPv6()).then(data :: setChargeAbandonPv6);
			OperationUtil.of(param.getChargeAbandonPv7()).then(data :: setChargeAbandonPv7);
			OperationUtil.of(param.getChargeAbandonPv8()).then(data :: setChargeAbandonPv8);
			OperationUtil.of(param.getChargeAbandonPv9()).then(data :: setChargeAbandonPv9);
			OperationUtil.of(param.getChargeAbandonPv10()).then(data :: setChargeAbandonPv10);
			OperationUtil.of(param.getChargeAbandonPv11()).then(data :: setChargeAbandonPv11);
			OperationUtil.of(param.getChargeAbandonPv12()).then(data :: setChargeAbandonPv12);
			OperationUtil.of(param.getDischargeAbandonPv1()).then(data :: setDischargeAbandonPv1);
			OperationUtil.of(param.getDischargeAbandonPv2()).then(data :: setDischargeAbandonPv2);
			OperationUtil.of(param.getDischargeAbandonPv3()).then(data :: setDischargeAbandonPv3);
			OperationUtil.of(param.getDischargeAbandonPv4()).then(data :: setDischargeAbandonPv4);
			OperationUtil.of(param.getDischargeAbandonPv5()).then(data :: setDischargeAbandonPv5);
			OperationUtil.of(param.getDischargeAbandonPv6()).then(data :: setDischargeAbandonPv6);
			OperationUtil.of(param.getDischargeAbandonPv7()).then(data :: setDischargeAbandonPv7);
			OperationUtil.of(param.getDischargeAbandonPv8()).then(data :: setDischargeAbandonPv8);
			OperationUtil.of(param.getDischargeAbandonPv9()).then(data :: setDischargeAbandonPv9);
			OperationUtil.of(param.getDischargeAbandonPv10()).then(data :: setDischargeAbandonPv10);
			OperationUtil.of(param.getDischargeAbandonPv11()).then(data :: setDischargeAbandonPv11);
			OperationUtil.of(param.getDischargeAbandonPv12()).then(data :: setDischargeAbandonPv12);

			clientCustomizeService.updateById(data);
		} else {
			data = new ClientCustomizeDo();
			data.setId(snowFlakeUtil.generateId());
			data.setDeviceId(param.getDeviceId());
			data.setChargeMode(Optional.ofNullable(data.getChargeMode()).orElse(0));
			data.setBatteryMin(Optional.ofNullable(data.getBatteryMin()).orElse(10));
			data.setMaxFeedIn(Optional.ofNullable(data.getMaxFeedIn()).orElse(0));
			data.setChargeStartHour1(Optional.ofNullable(data.getChargeStartHour1()).orElse(0));
			data.setChargeStartMinute1(Optional.ofNullable(data.getChargeStartMinute1()).orElse(0));
			data.setChargeEndHour1(Optional.ofNullable(data.getChargeEndHour1()).orElse(0));
			data.setChargeEndMinute1(Optional.ofNullable(data.getChargeEndMinute1()).orElse(0));
			data.setChargeStartHour2(Optional.ofNullable(data.getChargeStartHour2()).orElse(0));
			data.setChargeStartMinute2(Optional.ofNullable(data.getChargeStartMinute2()).orElse(0));
			data.setChargeEndHour2(Optional.ofNullable(data.getChargeEndHour2()).orElse(0));
			data.setChargeEndMinute2(Optional.ofNullable(data.getChargeEndMinute2()).orElse(0));

			data.setChargeStartHour3(Optional.ofNullable(data.getChargeStartHour3()).orElse(0));
			data.setChargeStartMinute3(Optional.ofNullable(data.getChargeStartMinute3()).orElse(0));
			data.setChargeEndHour3(Optional.ofNullable(data.getChargeEndHour3()).orElse(0));
			data.setChargeEndMinute3(Optional.ofNullable(data.getChargeEndMinute3()).orElse(0));

			data.setChargeStartHour4(Optional.ofNullable(data.getChargeStartHour4()).orElse(0));
			data.setChargeStartMinute4(Optional.ofNullable(data.getChargeStartMinute4()).orElse(0));
			data.setChargeEndHour4(Optional.ofNullable(data.getChargeEndHour4()).orElse(0));
			data.setChargeEndMinute4(Optional.ofNullable(data.getChargeEndMinute4()).orElse(0));

			data.setChargeStartHour5(Optional.ofNullable(data.getChargeStartHour5()).orElse(0));
			data.setChargeStartMinute5(Optional.ofNullable(data.getChargeStartMinute5()).orElse(0));
			data.setChargeEndHour5(Optional.ofNullable(data.getChargeEndHour5()).orElse(0));
			data.setChargeEndMinute5(Optional.ofNullable(data.getChargeEndMinute5()).orElse(0));

			data.setChargeStartHour6(Optional.ofNullable(data.getChargeStartHour6()).orElse(0));
			data.setChargeStartMinute6(Optional.ofNullable(data.getChargeStartMinute6()).orElse(0));
			data.setChargeEndHour6(Optional.ofNullable(data.getChargeEndHour6()).orElse(0));
			data.setChargeEndMinute6(Optional.ofNullable(data.getChargeEndMinute6()).orElse(0));

			data.setChargeStartHour7(Optional.ofNullable(data.getChargeStartHour7()).orElse(0));
			data.setChargeStartMinute7(Optional.ofNullable(data.getChargeStartMinute7()).orElse(0));
			data.setChargeEndHour7(Optional.ofNullable(data.getChargeEndHour7()).orElse(0));
			data.setChargeEndMinute7(Optional.ofNullable(data.getChargeEndMinute7()).orElse(0));

			data.setChargeStartHour8(Optional.ofNullable(data.getChargeStartHour8()).orElse(0));
			data.setChargeStartMinute8(Optional.ofNullable(data.getChargeStartMinute8()).orElse(0));
			data.setChargeEndHour8(Optional.ofNullable(data.getChargeEndHour8()).orElse(0));
			data.setChargeEndMinute8(Optional.ofNullable(data.getChargeEndMinute8()).orElse(0));

			data.setChargeStartHour9(Optional.ofNullable(data.getChargeStartHour9()).orElse(0));
			data.setChargeStartMinute9(Optional.ofNullable(data.getChargeStartMinute9()).orElse(0));
			data.setChargeEndHour9(Optional.ofNullable(data.getChargeEndHour9()).orElse(0));
			data.setChargeEndMinute9(Optional.ofNullable(data.getChargeEndMinute9()).orElse(0));

			data.setChargeStartHour10(Optional.ofNullable(data.getChargeStartHour10()).orElse(0));
			data.setChargeStartMinute10(Optional.ofNullable(data.getChargeStartMinute10()).orElse(0));
			data.setChargeEndHour10(Optional.ofNullable(data.getChargeEndHour10()).orElse(0));
			data.setChargeEndMinute10(Optional.ofNullable(data.getChargeEndMinute10()).orElse(0));

			data.setChargeStartHour11(Optional.ofNullable(data.getChargeStartHour11()).orElse(0));
			data.setChargeStartMinute11(Optional.ofNullable(data.getChargeStartMinute11()).orElse(0));
			data.setChargeEndHour11(Optional.ofNullable(data.getChargeEndHour11()).orElse(0));
			data.setChargeEndMinute11(Optional.ofNullable(data.getChargeEndMinute11()).orElse(0));

			data.setChargeStartHour12(Optional.ofNullable(data.getChargeStartHour12()).orElse(0));
			data.setChargeStartMinute12(Optional.ofNullable(data.getChargeStartMinute12()).orElse(0));
			data.setChargeEndHour12(Optional.ofNullable(data.getChargeEndHour12()).orElse(0));
			data.setChargeEndMinute12(Optional.ofNullable(data.getChargeEndMinute12()).orElse(0));
			
			data.setDischargeStartHour1(Optional.ofNullable(data.getDischargeStartHour1()).orElse(0));
			data.setDischargeStartMinute1(Optional.ofNullable(data.getDischargeStartMinute1()).orElse(0));
			data.setDischargeEndHour1(Optional.ofNullable(data.getDischargeEndHour1()).orElse(0));
			data.setDischargeEndMinute1(Optional.ofNullable(data.getDischargeEndMinute1()).orElse(0));
			data.setDischargeStartHour2(Optional.ofNullable(data.getDischargeStartHour2()).orElse(0));
			data.setDischargeStartMinute2(Optional.ofNullable(data.getDischargeStartMinute2()).orElse(0));
			data.setDischargeEndHour2(Optional.ofNullable(data.getDischargeEndHour2()).orElse(0));
			data.setDischargeEndMinute2(Optional.ofNullable(data.getDischargeEndMinute2()).orElse(0));

			data.setDischargeStartHour3(Optional.ofNullable(data.getDischargeStartHour3()).orElse(0));
			data.setDischargeStartMinute3(Optional.ofNullable(data.getDischargeStartMinute3()).orElse(0));
			data.setDischargeEndHour3(Optional.ofNullable(data.getDischargeEndHour3()).orElse(0));
			data.setDischargeEndMinute3(Optional.ofNullable(data.getDischargeEndMinute3()).orElse(0));

			data.setDischargeStartHour4(Optional.ofNullable(data.getDischargeStartHour4()).orElse(0));
			data.setDischargeStartMinute4(Optional.ofNullable(data.getDischargeStartMinute4()).orElse(0));
			data.setDischargeEndHour4(Optional.ofNullable(data.getDischargeEndHour4()).orElse(0));
			data.setDischargeEndMinute4(Optional.ofNullable(data.getDischargeEndMinute4()).orElse(0));

			data.setDischargeStartHour5(Optional.ofNullable(data.getDischargeStartHour5()).orElse(0));
			data.setDischargeStartMinute5(Optional.ofNullable(data.getDischargeStartMinute5()).orElse(0));
			data.setDischargeEndHour5(Optional.ofNullable(data.getDischargeEndHour5()).orElse(0));
			data.setDischargeEndMinute5(Optional.ofNullable(data.getDischargeEndMinute5()).orElse(0));

			data.setDischargeStartHour6(Optional.ofNullable(data.getDischargeStartHour6()).orElse(0));
			data.setDischargeStartMinute6(Optional.ofNullable(data.getDischargeStartMinute6()).orElse(0));
			data.setDischargeEndHour6(Optional.ofNullable(data.getDischargeEndHour6()).orElse(0));
			data.setDischargeEndMinute6(Optional.ofNullable(data.getDischargeEndMinute6()).orElse(0));

			data.setDischargeStartHour7(Optional.ofNullable(data.getDischargeStartHour7()).orElse(0));
			data.setDischargeStartMinute7(Optional.ofNullable(data.getDischargeStartMinute7()).orElse(0));
			data.setDischargeEndHour7(Optional.ofNullable(data.getDischargeEndHour7()).orElse(0));
			data.setDischargeEndMinute7(Optional.ofNullable(data.getDischargeEndMinute7()).orElse(0));

			data.setDischargeStartHour8(Optional.ofNullable(data.getDischargeStartHour8()).orElse(0));
			data.setDischargeStartMinute8(Optional.ofNullable(data.getDischargeStartMinute8()).orElse(0));
			data.setDischargeEndHour8(Optional.ofNullable(data.getDischargeEndHour8()).orElse(0));
			data.setDischargeEndMinute8(Optional.ofNullable(data.getDischargeEndMinute8()).orElse(0));

			data.setDischargeStartHour9(Optional.ofNullable(data.getDischargeStartHour9()).orElse(0));
			data.setDischargeStartMinute9(Optional.ofNullable(data.getDischargeStartMinute9()).orElse(0));
			data.setDischargeEndHour9(Optional.ofNullable(data.getDischargeEndHour9()).orElse(0));
			data.setDischargeEndMinute9(Optional.ofNullable(data.getDischargeEndMinute9()).orElse(0));

			data.setDischargeStartHour10(Optional.ofNullable(data.getDischargeStartHour10()).orElse(0));
			data.setDischargeStartMinute10(Optional.ofNullable(data.getDischargeStartMinute10()).orElse(0));
			data.setDischargeEndHour10(Optional.ofNullable(data.getDischargeEndHour10()).orElse(0));
			data.setDischargeEndMinute10(Optional.ofNullable(data.getDischargeEndMinute10()).orElse(0));

			data.setDischargeStartHour11(Optional.ofNullable(data.getDischargeStartHour11()).orElse(0));
			data.setDischargeStartMinute11(Optional.ofNullable(data.getDischargeStartMinute11()).orElse(0));
			data.setDischargeEndHour11(Optional.ofNullable(data.getDischargeEndHour11()).orElse(0));
			data.setDischargeEndMinute11(Optional.ofNullable(data.getDischargeEndMinute11()).orElse(0));

			data.setDischargeStartHour12(Optional.ofNullable(data.getDischargeStartHour12()).orElse(0));
			data.setDischargeStartMinute12(Optional.ofNullable(data.getDischargeStartMinute12()).orElse(0));
			data.setDischargeEndHour12(Optional.ofNullable(data.getDischargeEndHour12()).orElse(0));
			data.setDischargeEndMinute12(Optional.ofNullable(data.getDischargeEndMinute12()).orElse(0));

			data.setChargePower1(Optional.ofNullable(data.getChargePower1()).orElse(0));
			data.setChargePower2(Optional.ofNullable(data.getChargePower2()).orElse(0));
			data.setChargePower3(Optional.ofNullable(data.getChargePower3()).orElse(0));
			data.setChargePower4(Optional.ofNullable(data.getChargePower4()).orElse(0));
			data.setChargePower5(Optional.ofNullable(data.getChargePower5()).orElse(0));
			data.setChargePower6(Optional.ofNullable(data.getChargePower6()).orElse(0));
			data.setChargePower7(Optional.ofNullable(data.getChargePower7()).orElse(0));
			data.setChargePower8(Optional.ofNullable(data.getChargePower8()).orElse(0));
			data.setChargePower9(Optional.ofNullable(data.getChargePower9()).orElse(0));
			data.setChargePower10(Optional.ofNullable(data.getChargePower10()).orElse(0));
			data.setChargePower11(Optional.ofNullable(data.getChargePower11()).orElse(0));
			data.setChargePower12(Optional.ofNullable(data.getChargePower12()).orElse(0));

			data.setDischargePower1(Optional.ofNullable(data.getDischargePower1()).orElse(0));
			data.setDischargePower2(Optional.ofNullable(data.getDischargePower2()).orElse(0));
			data.setDischargePower3(Optional.ofNullable(data.getDischargePower3()).orElse(0));
			data.setDischargePower4(Optional.ofNullable(data.getDischargePower4()).orElse(0));
			data.setDischargePower5(Optional.ofNullable(data.getDischargePower5()).orElse(0));
			data.setDischargePower6(Optional.ofNullable(data.getDischargePower6()).orElse(0));
			data.setDischargePower7(Optional.ofNullable(data.getDischargePower7()).orElse(0));
			data.setDischargePower8(Optional.ofNullable(data.getDischargePower8()).orElse(0));
			data.setDischargePower9(Optional.ofNullable(data.getDischargePower9()).orElse(0));
			data.setDischargePower10(Optional.ofNullable(data.getDischargePower10()).orElse(0));
			data.setDischargePower11(Optional.ofNullable(data.getDischargePower11()).orElse(0));
			data.setDischargePower12(Optional.ofNullable(data.getDischargePower12()).orElse(0));

			data.setChargeAbandonPv1(Optional.ofNullable(data.getChargeAbandonPv1()).orElse(0));
			data.setChargeAbandonPv2(Optional.ofNullable(data.getChargeAbandonPv2()).orElse(0));
			data.setChargeAbandonPv3(Optional.ofNullable(data.getChargeAbandonPv3()).orElse(0));
			data.setChargeAbandonPv4(Optional.ofNullable(data.getChargeAbandonPv4()).orElse(0));
			data.setChargeAbandonPv5(Optional.ofNullable(data.getChargeAbandonPv5()).orElse(0));
			data.setChargeAbandonPv6(Optional.ofNullable(data.getChargeAbandonPv6()).orElse(0));
			data.setChargeAbandonPv7(Optional.ofNullable(data.getChargeAbandonPv7()).orElse(0));
			data.setChargeAbandonPv8(Optional.ofNullable(data.getChargeAbandonPv8()).orElse(0));
			data.setChargeAbandonPv9(Optional.ofNullable(data.getChargeAbandonPv9()).orElse(0));
			data.setChargeAbandonPv10(Optional.ofNullable(data.getChargeAbandonPv10()).orElse(0));
			data.setChargeAbandonPv11(Optional.ofNullable(data.getChargeAbandonPv11()).orElse(0));
			data.setChargeAbandonPv12(Optional.ofNullable(data.getChargeAbandonPv12()).orElse(0));

			data.setDischargeAbandonPv1(Optional.ofNullable(data.getDischargeAbandonPv1()).orElse(0));
			data.setDischargeAbandonPv2(Optional.ofNullable(data.getDischargeAbandonPv2()).orElse(0));
			data.setDischargeAbandonPv3(Optional.ofNullable(data.getDischargeAbandonPv3()).orElse(0));
			data.setDischargeAbandonPv4(Optional.ofNullable(data.getDischargeAbandonPv4()).orElse(0));
			data.setDischargeAbandonPv5(Optional.ofNullable(data.getDischargeAbandonPv5()).orElse(0));
			data.setDischargeAbandonPv6(Optional.ofNullable(data.getDischargeAbandonPv6()).orElse(0));
			data.setDischargeAbandonPv7(Optional.ofNullable(data.getDischargeAbandonPv7()).orElse(0));
			data.setDischargeAbandonPv8(Optional.ofNullable(data.getDischargeAbandonPv8()).orElse(0));
			data.setDischargeAbandonPv9(Optional.ofNullable(data.getDischargeAbandonPv9()).orElse(0));
			data.setDischargeAbandonPv10(Optional.ofNullable(data.getDischargeAbandonPv10()).orElse(0));
			data.setDischargeAbandonPv11(Optional.ofNullable(data.getDischargeAbandonPv11()).orElse(0));
			data.setDischargeAbandonPv12(Optional.ofNullable(data.getDischargeAbandonPv12()).orElse(0));
			clientCustomizeService.save(data);
		}
	}

	private Boolean bindAfterSaleAccount(Long deviceId, String deviceSn, String username) {
		clientUserService.queryOptionalUserByUsername(username)
						.ifPresent(user ->
							bindUserAndDevice(user.getId(), deviceId, 0, deviceSn)
						);
		return true;
	}


	private Boolean bindUserAndDevice(Long userId, Long deviceId, Integer masterFlag, String deviceSn) {
		if (middleClientUserDeviceService.lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getUserId, userId)
				.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
				.count() > 0) return true;

		long millis = System.currentTimeMillis();
		MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
		middleClientUserDeviceDo.setId(snowFlakeUtil.generateId());
		middleClientUserDeviceDo.setUserId(userId);
		middleClientUserDeviceDo.setDeviceId(deviceId);
		middleClientUserDeviceDo.setCreateTime(millis);
		middleClientUserDeviceDo.setUpdateTime(millis);
		middleClientUserDeviceDo.setWeight(1000);
		middleClientUserDeviceDo.setName(deviceSn);
		middleClientUserDeviceDo.setMaster(masterFlag);

		Boolean aBoolean = middleClientUserDeviceService.save(middleClientUserDeviceDo);

		// 查询该用户是否有家庭，有则把设备添加进家庭中
		List<ClientHomeUserDo> clientHomeUserDos = clientHomeUserService.list(Wrappers.<ClientHomeUserDo>lambdaQuery()
				.eq(ClientHomeUserDo::getUserId, userId)
				.eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));

		if (clientHomeUserDos.size() > 0) {
			List<Long> homeIds = clientHomeUserDos.stream().map(ClientHomeUserDo::getHomeId).collect(Collectors.toList());
			List<ClientHomeDo> homeDoList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
					.in(ClientHomeDo::getId, homeIds));
			if (masterFlag == 1) {
				List<ClientHomeDo> commonHomes = homeDoList.stream().filter(clientHomeDo -> clientHomeDo.getHomeType() == CommonConstants.HOME_COMMON).collect(Collectors.toList());
				if (commonHomes.size() > 0) {
					ClientHomeDo clientHomeDo = commonHomes.get(0);
					if (clientHomeDeviceService.existsOne(String.valueOf(clientHomeDo.getId()), String.valueOf(deviceId))) {
						return true;
					}
					return clientHomeDeviceService.saveRelation(String.valueOf(clientHomeDo.getId()), String.valueOf(deviceId), deviceSn);
				}
			} else {
				List<ClientHomeDo> shareHomes = homeDoList.stream().filter(clientHomeDo -> clientHomeDo.getHomeType() == CommonConstants.HOME_SHARED).collect(Collectors.toList());
				if (shareHomes.size() > 0) {
					ClientHomeDo clientHomeDo = shareHomes.get(0);
					if (clientHomeDeviceService.existsOne(String.valueOf(clientHomeDo.getId()), String.valueOf(deviceId))) {
						return true;
					}
					return clientHomeDeviceService.saveRelation(String.valueOf(clientHomeDo.getId()), String.valueOf(deviceId), deviceSn);
				}
			}
		}

		return aBoolean;
	}
}
