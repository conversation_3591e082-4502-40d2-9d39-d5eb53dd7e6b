package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.google.common.collect.Maps;
import com.jd.platform.async.executor.Async;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.adapter.HomeAdapter;
import com.weihengtech.ecos.adapter.OemAdapter;
import com.weihengtech.ecos.consts.RequestConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.TSDBAggUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.model.bos.app.HistoryHomeBo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.vos.app.home.HomeHistoryVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.worker.database.DatabaseDeltaQueryOneHourPointWorker;
import com.weihengtech.ecos.worker.database.DatabaseLastPointWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OemAdapterImpl implements OemAdapter {

	@Resource
	private HomeAdapter homeAdapter;

	@Resource
	private StrategyService strategyService;

	@Override
	public OemEnergyDto computeOemEnergy(OemEnergyVo oemEnergyVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Pair<ClientUserDo, HybridSinglePhaseDO> pair = homeAdapter.validateDeviceOwner(clientUserDo, oemEnergyVo.getDeviceId());
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
				.chooseTimeSeriesDatabaseService(pair.getValue());

		OemEnergyDto oemEnergyDto = new OemEnergyDto();
		oemEnergyDto.setToBattery(new BigDecimal("0"));
		oemEnergyDto.setFromBattery(new BigDecimal("0"));
		oemEnergyDto.setFromGrid(new BigDecimal("0"));
		oemEnergyDto.setToGrid(new BigDecimal("0"));
		oemEnergyDto.setFromSolar(new BigDecimal("0"));
		oemEnergyDto.setHomeEnergy(new BigDecimal("0"));
		oemEnergyDto.setSelfPowered(new BigDecimal("0"));
		oemEnergyDto.setCycleTimes(0);
		oemEnergyDto.setHomeEnergyDps(Maps.newHashMap());
		oemEnergyDto.setFromSolarDps(Maps.newHashMap());
		oemEnergyDto.setFromBatteryDps(Maps.newHashMap());
		oemEnergyDto.setToBatteryDps(Maps.newHashMap());
		oemEnergyDto.setFromGridDps(Maps.newHashMap());
		oemEnergyDto.setToGridDps(Maps.newHashMap());

		Long start = oemEnergyVo.getStart();
		Long end = oemEnergyVo.getEnd();
		if (start > end) {
			return oemEnergyDto;
		}

		List<String> metricList = ListUtil.toList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
				TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID,
				TsdbMetricsConstants.BAT_E_TOTAL_CHARGE, TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID
		);

		DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), start);
		DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
				ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), end);
		DatabaseDeltaQueryOneHourPointWorker deltaQueryOneHourPointWorker = new DatabaseDeltaQueryOneHourPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(), metricList, start, end);

		WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker1)
				.callback(lastPointWorker1)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
				.worker(lastPointWorker2)
				.callback(lastPointWorker2)
				.param("DatabaseLastPointWorker")
				.build();
		WorkerWrapper<String, Map<String, LinkedHashMap<Long, Object>>> deltaQueryOneHourPointWorkerWrapper = new WorkerWrapper.Builder<String, Map<String, LinkedHashMap<Long, Object>>>()
				.worker(deltaQueryOneHourPointWorker)
				.callback(deltaQueryOneHourPointWorker)
				.param("DatabaseDeltaQueryOneHourPointWorker")
				.build();

		try {
			Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, lastPointWorker1Wrapper, lastPointWorker2Wrapper, deltaQueryOneHourPointWorkerWrapper);
		} catch (ExecutionException | InterruptedException e) {
			log.warn(e.getMessage());
		}

		Map<String, LinkedHashMap<Long, Object>> metricDataMap = TSDBAggUtil.aggregateDeltaQueryHourToDay(deltaQueryOneHourPointWorkerWrapper.getWorkResult().getResult(), pair.getKey().getTimeZone());

		LinkedHashMap<Long, Object> homeEnergyDps = new LinkedHashMap<>(1024);

		if (CollUtil.isNotEmpty(metricDataMap)) {
			LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
			LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);

			if (fromSolarMap2 != null) {
				for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
					Long time = entry.getKey();
					Object value = entry.getValue();
					if (fromSolarMap.containsKey(time)) {
						// 如果键已存在，则将值相加
						BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
						BigDecimal additionalValue = new BigDecimal(value.toString());
						BigDecimal sum = originalValue.add(additionalValue);
						fromSolarMap.put(time, sum);
					} else {
						// 如果键不存在，则添加键值对
						fromSolarMap.put(time, value);
					}
				}
			}

			LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
					.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
			LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
					.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
			LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
			LinkedHashMap<Long, Object> fromGridMap = metricDataMap
					.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);

			List<Long> keyList = null == fromSolarMap
					? ListUtil.empty()
					: fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
					.collect(Collectors.toList());
			BigDecimal totalFromSolar = BigDecimal.ZERO;
			BigDecimal totalFromGrid = BigDecimal.ZERO;
			BigDecimal totalToGrid = BigDecimal.ZERO;
			BigDecimal totalFromBattery = BigDecimal.ZERO;
			BigDecimal totalToBattery = BigDecimal.ZERO;
			BigDecimal totalHomeEnergy = BigDecimal.ZERO;

			if (CollUtil.isNotEmpty(fromSolarMap)) {
				for (Long time : keyList) {
					BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
					fromSolar = fromSolar.signum() == -1 ? new BigDecimal("0") : fromSolar;
					totalFromSolar = NumberUtil.add(totalFromSolar, fromSolar);

					BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
					BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
					totalFromBattery = NumberUtil.add(totalFromBattery, fromBattery);
					totalToBattery = NumberUtil.add(totalToBattery, toBattery);

					BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());
					BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
					totalFromGrid = NumberUtil.add(totalFromGrid, fromGrid);
					totalToGrid = NumberUtil.add(totalToGrid, toGrid);

					BigDecimal homeEnergy = NumberUtil.round(
							NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid, fromBattery), toGrid, toBattery), 2,
							RoundingMode.HALF_UP
					);
					homeEnergyDps.put(time, homeEnergy);
					totalHomeEnergy = NumberUtil.add(totalHomeEnergy, homeEnergy);
				}
			}

			oemEnergyDto.setFromBattery(totalFromBattery);
			oemEnergyDto.setToBattery(totalToBattery);
			oemEnergyDto.setFromGrid(totalFromGrid);
			oemEnergyDto.setToGrid(totalToGrid);
			oemEnergyDto.setFromSolar(totalFromSolar);
			oemEnergyDto.setFromSolarDps(fromSolarMap);
			oemEnergyDto.setFromBatteryDps(fromBatteryMap);
			oemEnergyDto.setToBatteryDps(toBatteryMap);
			oemEnergyDto.setFromGridDps(fromGridMap);
			oemEnergyDto.setToGridDps(toGridMap);

			oemEnergyDto.setHomeEnergyDps(homeEnergyDps);
			oemEnergyDto.setHomeEnergy(NumberUtil.round(totalHomeEnergy, 2, RoundingMode.HALF_UP));

			if (totalFromSolar.compareTo(BigDecimal.ZERO) == 0 || totalHomeEnergy.compareTo(BigDecimal.ZERO) == 0) {
				oemEnergyDto.setSelfPowered(BigDecimal.ZERO);
			} else {
				BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(totalFromSolar, totalHomeEnergy), 2,
						RoundingMode.HALF_UP
				);
				BigDecimal percentData = NumberUtil.mul(
						solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
						new BigDecimal(100)
				);
				oemEnergyDto.setSelfPowered(percentData);
			}

			oemEnergyDto.setCycleTimes(homeAdapter.computeBatteryCycleTimes(lastPointWorker1Wrapper.getWorkResult().getResult(), lastPointWorker2Wrapper.getWorkResult().getResult()));
		}

		return oemEnergyDto;
	}

	@Override
	public OemRealTimePowerDto oemRealTimePower(HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String deviceId = homeNowDeviceRealtimeVo.getDeviceId();
		Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo,deviceId);
		String offset = userPair.getKey().getTimeZone();
		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(userPair.getValue());

		String deviceName = userPair.getValue().getDeviceSn();
		Map<String, LinkedHashMap<Long, Object>> metricMap;
		if (deviceName.startsWith("TH")) {
			metricMap = timeSeriesDatabaseService.graphQuery(
					userPair.getValue().getDeviceSn(), CommonConstants.TH_REALTIME_POWER_V2, TimeUtil.getDayStart(0, offset),
					TimeUtil.getCurrentTime(offset), true
			);
		} else {
			metricMap = timeSeriesDatabaseService.graphQuery(
					userPair.getValue().getDeviceSn(), CommonConstants.SH_REALTIME_POWER_V2, TimeUtil.getDayStart(0, offset),
					TimeUtil.getCurrentTime(offset), true
			);
		}
		HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = homeAdapter.packageNowDeviceRealtimeResult(metricMap);
		OemRealTimePowerDto oemRealTimePowerDto = new OemRealTimePowerDto();
		CglibUtil.copy(homeNowDeviceRealtimeDto, oemRealTimePowerDto);
		oemRealTimePowerDto.setDate(TimeUtil.longTimestampToSerialStringOffsetGMT8(System.currentTimeMillis(), offset, "dd/MM/yyyy"));
		return oemRealTimePowerDto;
	}

	@Override
	public OemHistoryEnergyDto oemHistoryEnergy(OemHistoryEnergyVo oemHistoryEnergyVo) {
		HomeHistoryVo homeHistoryVo = new HomeHistoryVo();
		CglibUtil.copy(oemHistoryEnergyVo, homeHistoryVo);
		Integer periodType = oemHistoryEnergyVo.getPeriodType();
		if (CommonConstants.PERIOD_DAY == periodType) {
			String choiceMonth = oemHistoryEnergyVo.getChoiceMonth();
			String day15OfMonth = choiceMonth + "-15";
			try {
				long l = LocalDateTimeUtil.parse(day15OfMonth, "yyyy-MM-dd").toEpochSecond(ZoneOffset.ofHours(8));
				homeHistoryVo.setTimestamp(l);
			} catch (Exception e) {
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
			}
		}
		HistoryHomeBo historyHomeBo = homeAdapter.queryHistoryHomeBo(homeHistoryVo);
		OemHistoryEnergyDto oemHistoryEnergyDto = new OemHistoryEnergyDto();
		CglibUtil.copy(historyHomeBo, oemHistoryEnergyDto);
		return oemHistoryEnergyDto;
	}
}
