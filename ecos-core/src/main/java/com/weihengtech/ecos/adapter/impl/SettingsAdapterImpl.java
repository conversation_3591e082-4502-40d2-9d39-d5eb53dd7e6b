package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.GuideAdapter;
import com.weihengtech.ecos.adapter.SettingsAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.enums.global.SmsEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.enums.global.AccountClearRecordStateEnum;
import com.weihengtech.ecos.enums.global.DatacenterEnum;
import com.weihengtech.ecos.enums.global.MailModelEnum;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.settings.SettingTransferDTO;
import com.weihengtech.ecos.model.dtos.settings.SettingsBindDeviceListDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsDeviceDetailDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsUserInfoDto;
import com.weihengtech.ecos.model.vos.settings.SettingHelpEmailVo;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.settings.SettingTransferMainVo;
import com.weihengtech.ecos.model.vos.settings.SettingsDeviceDetailUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserPasswordUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserUpdateVo;
import com.weihengtech.ecos.tasks.RunnableTask;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientAccountClearRecordDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dos.TimezoneDo;
import com.weihengtech.ecos.model.dos.TuyaDatacenterDo;
import com.weihengtech.ecos.service.global.CacheService;
import com.weihengtech.ecos.service.global.ClientAccountClearRecordService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.thirdpart.MessageService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.TimezoneService;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.thirdpart.TuyaService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.InitUtil;
import com.weihengtech.ecos.utils.OperationUtil;
import com.weihengtech.ecos.utils.RotUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SettingsAdapterImpl implements SettingsAdapter {

	private static final String CACHE_CLIENT_CLEAR = "{}:CACHE:CLIENT_CLEAR:{}";
	private static final String CODE_CLIENT_CLEAR = "{}:CODE:CLIENT_CLEAR:{}";
	private static final String CACHE_CLIENT_BIND = "{}:CACHE:CLIENT_BIND:{}";
	private static final String CODE_CLIENT_BIND = "{}:CODE:CLIENT_BIND:{}";

	@Value("${spring.profiles.active}")
	private String profile;
	@Resource
	private GuideAdapter guideAdapter;
	@Resource
	private ClientUserService clientUserService;
	@Resource
	private TimezoneService timezoneService;
	@Resource
	private PasswordEncoder passwordEncoder;
	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private MessageService messageService;
	@Resource
	private CacheService cacheService;
	@Resource
	private ClientAccountClearRecordService clientAccountClearRecordService;
	@Resource
	private OssGlobalConfigApi ossGlobalConfigApi;
	@Resource
	private EcosIotApi ecosIotApi;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private HubService hubService;
	@Resource
	private TuyaService tuyaService;

	@Override
	@DSTransactional
	public void updateUserInfo(SettingsUserUpdateVo settingsUserUpdateVo) {
		Optional<ClientUserDo> optionalClientUserDo = clientUserService.queryOptionalUserByUsername(
				String.valueOf(SecurityContextHolder.getContext().getAuthentication().getPrincipal()));
		if (optionalClientUserDo.isPresent()) {
			ClientUserDo clientUserDo = optionalClientUserDo.get();

			OperationUtil.of(settingsUserUpdateVo.getTimeZoneId()).then(timeZoneId -> {
				TimezoneDo timezoneDo = timezoneService.getById(timeZoneId);
				if (timezoneDo == null) {
					log.warn("时区id不存在");
					throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
				}
				clientUserDo.setTimeZoneId(timezoneDo.getId());
				clientUserDo.setTimeZone(timezoneDo.getTimezone());
			});
			OperationUtil.of(settingsUserUpdateVo.getNickname()).then(nickname -> {
				if (nickname != null) {
					clientUserDo.setNickname(nickname);
				}
			});
			clientUserDo.setUpdateTime(System.currentTimeMillis());
			try {
				clientUserService.updateById(clientUserDo);
			} catch (Exception e) {
				throw new EcosException(EcosExceptionEnum.INVALID_DATA);
			}
		} else {
			log.warn("用户不存在");
			throw new EcosException(EcosExceptionEnum.INVALID_DATA);
		}
	}

	@Override
	public SettingsUserInfoDto queryUserInfo() {
		String username = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		ClientUserDo clientUserDo = clientUserService.queryOptionalUserByUsername(username).orElseThrow(() -> {
			log.warn("账号不存在 {}", username);
			return new EcosException(EcosExceptionEnum.INVALID_DATA);
		});

		SettingsUserInfoDto settingsUserInfoDto = new SettingsUserInfoDto();
		CglibUtil.copy(clientUserDo, settingsUserInfoDto);

		OperationUtil.of(timezoneService.getById(clientUserDo.getTimeZoneId())).ifPresentOrElseThrow(timezoneDo -> {
			settingsUserInfoDto.setTimeZoneId(String.valueOf(timezoneDo.getId()));
			settingsUserInfoDto.setTimeZone(TimeUtil.getLightingTimezone(timezoneDo.getTimezone()));
			settingsUserInfoDto.setTimezoneName(timezoneService.getTimezoneName(timezoneDo));
		}, () -> {
			log.warn("时区id不存在");
			return new EcosException(EcosExceptionEnum.INVALID_DATA);
		});
		OperationUtil.of(clientUserDo.getDatacenterId())
				.ifPresentOrElse(
						datacenterId -> {
							TuyaDatacenterDo tuyaDatacenterDo = tuyaDatacenterService.getById(datacenterId);
							if (null == tuyaDatacenterDo) {
								throw new EcosException(EcosExceptionEnum.INVALID_DATA);
							}
							settingsUserInfoDto.setDatacenter(tuyaDatacenterDo.getDatacenter());
							settingsUserInfoDto.setDatacenterPhoneCode(tuyaDatacenterDo.getPhoneCode());
							settingsUserInfoDto.setDatacenterHost(DatacenterEnum.getDatacenterHost(DatacenterEnum.valueOf(tuyaDatacenterDo.getDatacenter()), profile));
						},
						() -> {
							settingsUserInfoDto.setDatacenter(DatacenterEnum.CN.name());
							settingsUserInfoDto.setDatacenterPhoneCode(86);
							settingsUserInfoDto.setDatacenterHost(DatacenterEnum.getDatacenterHost(DatacenterEnum.CN, profile));
						}
				);

		return settingsUserInfoDto;
	}

	@Override
	@DSTransactional
	public void updateUserPassword(SettingsUserPasswordUpdateVo settingsUserPasswordUpdateVo) {
		String username = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		ClientUserDo clientUserDo = clientUserService.queryOptionalUserByUsername(username)
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.INVALID_DATA));
		if (!passwordEncoder.matches(settingsUserPasswordUpdateVo.getOriginalPassword(), clientUserDo.getPassword())) {
			log.warn("原始密码校验不通过");
			throw new EcosException(EcosExceptionEnum.ORIGINAL_PASSWORD_ERROR);
		}
		guideAdapter.checkPasswordStrength(settingsUserPasswordUpdateVo.getTargetPassword());
		clientUserDo.setPassword(passwordEncoder.encode(settingsUserPasswordUpdateVo.getTargetPassword()));
		clientUserDo.setUpdateTime(System.currentTimeMillis());
		ActionFlagUtil.assertTrue(clientUserService.updateById(clientUserDo));
	}

	@Override
	public List<SettingsBindDeviceListDto> queryBindDeviceList() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		List<MiddleClientUserDeviceDo> middleClientUserDeviceDoList = queryUserBindDevice(clientUserDo.getId());
		// 提取设备ID列表
		List<Long> deviceIdList = middleClientUserDeviceDoList.stream().map(MiddleClientUserDeviceDo::getDeviceId).collect(Collectors.toList());

		// 一次性获取所有设备详情
		Map<Long, HybridSinglePhaseDO> hybridSinglePhaseDOMap = hubService.getBatchById(false, deviceIdList)
				.stream()
				.collect(Collectors.toMap(HybridSinglePhaseDO::getId, Function.identity()));

		// 获取代理ID列表
		Map<String, String> agentIdMap = hubService.getAgentsByIds(deviceIdList)
				.stream()
				.collect(Collectors.toMap(
						BindInfoDTO::getResourceId,
						BindInfoDTO::getUserId,
						(existing, replacement) -> existing // 保留第一个agentId，忽略后续的
				));
		return middleClientUserDeviceDoList.stream().map(middle -> {
					HybridSinglePhaseDO hybridSinglePhaseDO = hybridSinglePhaseDOMap.get(middle.getDeviceId());

					// 如果 hybridSinglePhaseDO 为 null，意味着设备不存在或获取设备时发生错误
					if (hybridSinglePhaseDO == null) {
						log.warn("设备不存在或无法获取: deviceId=" + middle.getDeviceId());
						return null;
					}
			SettingsBindDeviceListDto settingsBindDeviceListDto = new SettingsBindDeviceListDto();
			settingsBindDeviceListDto.setDeviceId(String.valueOf(middle.getDeviceId()));
			settingsBindDeviceListDto.setDeviceAliasName(middle.getName());
			settingsBindDeviceListDto.setStatus(hybridSinglePhaseDO.getState());
			settingsBindDeviceListDto.setSeriesId(1);
			settingsBindDeviceListDto.setDeviceSn(hybridSinglePhaseDO.getDeviceName());
			settingsBindDeviceListDto.setAgentId(agentIdMap.getOrDefault(hybridSinglePhaseDO.getId(), ""));
			return settingsBindDeviceListDto;
		})
				.filter(Objects::nonNull) // 这将会过滤掉所有的 null 值
				.collect(Collectors.toList());
	}

	@Override
	public SettingsDeviceDetailDto queryDeviceDetail(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Long handlerUserId = clientUserDo.getId();
		SettingsDeviceDetailDto settingsDeviceDetailDto = new SettingsDeviceDetailDto();

		getOptionalBindDevice(deviceId, clientUserDo.getId())
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> settingsDeviceDetailDto
						.setDeviceAliasName(middleClientUserDeviceDo.getName()), () -> {
					log.warn("未绑定设备");
					return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
		DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
		OssGlobalConfigBo configBo = globalConfig.getData();
		List<SettingsDeviceDetailDto.BindAccount> bindAccountList = getBindAccountList(deviceId, handlerUserId, configBo, clientUserDo, settingsDeviceDetailDto);

		settingsDeviceDetailDto.setAccountList(bindAccountList);
		settingsDeviceDetailDto.setDeviceId(deviceId);

		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));

		String deviceModel = hybridSinglePhaseDO.getDeviceModel();

		if (deviceModel != null && deviceModel.contains("Wifo_pro")) {
			deviceModel = "";
		}
		settingsDeviceDetailDto.setDeviceModel(deviceModel);
		settingsDeviceDetailDto.setType(hybridSinglePhaseDO.getDataSource());
		settingsDeviceDetailDto.setDeviceSn(hybridSinglePhaseDO.getDeviceName());
		settingsDeviceDetailDto.setWifiSn(hybridSinglePhaseDO.getWifiSn());
		return settingsDeviceDetailDto;
	}

	@Override
	@DSTransactional
	public void updateDeviceDetail(SettingsDeviceDetailUpdateVo settingsDeviceDetailUpdateVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		getOptionalBindDevice(settingsDeviceDetailUpdateVo.getDeviceId(), clientUserDo.getId())
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> {
					middleClientUserDeviceDo.setName(settingsDeviceDetailUpdateVo.getDeviceAliasName());
					middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
					ActionFlagUtil.assertTrue(middleClientUserDeviceService.updateById(middleClientUserDeviceDo));
				}, () -> {
					log.warn("未绑定设备");
					return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
	}

	@Override
	public void resetDevice(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Long handlerUserId = clientUserDo.getId();

		getOptionalBindDevice(deviceId, handlerUserId)
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> {
					HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));
					Boolean result = ecosIotApi.resetDevice(hybridSinglePhaseDO.getWifiSn(),"1");
					if (!result) {
						throw new EcosException(EcosExceptionEnum.CHANGE_DEVICE_CONFIG_ERROR);
					}
				}, () -> new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE));
	}

	@Override
	@DSTransactional
	public void removeDeviceBindAccount(SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Long handlerUserId = clientUserDo.getId();
		Long wannaDelAccountId = Long.parseLong(settingRemoveDeviceBindAccountVo.getAccountId());

		getOptionalBindDevice(settingRemoveDeviceBindAccountVo.getDeviceId(), handlerUserId)
				.ifPresentOrElseThrow(middleClientUserDeviceDo -> {
					int master = middleClientUserDeviceDo.getMaster();
					if (1 == master) {
						if (Objects.equals(handlerUserId, wannaDelAccountId)) {
							middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(
									MiddleClientUserDeviceDo::getDeviceId,
									Long.parseLong(settingRemoveDeviceBindAccountVo.getDeviceId())
							));
						} else {
							middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
									.eq(
											MiddleClientUserDeviceDo::getDeviceId,
											Long.parseLong(settingRemoveDeviceBindAccountVo.getDeviceId())
									)
									.eq(MiddleClientUserDeviceDo::getUserId, wannaDelAccountId));
						}
					} else {
						throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
					}
				}, () -> {
					log.warn("未绑定设备");
					return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
	}

	@Override
	public String qrCodeEncryption(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Long userId = clientUserDo.getId();
		Long deviceLongId = Long.parseLong(deviceId);

		MiddleClientUserDeviceDo middleDo = middleClientUserDeviceService
				.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getUserId, userId)
						.eq(MiddleClientUserDeviceDo::getDeviceId, deviceLongId));

		if (null == middleDo || 1 != middleDo.getMaster()) {
			log.warn("非主账号");
			throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
		long timestamp = System.currentTimeMillis() / 1000 / 60;
		int offset = new Random().nextInt(9);
		return offset + RotUtil.encode(timestamp + deviceId, offset);
	}

	@Override
	public String qrCodeEncryptionTransferMain(SettingTransferMainVo dto) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		Long userId = clientUserDo.getId();
		Long deviceLongId = Long.valueOf(dto.getDeviceId());

		MiddleClientUserDeviceDo middleDo = middleClientUserDeviceService
				.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getUserId, userId)
						.eq(MiddleClientUserDeviceDo::getDeviceId, deviceLongId));

		if (null == middleDo || 1 != middleDo.getMaster()) {
			log.warn("非主账号");
			throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
		long timestamp = System.currentTimeMillis() / 1000 / 60;
		int offset = new Random().nextInt(9);
		return offset + RotUtil.encode(timestamp + JSONObject.toJSONString(dto), offset);
	}

	@Override
	@DSTransactional
	public void bindSlaveAccount(String code) {
		String deviceIdWithRandomStr = RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
				.orElseThrow(() -> {
					log.warn("二维码解密失败");
					return new EcosException(EcosExceptionEnum.INVALID_PARAM);
				});
		String deviceId;
		String minute;
		try {
			minute = deviceIdWithRandomStr.substring(0, 8);
			deviceId = deviceIdWithRandomStr.substring(8);
		} catch (Exception e) {
			log.warn(e.getMessage());
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		long currMinute = System.currentTimeMillis() / 1000 / 60;
		if ((currMinute - Long.parseLong(minute)) > 30) {
			log.warn("超过绑定时效");
			throw new EcosException(EcosExceptionEnum.QR_CODE_EXPIRED);
		}

		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));

		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		if (!tuyaDatacenterService.isSameDatacenter(clientUserDo.getDatacenterId(), hybridSinglePhaseDO.getDatacenterId())) {
			throw new EcosException(EcosExceptionEnum.INVALID_DATACENTER);
		}

		MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService
				.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId))
						.eq(MiddleClientUserDeviceDo::getMaster, 1));
		if (null == middleClientUserDeviceDo) {
			log.warn("未绑定设备");
			throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
		MiddleClientUserDeviceDo boundDeviceDo = middleClientUserDeviceService
				.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId))
						.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId()));
		if (null != boundDeviceDo) {
			log.warn("已经绑定过设备");
			throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
		}
		MiddleClientUserDeviceDo newMiddleDo = new MiddleClientUserDeviceDo();
		newMiddleDo.setId(snowFlakeUtil.generateId());
		newMiddleDo.setUserId(clientUserDo.getId());
		newMiddleDo.setDeviceId(Long.parseLong(deviceId));
		newMiddleDo.setCreateTime(System.currentTimeMillis());
		newMiddleDo.setUpdateTime(System.currentTimeMillis());
		newMiddleDo.setWeight(0);
		newMiddleDo.setName(hybridSinglePhaseDO.getDeviceName());
		newMiddleDo.setMaster(0);
		ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(newMiddleDo));
	}

	@Override
	public void delAccountEmail() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String email = clientUserDo.getEmail();
		String cacheKey = StrUtil.format(CACHE_CLIENT_CLEAR, "EMAIL", email);
		String codeKey = StrUtil.format(CODE_CLIENT_CLEAR, "EMAIL", email);
		messageService.sendEmailWithCache(email, codeKey, cacheKey, MailModelEnum.CLIENT_DEL_ACCOUNT);
	}

	@Override
	public void delAccountPhone() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String phone = clientUserDo.getPhone();
		if (StrUtil.isNotBlank(phone)) {
			String cacheKey = StrUtil.format(CACHE_CLIENT_CLEAR, "PHONE", phone);
			String codeKey = StrUtil.format(CODE_CLIENT_CLEAR, "PHONE", phone);
			messageService.sendPhoneWithCache(SmsEnum.CLEAR, phone, codeKey, cacheKey);
		}
	}

    @Override
    public void sendBindEmail(String email) {
		clientUserService.queryOptionalUserByEmail(email).ifPresent(user -> {throw new EcosException(EcosExceptionEnum.EMAIL_ALREADY_BOUND);});
		String cacheKey = StrUtil.format(CACHE_CLIENT_BIND, "EMAIL", email);
		String codeKey = StrUtil.format(CODE_CLIENT_BIND, "EMAIL", email);
		messageService.sendEmailWithCache(email, codeKey, cacheKey, MailModelEnum.CLIENT_BIND_EMAIL);
    }

	@Override
	public void sendBindPhone(String phone) {
		clientUserService.queryOptionalUserByPhone(phone).ifPresent(user -> {throw new EcosException(EcosExceptionEnum.PHONE_ALREADY_BOUND);});
		String cacheKey = StrUtil.format(CACHE_CLIENT_BIND, "PHONE", phone);
		String codeKey = StrUtil.format(CODE_CLIENT_BIND, "PHONE", phone);
		messageService.sendPhoneWithCache(SmsEnum.COMMON, phone, codeKey, cacheKey);
	}

	@Override
	@DSTransactional
	public void userBindEmail(String email, String code) {
		String codeKey = StrUtil.format(CODE_CLIENT_BIND, "EMAIL", email);
		Optional<String> opCode = cacheService.getStr(codeKey);
		if (opCode.isPresent() && opCode.get().equals(code)) {
			ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
			clientUserDo.setEmail(email);
			clientUserService.updateById(clientUserDo);
		} else {
			throw new EcosException(EcosExceptionEnum.VALID_CODE_NOT_FIT);
		}
	}

	@Override
	@DSTransactional
	public void userBindPhone(String phone, String code) {
		String codeKey = StrUtil.format(CODE_CLIENT_BIND, "PHONE", phone);
		Optional<String> opCode = cacheService.getStr(codeKey);
		if (opCode.isPresent() && opCode.get().equals(code)) {
			ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
			clientUserDo.setPhone(phone);
			clientUserService.updateById(clientUserDo);
		} else {
			throw new EcosException(EcosExceptionEnum.VALID_CODE_NOT_FIT);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deviceTransfer(String code) {
		// 校验二维码合法
		String codeStr = checkQrCode(code);
		// 校验二维码是否过期
		String paramStr = checkQrTime(codeStr, 10);
		// 解析二维码数据
		SettingTransferDTO param = JSONObject.parseObject(paramStr, SettingTransferDTO.class);
		if (CollUtil.isEmpty(param.getDeviceIds())) {
			log.info("deviceIds is empty, param is {}", JSONUtil.toJsonStr(param));
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		List<HybridSinglePhaseDO> deviceInfoList = hubService.getBatchById(false, param.getDeviceIds());
		if (CollUtil.isEmpty(deviceInfoList)) {
			log.info("deviceInfo is empty, param is {}", JSONUtil.toJsonStr(param));
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		ClientUserDo userInfo = SecurityUtil.getClientUserDo();
		// 校验是否在同一数据中心
		checkSameDatacenter(userInfo, deviceInfoList);
		// 校验是否已经绑定过
		List<MiddleClientUserDeviceDo> dealList = checkAlreadyBind(deviceInfoList, userInfo.getId());
		// 执行绑定过程
		doBindAction(userInfo, dealList, deviceInfoList, param);
	}

	/**
	 * 执行转移过程
	 *
	 * @param userInfo 用户
	 * @param boundList 已绑定主账号列表
	 * @param deviceList 设备列表
	 * @param param 入参
	 */
	private void doBindAction(ClientUserDo userInfo, List<MiddleClientUserDeviceDo> boundList,
							  List<HybridSinglePhaseDO> deviceList,
							  SettingTransferDTO param) {
		// 1、绑定设备用户关系
		boolean res = middleClientUserDeviceService.saveMasterBind(userInfo.getId(), boundList, deviceList);
		ActionFlagUtil.assertTrue(res);
		// 2、转移tuya云的家庭
		tuyaService.transTuyaHome(userInfo, param);
		// 3、更新系统转移时间
		hubService.updateSysTransTime(param.getSystemInfoId(), null);
	}

	/**
	 * 校验二维码合法性
	 *
	 * @param code 二维码
	 * @return 解析结果
	 */
	private String checkQrCode(String code) {
		return RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
				.orElseThrow(() -> {
					log.warn("二维码解密失败");
					return new EcosException(EcosExceptionEnum.INVALID_PARAM);
				});
	}

	/**
	 * 校验二维码是否过期
	 *
	 * @param codeStr 二维码解析结果
	 * @param expiredTime 过期时间
	 * @return 反序列化结果
	 */
	private String checkQrTime(String codeStr, int expiredTime) {
		String minute;
		try {
			minute = codeStr.substring(0, 8);
		} catch (Exception e) {
			log.warn(e.getMessage());
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		long currMinute = System.currentTimeMillis() / 1000 / 60;
		if ((currMinute - Long.parseLong(minute)) > expiredTime) {
			log.warn("超过转移时效");
			throw new EcosException(EcosExceptionEnum.QR_CODE_EXPIRED);
		}
		return codeStr.substring(8);
	}

	/**
	 * 校验是否是同一数据中心
	 *
	 * @param userInfo 用户信息
	 * @param deviceInfoList 设备信息
	 */
	private void checkSameDatacenter(ClientUserDo userInfo, List<HybridSinglePhaseDO> deviceInfoList) {
		for (HybridSinglePhaseDO deviceInfo : deviceInfoList) {
			if (!tuyaDatacenterService.isSameDatacenter(userInfo.getDatacenterId(), deviceInfo.getDatacenterId())) {
				throw new EcosException(EcosExceptionEnum.INVALID_DATACENTER);
			}
		}
	}

	/**
	 * 校验是否已绑定过
	 *
	 * @param deviceInfoList 设备列表
	 * @param userId 用户id
	 * @return 已绑定的主账号关系
	 */
	private List<MiddleClientUserDeviceDo> checkAlreadyBind(List<HybridSinglePhaseDO> deviceInfoList, Long userId) {
		List<Long> deviceIds = deviceInfoList.stream()
				.map(HybridSinglePhaseDO::getId)
				.collect(Collectors.toList());
		List<MiddleClientUserDeviceDo> boundDeviceList = middleClientUserDeviceService
				.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.in(MiddleClientUserDeviceDo::getDeviceId, deviceIds)
						.eq(MiddleClientUserDeviceDo::getMaster, 1));
		if (CollUtil.isEmpty(boundDeviceList)) {
			return boundDeviceList;
		}
		// 存在其他主账号已绑定其中设备，直接抛出异常
		boolean existsOtherMaster = boundDeviceList.stream()
				.anyMatch(i -> !userId.equals(i.getUserId()));
		if (existsOtherMaster) {
			throw new EcosException(EcosExceptionEnum.DEVICE_ALREADY_BOUND);
		}
		return boundDeviceList;
	}

	@Override
	public synchronized void delAccount(String code) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String emailCodeKey = StrUtil.format(CODE_CLIENT_CLEAR, "EMAIL", clientUserDo.getEmail());
		String phoneCodeKey = StrUtil.format(CODE_CLIENT_CLEAR, "PHONE", clientUserDo.getPhone());
		Optional<String> emailCode = cacheService.getStr(emailCodeKey);
		Optional<String> phoneCode = cacheService.getStr(phoneCodeKey);
		if (emailCode.isPresent() && emailCode.get().equals(code)) {
			cacheService.delKey(emailCodeKey);
			ClientAccountClearRecordDo clientAccountClearRecordDo = saveClientAccountClearRecord(clientUserDo.getId());
			InitUtil.SCHEDULED_EXECUTOR.schedule(
					new RunnableTask.ClearAccountTask(clientAccountClearRecordDo.getId()), 1, TimeUnit.MILLISECONDS
			);
		} else if (phoneCode.isPresent() && phoneCode.get().equals(code)) {
			cacheService.delKey(phoneCodeKey);
			ClientAccountClearRecordDo clientAccountClearRecordDo = saveClientAccountClearRecord(clientUserDo.getId());
			InitUtil.SCHEDULED_EXECUTOR.schedule(
					new RunnableTask.ClearAccountTask(clientAccountClearRecordDo.getId()), 1, TimeUnit.MILLISECONDS
			);
		} else {
			throw new EcosException(EcosExceptionEnum.VALID_CODE_NOT_FIT);
		}
	}

	@Override
	public void sendHelpEmail(SettingHelpEmailVo settingHelpEmailVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
		Integer code = globalConfig.getCode();
		if (HttpStatus.OK.value() != code) {
			log.warn("oss config 请求失败");
			throw new EcosException(EcosExceptionEnum.SEND_EMAIL_ERROR);
		}
		messageService.sendEmail(globalConfig.getData().getEcos().getFeedbackEmail(), MailModelEnum.HELP_EMAIL,
				clientUserDo.getUsername(), settingHelpEmailVo.getContent()
		);
	}

	@DSTransactional
	private ClientAccountClearRecordDo saveClientAccountClearRecord(long userId) {
		long recordId = snowFlakeUtil.generateId();
		Long clearTime = System.currentTimeMillis() + 5000L;
		ClientAccountClearRecordDo clientAccountClearRecordDo = new ClientAccountClearRecordDo();
		clientAccountClearRecordDo.setId(recordId);
		clientAccountClearRecordDo.setUserId(userId);
		clientAccountClearRecordDo.setCreateTime(System.currentTimeMillis());
		clientAccountClearRecordDo.setClearTime(clearTime);
		clientAccountClearRecordDo.setState(AccountClearRecordStateEnum.ING.getCode());
		ActionFlagUtil.assertTrue(clientAccountClearRecordService.save(clientAccountClearRecordDo));
		return clientAccountClearRecordDo;
	}

	private OperationUtil<MiddleClientUserDeviceDo> getOptionalBindDevice(String deviceId, Long userId) {
		return OperationUtil.of(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
				.eq(MiddleClientUserDeviceDo::getUserId, userId)));
	}

	private List<MiddleClientUserDeviceDo> queryUserBindDevice(Long userId) {
		return middleClientUserDeviceService
				.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getUserId, userId));
	}

	private List<SettingsDeviceDetailDto.BindAccount> getBindAccountList(String deviceId, Long handlerUserId, OssGlobalConfigBo configBo, ClientUserDo clientUserDo, SettingsDeviceDetailDto settingsDeviceDetailDto) {
		List<MiddleClientUserDeviceDo> deviceList = middleClientUserDeviceService
				.list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
						.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
						.orderByDesc(MiddleClientUserDeviceDo::getCreateTime));

		// Extract user IDs and perform a batch query
		List<Long> userIds = deviceList.stream()
				.map(MiddleClientUserDeviceDo::getUserId)
				.distinct()
				.collect(Collectors.toList());

		Map<Long, ClientUserDo> userCache = clientUserService.listByIds(userIds).stream()
				.collect(Collectors.toMap(ClientUserDo::getId, userDo -> userDo));

		return deviceList.stream()
				.filter(middleClientUserDeviceDo -> shouldIncludeDevice(middleClientUserDeviceDo, handlerUserId, configBo, userCache, settingsDeviceDetailDto))
				.map(middleClientUserDeviceDo -> createBindAccount(middleClientUserDeviceDo, clientUserDo, userCache))
				.collect(Collectors.toList());
	}

	private boolean shouldIncludeDevice(MiddleClientUserDeviceDo middleClientUserDeviceDo, Long handlerUserId, OssGlobalConfigBo configBo, Map<Long, ClientUserDo> userCache, SettingsDeviceDetailDto settingsDeviceDetailDto) {
		Long userId = middleClientUserDeviceDo.getUserId();
		ClientUserDo userDo = userCache.get(userId);

		if (configBo != null && configBo.getAftersales().contains(userDo.getUsername())) {
			return false;
		}
		if (!userId.equals(handlerUserId)) {
			return true;
		} else {
			settingsDeviceDetailDto.setMaster(middleClientUserDeviceDo.getMaster());
			return false;
		}
	}

	private SettingsDeviceDetailDto.BindAccount createBindAccount(MiddleClientUserDeviceDo middleClientUserDeviceDo, ClientUserDo clientUserDo, Map<Long, ClientUserDo> userCache) {
		SettingsDeviceDetailDto.BindAccount bindAccount = new SettingsDeviceDetailDto.BindAccount();
		bindAccount.setSeriesId(1);

		ClientUserDo bindUser = userCache.get(middleClientUserDeviceDo.getUserId());
		bindAccount.setAccount(bindUser.getUsername());
		bindAccount.setBindTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(
				middleClientUserDeviceDo.getCreateTime(), clientUserDo.getTimeZone()));
		bindAccount.setMaster(middleClientUserDeviceDo.getMaster());
		bindAccount.setAccountId(String.valueOf(middleClientUserDeviceDo.getUserId()));

		return bindAccount;
	}
}
