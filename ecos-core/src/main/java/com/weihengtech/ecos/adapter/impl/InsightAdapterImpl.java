package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.InsightAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyAvgStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.model.dos.ClientEnergyNotifyDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;
import com.weihengtech.ecos.service.app.ClientEnergyNotifyService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InsightAdapterImpl implements InsightAdapter {

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;

	@Resource
	private StrategyService strategyService;

	@Resource
	private ClientEnergyNotifyService clientEnergyNotifyService;

	@Resource
	private SnowFlakeUtil snowFlakeUtil;

	@Resource
	private HubService hubService;

	@Resource
	private V2HomeAdapter v2HomeAdapter;

	private InsightDeviceEnergyAvgStatisticsDto computeDeviceEnergy(
			HybridSinglePhaseDO hybridSinglePhaseDO, String offset, Integer offsetDay
	) {

		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
		Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(
				hybridSinglePhaseDO.getDeviceSn(), CommonConstants.METRIC_LIST, TimeUtil.getDayStart(offsetDay, offset),
				System.currentTimeMillis(), TsdbSampleEnum.ONE_HOUR_NONE_POINT
		);
		LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
		LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
				.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
		LinkedHashMap<Long, Object> toBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
		LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
		LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);

		Map<Integer, InsightDeviceEnergyAvgStatisticsDto.DeviceEnergy> map = new HashMap<>(Math.abs(offsetDay));
		List<Long> keyList = null == fromSolarMap
				? ListUtil.empty()
				: fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
				.collect(Collectors.toList());

		InsightDeviceEnergyAvgStatisticsDto insightDeviceEnergyAvgStatisticsDto = new InsightDeviceEnergyAvgStatisticsDto();

		BigDecimal total = BigDecimal.ZERO;

		for (Long time : keyList) {
			assert fromSolarMap != null;
			BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
			BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
			BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
			BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
			BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());

			Integer dayOfWeekend = TimeUtil.getDayOfWeekend(time * 1000, offset);
			BigDecimal solar = fromSolar.signum() == -1 ? new BigDecimal(0) : fromSolar;
			InsightDeviceEnergyAvgStatisticsDto.DeviceEnergy deviceEnergy;
			if (!map.containsKey(dayOfWeekend)) {
				deviceEnergy = new InsightDeviceEnergyAvgStatisticsDto.DeviceEnergy();
				deviceEnergy.setSolarEnergy(NumberUtil.round(solar, 3, RoundingMode.HALF_UP));
				deviceEnergy.setBatteryEnergy(
						NumberUtil.round(NumberUtil.sub(fromBattery, toBattery), 3, RoundingMode.HALF_UP));
				deviceEnergy.setGridEnergy(NumberUtil.round(NumberUtil.sub(fromGrid, toGrid), 3, RoundingMode.HALF_UP));
			} else {
				deviceEnergy = map.get(dayOfWeekend);
				deviceEnergy.setSolarEnergy(NumberUtil.round(NumberUtil.add(deviceEnergy.getSolarEnergy(), solar), 3,
						RoundingMode.HALF_UP
				));
				deviceEnergy.setBatteryEnergy(NumberUtil.round(
						NumberUtil.add(deviceEnergy.getBatteryEnergy(), NumberUtil.sub(fromBattery, toBattery)), 3,
						RoundingMode.HALF_UP
				));
				deviceEnergy.setGridEnergy(
						NumberUtil.round(NumberUtil.add(deviceEnergy.getGridEnergy(), NumberUtil.sub(fromGrid, toGrid)),
								3, RoundingMode.HALF_UP
						));
			}
			map.put(dayOfWeekend, deviceEnergy);
		}
		if (map.size() != 0) {
			for (InsightDeviceEnergyAvgStatisticsDto.DeviceEnergy deviceEnergy : map.values()) {
				total = NumberUtil.add(deviceEnergy.getBatteryEnergy(), deviceEnergy.getSolarEnergy(),
						deviceEnergy.getGridEnergy(), total
				);
			}
		}

		insightDeviceEnergyAvgStatisticsDto.setMinEnergy(map.size() != 0
				? map.values().stream()
				.map(deviceEnergy -> NumberUtil.round(NumberUtil.add(deviceEnergy.getBatteryEnergy(),
						deviceEnergy.getSolarEnergy(),
						deviceEnergy.getGridEnergy()
				), 3, RoundingMode.HALF_UP))
				.min(Comparator.naturalOrder()).orElse(BigDecimal.ZERO)
				: BigDecimal.ZERO);
		insightDeviceEnergyAvgStatisticsDto.setMaxEnergy(map.size() != 0
				? map.values().stream()
				.map(deviceEnergy -> NumberUtil.round(NumberUtil.add(deviceEnergy.getBatteryEnergy(),
						deviceEnergy.getSolarEnergy(),
						deviceEnergy.getGridEnergy()
				), 3, RoundingMode.HALF_UP))
				.max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO)
				: BigDecimal.ZERO);
		insightDeviceEnergyAvgStatisticsDto.setAvgEnergy(map.size() != 0
				? NumberUtil.round(NumberUtil.div(total, map.size()), 2, RoundingMode.HALF_UP)
				: BigDecimal.ZERO);
		insightDeviceEnergyAvgStatisticsDto.setWeekEnergy(map);
		return insightDeviceEnergyAvgStatisticsDto;
	}

	@Override
	public InsightDeviceEnergyAvgStatisticsDto getOffsetDaysDeviceEnergy(
			String deviceId, Integer offsetDay,
			String timezone
	) {
		Pair<HybridSinglePhaseDO, String> pair = validateUserDevice(deviceId, timezone);
		return computeDeviceEnergy(pair.getKey(), pair.getValue(), offsetDay);
	}

	@Override
	public InsightDeviceEnergyAvgStatisticsDto getOffsetMonthDeviceEnergy(
			String deviceId, Integer offsetMonth,
			String timezone
	) {
		Pair<HybridSinglePhaseDO, String> pair = validateUserDevice(deviceId, timezone);
		return computeDeviceEnergy(pair.getKey(), pair.getValue(),
				TimeUtil.getDayCountWithOffsetMonth(pair.getValue(), offsetMonth)
		);
	}

	private InsightDeviceEnergyHeatmapStatisticsDto computeDeviceEnergyHeatmap(
			HybridSinglePhaseDO hybridSinglePhaseDO, long start, long end,
			String offset
	) {

		TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

		Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(hybridSinglePhaseDO.getDeviceSn(),
				CommonConstants.METRIC_LIST, start, end,
				TsdbSampleEnum.TWO_HOUR_NONE_POINT
		);
		LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
		LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
		LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
		LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
				.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
		LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);

		InsightDeviceEnergyHeatmapStatisticsDto insightDeviceEnergyHeatmapStatisticsDto = new InsightDeviceEnergyHeatmapStatisticsDto();
		insightDeviceEnergyHeatmapStatisticsDto.setMaxEnergyOfHour(new BigDecimal("0"));
		insightDeviceEnergyHeatmapStatisticsDto.setMinEnergyOfHour(new BigDecimal("0"));

		Map<Integer, Map<Integer, List<BigDecimal>>> map = new HashMap<>(TimeUtil.getDaysInStartAndEnd(start, end));
		List<Long> keyList = null == fromSolarMap
				? ListUtil.empty()
				: fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
				.collect(Collectors.toList());

		BigDecimal max = new BigDecimal(Integer.MIN_VALUE);
		BigDecimal min = new BigDecimal(Integer.MAX_VALUE);

		if (CollUtil.isNotEmpty(fromSolarMap)) {
			for (Long time : keyList) {
				Integer hourOfDay = TimeUtil.getHourOfDay(time * 1000, offset);
				Integer hourKey = hourOfDay / 2;
				Integer dayOfWeekend = TimeUtil.getDayOfWeekend(time * 1000, offset);
				BigDecimal toBattery = new BigDecimal(Optional.ofNullable(toBatteryMap.get(time))
						.map(Object::toString)
						.orElse("0"));
				BigDecimal toGrid = new BigDecimal(Optional.ofNullable(toGridMap.get(time))
						.map(Object::toString)
						.orElse("0"));
				BigDecimal fromSolar = new BigDecimal(Optional.ofNullable(fromSolarMap.get(time))
						.map(Object::toString)
						.orElse("0"));

				BigDecimal solar = fromSolar.signum() == -1 ? new BigDecimal(0) : fromSolar;
				BigDecimal fromBattery = new BigDecimal(Optional.ofNullable(fromBatteryMap.get(time))
						.map(Object::toString)
						.orElse("0"));
				BigDecimal fromGrid = new BigDecimal(Optional.ofNullable(fromGridMap.get(time))
						.map(Object::toString)
						.orElse("0"));

				Map<Integer, List<BigDecimal>> innerMap;
				BigDecimal curr = NumberUtil.round(
						NumberUtil.sub(NumberUtil.add(solar, fromGrid, fromBattery), toGrid, toBattery), 2,
						RoundingMode.HALF_UP
				);

				if (!map.containsKey(dayOfWeekend)) {
					innerMap = new HashMap<>(12);
					innerMap.put(hourKey, ListUtil.toList(curr));
				} else {
					innerMap = map.get(dayOfWeekend);
					if (!innerMap.containsKey(hourKey)) {
						innerMap.put(hourKey, ListUtil.toList(curr));
					} else {
						List<BigDecimal> bigDecimalList = innerMap.get(hourKey);
						bigDecimalList.add(curr);
						innerMap.put(hourKey, bigDecimalList);
					}
				}

				max = max.compareTo(curr) > 0 ? max : curr;
				min = min.compareTo(curr) > 0 ? curr : min;
				map.put(dayOfWeekend, innerMap);
			}
		}

		Map<Integer, Map<Integer, BigDecimal>> result = new HashMap<>(map.size());

		for (Integer weekend : map.keySet()) {
			Map<Integer, List<BigDecimal>> innerMap = map.get(weekend);
			Map<Integer, BigDecimal> resultInnerMap = new HashMap<>(innerMap.size());
			for (Integer hour : innerMap.keySet()) {
				BigDecimal sum = innerMap.get(hour).stream().reduce(BigDecimal.ZERO, NumberUtil::add);
				resultInnerMap.put(hour, NumberUtil.div(sum, innerMap.get(hour).size(), 2));
			}
			result.put(weekend, resultInnerMap);
		}

		insightDeviceEnergyHeatmapStatisticsDto.setMaxEnergyOfHour(keyList.size() == 0 ? BigDecimal.ZERO : max);
		insightDeviceEnergyHeatmapStatisticsDto.setMinEnergyOfHour(keyList.size() == 0 ? BigDecimal.ZERO : min);
		insightDeviceEnergyHeatmapStatisticsDto.setWeekEnergyOfHour(result);
		Integer dayOfWeekend = TimeUtil.getDayOfWeekend(System.currentTimeMillis() , offset);
		insightDeviceEnergyHeatmapStatisticsDto.setToday(dayOfWeekend);
		return insightDeviceEnergyHeatmapStatisticsDto;
	}

	@Override
	public InsightDeviceEnergyHeatmapStatisticsDto getOffsetDayDeviceEnergyHeatmap(String deviceId, Integer offsetDay) {
		Pair<HybridSinglePhaseDO, String> pair = validateUserDevice(deviceId, null);
		Pair<Long, Long> timePair = TimeUtil.computeStartAndEndByOffsetDay(offsetDay, pair.getValue());
		return computeDeviceEnergyHeatmap(pair.getKey(), timePair.getKey(), timePair.getValue(), pair.getValue()
		);
	}

	@Override
	@DSTransactional
	public InsightMoreInformationEnergyNotifyDto getEnergyNotifyConfig(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		validateUserDevice(deviceId, "");

		InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto = new InsightMoreInformationEnergyNotifyDto();
		saveOrGetEnergyNotify(insightMoreInformationEnergyNotifyDto, Long.parseLong(deviceId), clientUserDo.getId());

		return insightMoreInformationEnergyNotifyDto;
	}

	private void saveOrGetEnergyNotify(
			InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto,
			Long deviceId, Long userId
	) {
		ClientEnergyNotifyDo clientEnergyNotifyDo = clientEnergyNotifyService
				.getOne(Wrappers.<ClientEnergyNotifyDo>lambdaQuery().eq(ClientEnergyNotifyDo::getUserId, userId)
						.eq(ClientEnergyNotifyDo::getDeviceId, deviceId));
		if (null == clientEnergyNotifyDo) {
			clientEnergyNotifyDo = new ClientEnergyNotifyDo();
			clientEnergyNotifyDo.setId(snowFlakeUtil.generateId());
			clientEnergyNotifyDo.setDeviceId(deviceId);
			clientEnergyNotifyDo.setUserId(userId);
			ActionFlagUtil.assertTrue(clientEnergyNotifyService.save(clientEnergyNotifyDo));
			insightMoreInformationEnergyNotifyDto.setOpen(2);
			insightMoreInformationEnergyNotifyDto.setThreshold(0);
			insightMoreInformationEnergyNotifyDto.setEmail("");
		} else {
			CglibUtil.copy(clientEnergyNotifyDo, insightMoreInformationEnergyNotifyDto);
		}
	}

	@Override
	public InsightMoreInformationEnergyNotifyDto updateEnergyNotifyConfig(
			InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
	) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		String deviceId = insightMoreInformationEnergyNotifyVo.getDeviceId();
		validateUserDevice(deviceId, "");

		InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto = new InsightMoreInformationEnergyNotifyDto();
		updateOrGetEnergyNotify(insightMoreInformationEnergyNotifyDto, Long.parseLong(deviceId), clientUserDo.getId(),
				insightMoreInformationEnergyNotifyVo
		);

		return insightMoreInformationEnergyNotifyDto;
	}

	private void updateOrGetEnergyNotify(
			InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto,
			Long deviceId, Long userId, InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
	) {
		ClientEnergyNotifyDo clientEnergyNotifyDo = clientEnergyNotifyService
				.getOne(Wrappers.<ClientEnergyNotifyDo>lambdaQuery().eq(ClientEnergyNotifyDo::getUserId, userId)
						.eq(ClientEnergyNotifyDo::getDeviceId, deviceId));

		if (null == clientEnergyNotifyDo) {
			clientEnergyNotifyDo = new ClientEnergyNotifyDo();
			clientEnergyNotifyDo.setId(snowFlakeUtil.generateId());
			clientEnergyNotifyDo.setDeviceId(deviceId);
			clientEnergyNotifyDo.setUserId(userId);
			CglibUtil.copy(insightMoreInformationEnergyNotifyVo, clientEnergyNotifyDo);
			ActionFlagUtil.assertTrue(clientEnergyNotifyService.save(clientEnergyNotifyDo));
		} else {
			CglibUtil.copy(insightMoreInformationEnergyNotifyVo, clientEnergyNotifyDo);
			ActionFlagUtil.assertTrue(clientEnergyNotifyService.updateById(clientEnergyNotifyDo));
		}
		CglibUtil.copy(clientEnergyNotifyDo, insightMoreInformationEnergyNotifyDto);
	}

	private Pair<HybridSinglePhaseDO, String> validateUserDevice(String deviceId, String timezone) {
		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));

		if (StrUtil.isBlank(timezone)) {
			// 先判断当前操作的用户下是否有家庭，家庭中是否有该设备
			ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
			Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, deviceId);
			if (!checkUserHasDevice) {
				Optional.ofNullable(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
								.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
								.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())))
						.orElseThrow(() -> {
							log.warn("未绑定设备");
							return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
						});
			}
			return Pair.of(hybridSinglePhaseDO, clientUserDo.getTimeZone());
		}
		return Pair.of(hybridSinglePhaseDO, timezone);
	}
}
