package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.V2AISessionDto;
import com.weihengtech.ecos.model.dtos.app.V2AISessionMessagesDto;
import com.weihengtech.ecos.model.vos.app.V2AIChatCompletionsVo;
import com.weihengtech.ecos.model.vos.app.V2AIChatMessagesPageVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Locale;

/**
 * @program: ecos-server
 * @description: AI问答相关方法
 * @author: jiahao.jin
 * @create: 2024-05-09 15:01
 **/
public interface V2AIAdapter {

    void chatCompletions(V2AIChatCompletionsVo chatContent, ClientUserDo clientUserDo, HttpServletResponse rp, Locale locale);

    List<V2AISessionDto> chatConversations(ClientUserDo clientUserDo);

    V2AISessionMessagesDto chatConversationMessages(ClientUserDo clientUserDo, V2AIChatMessagesPageVo historyParam, Locale locale);
}
