package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.api.pojo.dtos.EleDataSourceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleTimeZoneDto;
import com.weihengtech.ecos.api.pojo.vos.AheadPriceVo;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.ele.EleRegionDTO;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.model.dtos.app.RippleControlDTO;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomizeAdapter {

	/**
	 * 读取 Customize 信息
	 *
	 * @param deviceId 设备id
	 * @return Customize页面信息
	 */
	CustomizeInfoDto readCustomize(String deviceId);

	/**
	 * 写入 Customize 信息
	 *
	 * @param customizeInfoVo 要配置的信息
	 */
	void writeCustomize(CustomizeInfoVo customizeInfoVo);

	/**
	 * 写入 Customize 信息 V2
	 *
	 * @param customizeInfoEzV2Vo 要配置的信息
	 */
	void writeCustomizeV2(CustomizeInfoEzV2Vo customizeInfoEzV2Vo);

	/**
	 * 备份充放电数据
	 *
	 * @param customizeInfoEzV2Vo
	 */
	void backupLastTimeList(CustomizeInfoEzV2Vo customizeInfoEzV2Vo);


	/**
	 * 查询充放电数据
	 *
	 * @param deviceId 设备id
	 * @return
	 */
	TimeListLastDTO queryLastTimeList(String deviceId);

	/**
	 * 获取电价时区
	 *
	 */
	List<EleTimeZoneDto> getEleTimeZone();

	/**
	 * 获取电价国家地区
	 *
	 */
	List<EleRegionDTO> getEleCountryRegion();

	/**
	 * 获取电价数据来源
	 *
	 */
	List<EleDataSourceDto> getEleDataSource();

	/**
	 * 获取电价信息
	 *
	 * @param aheadPriceVo 查询电价参数
	 */
	List<EleDayAheadPriceDto> getEleAheadPrice(AheadPriceVo aheadPriceVo);

	/**
	 * 自动策略预览
	 *
	 * @param param 时间、地区
	 * @return 自动策略
	 */
	List<EleStrategyDTO> strategyPreview(EleStrategyPreviewVO param);

	/**
	 * 根据日前电价获取自动策略
	 *
	 * @return 充放电策略
	 */
	CustomizeInfoEzDto queryStrategy(EleStrategyPreviewVO param);

	/**
	 * 查询设备脉冲配置
	 *
	 * @param deviceId 设备id
	 * @return 脉冲控制
	 */
    RippleControlDTO rippleControlQuery(Long deviceId);

	/**
	 * 下发脉冲控制指令
	 *
	 * @param param param
	 */
	void rippleControlConfig(RippleControlDTO param);

    DynamicExportDTO dynamicExport(String deviceName);

	DynamicDesignDTO designInfo(String deviceName);

	void dynamicSave(DynamicSaveVO param);

	DynamicTestDTO dynamicTest(String deviceName);

	void dynamicSwitch(DynamicSwitchVO param);
}
