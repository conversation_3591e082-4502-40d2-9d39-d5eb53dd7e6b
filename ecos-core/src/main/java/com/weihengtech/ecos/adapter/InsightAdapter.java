package com.weihengtech.ecos.adapter;

import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyAvgStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;

/**
 * <AUTHOR>
 */
public interface InsightAdapter {

	/**
	 * 获取偏移量天数的设备能量
	 *
	 * @param deviceId  设备id
	 * @param offsetDay 查询几天 负数
	 * @param timezone  用户时区
	 * @return 计算结果
	 */
	InsightDeviceEnergyAvgStatisticsDto getOffsetDaysDeviceEnergy(String deviceId, Integer offsetDay, String timezone);

	/**
	 * 获取偏移量月份的设备能量
	 *
	 * @param deviceId    设备id
	 * @param offsetMonth 查询几月 负数
	 * @param timezone    用户时区
	 * @return 计算结果
	 */
	InsightDeviceEnergyAvgStatisticsDto getOffsetMonthDeviceEnergy(
			String deviceId, Integer offsetMonth,
			String timezone
	);

	/**
	 * 获取偏移量天数的设备能量热图
	 *
	 * @param deviceId  设备id
	 * @param offsetDay 偏移量天数
	 * @return 热图统计
	 */
	InsightDeviceEnergyHeatmapStatisticsDto getOffsetDayDeviceEnergyHeatmap(String deviceId, Integer offsetDay);

	/**
	 * 获取更多信息页面的通知配置
	 *
	 * @param deviceId 设备id
	 * @return 配置信息回参
	 */
	InsightMoreInformationEnergyNotifyDto getEnergyNotifyConfig(String deviceId);

	/**
	 * 更新更多信息页面通知配置
	 *
	 * @param insightMoreInformationEnergyNotifyVo 更多信息通知
	 * @return 配置信息回参
	 */
	InsightMoreInformationEnergyNotifyDto updateEnergyNotifyConfig(
			InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
	);
}
