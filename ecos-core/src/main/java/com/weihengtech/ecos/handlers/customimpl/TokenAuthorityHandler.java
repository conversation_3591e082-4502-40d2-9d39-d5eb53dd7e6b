package com.weihengtech.ecos.handlers.customimpl;

import com.weihengtech.ecos.annotation.SetOrder;
import com.weihengtech.ecos.handlers.ICustomProcessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
@SetOrder(value = -9999)
public class TokenAuthorityHandler implements ICustomProcessHandler {

	@Override
	public void preHandle(HttpServletRequest request, Object handler) {
	}

	@Override
	public void postHandle(
			HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView
	) {
	}

	@Override
	public void afterCompletion() {
	}
}
