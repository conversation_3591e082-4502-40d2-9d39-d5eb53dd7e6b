package com.weihengtech.ecos.handlers;

import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.LoginLimitException;
import com.weihengtech.ecos.common.exception.RetryException;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.common.exception.*;
import com.weihengtech.ecos.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice(basePackages = {"com.weihengtech.ecos.controller", "com.weihengtech.ecos.device"})
public class GlobalExceptionHandler {

	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	@ResponseBody
	public DataResponse<Map<String, String>> bindExceptionHandler(MethodArgumentNotValidException e) {
		List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
		HashMap<String, String> hashMap = new HashMap<>(64);
		DataResponse<Map<String, String>> dataResponse = new DataResponse<>();

		for (FieldError fieldError : fieldErrors) {
			String defaultMessage = Optional.ofNullable(fieldError.getDefaultMessage()).orElse("");
			String field = fieldError.getField();
			String localeMessage = LocaleUtil.getMessage(defaultMessage).orElse(defaultMessage);
			String message = defaultMessage.equals(localeMessage) ? defaultMessage : localeMessage;
			hashMap.put(field, message);
		}
		dataResponse.setData(hashMap);
		dataResponse.setCode(20000);
		dataResponse.setMessage(hashMap.size() > 0
				? hashMap.values().stream().findFirst().get()
				: LocaleUtil.getMessage(EcosExceptionEnum.INVALID_PARAM.getMsg()).orElse(""));
		log.error("handleBindException" + dataResponse);
		return dataResponse;
	}

	@ExceptionHandler(value = EcosException.class)
	@ResponseBody
	public EmptyResponse handleEcosException(EcosException e) {
		return EmptyResponse.fail(e.getCode(), e.getMessage());
	}

	@ExceptionHandler(value = RetryException.class)
	@ResponseBody
	public EmptyResponse handleRetryException(RetryException e) {
		return EmptyResponse.fail(e.getCode(), e.getMessage());
	}

	@ResponseStatus(code = HttpStatus.UNAUTHORIZED)
	@ExceptionHandler(value = UnauthorizedException.class)
	@ResponseBody
	public EmptyResponse handleUnauthorizedException(UnauthorizedException e) {
		return EmptyResponse.fail(e.getCode(), e.getMessage());
	}

	@ExceptionHandler(value = LoginLimitException.class)
	@ResponseBody
	public DataResponse<Map<String, Integer>> handleLoginLimitException(LoginLimitException e) {
		Map<String, Integer> hashMap = new HashMap<>(2);
		hashMap.put("times", e.getTimes());
		hashMap.put("minute", e.getMinute());
		return DataResponse.fail(new EcosException(EcosExceptionEnum.INVALID_USERNAME_OR_PASSWORD), hashMap);
	}

	@ExceptionHandler(value = RuntimeException.class)
	@ResponseBody
	public EmptyResponse runtimeExceptionHandler(RuntimeException e) {
		log.error(String.format("GlobalExceptionHandler=====>%s", e.getMessage()), e);
		return EmptyResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
	}
}
