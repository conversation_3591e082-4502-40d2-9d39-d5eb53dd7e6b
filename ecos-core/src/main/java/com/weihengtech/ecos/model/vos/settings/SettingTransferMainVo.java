package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 转移主账号入参
 * @author: jiahao.jin
 * @create: 2023-11-10 16:45
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "转移主账号入参")
public class SettingTransferMainVo {

    @ApiModelProperty("app标识")
    @NotBlank(message = "err.not.blank")
    private String appSchema;

    @ApiModelProperty("国家编码")
    @NotBlank(message = "err.not.blank")
    private String countryCode;

    @ApiModelProperty("转移者uuid：tuya平台的账号")
    @NotBlank(message = "err.not.blank")
    private String fromUserId;

    @ApiModelProperty("家庭id")
    @NotNull(message = "err.not.blank")
    private Long homeId;

    @ApiModelProperty("设备id：绑定账号设备关系")
    @NotEmpty(message = "err.not.null")
    private String deviceId;

}
