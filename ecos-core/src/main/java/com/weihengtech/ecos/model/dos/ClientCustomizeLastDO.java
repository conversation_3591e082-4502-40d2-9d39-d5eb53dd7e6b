package com.weihengtech.ecos.model.dos;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("client_customize_last")
public class ClientCustomizeLastDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long deviceId;

    private Integer chargeStartHour1;

    private Integer chargeStartMinute1;

    private Integer chargeEndHour1;

    private Integer chargeEndMinute1;

    private Integer chargeStartHour2;

    private Integer chargeStartMinute2;

    private Integer chargeEndHour2;

    private Integer chargeEndMinute2;

    private Integer dischargeStartHour1;

    private Integer dischargeStartMinute1;

    private Integer dischargeEndHour1;

    private Integer dischargeEndMinute1;

    private Integer dischargeStartHour2;

    private Integer dischargeStartMinute2;

    private Integer dischargeEndHour2;

    private Integer dischargeEndMinute2;

    private Integer chargeStartHour3;

    private Integer chargeStartMinute3;

    private Integer chargeEndHour3;

    private Integer chargeEndMinute3;

    private Integer dischargeStartHour3;

    private Integer dischargeStartMinute3;

    private Integer dischargeEndHour3;

    private Integer dischargeEndMinute3;

    private Integer chargeStartHour4;

    private Integer chargeStartMinute4;

    private Integer chargeEndHour4;

    private Integer chargeEndMinute4;

    private Integer dischargeStartHour4;

    private Integer dischargeStartMinute4;

    private Integer dischargeEndHour4;

    private Integer dischargeEndMinute4;

    private Integer chargeStartHour5;

    private Integer chargeStartMinute5;

    private Integer chargeEndHour5;

    private Integer chargeEndMinute5;

    private Integer dischargeStartHour5;

    private Integer dischargeStartMinute5;

    private Integer dischargeEndHour5;

    private Integer dischargeEndMinute5;

    private Integer chargeStartHour6;

    private Integer chargeStartMinute6;

    private Integer chargeEndHour6;

    private Integer chargeEndMinute6;

    private Integer dischargeStartHour6;

    private Integer dischargeStartMinute6;

    private Integer dischargeEndHour6;

    private Integer dischargeEndMinute6;

    private Integer chargeStartHour7;

    private Integer chargeStartMinute7;

    private Integer chargeEndHour7;

    private Integer chargeEndMinute7;

    private Integer dischargeStartHour7;

    private Integer dischargeStartMinute7;

    private Integer dischargeEndHour7;

    private Integer dischargeEndMinute7;

    private Integer chargeStartHour8;

    private Integer chargeStartMinute8;

    private Integer chargeEndHour8;

    private Integer chargeEndMinute8;

    private Integer dischargeStartHour8;

    private Integer dischargeStartMinute8;

    private Integer dischargeEndHour8;

    private Integer dischargeEndMinute8;

    private Integer chargeStartHour9;

    private Integer chargeStartMinute9;

    private Integer chargeEndHour9;

    private Integer chargeEndMinute9;

    private Integer dischargeStartHour9;

    private Integer dischargeStartMinute9;

    private Integer dischargeEndHour9;

    private Integer dischargeEndMinute9;

    private Integer chargeStartHour10;

    private Integer chargeStartMinute10;

    private Integer chargeEndHour10;

    private Integer chargeEndMinute10;

    private Integer dischargeStartHour10;

    private Integer dischargeStartMinute10;

    private Integer dischargeEndHour10;

    private Integer dischargeEndMinute10;

    private Integer chargeStartHour11;

    private Integer chargeStartMinute11;

    private Integer chargeEndHour11;

    private Integer chargeEndMinute11;

    private Integer dischargeStartHour11;

    private Integer dischargeStartMinute11;

    private Integer dischargeEndHour11;

    private Integer dischargeEndMinute11;

    private Integer chargeStartHour12;

    private Integer chargeStartMinute12;

    private Integer chargeEndHour12;

    private Integer chargeEndMinute12;

    private Integer dischargeStartHour12;

    private Integer dischargeStartMinute12;

    private Integer dischargeEndHour12;

    private Integer dischargeEndMinute12;

    private Integer chargePower1;

    private Integer dischargePower1;

    private Integer chargePower2;

    private Integer dischargePower2;

    private Integer chargePower3;

    private Integer dischargePower3;

    private Integer chargePower4;

    private Integer dischargePower4;

    private Integer chargePower5;

    private Integer dischargePower5;

    private Integer chargePower6;

    private Integer dischargePower6;

    private Integer chargePower7;

    private Integer dischargePower7;

    private Integer chargePower8;

    private Integer dischargePower8;

    private Integer chargePower9;

    private Integer dischargePower9;

    private Integer chargePower10;

    private Integer dischargePower10;

    private Integer chargePower11;

    private Integer dischargePower11;

    private Integer chargePower12;

    private Integer dischargePower12;

    private Integer chargeAbandonPv1;

    private Integer chargeAbandonPv2;

    private Integer chargeAbandonPv3;

    private Integer chargeAbandonPv4;

    private Integer chargeAbandonPv5;

    private Integer chargeAbandonPv6;

    private Integer chargeAbandonPv7;

    private Integer chargeAbandonPv8;

    private Integer chargeAbandonPv9;

    private Integer chargeAbandonPv10;

    private Integer chargeAbandonPv11;

    private Integer chargeAbandonPv12;

    private Integer dischargeAbandonPv1;

    private Integer dischargeAbandonPv2;

    private Integer dischargeAbandonPv3;

    private Integer dischargeAbandonPv4;

    private Integer dischargeAbandonPv5;

    private Integer dischargeAbandonPv6;

    private Integer dischargeAbandonPv7;

    private Integer dischargeAbandonPv8;

    private Integer dischargeAbandonPv9;

    private Integer dischargeAbandonPv10;

    private Integer dischargeAbandonPv11;

    private Integer dischargeAbandonPv12;


    public static ClientCustomizeLastDO list2DO(List<ChargingStructDTO> chargingList, List<ChargingStructDTO> dischargingList) {
        ClientCustomizeLastDO resItem = new ClientCustomizeLastDO();
        Class<? extends ClientCustomizeLastDO> clazz = resItem.getClass();
        if (CollUtil.isNotEmpty(chargingList)) {
            for (int i = 0; i < chargingList.size(); i++) {
                ChargingStructDTO chargingStruct = chargingList.get(i);
                try {
                    Method setChargeStartHour = clazz.getMethod("setChargeStartHour" + (i+1), Integer.class);
                    setChargeStartHour.invoke(resItem, chargingStruct.getStartHour());
                    Method setChargeStartMinute = clazz.getMethod("setChargeStartMinute" + (i+1), Integer.class);
                    setChargeStartMinute.invoke(resItem, chargingStruct.getStartMinute());
                    Method setChargeEndHour = clazz.getMethod("setChargeEndHour" + (i+1), Integer.class);
                    setChargeEndHour.invoke(resItem, chargingStruct.getEndHour());
                    Method setChargeEndMinute = clazz.getMethod("setChargeEndMinute" + (i+1), Integer.class);
                    setChargeEndMinute.invoke(resItem, chargingStruct.getEndMinute());
                    Method setChargePower = clazz.getMethod("setChargePower" + (i+1), Integer.class);
                    setChargePower.invoke(resItem, chargingStruct.getPower());
                    Method setChargeAbandonPv = clazz.getMethod("setChargeAbandonPv" + (i+1), Integer.class);
                    setChargeAbandonPv.invoke(resItem, chargingStruct.getAbandonPv());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        if (CollUtil.isNotEmpty(dischargingList)) {
            for (int i = 0; i < dischargingList.size(); i++) {
                ChargingStructDTO dischargingStruct = dischargingList.get(i);
                try {
                    Method setDischargeStartHour = clazz.getMethod("setDischargeStartHour" + (i+1), Integer.class);
                    setDischargeStartHour.invoke(resItem, dischargingStruct.getStartHour());
                    Method setDischargeStartMinute = clazz.getMethod("setDischargeStartMinute" + (i+1), Integer.class);
                    setDischargeStartMinute.invoke(resItem, dischargingStruct.getStartMinute());
                    Method setDischargeEndHour = clazz.getMethod("setDischargeEndHour" + (i+1), Integer.class);
                    setDischargeEndHour.invoke(resItem, dischargingStruct.getEndHour());
                    Method setDischargeEndMinute = clazz.getMethod("setDischargeEndMinute" + (i+1), Integer.class);
                    setDischargeEndMinute.invoke(resItem, dischargingStruct.getEndMinute());
                    Method setDischargePower = clazz.getMethod("setDischargePower" + (i+1), Integer.class);
                    setDischargePower.invoke(resItem, dischargingStruct.getPower());
                    Method setDischargeAbandonPv = clazz.getMethod("setDischargeAbandonPv" + (i+1), Integer.class);
                    setDischargeAbandonPv.invoke(resItem, dischargingStruct.getAbandonPv());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return resItem;
    }

    public static TimeListLastDTO do2List(ClientCustomizeLastDO item) {
        List<ChargingStructDTO> chargeList = new ArrayList<>(12);
        List<ChargingStructDTO> dischargeList = new ArrayList<>(12);
        JSONObject jsonItem = JSONUtil.parseObj(item);
        for (int i = 1; i <= 12; i++) {
            Integer chargeStartHour = jsonItem.getInt("chargeStartHour" + i);
            Integer chargeStartMinute = jsonItem.getInt("chargeStartMinute" + i);
            Integer chargeEndHour = jsonItem.getInt("chargeEndHour" + i);
            Integer chargeEndMinute = jsonItem.getInt("chargeEndMinute" + i);
            if (!Objects.equals(chargeStartHour, chargeEndHour) || !Objects.equals(chargeStartMinute, chargeEndMinute)) {
                chargeList.add(new ChargingStructDTO()
                        .withStartHour(chargeStartHour)
                        .withStartMinute(chargeStartMinute)
                        .withEndHour(chargeEndHour)
                        .withEndMinute(chargeEndMinute)
                        .withPower(jsonItem.getInt("chargePower" + i))
                        .withAbandonPv(jsonItem.getInt("chargeAbandonPv" + i)));
            }
            Integer dischargeStartHour = jsonItem.getInt("dischargeStartHour" + i);
            Integer dischargeStartMinute = jsonItem.getInt("dischargeStartMinute" + i);
            Integer dischargeEndHour = jsonItem.getInt("dischargeEndHour" + i);
            Integer dischargeEndMinute = jsonItem.getInt("dischargeEndMinute" + i);
            if (!Objects.equals(dischargeStartMinute, dischargeEndMinute) || !Objects.equals(dischargeStartHour, dischargeEndHour)) {
                dischargeList.add(new ChargingStructDTO()
                        .withStartHour(dischargeStartHour)
                        .withStartMinute(dischargeStartMinute)
                        .withEndHour(dischargeEndHour)
                        .withEndMinute(dischargeEndMinute)
                        .withPower(jsonItem.getInt("dischargePower" + i))
                        .withAbandonPv(jsonItem.getInt("dischargeAbandonPv" + i)));
            }
        }
        return TimeListLastDTO.builder()
                .chargingList(chargeList)
                .dischargingList(dischargeList)
                .build();
    }
}
