package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "发送帮助邮件入参")
public class SettingHelpEmailVo {

	@ApiModelProperty(name = "content", value = "邮箱内容", required = true)
	@NotBlank(message = "err.not.blank")
	private String content;
}
