package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 添加充电桩入参
 * @author: jiahao.jin
 * @create: 2024-02-18 14:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "ecos配网创建新设备入参")
public class ChargerSaveVO {

    @ApiModelProperty(value = "设备id", required = true)
    @NotNull(message = "err.not.null")
    private Long deviceId;

    @ApiModelProperty(value = "联网模式（0：即插即充，1：有线联网，2：无线联网）", required = true)
    @NotNull(message = "err.not.null")
    private Integer bindMode;

    @ApiModelProperty(value = "数据中心id", required = true)
    private Integer dataCenterId;

    @ApiModelProperty(value = "纬度", required = true)
    private Double lat;

    @ApiModelProperty(value = "经度", required = true)
    private Double lon;

    @ApiModelProperty(value = "ip地址", required = true)
    private String ip;

    @ApiModelProperty(value = "网关sn", required = true)
    @NotBlank(message = "err.not.blank")
    private String gateSn;

    @ApiModelProperty(value = "充电桩sn", required = true)
    @NotBlank(message = "err.not.blank")
    private String chargerSn;

    @ApiModelProperty(value = "最大充电功率")
    private Double maxPower;

    @ApiModelProperty(value = "固件版本")
    private String cpFirmwareVersion;

    @ApiModelProperty(value = "即插即用开关")
    private String cpPlugAndChargeMsg;

}