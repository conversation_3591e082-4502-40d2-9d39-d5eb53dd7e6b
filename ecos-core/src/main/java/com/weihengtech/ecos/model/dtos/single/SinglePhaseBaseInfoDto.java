package com.weihengtech.ecos.model.dtos.single;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 单相机基础详情回参
 * @author: jiahao.jin
 * @create: 2024-03-11 11:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "单相机详情回参")
public class SinglePhaseBaseInfoDto {

    @ApiModelProperty(name = "name", value = "设备名称")
    private String name;

    @ApiModelProperty(name = "deviceSn", value = "设备SN")
    private String deviceSn;

    @ApiModelProperty(name = "wifiSn", value = "WIFI棒SN")
    private String wifiSn;

    @ApiModelProperty(name = "deviceModel", value = "设备型号")
    private String deviceModel;

    @ApiModelProperty(name = "accountList", value = "设备相关用户列表")
    private List<BindAccount> accountList;

    @ApiModelProperty(name = "master", value = "操作者的主从状态 0: 从 1: 主", required = true)
    private Integer master;

    @ApiModelProperty(name = "type", value = "0: 易联; 1: 涂鸦", required = true)
    private Integer type;

    @ApiModelProperty(name = "emsSoftwareVersion", value = "ems软件版本", required = true)
    private String emsSoftwareVersion;

    @ApiModelProperty(name = "dsp1SoftwareVersion", value = "dsp软件版本", required = true)
    private String dsp1SoftwareVersion;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BindAccount {

        private Integer seriesId;

        private String accountId;

        private String account;

        private String bindTime;

        @ApiModelProperty(name = "master", value = "0: 从 1: 主", required = true)
        private Integer master;

        private Boolean isInstaller;

        private Long countdownTime;
    }
}
