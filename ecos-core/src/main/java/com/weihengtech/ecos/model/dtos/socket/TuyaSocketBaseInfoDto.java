package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 涂鸦插座详情回参
 * @author: jiahao.jin
 * @create: 2024-01-28 16:35
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "涂鸦插座详情回参")
public class TuyaSocketBaseInfoDto {

    @ApiModelProperty(name = "name", value = "插座名称")
    private String name;

    @ApiModelProperty(name = "switch1", value = "插座开关状态")
    private Boolean switch1;

    @ApiModelProperty(name = "curCurrent", value = "当前电流(A)")
    private Double curCurrent;

    @ApiModelProperty(name = "curPower", value = "当前功率(W)")
    private Double curPower;

    @ApiModelProperty(name = "curVoltage", value = "当前电压(V)")
    private Double curVoltage;

    @ApiModelProperty(name = "relayStatus", value = "上电状态: {断电：0、通电：1、保持通电前状态：2}")
    private Integer relayStatus;

    @ApiModelProperty(name = "overchargeSwitch", value = "过冲保护")
    private Boolean overchargeSwitch;

    @ApiModelProperty(name = "countdown1", value = "倒计时")
    private Double countdown1;

    @ApiModelProperty(name = "randomTime", value = "随机定时")
    private Map<Integer, TuyaSocketRandomTimeDto> randomTime;

    @ApiModelProperty(name = "sysRunMode", value = "-5: 未知 -1 设备离线 0: 等待 13: 开启 14: 关闭 ")
    private Integer sysRunMode = -5;

    private List<BindAccount> accountList;

    @ApiModelProperty(name = "master", value = "操作者的主从状态 0: 从 1: 主", required = true)
    private Integer master;

    @ApiModelProperty(name = "countdownEnd", value = "倒计时结束时间")
    private Long countdownEnd;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BindAccount {

        private Integer seriesId;

        private String accountId;

        private String account;

        private String bindTime;

        @ApiModelProperty(name = "master", value = "0: 从 1: 主", required = true)
        private Integer master;

        private Boolean isInstaller;

        private Long countdownTime;
    }

}
