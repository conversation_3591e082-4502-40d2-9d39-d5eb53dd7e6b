package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 查询单插历史用电量入参
 * @author: jiahao.jin
 * @create: 2024-01-31 15:55
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "查询单插历史用电量入参")
public class SocketHistoryEleVo extends SocketBaseVo {

    @ApiModelProperty(name = "periodType", value = "0: 天; 2: 月; 4: 年; 5: 总共", required = true)
    @NotNull(message = "err.not.null")
    private Integer periodType;

    @ApiModelProperty(name = "timestamp", value = "时间戳(查询总共可以不用传)")
    private Long timestamp;

    @Override
    public void checkParams() {

    }
}
