package com.weihengtech.ecos.model.dtos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "用户信息回参")
public class SettingsUserInfoDto {

	@ApiModelProperty(value = "用户名")
	private String username;
	@ApiModelProperty(value = "昵称")
	private String nickname;
	@ApiModelProperty(value = "邮箱")
	private String email;
	@ApiModelProperty(value = "手机号")
	private String phone;
	@ApiModelProperty(value = "时区id")
	private String timeZoneId;
	@ApiModelProperty(value = "时区")
	private String timeZone;
	@ApiModelProperty(value = "时区名")
	private String timezoneName;
	@ApiModelProperty(value = "数据中心电话前缀")
	private Integer datacenterPhoneCode;
	@ApiModelProperty(value = "数据中心字符串 CN US EU AU")
	private String datacenter;
	@ApiModelProperty(value = "数据中心url")
	private String datacenterHost;
}
