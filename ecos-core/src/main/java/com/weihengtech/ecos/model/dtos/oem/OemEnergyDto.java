package com.weihengtech.ecos.model.dtos.oem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OemEnergyDto {

	@ApiModelProperty(name = "toBattery", value = "充电量", required = true)
	private BigDecimal toBattery;

	@ApiModelProperty(name = "fromBattery", value = "放电量", required = true)
	private BigDecimal fromBattery;

	@ApiModelProperty(name = "fromGrid", value = "买电量", required = true)
	private BigDecimal fromGrid;

	@ApiModelProperty(name = "toGrid", value = "卖点量", required = true)
	private BigDecimal toGrid;

	@ApiModelProperty(name = "fromSolar", value = "发电量", required = true)
	private BigDecimal fromSolar;

	@ApiModelProperty(name = "homeEnergy", value = "家庭能耗", required = true)
	private BigDecimal homeEnergy;

	@ApiModelProperty(name = "selfPowered", value = "自发自用率", required = true)
	private BigDecimal selfPowered;

	@ApiModelProperty(name = "cycleTimes", value = "循环次数", required = true)
	private Integer cycleTimes;

	@ApiModelProperty(name = "homeEnergyDps", value = "家庭能耗图", required = true)
	private Map<Long, Object> homeEnergyDps;

	@ApiModelProperty(name = "fromSolarDps", value = "光伏发电图", required = true)
	private Map<Long, Object> fromSolarDps;

	@ApiModelProperty(name = "fromBatteryDps", value = "放电量图", required = true)
	private Map<Long, Object> fromBatteryDps;

	@ApiModelProperty(name = "toBatteryDps", value = "充电量图", required = true)
	private Map<Long, Object> toBatteryDps;

	@ApiModelProperty(name = "fromGridDps", value = "买电量图表", required = true)
	private Map<Long, Object> fromGridDps;

	@ApiModelProperty(name = "toGridDps", value = "卖电量图表", required = true)
	private Map<Long, Object> toGridDps;
}
