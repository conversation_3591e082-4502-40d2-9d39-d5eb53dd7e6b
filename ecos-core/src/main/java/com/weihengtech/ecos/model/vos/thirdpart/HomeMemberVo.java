package com.weihengtech.ecos.model.vos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "成员对象")
public class HomeMemberVo {

    @ApiModelProperty(value = "国家码", required = true)
    @NotBlank(message = "err.not.blank")
    private String countryCode;

    @ApiModelProperty(value = "成员账号", required = true)
    @NotBlank(message = "err.not.blank")
    private String memberAccount;

    @ApiModelProperty(value = "是否为管理员", required = true)
    @NotNull(message = "err.not.null")
    private Boolean admin;

    @ApiModelProperty(value = "成员名字", required = true)
    private String name;
}
