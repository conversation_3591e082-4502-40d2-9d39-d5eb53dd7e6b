package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: EN+充电桩预约充电记录返回值
 * @author: jiahao.jin
 * @create: 2024-03-01 16:57
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "EN+充电桩预约充电记录返回值")
public class ENPlusChargeStationTimingChargeDto {

    @ApiModelProperty(name = "chargeTaskId", value = "定时充电任务ID")
    private Long chargeTaskId;

    @ApiModelProperty(name = "startChargeStatus", value = "开始充电状态（0：失败，1：成功）")
    private Integer startChargeStatus;

    @ApiModelProperty(name = "startChargeTime", value = "开始充电时间")
    private Long startChargeTime;

    @ApiModelProperty(name = "stopChargeStatus", value = "结束充电状态（0：失败，1：成功）")
    private Integer stopChargeStatus;

    @ApiModelProperty(name = "stopChargeTime", value = "结束充电时间")
    private Long stopChargeTime;
}
