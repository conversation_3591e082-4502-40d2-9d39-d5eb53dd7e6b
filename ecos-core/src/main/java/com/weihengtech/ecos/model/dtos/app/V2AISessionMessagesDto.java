package com.weihengtech.ecos.model.dtos.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 会话的聊天记录类
 * @author: jiahao.jin
 * @create: 2024-05-11 09:50
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "会话的聊天记录类")
public class V2AISessionMessagesDto {

    // 是否有下一页
    @ApiModelProperty(name = "hasNext", value = "是否有下一页")
    private Boolean hasNext;

    // 消息列表
    @ApiModelProperty(name = "messages", value = "消息列表")
    private List<V2AISessionMessages> messages;

    @ApiModelProperty(name = "sessionId", value = "会话ID")
    private String sessionId;

    @ApiModelProperty(name = "promptList", value = "预设问题列表")
    private List<String> promptList;

    @Getter
    @Setter
    @ToString
    @ApiModel(value = "单条聊天记录类")
    public static class V2AISessionMessages {
        @ApiModelProperty(name = "id", value = "消息ID")
        private String id;

        @ApiModelProperty(name = "content", value = "消息内容")
        private String content;

        @ApiModelProperty(name = "role", value = "消息角色")
        private String role;

        @ApiModelProperty(name = "timestamp", value = "消息发送时间")
        private Long timestamp; // 使用LocalDateTime类型表示日期时间
    }
}
