package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 充电桩定时充电时间回参
 * @author: jiahao.jin
 * @create: 2024-02-23 16:23
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电桩定时充电时间回参")
public class ENPlusChargeStationChargeTimeDto {

    @ApiModelProperty(name = "id", value = "定时充电任务id")
    private String id;

    @ApiModelProperty(name = "status", value = "定时充电任务状态")
    private Boolean status;

    @ApiModelProperty(name = "power", value = "预期充电功率")
    private Double power;

    @ApiModelProperty(name = "week", value = "星期(空：最近一次， 1-7：固定周几)")
    private List<Integer> week;

    @ApiModelProperty(name = "startTime", value = "开始时间")
    private String startTime ;

    @ApiModelProperty(name = "endTime", value = "结束时间")
    private String endTime;
}
