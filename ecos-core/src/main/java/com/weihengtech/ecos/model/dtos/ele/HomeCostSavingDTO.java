package com.weihengtech.ecos.model.dtos.ele;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 15:45
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "家庭成本节省数据")
public class HomeCostSavingDTO {

    @ApiModelProperty(name = "todaySavings", value = "今日成本节省")
    private BigDecimal todaySavings;

    @ApiModelProperty(name = "weeklySavings", value = "周数据")
    private List<Detail> weeklySavings;

    @ApiModelProperty(name = "monthSavings", value = "月成本节省")
    private BigDecimal monthSavings;

    @ApiModelProperty(name = "monthLoadCost", value = "负载消耗成本")
    private BigDecimal monthLoadCost;

    @ApiModelProperty(name = "monthGridCost", value = "电网取电成本")
    private BigDecimal monthGridCost;

    @ApiModelProperty(name = "monthFeedEarnings", value = "馈网收益")
    private BigDecimal monthFeedEarnings;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Detail {

        @ApiModelProperty(name = "day", value = "星期几")
        private Date day;

        @ApiModelProperty(name = "loadCost", value = "负载消耗成本")
        private BigDecimal loadCost;

        @ApiModelProperty(name = "gridCost", value = "电网取电成本")
        private BigDecimal gridCost;

        @ApiModelProperty(name = "feedEarnings", value = "馈网收益")
        private BigDecimal feedEarnings;

        @ApiModelProperty(name = "daySavings", value = "天成本节省")
        private BigDecimal daySavings;

        public void calSavings() {
            if (getLoadCost() == null || getGridCost() == null || getFeedEarnings() == null) {
                return;
            }
            BigDecimal daySaving = this.getLoadCost().subtract(this.getGridCost()).add(this.getFeedEarnings());
            this.setDaySavings(daySaving);
        }
    }
}
