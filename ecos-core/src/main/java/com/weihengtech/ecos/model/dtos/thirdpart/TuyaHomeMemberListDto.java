package com.weihengtech.ecos.model.dtos.thirdpart;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
public class TuyaHomeMemberListDto {

    private List<TuyaHomeMemberListInfoDto> list;

    @JSONField(name = "has_more")
    private Boolean hasMore;

    private Long total;
}
