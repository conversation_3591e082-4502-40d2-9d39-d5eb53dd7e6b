package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 充电记录通知表
 * @author: jiahao.jin
 * @create: 2024-02-29 11:49
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_charge_record_read")
public class ClientChargeRecordReadDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long userId;

    private Long chargeTaskId;

    private Integer readed;

    private Integer startChargeStatus;

    private Long startChargeTime;

    private Integer stopChargeStatus;

    private Long stopChargeTime;

    private Integer status;

}
