package com.weihengtech.ecos.model.vos.guide;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "邮件入参")
public class GuideSendEmailVo implements Serializable {

	private static final long serialVersionUID = 8802100729401508428L;

	@Email(message = "err.valid.email")
	@NotBlank(message = "err.not.blank")
	private String email;
}
