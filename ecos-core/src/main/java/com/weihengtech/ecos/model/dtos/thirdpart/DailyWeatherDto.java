package com.weihengtech.ecos.model.dtos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-26 10:26
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "每天天气回参")
public class DailyWeatherDto {

    @ApiModelProperty(name = "fxDate", value = "预报日期", required = true)
    private String fxDate;
    @ApiModelProperty(name = "sunrise", value = "日出时间，在高纬度地区可能为空", required = false)
    private String sunrise;
    @ApiModelProperty(name = "sunset", value = "日落时间，在高纬度地区可能为空", required = false)
    private String sunset;
    @ApiModelProperty(name = "moonrise", value = "当天月升时间，可能为空", required = false)
    private String moonrise;
    @ApiModelProperty(name = "moonset", value = "当天月落时间，可能为空", required = false)
    private String moonset;
    @ApiModelProperty(name = "moonPhase", value = "月相名称", required = true)
    private String moonPhase;
    @ApiModelProperty(name = "moonPhaseIcon", value = "月相图标代码", required = true)
    private String moonPhaseIcon;
    @ApiModelProperty(name = "tempMax", value = "预报当天最高温度", required = true)
    private String tempMax;
    @ApiModelProperty(name = "tempMin", value = "预报当天最低温度", required = true)
    private String tempMin;
    @ApiModelProperty(name = "iconDay", value = "预报白天天气状况的图标代码", required = true)
    private String iconDay;
    @ApiModelProperty(name = "textDay", value = "预报白天天气状况文字描述，包括阴晴雨雪等天气状态的描述", required = true)
    private String textDay;
    @ApiModelProperty(name = "iconNight", value = "预报夜间天气状况的图标代码", required = true)
    private String iconNight;
    @ApiModelProperty(name = "textNight", value = "预报晚间天气状况文字描述，包括阴晴雨雪等天气状态的描述", required = true)
    private String textNight;
    @ApiModelProperty(name = "wind360Day", value = "预报白天风向360角度", required = true)
    private String wind360Day;
    @ApiModelProperty(name = "windDirDay", value = "预报白天风向", required = true)
    private String windDirDay;
    @ApiModelProperty(name = "windScaleDay", value = "预报白天风力等级", required = true)
    private String windScaleDay;
    @ApiModelProperty(name = "windSpeedDay", value = "预报白天风速，公里/小时", required = true)
    private String windSpeedDay;
    @ApiModelProperty(name = "wind360Night", value = "预报夜间风向360角度", required = true)
    private String wind360Night;
    @ApiModelProperty(name = "windDirNight", value = "预报夜间当天风向", required = true)
    private String windDirNight;
    @ApiModelProperty(name = "windScaleNight", value = "预报夜间风力等级", required = true)
    private String windScaleNight;
    @ApiModelProperty(name = "windSpeedNight", value = "预报夜间风速，公里/小时", required = true)
    private String windSpeedNight;
    @ApiModelProperty(name = "humidity", value = "相对湿度，百分比数值", required = true)
    private String humidity;
    @ApiModelProperty(name = "precip", value = "预报当天总降水量，默认单位：毫米", required = true)
    private String precip;
    @ApiModelProperty(name = "pressure", value = "大气压强，默认单位：百帕", required = true)
    private String pressure;
    @ApiModelProperty(name = "vis", value = "能见度，默认单位：公里", required = true)
    private String vis;
    @ApiModelProperty(name = "cloud", value = "云量，百分比数值。可能为空", required = false)
    private String cloud;
    @ApiModelProperty(name = "uvIndex", value = "紫外线强度指数", required = true)
    private String uvIndex;
}
