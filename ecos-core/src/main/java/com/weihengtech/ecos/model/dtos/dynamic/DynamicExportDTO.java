package com.weihengtech.ecos.model.dtos.dynamic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备sn模糊查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1 9:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DynamicExportDTO {

    @ApiModelProperty(name = "parallelWorkMode", value = "并机模式")
    private String parallelWorkMode;

    @ApiModelProperty(name = "parallelNum", value = "并机数量")
    private String parallelNum;

    @ApiModelProperty(name = "dynamicExport", value = "是否开启")
    private Boolean dynamicExport;

    @ApiModelProperty(name = "status", value = "0:未生效|1:已生效|2:未配置")
    private Integer status;

    @ApiModelProperty(name = "country", value = "国家")
    private Integer country;

    @ApiModelProperty(name = "gridCompany", value = "电网公司")
    private Integer gridCompany;

    @ApiModelProperty(name = "nmi", value = "户号")
    private String nmi;

}
