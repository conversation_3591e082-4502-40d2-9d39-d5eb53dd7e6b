package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分页数据回参")
public class PageInfoDTO<T> implements Serializable {

	private static final long serialVersionUID = -7348297409631081980L;

	private Integer totalPages = 0;

	private Long totalCount = 0L;

	private List<T> data = new ArrayList<>();
}
