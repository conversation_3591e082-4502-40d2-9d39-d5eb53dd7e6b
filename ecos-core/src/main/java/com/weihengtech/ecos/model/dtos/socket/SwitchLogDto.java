package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-01-31 15:24
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座开关单条日志回参")
public class SwitchLogDto {

    @ApiModelProperty(name = "code", value = "开关编号")
    private String code;

    @ApiModelProperty(name = "time", value = "开关时间")
    private String time;

    @ApiModelProperty(name = "value", value = "开关")
    private String value;
}
