package com.weihengtech.ecos.model.bos.global;

import cn.hutool.json.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OssGlobalConfigBo {

	private EcosConfig ecos;

	private HubConfig hub;

	private List<String> hosts;

	private List<String> aftersales;

	private JSONObject firmware;

	@Getter
	@Setter
	@ToString
	public static class EcosConfig {

		private Maintain maintain;
		private String feedbackEmail;
	}

	@Getter
	@Setter
	@ToString
	public static class HubConfig {

		private Maintain maintain;
	}

	@Getter
	@Setter
	@ToString
	public static class Maintain {

		private Integer state;
		private String content;
	}
}
