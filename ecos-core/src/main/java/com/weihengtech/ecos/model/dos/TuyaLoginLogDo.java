package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: ecos-server
 * @description: 涂鸦登录日志表
 * @author: jiahao.jin
 * @create: 2024-04-13 09:25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tuya_login_log")
public class TuyaLoginLogDo {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String ecosAccount;

    private String tuyaAccount;

    private String loginRes;

    private String failReason;

    private LocalDateTime loginTime;
}
