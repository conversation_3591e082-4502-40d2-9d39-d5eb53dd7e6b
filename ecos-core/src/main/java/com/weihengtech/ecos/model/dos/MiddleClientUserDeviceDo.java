package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("middle_client_user_device")
public class MiddleClientUserDeviceDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private Long userId;

	private Long deviceId;

	private Long createTime;

	private Long updateTime;

	private Integer weight;

	private String name;

	// 0 从 1 主
	private Integer master;
}
