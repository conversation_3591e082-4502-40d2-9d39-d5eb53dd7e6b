package com.weihengtech.ecos.model.dtos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-25 19:52
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "实时天气回参")
public class NowWeatherDto {

    @ApiModelProperty(name = "obsTime", value = "数据观测时间", required = true)
    private String obsTime;

    @ApiModelProperty(name = "temp", value = "温度，默认单位：摄氏度", required = true)
    private String temp;

    @ApiModelProperty(name = "feelsLike", value = "体感温度，默认单位：摄氏度", required = true)
    private String feelsLike;

    @ApiModelProperty(name = "icon", value = "天气状况的图标代码", required = true)
    private String icon;

    @ApiModelProperty(name = "text", value = "天气状况的文字描述，包括阴晴雨雪等天气状态的描述", required = true)
    private String text;

    @ApiModelProperty(name = "wind360", value = "风向360角度", required = true)
    private String wind360;

    @ApiModelProperty(name = "windDir", value = "风向", required = true)
    private String windDir;

    @ApiModelProperty(name = "windScale", value = "风力等级", required = true)
    private String windScale;

    @ApiModelProperty(name = "windSpeed", value = "风速，公里/小时", required = true)
    private String windSpeed;

    @ApiModelProperty(name = "humidity", value = "相对湿度，百分比数值", required = true)
    private String humidity;

    @ApiModelProperty(name = "precip", value = "当前小时累计降水量，默认单位：毫米", required = true)
    private String precip;

    @ApiModelProperty(name = "pressure", value = "大气压强，默认单位：百帕", required = true)
    private String pressure;

    @ApiModelProperty(name = "vis", value = "能见度，默认单位：公里", required = true)
    private String vis;

    @ApiModelProperty(name = "cloud", value = "云量，百分比数值。", required = false)
    private String cloud;

    @ApiModelProperty(name = "dew", value = "露点温度", required = false)
    private String dew;


}
