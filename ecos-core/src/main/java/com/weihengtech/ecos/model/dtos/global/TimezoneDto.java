package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "时区回参")
public class TimezoneDto implements Serializable {

	private static final long serialVersionUID = -2399646858509938943L;
	private Integer id;

	private String timezone;

	private String name;

	private String code;
}
