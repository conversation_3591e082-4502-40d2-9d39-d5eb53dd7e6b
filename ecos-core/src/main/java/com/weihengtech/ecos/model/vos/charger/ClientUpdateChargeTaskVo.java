package com.weihengtech.ecos.model.vos.charger;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.regex.Pattern;

/**
 * @program: ecos-server
 * @description: 更新充电任务入参
 * @author: jiahao.jin
 * @create: 2024-02-23 16:56
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "更新充电任务入参")
public class ClientUpdateChargeTaskVo {

    @ApiModelProperty(name = "taskId", value = "任务id", required = true)
    @NotBlank(message = "err.not.blank")
    private String taskId;

    @ApiModelProperty(name = "socketRandomTimeVoMap", value = "定时充电时间")
    @Valid
    private ChargeTaskTimeVo chargeTimeVoMap;

    @Getter
    @Setter
    @ToString
    public static class ChargeTaskTimeVo extends SocketRandomTimeMapVo.SocketRandomTimeVo {
        @ApiModelProperty(name = "power", value = "预期充电功率（KW）")
        @NotNull(message = "err.not.null")
        public Double power;
    }


    // 正则表达式，确保时间格式为HH:mm，小时00-23，分钟00-59
    private static final java.util.regex.Pattern TIME_PATTERN = Pattern.compile("^(?:[01]\\d|2[0-3]):[0-5]\\d$");

    public void checkParams() {
        if (chargeTimeVoMap.getWeek().contains(0) && chargeTimeVoMap.getWeek().size() > 1) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        for (Integer w : chargeTimeVoMap.getWeek()) {
            if (w < 0 || w > 7) {
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }


        if (!TIME_PATTERN.matcher(chargeTimeVoMap.startTime).matches() || !TIME_PATTERN.matcher(chargeTimeVoMap.endTime).matches()) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
    }
}
