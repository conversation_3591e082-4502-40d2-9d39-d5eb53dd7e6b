package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 模式相关回参
 * @author: jiahao.jin
 * @create: 2024-03-12 11:36
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "模式相关回参")
public class ExtInfoDto {
    private Integer id;

    private Long deviceId;

    private Integer mode;

    private Double maxPower;

    @ApiModelProperty(name = "cpFirmwareVersion", value = "固件版本")
    private String cpFirmwareVersion;

    @ApiModelProperty(name = "cpPlugAndChargeMsg", value = "即插即用开关")
    private String cpPlugAndChargeMsg;
}
