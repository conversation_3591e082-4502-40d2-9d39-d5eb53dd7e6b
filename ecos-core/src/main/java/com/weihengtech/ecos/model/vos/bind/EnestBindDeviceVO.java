package com.weihengtech.ecos.model.vos.bind;

import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "用户绑定设备入参")
public class EnestBindDeviceVO extends BindDeviceVO{

	@ApiModelProperty(value = "0: 易联 1: 涂鸦", required = true)
	@NotNull(message = "err.not.null")
	private Integer type;

	@ApiModelProperty(name = "deviceAliasName", value = "设备别名")
	private String deviceAliasName;

	@Override
	public void buildSourceParam() {
		if (DeviceTypeInfoEnum.ELINK.getDatasource() == getType()) {
			this.setTsdbSource(TsdbSourceEnum.ELINK_LINDORM.getCode());
			this.setDataSource(DeviceTypeInfoEnum.ELINK.getDatasource());
		} else {
			this.setTsdbSource(TsdbSourceEnum.TUYA_LINDORM.getCode());
			this.setDataSource(DeviceTypeInfoEnum.TUYA.getDatasource());
		}

	}
}
