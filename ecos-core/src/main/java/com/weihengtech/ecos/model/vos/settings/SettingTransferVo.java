package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "转移设备绑定关系入参")
public class SettingTransferVo {

	@ApiModelProperty(name = "code", value = "二维码信息", required = true)
	@NotBlank(message = "err.not.blank")
	private String code;
}
