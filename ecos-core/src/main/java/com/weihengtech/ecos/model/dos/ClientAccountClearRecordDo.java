package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_account_clear_record")
public class ClientAccountClearRecordDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private Long userId;

	private Long createTime;

	private Long clearTime;

	private Integer state;
}
