package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 涂鸦单插开关日志入参
 * @author: jiahao.jin
 * @create: 2024-01-31 13:55
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "开关插座日志入参")
public class SocketSwitchLogsVo extends SocketBaseVo {

    @ApiModelProperty(name = "deviceId", value = "下一页开始row")
    private String pageStartRow;

    @ApiModelProperty(name = "size", value = "要查询的日志数量（默认为：20）")
    @NotNull(message = "err.not.null")
    @Min(value = 1, message = "err.invalid.param")
    private Integer size = 20;
    @Override
    public void checkParams() {

    }
}
