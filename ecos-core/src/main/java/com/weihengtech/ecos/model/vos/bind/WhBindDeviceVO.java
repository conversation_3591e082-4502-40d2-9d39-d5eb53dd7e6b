package com.weihengtech.ecos.model.vos.bind;

import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/5/16 14:20
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "wh iot储能机配网绑定设备入参")
public class WhBindDeviceVO extends BindDeviceVO{

    @ApiModelProperty(value = "设备SN标识", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceSn;

    @ApiModelProperty(name = "homeId", value = "家庭ID" , required = true)
    private String homeId;

    @Override
    public void buildSourceParam() {
        this.setTsdbSource(TsdbSourceEnum.WH_LINDORM.getCode());
        this.setDataSource(DeviceTypeInfoEnum.WH.getDatasource());
    }
}
