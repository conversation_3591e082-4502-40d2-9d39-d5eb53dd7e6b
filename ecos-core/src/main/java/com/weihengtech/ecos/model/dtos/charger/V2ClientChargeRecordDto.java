package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: V2充电桩充电记录查询回参
 * @author: jiahao.jin
 * @create: 2024-03-05 14:51
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "V2充电桩充电记录查询回参")
public class V2ClientChargeRecordDto {

    @ApiModelProperty(name = "id", value = "记录ID")
    private Integer id;

    @ApiModelProperty(name = "transactionId", value = "充电事务ID")
    private Long transactionId;

    @ApiModelProperty(name = "deviceId", value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(name = "batCap", value = "充电量")
    private String batCap;

    @ApiModelProperty(name = "startTime", value = "开始时间")
    private Long startTime;

    @ApiModelProperty(name = "endTime", value = "结束时间")
    private Long endTime;

    @ApiModelProperty(name = "duration", value = "时长")
    private Long duration;
}
