package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 充电记录更新入参
 * @author: jiahao.jin
 * @create: 2024-03-05 15:10
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电记录更新入参")
public class V2ClientChargeRecordUpdateVo {

    @ApiModelProperty(name = "id", value = "id", required = true)
    @NotNull(message = "err.not.blank")
    private Integer id;

    @ApiModelProperty(name = "deviceId", value = "设备ID", required = true)
    @NotNull(message = "err.not.blank")
    private Long deviceId;

    @ApiModelProperty(name = "transactionId", value = "事务id", required = true)
    @NotNull(message = "err.not.blank")
    private Long transactionId;

    @ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
    private Long startTime;

    @ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
    private Long endTime;

    @ApiModelProperty(name = "duration", value = "充电时长", required = true)
    private Long duration;

    @ApiModelProperty(name = "endTime", value = "充电量", required = true)
    private String batCap;
}
