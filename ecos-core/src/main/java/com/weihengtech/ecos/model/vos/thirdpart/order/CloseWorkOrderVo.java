package com.weihengtech.ecos.model.vos.thirdpart.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "关闭工单入参")
public class CloseWorkOrderVo {

	@ApiModelProperty(value = "工单id", required = true)
	@NotBlank(message = "err.not.blank")
	private String workOrderId;
}
