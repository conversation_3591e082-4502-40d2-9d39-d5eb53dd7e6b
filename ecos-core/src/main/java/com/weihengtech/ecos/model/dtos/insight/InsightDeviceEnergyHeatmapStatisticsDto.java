package com.weihengtech.ecos.model.dtos.insight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备能量热图统计回参")
public class InsightDeviceEnergyHeatmapStatisticsDto {

	@ApiModelProperty(name = "today", value = "周几", required = true)
	private Integer today;

	private BigDecimal maxEnergyOfHour;

	private BigDecimal minEnergyOfHour;

	private Map<Integer, Map<Integer, BigDecimal>> weekEnergyOfHour;
}
