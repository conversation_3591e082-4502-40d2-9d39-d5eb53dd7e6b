package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 转移设备绑定关系入参
 * @author: jiahao.jin
 * @create: 2024-03-08 09:45
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "转移设备绑定关系入参")
public class V2SettingTransferVo {

    @ApiModelProperty(name = "code", value = "二维码信息", required = true)
    @NotBlank(message = "err.not.blank")
    private String code;

    @ApiModelProperty(name = "homeId", value = "要转入的家庭ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String homeId;

    @ApiModelProperty("安装商保留设备时长:-1,3,999")
    private Integer saveDeviceTime;
}
