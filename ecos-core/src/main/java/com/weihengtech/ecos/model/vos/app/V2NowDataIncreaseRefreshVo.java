package com.weihengtech.ecos.model.vos.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 提高设备采集率入参
 * @author: jiahao.jin
 * @create: 2024-03-12 09:04
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "提高设备采集率入参")
public class V2NowDataIncreaseRefreshVo {

    @ApiModelProperty(name = "homeId", value = "家庭id", required = true)
    @NotBlank(message = "err.not.blank")
    private String homeId;
}
