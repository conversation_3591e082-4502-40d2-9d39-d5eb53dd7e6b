package com.weihengtech.ecos.model.dtos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "配置绑定设备列表")
public class SettingsBindDeviceListDto {

	private String deviceId;

	private String deviceAliasName;

	@ApiModelProperty(name = "status", value = "-4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检", required = true)
	private Integer status;

	private Integer seriesId;

	private String deviceSn;

	@ApiModelProperty(value = "代理商id")
	private String agentId;
}
