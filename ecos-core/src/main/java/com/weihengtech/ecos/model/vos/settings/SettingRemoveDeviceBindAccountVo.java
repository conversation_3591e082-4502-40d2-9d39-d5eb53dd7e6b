package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备详情移除绑定账号")
public class SettingRemoveDeviceBindAccountVo {

	@ApiModelProperty(name = "accountId", value = "账号id", required = true)
	@NotBlank(message = "err.not.blank")
	private String accountId;

	@ApiModelProperty(name = "deviceId", value = "设备Id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "isInstaller", value = "是否安装商", required = true)
	private Boolean isInstaller;
}
