package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 充电记录表
 * @author: jiahao.jin
 * @create: 2024-02-20 17:12
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_charge_record")
public class ClientChargeRecordDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Integer transactionId;

    private Long deviceId;

    private Long userId;

    private Double chargeCapacity;

    private Long startTime;

    private Long endTime;

    private Integer chargeStatus;
}
