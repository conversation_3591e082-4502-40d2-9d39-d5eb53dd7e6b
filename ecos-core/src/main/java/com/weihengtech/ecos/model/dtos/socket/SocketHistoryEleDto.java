package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 插座历史电量数据
 * @author: jiahao.jin
 * @create: 2024-01-31 17:18
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座历史电量数据")
public class SocketHistoryEleDto {

    @ApiModelProperty(name = "eleDps", value = "用电量Dps", required = true)
    private Map<Long, Object> eleDps = new HashMap<>();

    @ApiModelProperty(name = "totalEle", value = "总用电量（kWh）", required = true)
    private BigDecimal totalEle;
}
