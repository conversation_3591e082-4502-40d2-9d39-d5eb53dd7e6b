package com.weihengtech.ecos.model.vos.socket;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 插座倒计时
 * @author: jiahao.jin
 * @create: 2024-01-29 19:49
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座倒计时入参")
public class SocketCountdownVo extends SocketBaseVo{

    @ApiModelProperty(name = "second", value = "倒计时时间（秒）", required = true)
    @NotNull(message = "err.not.null")
    private Integer second;

    @Override
    public void checkParams() {
        if (second % 60 != 0) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
    }
}
