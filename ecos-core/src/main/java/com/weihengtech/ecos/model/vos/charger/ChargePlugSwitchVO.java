package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 即插即充开关入参
 * @author: jiahao.jin
 * @create: 2024-03-04 20:19
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "即插即充开关入参")
public class ChargePlugSwitchVO {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "状态开关", value = "0: 天; 2: 月; 4: 年; 5: 总共", required = true)
    @NotNull(message = "err.not.null")
    private Boolean switchStatus;
}
