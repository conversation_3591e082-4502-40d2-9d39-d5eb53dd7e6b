package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "恢复出厂设置")
public class SettingsResetDeviceVo {

	@ApiModelProperty(value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
