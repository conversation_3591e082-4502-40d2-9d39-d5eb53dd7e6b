package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: 查询单插插座详情入参
 * @author: jiahao.jin
 * @create: 2024-01-29 20:08
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座详情入参")
public class SocketInfoVo extends SocketBaseVo{
    @Override
    public void checkParams() {

    }
}
