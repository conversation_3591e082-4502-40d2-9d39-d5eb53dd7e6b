package com.weihengtech.ecos.model.vos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "AddHomeMember入参")
public class TuyaAddHomeMemberVo {

    @ApiModelProperty(value = "账号标识", required = true)
    @NotBlank(message = "err.not.blank")
    private String appSchema;

    @ApiModelProperty(value = "成员对象", required = true)
    @Valid
    private HomeMemberVo member;
}
