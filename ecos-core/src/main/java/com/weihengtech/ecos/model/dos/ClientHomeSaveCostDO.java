package com.weihengtech.ecos.model.dos;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_home_save_cost")
public class ClientHomeSaveCostDO{

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 家庭Id
     */
    private Long homeId;

    /**
     * 作用日期
     */
    private Date day;

    /**
     * 作用时段
     */
    private Integer hour;

    /**
     * 负载消耗成本
     */
    private BigDecimal loadCost;

    /**
     * 电网取电成本
     */
    private BigDecimal gridCost;

    /**
     * 馈网收益
     */
    private BigDecimal feedEarnings;

    /**
     * 创建时间
     */
    private Date createTime;


}
