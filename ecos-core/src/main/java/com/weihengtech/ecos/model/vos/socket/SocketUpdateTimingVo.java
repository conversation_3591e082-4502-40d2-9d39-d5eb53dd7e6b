package com.weihengtech.ecos.model.vos.socket;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @program: ecos-server
 * @description: 更新定时任务入参
 * @author: jiahao.jin
 * @create: 2024-02-28 19:15
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "单插更新定时开关任务入参")
public class SocketUpdateTimingVo {

    @ApiModelProperty(name = "taskId", value = "任务id", required = true)
    private String taskId;

    @ApiModelProperty(name = "status", value = "定时任务状态")
    @NotNull(message = "err.not.null")
    public Boolean status;

    @ApiModelProperty(name = "status", value = "单插开关状态(0：关闭，1：开启)")
    @NotNull(message = "err.not.null")
    @Max(value = 1, message = "err.invalid.param")
    @Min(value = 0, message = "err.invalid.param")
    public int socketSwitch;

    @ApiModelProperty(name = "week", value = "星期(0：仅一次， 1-7：固定周几)")
    @NotEmpty(message = "err.not.empty")
    public List<Integer> week;

    @ApiModelProperty(name = "startTime", value = "开关时间")
    @NotBlank(message = "err.not.blank")
    public String socketTime;

    private static final java.util.regex.Pattern TIME_PATTERN = Pattern.compile("^(?:[01]\\d|2[0-3]):[0-5]\\d$");

    public void checkParams() {
        if (week.size() != 0) {
            for (Integer w : week) {
                if (w < 0 || w > 7) {
                    throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
                }
            }
        }

        if (!TIME_PATTERN.matcher(socketTime).matches()) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }

    }
}
