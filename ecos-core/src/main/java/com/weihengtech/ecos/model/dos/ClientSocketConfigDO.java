package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * client_socket_config
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_socket_config")
public class ClientSocketConfigDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long deviceId;

    /**
     * 倒计时开始时间
     */
    private Date countdownEnd;
}