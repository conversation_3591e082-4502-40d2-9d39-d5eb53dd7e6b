package com.weihengtech.ecos.model.dtos.insight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备平均耗能统计回参")
public class InsightDeviceEnergyAvgStatisticsDto {

	@ApiModelProperty(name = "maxEnergy", value = "最大耗能", required = true)
	private BigDecimal maxEnergy;

	@ApiModelProperty(name = "avgEnergy", value = "平均耗能", required = true)
	private BigDecimal avgEnergy;

	@ApiModelProperty(name = "minEnergy", value = "最小耗能", required = true)
	private BigDecimal minEnergy;

	@ApiModelProperty(name = "weekEnergy", value = "周能量", required = true)
	private Map<Integer, DeviceEnergy> weekEnergy;

	@Getter
	@Setter
	public static class DeviceEnergy {

		@ApiModelProperty(name = "solarEnergy", value = "光伏能量", required = true)
		private BigDecimal solarEnergy;

		@ApiModelProperty(name = "batteryEnergy", value = "电池能量", required = true)
		private BigDecimal batteryEnergy;

		@ApiModelProperty(name = "gridEnergy", value = "电网能量", required = true)
		private BigDecimal gridEnergy;
	}
}
