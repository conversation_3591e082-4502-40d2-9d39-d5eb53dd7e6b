package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设置页手机入参")
public class SettingBindPhoneVo {

	@ApiModelProperty(name = "phone", value = "phone手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;

	@ApiModelProperty(name = "code", value = "验证码", required = true)
	@NotBlank(message = "err.not.blank")
	private String code;
}
