package com.weihengtech.ecos.model.dtos.insight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: Insight页面功耗图表数据
 * @author: jiahao.jin
 * @create: 2023-11-09 13:37
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "Insight页面功耗图表数据")
public class InsightConsumptionDataDto {
    @ApiModelProperty(name = "fromBatteryDps", value = "电池放电图表", required = true)
    private Map<Long, Object> fromBatteryDps = new HashMap<>();
    @ApiModelProperty(name = "toBatteryDps", value = "电池充电图表", required = true)
    private Map<Long, Object> toBatteryDps = new HashMap<>();
    @ApiModelProperty(name = "fromGridDps", value = "电网买电图表", required = true)
    private Map<Long, Object> fromGridDps = new HashMap<>();
    @ApiModelProperty(name = "toGridDps", value = "电网馈电图表", required = true)
    private Map<Long, Object> toGridDps = new HashMap<>();
    @ApiModelProperty(name = "fromSolarDps", value = "光伏发电图表", required = true)
    private Map<Long, Object> fromSolarDps = new HashMap<>();
    @ApiModelProperty(name = "homeEnergyDps", value = "家庭能耗图表", required = true)
    private Map<Long, Object> homeEnergyDps = new HashMap<>();
    @ApiModelProperty(name = "epsDps", value = "EPS能耗图表", required = true)
    private Map<Long, Object> epsDps = new HashMap<>();
    @ApiModelProperty(name = "selfPoweredDps", value = "自发自用率图表", required = true)
    private Map<Long, Object> selfPoweredDps = new HashMap<>();
}
