package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: ecos-server
 * @description: 消息表
 * @author: jiahao.jin
 * @create: 2024-05-09 15:20
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_session_message")
public class ClientSessionMessageDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long sessionId;

    private Long userId;

    private Integer type; // 使用Integer类型表示消息类型

    private Long parentId;

    private String content;

    private LocalDateTime timestamp; // 使用LocalDateTime类型表示日期时间
}
