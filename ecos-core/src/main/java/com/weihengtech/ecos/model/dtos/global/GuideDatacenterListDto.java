package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "数据中心国家回参")
public class GuideDatacenterListDto {
	@ApiModelProperty(value = "主键id")
	private Integer id;
	@ApiModelProperty(value = "数据中心")
	private String datacenter;
	@ApiModelProperty(value = "电话号码前缀")
	private Integer phoneCode;
	@ApiModelProperty(value = "国家")
	private String country;
	@ApiModelProperty(value = "图片")
	private String pic;
}
