package com.weihengtech.ecos.model.vos.settings;

import com.weihengtech.ecos.annotation.validator.PasswordValidate;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "更新用户密码入参")
public class SettingsUserPasswordUpdateVo extends BaseVO {

	@ApiModelProperty(name = "originalPassword", value = "原密码", required = true)
	@NotBlank(message = "err.not.blank")
	private String originalPassword;

	@ApiModelProperty(name = "targetPassword", value = "目标密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String targetPassword;

	@ApiModelProperty(name = "repeatTargetPassword", value = "重复目标密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String repeatTargetPassword;

	@Override
	public void checkParams() {
		if (!targetPassword.equals(repeatTargetPassword)) {
			throw new EcosException(EcosExceptionEnum.TWICE_PASSWORD_NOT_EQUALS);
		}
	}
}
