package com.weihengtech.ecos.model.vos.guide;

import com.weihengtech.ecos.annotation.validator.PasswordValidate;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "忘记密码入参")
public class GuideForgetPasswordVo extends BaseVO implements Serializable {

	private static final long serialVersionUID = -662486987159230645L;

	@ApiModelProperty(name = "token", value = "验证token", required = true)
	@NotBlank(message = "err.not.blank")
	private String token;

	@ApiModelProperty(name = "password", value = "密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String password;

	@ApiModelProperty(name = "confirmPassword", value = "重复密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String confirmPassword;

	@Override
	public void checkParams() {
		if (!password.equals(confirmPassword)) {
			throw new EcosException(EcosExceptionEnum.TWICE_PASSWORD_NOT_EQUALS);
		}
	}
}
