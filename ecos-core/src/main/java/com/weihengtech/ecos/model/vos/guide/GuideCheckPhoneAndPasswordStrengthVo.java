package com.weihengtech.ecos.model.vos.guide;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "手机号&密码强度检查入参")
public class GuideCheckPhoneAndPasswordStrengthVo {

	@ApiModelProperty(name = "password", value = "要校验的密码", required = true)
	@NotBlank(message = "err.not.blank")
	@Length(min = 8, max = 16, message = "err.password.length")
	private String password;

	@ApiModelProperty(name = "phone", value = "手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;

	@ApiModelProperty(name = "code", value = "注册验证码", required = true)
	@NotBlank(message = "err.not.blank")
	private String code;
}
