package com.weihengtech.ecos.model.vos.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElePriceWholesaleVO {

	@ApiModelProperty(name = "homeId", value = "家庭Id", required = true)
	@NotNull(message = "err.not.blank")
	private String homeId;

	@ApiModelProperty(name = "homeId", value = "电价区域", required = true)
	private String region;

	@ApiModelProperty(name = "homeId", value = "购电税费", required = true)
	private BigDecimal purchaseTax;

	@ApiModelProperty(name = "currency", value = "币种")
	private Integer currency;
}
