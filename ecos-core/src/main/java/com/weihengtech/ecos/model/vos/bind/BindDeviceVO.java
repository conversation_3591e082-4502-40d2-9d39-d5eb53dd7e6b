package com.weihengtech.ecos.model.vos.bind;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 绑定通用参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/7 14:24
 */
@Data
public class BindDeviceVO {

    @ApiModelProperty(value = "网关SN标识", required = true)
    @NotBlank(message = "err.not.blank")
    private String wifiSn;

    @ApiModelProperty(name = "lon", value = "地区/城市经度")
    private Double lon;

    @ApiModelProperty(name = "lat", value = "地区/城市纬度")
    private Double lat;

    private Integer dataSource;
    private Integer tsdbSource;

    public void buildSourceParam() {}
}
