package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 资源分类树回参
 * @author: jiahao.jin
 * @create: 2024-02-01 15:19
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "资源分类树回参")
public class ResourceCategoryTreeDto {

    @ApiModelProperty(name = "id", value = "资源Id", required = true)
    private Integer id;

    @ApiModelProperty(name = "code", value = "资源英文名", required = true)
    private String code;

    @ApiModelProperty(name = "name", value = "资源中文名字", required = true)
    private String name;

    @ApiModelProperty(name = "name", value = "资源中文名字", required = true)
    private List<ResourceCategoryTreeDto> types;
}
