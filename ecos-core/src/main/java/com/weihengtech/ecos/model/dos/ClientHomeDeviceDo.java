package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 家庭-设备关联表
 * @author: jiahao.jin
 * @create: 2024-01-21 11:20
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_home_device")
public class ClientHomeDeviceDo {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long homeId;

    private Long deviceId;

    // 设备别名
    private String deviceName;

    private Long createTime;

    private Long updateTime;
}
