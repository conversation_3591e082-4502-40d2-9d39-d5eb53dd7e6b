package com.weihengtech.ecos.model.dtos.insight;

import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2023-11-08 17:09
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "能耗界面数据回参")
public class InsightDeviceDataDto {

    @ApiModelProperty(name = "selfPowered", value = "自发自用率", required = true)
    private BigDecimal selfPowered = BigDecimal.ZERO;

    @ApiModelProperty(name = "deviceRealtimeDto", value = "每天的功率数据（按天查询时有数据）")
    private HomeNowDeviceRealtimeDto deviceRealtimeDto;

    @ApiModelProperty(name = "deviceStatisticsDto", value = "每天、每月、每年、总共的功耗", required = true)
    private InsightDeviceStatisticsDto deviceStatisticsDto;

    @ApiModelProperty(name = "insightConsumptionDataDto", value = "每月、每年、总共(以首次安装时间为开始)的功耗图表")
    private InsightConsumptionDataDto insightConsumptionDataDto;
}
