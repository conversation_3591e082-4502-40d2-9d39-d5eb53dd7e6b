package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_ele_price_wholesale")
public class ClientElePriceWholesaleDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 家庭Id
     */
    private Long homeId;

    /**
     * 电价区域
     */
    private String region;

    /**
     * 购电税费
     */
    private BigDecimal purchaseTax;

}
