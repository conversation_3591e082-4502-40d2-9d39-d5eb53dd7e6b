package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 更新最大充电功率入参
 * @author: jiahao.jin
 * @create: 2024-03-11 17:12
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "更新最大充电功率入参")
public class UpdateMaxChargePowerVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    String deviceId;

    @ApiModelProperty(name = "maxPower", value = "功率（KW）", required = true)
    @NotNull(message = "err.not.null")
    Double maxPower;
}
