package com.weihengtech.ecos.model.dtos.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/12 16:02
 * @version 1.0
 */
@Data
@ApiModel(value = "设备信息入参类")
public class DynamicSwitchVO {

	@ApiModelProperty(name = "deviceName", value = "设备名")
	@NotBlank(message = "err.not.blank")
	private String deviceName;

	@ApiModelProperty(name = "dynamicExport", value = "是否开启")
	@NotNull(message = "err.not.null")
	private Boolean dynamicExport;
}
