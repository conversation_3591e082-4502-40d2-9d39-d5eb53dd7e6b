package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "引导页时区回参")
public class GuideTimezoneDto implements Serializable {

	private static final long serialVersionUID = 2623355573900090942L;

	private Integer id;

	private String timeZone;

	private String enName;

	private String cnName;
}
