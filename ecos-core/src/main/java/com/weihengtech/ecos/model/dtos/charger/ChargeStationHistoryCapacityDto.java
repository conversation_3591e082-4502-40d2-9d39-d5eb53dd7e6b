package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 充电桩历史充电量数据
 * @author: jiahao.jin
 * @create: 2024-03-04 20:17
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电桩历史充电量数据")
public class ChargeStationHistoryCapacityDto {

    @ApiModelProperty(name = "capacityDps", value = "充电量Dps", required = true)
    private Map<Long, Object> capacityDps = new HashMap<>();

    @ApiModelProperty(name = "totalCapacity", value = "总充电量", required = true)
    private BigDecimal totalCapacity;
}
