package com.weihengtech.ecos.model.dtos.ele;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 11:39
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EleRegionDTO {
    private String countryCn;
    private String countryEn;
    private String nationalFlag;
    private List<Region> regions;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Region {
        private String regionCn;
        private String regionEn;
    }
}
