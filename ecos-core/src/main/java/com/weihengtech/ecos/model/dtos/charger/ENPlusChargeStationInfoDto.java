package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: ecos-server
 * @description: EN+充电桩详情回参
 * @author: jiahao.jin
 * @create: 2024-02-19 11:49
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "EN+充电桩详情回参")
public class ENPlusChargeStationInfoDto {

    @ApiModelProperty(name = "name", value = "充电桩名称")
    private String name;

    @ApiModelProperty(name = "mode", value = "充电桩联网模式，（0：即插即充，1：远程未联网，2：有线联网，3：无线联网）")
    private Integer mode;

    @ApiModelProperty(name = "ratedPower", value = "额定功率")
    private String ratedPower;

    @ApiModelProperty(name = "minRatedPower", value = "最小功率")
    private String minRatedPower;

    @ApiModelProperty(name = "ratedCurrent", value = "额定电流")
    private String ratedCurrent;

    @ApiModelProperty(name = "voltageUp", value = "电压上限")
    private String voltageUp;

    @ApiModelProperty(name = "voltageDown", value = "电压下限")
    private String voltageDown;

    @ApiModelProperty(name = "sysRunMode", value = "1: 正常; 2: 故障; 8: 备电; 9: 离线")
    private Integer sysRunMode = -5;

    @ApiModelProperty(name = "cpFirmwareVersion", value = "固件版本")
    private String cpFirmwareVersion;

    @ApiModelProperty(name = "cpPlugAndChargeMsg", value = "即插即用开关")
    private String cpPlugAndChargeMsg;

    @ApiModelProperty(name = "master", value = "操作者的主从状态 0: 从 1: 主", required = true)
    private Integer master;

    private List<ENPlusChargeStationInfoDto.BindAccount> accountList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BindAccount {

        private Integer seriesId;

        private String accountId;

        private String account;

        private String bindTime;

        @ApiModelProperty(name = "master", value = "0: 从 1: 主", required = true)
        private Integer master;

        private Boolean isInstaller;

        private Long countdownTime;
    }
}
