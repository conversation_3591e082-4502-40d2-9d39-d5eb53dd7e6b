package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
@TableName("client_user_role")
public class ClientUserRoleDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private Long clientUserId;

	private String roleName;
}
