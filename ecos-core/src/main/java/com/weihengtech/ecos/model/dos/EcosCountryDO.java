package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("ecos_country")
public class EcosCountryDO {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	private String name;
}
