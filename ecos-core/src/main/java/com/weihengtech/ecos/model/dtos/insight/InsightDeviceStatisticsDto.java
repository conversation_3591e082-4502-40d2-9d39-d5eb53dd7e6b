package com.weihengtech.ecos.model.dtos.insight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @program: ecos-server
 * @description: 能耗页面能耗统计数据
 * @author: jiahao.jin
 * @create: 2023-12-19 19:00
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "能耗页面能耗统计数据")
public class InsightDeviceStatisticsDto {

    @ApiModelProperty(name = "consumptionEnergy", value = "家庭用电量", required = true)
    private BigDecimal consumptionEnergy;

    @ApiModelProperty(name = "fromBattery", value = "电池放电量", required = true)
    private BigDecimal fromBattery;

    @ApiModelProperty(name = "toBattery", value = "电池充电量", required = true)
    private BigDecimal toBattery;

    @ApiModelProperty(name = "fromGrid", value = "电网买电量", required = true)
    private BigDecimal fromGrid;

    @ApiModelProperty(name = "toGrid", value = "电网卖电量", required = true)
    private BigDecimal toGrid;

    @ApiModelProperty(name = "fromSolar", value = "光伏充电量", required = true)
    private BigDecimal fromSolar;

    @ApiModelProperty(name = "eps", value = "EPS用电量", required = true)
    private BigDecimal eps;

    @ApiModelProperty(name = "soc", value = "电池容量", required = true)
    private BigDecimal soc;
}
