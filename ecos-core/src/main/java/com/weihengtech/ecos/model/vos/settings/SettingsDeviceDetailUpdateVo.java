package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备详情更新入参")
public class SettingsDeviceDetailUpdateVo {

	@ApiModelProperty(name = "deviceAliasName", value = "设备别名", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceAliasName;

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
