package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 设备动态输出校验阶段
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("storage_dynamic_stage")
public class StorageDynamicStageDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer storageId;

    /**
     * 阶段
     */
    private Integer stage;

    /**
     * 阶段状态
     */
    private Integer stageState;

    /**
     * 异常信息
     */
    private String errMsg;

    /**
     * 阶段执行时间
     */
    private Date stageTime;


}
