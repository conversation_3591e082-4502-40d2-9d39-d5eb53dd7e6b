package com.weihengtech.ecos.model.vos.customize;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2023-11-17 10:03
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "customize页面聚合入参")
public class CustomizeInfoEzV2Vo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @Min(value = 0)
    @Max(value = 2)
    @ApiModelProperty(name = "chargeUserMode", value = "selfPowered = 0;loadShifting = 1;backup = 2", required = true)
    private Integer chargeUseMode;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "minCapacity", value = "预留SOC")
    private Integer minCapacity;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "epsBatteryMin", value = "离网预留SOC")
    private Integer epsBatteryMin;

    @Min(value = 0)
    @Max(value = 100)
    @ApiModelProperty(name = "maxFeedIn", value = "馈网功率比例")
    private Integer maxFeedIn;

    @Min(value = 0)
    @Max(value = 1)
    @ApiModelProperty(name = "dischargeToGridFlag", value = "电池馈网")
    private Integer dischargeToGridFlag;


    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "selfSoc", value = "自发自用SOC")
    private Integer selfSoc;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "selfEpsBat", value = "自发自用离网预留SOC")
    private Integer selfEpsBat;

    @Min(value = 0)
    @Max(value = 100)
    @ApiModelProperty(name = "selfFeedIn", value = "自发自用馈网功率比例")
    private Integer selfFeedIn;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "regularSoc", value = "定时充放SOC")
    private Integer regularSoc;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "regularEpsBat", value = "定时充放离网预留SOC")
    private Integer regularEpsBat;

    @Min(value = 0)
    @Max(value = 100)
    @ApiModelProperty(name = "regularFeedIn", value = "定时充放馈网功率比例")
    private Integer regularFeedIn;

    @Min(value = 50)
    @Max(value = 100)
    @ApiModelProperty(name = "backupSoc", value = "备电预留SOC")
    private Integer backupSoc;

    @Min(value = 10)
    @Max(value = 100)
    @ApiModelProperty(name = "backupEpsBat", value = "备电离网预留SOC")
    private Integer backupEpsBat;

    @Min(value = 0)
    @Max(value = 100)
    @ApiModelProperty(name = "backupFeedIn", value = "备电预留馈网功率比例")
    private Integer backupFeedIn;

    @ApiModelProperty(name = "region", value = "电价地区")
    private String region;
    @ApiModelProperty(name = "autoStrategy", value = "开启自动策略")
    private Integer autoStrategy;
    @ApiModelProperty(name = "autoHomeStrategy", value = "开启家庭联动自动策略")
    private Integer autoHomeStrategy;
    @ApiModelProperty(name = "strategyMode", value = "策略模式")
    private Integer strategyMode;
    @ApiModelProperty(name = "defChargePower", value = "自定义充电功率")
    private Integer defChargePower;
    @ApiModelProperty(name = "defDischargePower", value = "自定义放电功率")
    private Integer defDischargePower;
    @ApiModelProperty(name = "timezone", value = "时区")
    private String timezone;
    @ApiModelProperty(name = "priceImportType", value = "电价导入类型：0：自定义|1：家庭电价")
    private Integer priceImportType = 0;
    @ApiModelProperty(name = "purchaseTax", value = "购电税费")
    private BigDecimal purchaseTax;

    // JBW need remove
    private List<ChargingStructDTO> chargingList;

    private List<ChargingStructDTO> dischargingList;

    public List<ChargingStructDTO> copyChargingList() {
        if (CollUtil.isEmpty(chargingList)) {
            return Collections.emptyList();
        }
        List<ChargingStructDTO> chargingList = new ArrayList<>();
        this.chargingList.forEach(i -> chargingList.add(new ChargingStructDTO()
                .withStartMinute(i.getStartMinute())
                .withStartHour(i.getStartHour())
                .withEndMinute(i.getEndMinute())
                .withEndHour(i.getEndHour())
                .withPower(i.getPower())
                .withAbandonPv(i.getAbandonPv())));
        return chargingList;
    }

    public List<ChargingStructDTO> copyDischargingList() {
        if (CollUtil.isEmpty(dischargingList)) {
            return Collections.emptyList();
        }
        List<ChargingStructDTO> dischargingList = new ArrayList<>();
        this.dischargingList.forEach(i -> dischargingList.add(new ChargingStructDTO()
                .withStartMinute(i.getStartMinute())
                .withStartHour(i.getStartHour())
                .withEndMinute(i.getEndMinute())
                .withEndHour(i.getEndHour())
                .withPower(i.getPower())
                .withAbandonPv(i.getAbandonPv())));
        return dischargingList;
    }
}
