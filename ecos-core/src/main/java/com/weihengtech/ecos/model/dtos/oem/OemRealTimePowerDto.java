package com.weihengtech.ecos.model.dtos.oem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OemRealTimePowerDto {

	@ApiModelProperty(name = "solarPowerDps", value = "光伏功率Dps")
	private Map<Long, Object> solarPowerDps = new HashMap<>();

	@ApiModelProperty(name = "batteryPowerDps", value = "电池功率Dps")
	private Map<Long, Object> batteryPowerDps = new HashMap<>();

	@ApiModelProperty(name = "gridPowerDps", value = "电网功率Dps")
	private Map<Long, Object> gridPowerDps = new HashMap<>();

	@ApiModelProperty(name = "meterPowerDps", value = "仪表功率Dps")
	private Map<Long, Object> meterPowerDps = new HashMap<>();

	@ApiModelProperty(name = "homePowerDps", value = "家庭功率Dps")
	private Map<Long, Object> homePowerDps = new HashMap<>();

	@ApiModelProperty(name = "epsPowerDps", value = "EPS功率Dps")
	private Map<Long, Object> epsPowerDps = new HashMap<>();

	@ApiModelProperty(name = "date", value = "日期")
	private String date;

}
