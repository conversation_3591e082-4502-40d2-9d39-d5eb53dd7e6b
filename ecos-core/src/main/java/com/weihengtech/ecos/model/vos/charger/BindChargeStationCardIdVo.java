package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 充电桩绑定卡片入参
 * @author: jiahao.jin
 * @create: 2024-03-08 10:31
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电桩绑定卡片入参")
public class BindChargeStationCardIdVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "cardId", value = "卡片码", required = true)
    @NotBlank(message = "err.not.blank")
    private String cardId;
}
