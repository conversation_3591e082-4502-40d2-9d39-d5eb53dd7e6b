package com.weihengtech.ecos.model.dtos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页备电分页回参")
public class HomeEventsBackupPageDto {

	@ApiModelProperty(name = "energy", value = "单次消耗能源", required = true)
	private BigDecimal energy;

	@ApiModelProperty(name = "minutes", value = "单次供电持续时间", required = true)
	private BigDecimal minutes;

	@ApiModelProperty(name = "startTime", value = "开始时间", required = true)
	private String startTime;
}
