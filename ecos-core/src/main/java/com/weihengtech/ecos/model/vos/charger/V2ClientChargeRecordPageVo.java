package com.weihengtech.ecos.model.vos.charger;

import com.weihengtech.ecos.model.vos.global.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 查询充电记录数据列表
 * @author: jiahao.jin
 * @create: 2024-03-05 15:02
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "分页查询充电记录数据")
public class V2ClientChargeRecordPageVo extends PageInfoVO {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "startTime", value = "开始时间戳", required = true)
    private Long startTime;

    @ApiModelProperty(name = "endTime", value = "结束时间戳", required = true)
    private Long endTime;
}
