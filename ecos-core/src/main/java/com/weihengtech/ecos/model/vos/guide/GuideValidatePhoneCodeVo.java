package com.weihengtech.ecos.model.vos.guide;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "校验手机验证码入参")
public class GuideValidatePhoneCodeVo {

	@ApiModelProperty(name = "phone", value = "手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;

	@ApiModelProperty(name = "code", value = "验证码", required = true)
	@NotBlank(message = "err.not.blank")
	private String code;
}
