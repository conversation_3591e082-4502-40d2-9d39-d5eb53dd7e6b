package com.weihengtech.ecos.model.vos.charger;

import com.weihengtech.ecos.model.vos.global.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 充电记录查询入参
 * @author: jiahao.jin
 * @create: 2024-02-21 15:01
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电记录查询入参")
public class ChargeRecordListPageVo extends PageInfoVO {

    @NotBlank(message = "err.not.blank")
    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    private String deviceId;

    @NotNull(message = "err.not.null")
    @ApiModelProperty(name = "startTime", value = "开始时间时间戳(单位秒)", required = true)
    private Long startTime;

    @NotNull(message = "err.not.null")
    @ApiModelProperty(name = "endTime", value = "结束时间时间戳(单位秒)", required = true)
    private Long endTime;
}
