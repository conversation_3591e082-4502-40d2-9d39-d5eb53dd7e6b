package com.weihengtech.ecos.model.dtos.settings;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备转移入参
 *
 * <AUTHOR>
 * @date 2023/8/21 16:26
 * @version 1.0
 */
@Data
public class SettingTransferDTO {

	@ApiModelProperty("app标识")
	private String appSchema;

	@ApiModelProperty("国家编码")
	private String countryCode;

	@ApiModelProperty("安装商uuid：tuya平台的账号")
	private String installerId;

	@ApiModelProperty("家庭id")
	private Long homeId;

	@ApiModelProperty("设备id：绑定账号设备关系")
	private List<Long> deviceIds;

	@ApiModelProperty("系统id：更新转移时间")
	private Long systemInfoId;

}
