package com.weihengtech.ecos.model.dtos.oem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OemHistoryEnergyDto {

	@ApiModelProperty(name = "fromBatteryDps", value = "电池放电图表", required = true)
	private Map<Long, Object> fromBatteryDps = new HashMap<>();
	@ApiModelProperty(name = "toBatteryDps", value = "电池充电图表", required = true)
	private Map<Long, Object> toBatteryDps = new HashMap<>();
	@ApiModelProperty(name = "fromGridDps", value = "电网买电图表", required = true)
	private Map<Long, Object> fromGridDps = new HashMap<>();
	@ApiModelProperty(name = "toGridDps", value = "电网馈电图表", required = true)
	private Map<Long, Object> toGridDps = new HashMap<>();
	@ApiModelProperty(name = "fromSolarDps", value = "光伏发电图表", required = true)
	private Map<Long, Object> fromSolarDps = new HashMap<>();
	@ApiModelProperty(name = "homeEnergyDps", value = "家庭能耗图表", required = true)
	private Map<Long, Object> homeEnergyDps = new HashMap<>();
	@ApiModelProperty(name = "fromBattery", value = "电池放电统计", required = true)
	private BigDecimal fromBattery = BigDecimal.ZERO;
	@ApiModelProperty(name = "toBattery", value = "电池充电统计", required = true)
	private BigDecimal toBattery = BigDecimal.ZERO;
	@ApiModelProperty(name = "fromGrid", value = "电网买电统计", required = true)
	private BigDecimal fromGrid = BigDecimal.ZERO;
	@ApiModelProperty(name = "toGrid", value = "电网馈电统计", required = true)
	private BigDecimal toGrid = BigDecimal.ZERO;
	@ApiModelProperty(name = "fromSolar", value = "光伏发电统计", required = true)
	private BigDecimal fromSolar = BigDecimal.ZERO;
	@ApiModelProperty(name = "homeEnergy", value = "家庭能耗统计", required = true)
	private BigDecimal homeEnergy = BigDecimal.ZERO;
	@ApiModelProperty(name = "cycleTimes", value = "循环次数", required = true)
	private Integer cycleTimes = 0;
	@ApiModelProperty(name = "selfPowered", value = "自发自用率", required = true)
	private BigDecimal selfPowered = BigDecimal.ZERO;
}
