package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class GlobalConfigDto {

	@ApiModelProperty(name = "batteryHealth", value = "0: 关; 1: 开", required = true)
	private Integer batteryHealth;

	@ApiModelProperty(name = "batteryCapacity", value = "电池电量是否显示 0:全不显示 1:全显示  2: 仅显示数字 3:仅显示图标", required = true)
	private Integer batteryCapacity;

	@ApiModelProperty(name = "helpManual", value = "帮助手册 0: 关 1: 开", required = true)
	private Boolean helpManual;

	@ApiModelProperty(name = "faq", value = "常见问题 0: 关 1: 开", required = true)
	private Boolean faq;

	@ApiModelProperty(name = "helpManualUrl", value = "帮助手册", required = true)
	private String helpManualUrl;

	@ApiModelProperty(name = "faqUrl", value = "常见问题", required = true)
	private String faqUrl;
}
