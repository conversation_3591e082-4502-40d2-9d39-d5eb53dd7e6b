package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: EN+充电桩充电记录返回值
 * @author: jiahao.jin
 * @create: 2024-02-21 14:37
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "EN+充电桩充电记录返回值")
public class ENPlusChargeStationChargeRecordDto {

    @ApiModelProperty(name = "id", value = "充电记录ID")
    private Integer id;

    @ApiModelProperty(name = "chargeCapacity", value = "充电量")
    private String chargeCapacity;

    @ApiModelProperty(name = "startTime", value = "充电开始时间")
    private Long startTime;

    @ApiModelProperty(name = "endTime", value = "充电结束时间")
    private Long endTime;

}
