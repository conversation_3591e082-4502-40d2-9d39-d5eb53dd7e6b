package com.weihengtech.ecos.model.vos.guide;

import com.weihengtech.ecos.annotation.validator.PasswordValidate;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "用户手机号注册入参")
public class GuideClientUserRegisterPhoneVo extends BaseVO {

	@ApiModelProperty(value = "密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String password;

	@ApiModelProperty(value = "确定密码", required = true)
	@NotBlank(message = "err.not.blank")
	@PasswordValidate(message = "err.password.validate")
	private String confirmPassword;

	@ApiModelProperty(value = "手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;

	@ApiModelProperty(value = "时区id", required = true)
	@NotNull(message = "err.not.null")
	private Integer timeZoneId;

	@ApiModelProperty(name = "code", value = "验证码", required = true)
	@NotBlank(message = "err.not.blank")
	private String code;

	@ApiModelProperty(value = "数据中心id")
	private Integer datacenterId;

	@Override
	public void checkParams() {
		if (!password.equals(confirmPassword)) {
			throw new EcosException(EcosExceptionEnum.TWICE_PASSWORD_NOT_EQUALS);
		}
	}
}
