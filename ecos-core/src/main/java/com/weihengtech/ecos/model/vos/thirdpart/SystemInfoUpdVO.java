package com.weihengtech.ecos.model.vos.thirdpart;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 配网进度
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 16:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemInfoUpdVO {

    @ApiModelProperty("系统id")
    @NotNull(message = "err.not.blank")
    private Long id;

    @ApiModelProperty("系统名称")
    private String name;

    @ApiModelProperty("系统地址")
    private Integer datacenterId;

    @ApiModelProperty("用户名")
    private String ownerFirstName;

    @ApiModelProperty("用户姓")
    private String ownerLastName;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机")
    private String phone;

    @ApiModelProperty("安装日期")
    private Date installDate;

    @ApiModelProperty("当前步骤")
    private Integer currentStep;

    @ApiModelProperty
    private Date transferTime;

    @ApiModelProperty("安装商保留设备时长:-1,3,999")
    private Integer saveDeviceTime;
}
