package com.weihengtech.ecos.model.dtos.thirdpart;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
public class TuyaHomeUserListInfoDto {

    @JSONField(name = "country_code")
    private String countryCode;

    @JSONField(name = "member_account")
    private String memberAccount;

    private String uid;

    private Boolean admin;

    private String name;

    private String avatar;

    private Boolean owner;

}
