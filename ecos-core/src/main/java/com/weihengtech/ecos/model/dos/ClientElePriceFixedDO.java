package com.weihengtech.ecos.model.dos;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("client_ele_price_fixed")
public class ClientElePriceFixedDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 家庭Id
     */
    private Long homeId;

    /**
     * 购电电价
     */
    private BigDecimal purchasePrice;

    /**
     * 购电税费
     */
    private BigDecimal purchaseTax;

    /**
     * 馈网电价
     */
    private BigDecimal feedInPrice;


}
