package com.weihengtech.ecos.model.vos.guide;

import com.weihengtech.ecos.annotation.validator.IsAllowedClientType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "邮箱密码登录入参")
public class GuideEmailPasswordLoginVo implements Serializable {

	@ApiModelProperty(name = "email", value = "邮箱账号", required = true)
	@NotBlank(message = "err.not.blank")
	private String email;

	@ApiModelProperty(name = "password", value = "密码", required = true)
	@NotBlank(message = "err.not.blank")
	private String password;

	@ApiModelProperty(name = "clientType", value = "客户端类型", required = true)
	@NotBlank(message = "err.not.blank")
	@IsAllowedClientType(message = "err.valid.client.type")
	private String clientType;

	@ApiModelProperty(name = "clientVersion", value = "客户端版本号", required = true)
	@NotBlank(message = "err.not.blank")
	private String clientVersion;
}
