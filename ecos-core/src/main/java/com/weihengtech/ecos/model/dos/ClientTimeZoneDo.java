package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_time_zone")
public class ClientTimeZoneDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Integer id;

	private String timeZone;

	private String enName;

	private String cnName;
}
