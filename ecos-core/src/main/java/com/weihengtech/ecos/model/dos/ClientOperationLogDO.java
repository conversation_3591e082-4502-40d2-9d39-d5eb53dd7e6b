package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 操作记录实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/6 14:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_operation_log")
public class ClientOperationLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方法名
     */
    private String method;

    /**
     * 参数
     */
    private String param;

    /**
     * 方法返回值
     */
    private String result;

    /**
     * 异常信息
     */
    private String error;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 请求IP
     */
    private String ip;

    /**
     * 方法运行时间
     */
    private Long elapsedTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
