package com.weihengtech.ecos.model.vos.app;

import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: 分页查询历史对话记录入参
 * @author: jiahao.jin
 * @create: 2024-05-14 10:11
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "查询历史对话记录入参")
public class V2AIChatMessagesPageVo extends BaseVO {

    @ApiModelProperty(name = "sessionId", value = "会话ID，默认查询最新的那一个会话的聊天记录", required = true)
    private String sessionId;

    @ApiModelProperty(name = "sessionId", value = "消息ID，默认查询最新的那一个聊天", required = true)
    private String messageId;

    @ApiModelProperty(name = "size", value = "查询会话条数，默认10条")
    private Integer size;

    @Override
    public void checkParams() {
        if (size == null || size <= 0) {
            size = 10;
        }
    }
}
