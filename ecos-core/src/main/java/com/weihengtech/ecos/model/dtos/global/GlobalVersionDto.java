package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "全局版本回参")
public class GlobalVersionDto {

	@ApiModelProperty(name = "androidVersion", value = "安卓版本号", required = true)
	private String androidVersion;

	@ApiModelProperty(name = "iosVersion", value = "ios版本号", required = true)
	private String iosVersion;

	@ApiModelProperty(name = "isImportant", value = "是否重要", required = true)
	private Boolean isImportant;

	@ApiModelProperty(name = "flag", value = "0: 单次 1: 每次 2: 强制", required = true)
	private Integer flag;

	@ApiModelProperty(name = "content", value = "国际化更新描述", required = true)
	private String content;

	@ApiModelProperty(name = "preForceIosVersion", value = "上一个强制更新的ios版本", required = true)
	private String preForceIosVersion;

	@ApiModelProperty(name = "preForceAndroidVersion", value = "上一个强制更新的Android版本", required = true)
	private String preForceAndroidVersion;
}
