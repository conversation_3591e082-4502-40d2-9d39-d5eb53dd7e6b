package com.weihengtech.ecos.model.vos.app;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Insight更多信息能量通知入参")
public class InsightMoreInformationEnergyNotifyVo extends BaseVO {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "open", value = "2: 关 1: 开", required = true)
	@NotNull(message = "err.not.null")
	private Integer open;

	@ApiModelProperty(name = "threshold", value = "阈值", required = true)
	private Integer threshold;

	@ApiModelProperty(name = "email", value = "邮箱", required = true)
	private String email;

	@Override
	public void checkParams() {
		if (null == open || (2 != open && 1 != open)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
	}
}
