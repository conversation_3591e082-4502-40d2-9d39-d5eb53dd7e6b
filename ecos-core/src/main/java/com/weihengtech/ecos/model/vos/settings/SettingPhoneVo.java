package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "SettingPhoneVo")
public class SettingPhoneVo {

	@ApiModelProperty(name = "phone", value = "phone手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;
}
