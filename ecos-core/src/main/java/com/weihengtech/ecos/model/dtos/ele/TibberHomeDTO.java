package com.weihengtech.ecos.model.dtos.ele;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TibberHomeDTO {

	private String id;
	private String timeZone;
	private String type;
	private String appNickname;
	private String appAvatar;
	private Address address;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	private static class Address {
		private String address1;
		private String address2;
		private String address3;
	}

}
