package com.weihengtech.ecos.model.dtos.ele;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElePriceDetailDTO {

	/**
	 * 购电电价
	 */
	private BigDecimal purchasePrice;

	/**
	 * 购电税费
	 */
	private BigDecimal purchaseTax;

	/**
	 * 馈网电价
	 */
	private BigDecimal feedInPrice;

	public BigDecimal getSumPrice() {
		return purchasePrice.add(Optional.ofNullable(purchaseTax).orElse(BigDecimal.ZERO));
	}
}
