package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "tuya_datacenter")
public class TuyaDatacenterDo {
	@TableId(type = IdType.AUTO)
	private Integer id;
	private String datacenter;
	private String pic;
	private Integer phoneCode;
	private String zhCn;
	private String enUs;
	private String deDe;
	private String nlNl;
	private String frFr;
	private String esEs;
	private String ptPt;
	private String itIt;
	private String plPl;
}
