package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dict_item")
public class DictItemDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型Id
     */
    private Long dictId;

    /**
     * 字典项编码
     */
    private String code;

    /**
     * 缩略图base64
     */
    private String pic;

    @TableField("zh_CN")
    private String zhCN;

    @TableField("en_US")
    private String enUS;

    @TableField("de_DE")
    private String deDE;

    @TableField("es_ES")
    private String esES;

    @TableField("fr_FR")
    private String frFR;

    @TableField("it_IT")
    private String itIT;

    @TableField("nl_NL")
    private String nlNL;

    @TableField("pl_PL")
    private String plPL;

    @TableField("pt_PT")
    private String ptPT;
}
