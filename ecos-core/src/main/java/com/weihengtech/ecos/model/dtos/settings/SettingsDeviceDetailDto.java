package com.weihengtech.ecos.model.dtos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "配置设备详情回参")
public class SettingsDeviceDetailDto {

	private String deviceId;

	private String deviceAliasName;

	private String deviceSn;

	private String wifiSn;

	private String deviceModel;

	private List<BindAccount> accountList;

	@ApiModelProperty(name = "master", value = "操作者的主从状态 0: 从 1: 主", required = true)
	private Integer master;

	@ApiModelProperty(name = "type", value = "0: 易联; 1: 涂鸦", required = true)
	private Integer type;

	@Getter
	@Setter
	@ToString
	public static class BindAccount {

		private Integer seriesId;

		private String accountId;

		private String account;

		private String bindTime;

		@ApiModelProperty(name = "master", value = "0: 从 1: 主", required = true)
		private Integer master;
	}
}
