package com.weihengtech.ecos.model.dtos.ecos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页备电统计回参")
public class HomeEventsBackupStatisticsDto {

	@ApiModelProperty(name = "backupCount", value = "备电次数")
	private Integer backupCount;

	@ApiModelProperty(name = "backupEnergy", value = "备电总能量")
	private BigDecimal backupEnergy;

	@ApiModelProperty(name = "backupDuration", value = "备电总时长")
	private BigDecimal backupDuration;
}
