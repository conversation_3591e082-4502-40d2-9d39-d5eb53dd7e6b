package com.weihengtech.ecos.model.dtos.ele;

import com.weihengtech.ecos.model.dos.ClientElePriceUseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElePriceUseDTO {

	@ApiModelProperty(name = "homeId", value = "家庭Id", required = true)
	private String homeId;

	@ApiModelProperty(name = "monthPriceList", value = "月份电价配置", required = true)
	private List<ClientElePriceUseDO.MonthUsePrice> monthUsePriceList;

}
