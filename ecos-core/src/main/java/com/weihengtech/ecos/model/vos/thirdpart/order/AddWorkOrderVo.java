package com.weihengtech.ecos.model.vos.thirdpart.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "工单新增入参")
public class AddWorkOrderVo {

	@ApiModelProperty(value = "标题", required = true)
	@NotBlank(message = "err.not.blank")
	private String subject;
	@ApiModelProperty(value = "内容")
	private String content = "";
	@ApiModelProperty(value = "图片列表")
	private List<String> picList = new ArrayList<>();
	@ApiModelProperty(value = "设备序列号", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceSn;

	@ApiModelProperty(value = "通知邮箱", required = true)
	@NotBlank(message = "err.not.blank")
	@Email
	private String email;
}
