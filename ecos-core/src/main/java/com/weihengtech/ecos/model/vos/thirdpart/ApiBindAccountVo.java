package com.weihengtech.ecos.model.vos.thirdpart;

import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiBindAccountVo {

	@ApiModelProperty(name = "device", value = "设备bean", required = true)
	@NotNull(message = "err.not.null")
	private HybridSinglePhaseDO device;

	@ApiModelProperty(name = "email", value = "Ecos username", required = true)
	@NotBlank(message = "err.not.blank")
	private String username;
}
