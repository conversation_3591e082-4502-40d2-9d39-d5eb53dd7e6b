package com.weihengtech.ecos.model.dtos.global;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "减少碳排放量与节约标准煤回参")
public class GlobalReduceCarbonEmissionsDto {

	@ApiModelProperty(name = "reduceCarbonEmission", value = "碳减排", required = true)
	private String reduceCarbonEmission;

	@ApiModelProperty(name = "saveStandardCoal", value = "节约标准煤", required = true)
	private String saveStandardCoal;

	@ApiModelProperty(name = "level", value = "等级", required = true)
	private Integer level;
}
