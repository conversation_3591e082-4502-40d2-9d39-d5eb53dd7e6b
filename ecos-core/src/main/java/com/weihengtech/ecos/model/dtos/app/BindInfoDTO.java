package com.weihengtech.ecos.model.dtos.app;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-01-18 15:15
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "Hub批量查询代理商回参")
public class BindInfoDTO {
    private String resourceId;
    private String userId;
    private String roleId;
    private String remark;
    private String appId;
    private Long expireAt;
    private Long ct;
    private Long ut;
}
