package com.weihengtech.ecos.model.vos.app;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.CommonConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Oem历史能耗入参")
public class OemHistoryEnergyVo {

	@ApiModelProperty(name = "periodType", value = "0: 自选月; 1: 本周; 2: 本月; 3: 本季度; 4: 本年", required = true)
	@NotNull(message = "err.not.null")
	private Integer periodType;

	@ApiModelProperty(name = "choiceMonth", value = "自选月份", example = "2022-01")
	private String choiceMonth;

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	public void checkParams() {
		LinkedList<Integer> periodTypeList = ListUtil.toLinkedList(CommonConstants.PERIOD_DAY,
				CommonConstants.PERIOD_WEEK, CommonConstants.PERIOD_MONTH, CommonConstants.PERIOD_SEASON,
				CommonConstants.PERIOD_YEAR
		);
		if (!periodTypeList.contains(periodType)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		if (CommonConstants.PERIOD_DAY == periodType && StrUtil.isBlank(choiceMonth)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
	}
}
