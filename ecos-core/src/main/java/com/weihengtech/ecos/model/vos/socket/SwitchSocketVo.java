package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 开关插座入参
 * @author: jiahao.jin
 * @create: 2024-01-28 15:11
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "开关插座入参")
public class SwitchSocketVo extends SocketBaseVo {
    @ApiModelProperty(name = "switchStatus", value = "想要的开关状态", required = true)
    @NotNull(message = "err.not.null")
    private Boolean switchStatus ;

    @ApiModelProperty(name = "switchIndex", value = "开关编号(单插填：1)", required = true)
    @NotNull(message = "err.not.null")
    private Integer switchIndex ;

    @Override
    public void checkParams() {

    }
}
