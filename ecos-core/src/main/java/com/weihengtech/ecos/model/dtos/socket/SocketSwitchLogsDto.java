package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 插座开关日志回参
 * @author: jiahao.jin
 * @create: 2024-01-31 15:21
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座开关日志回参")
public class SocketSwitchLogsDto {

    @ApiModelProperty(name = "hasNext", value = "是否有下一页")
    private Boolean hasNext;

    @ApiModelProperty(name = "nextRowKey", value = "下一页列表key")
    private String nextRowKey;

    @ApiModelProperty(name = "logs", value = "开关日志列表")
    private List<SwitchLogDto> logs;
}
