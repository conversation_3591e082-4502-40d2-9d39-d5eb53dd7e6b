package com.weihengtech.ecos.model.dtos.thirdpart;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
public class TuyaHomeMemberListInfoDto {

    private String uid;

    private String username;

    private String mobile;

    private String email;

    @JSONField(name = "create_time")
    private Long createTime;

    @JSONField(name = "update_time")
    private Long updateTime;

    @JSONField(name = "country_code")
    private String countryCode;

}
