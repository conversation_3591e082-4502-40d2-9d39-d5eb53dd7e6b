package com.weihengtech.ecos.model.vos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @program ecos-admin-backend
 * @description
 * @create 2023-08-22 17:17
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "获取用户列表入参")
public class TuyaHomeUserListVo {

    @ApiModelProperty(value = "当前页数", required = true)
    @NotNull(message = "err.not.null")
    private Integer pageNo;

    @ApiModelProperty(value = "每页的行数。取值范围：0 到 100", required = true)
    @NotNull(message = "err.not.null")
    @Min(value = 1, message = "err.value.out")
    @Max(value = 20, message = "err.value.out")
    private Integer pageSize;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "开始时间，以 10 位时间戳表示")
    private Long startTime;

    @ApiModelProperty(value = "截止时间，以 10 位时间戳表示")
    private Long endTime;
}
