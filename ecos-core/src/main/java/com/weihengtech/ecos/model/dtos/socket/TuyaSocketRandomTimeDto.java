package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 涂鸦随机定时时间回参
 * @author: jiahao.jin
 * @create: 2024-01-30 17:33
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "涂鸦随机定时时间回参")
public class TuyaSocketRandomTimeDto {

    @ApiModelProperty(name = "status", value = "随机定时时间状态(0：关闭，1：开启)")
    private int status;

    @ApiModelProperty(name = "week", value = "星期(0：仅一次， 1-7：固定周几)")
    private List<Integer> week;

    @ApiModelProperty(name = "startTime", value = "开始时间")
    private String startTime ;

    @ApiModelProperty(name = "endTime", value = "结束时间")
    private String endTime;
}
