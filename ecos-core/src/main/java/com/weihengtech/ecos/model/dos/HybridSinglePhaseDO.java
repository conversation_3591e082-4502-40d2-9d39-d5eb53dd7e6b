package com.weihengtech.ecos.model.dos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
public class HybridSinglePhaseDO {

	private Long id;
	/** 别名 */
	private String alias;
	/** 设备sn */
	private String deviceSn;
	/** 配网sn */
	private String wifiSn;
	/** 设备名称 */
	private String deviceName;
	/** -5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检 */
	private Integer state;
	/** 经度 */
	private Double longitude;
	/** 纬度 */
	private Double latitude;
	/** 首次安装时间 */
	private Long firstInstall;
	/** 发货地区 */
	private Integer countryId;
	/** 数据中心 */
	private Integer datacenterId;
	/** IP地址 */
	private String ip;
	/** 品类 */
	private String category;
	/** 型号 */
	private String model;
	/** 类型 */
	private String type;
	/** 资源系列id */
	private Integer resourceSeriesId;
	/** 资源类型id */
	private Integer resourceTypeId;

	/** 平台 */
	private Integer dataSource;
	/** 0: 易联tsdb 1: tuya lindorm 2 易联 lindorm */
	private Integer tsdbSource;
	/** 创建时间 */
	private LocalDateTime createTime;
	/** 更新时间 */
	private LocalDateTime updateTime;

	/** 储能相关扩展字段 */
	private Boolean vppMode;
	private String deviceModel;
	private String brand;
	private String factory;
	private String powerBoardHardwareVersion;
	private String dsp1SoftwareVersion;
	private String dsp2SoftwareVersion;
	private String emsSoftwareVersion;
	private String emsHardwareVersion;
	private String wifiHardwareVersion;
	private String wifiSoftwareVersion;
	private String bmsGaugeVersion;
	private String bmsSn;
	private String bmsVendor;
	private String bmsSoftwareVersion;
	private String bmsHardwareVersion;
	private String dsp1SubVersion;
	private String dsp2SubVersion;
	private String emsSubVersion;
	private String arcDspSoftwareVersion;
	private String arcDspSubVersion;
	private String arcDspBootLoaderSoftwareVersion;
	private String ratedPower;

	/** 扩展信息 */
	private Object extInfo;
	private Boolean isMain;
}
