package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 充电桩查询历史充电量入参
 * @author: jiahao.jin
 * @create: 2024-03-04 20:19
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电桩查询历史充电量入参")
public class ChargeStationHistoryCapacityVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "periodType", value = "0: 天; 2: 月; 4: 年; 5: 总共", required = true)
    @NotNull(message = "err.not.null")
    private Integer periodType;

    @ApiModelProperty(name = "timestamp", value = "时间戳(查询总共可以不用传)")
    private Long timestamp;
}
