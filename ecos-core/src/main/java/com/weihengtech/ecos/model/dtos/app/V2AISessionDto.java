package com.weihengtech.ecos.model.dtos.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: 用户聊天会话回参
 * @author: jiahao.jin
 * @create: 2024-05-11 09:34
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "用户聊天会话回参")
public class V2AISessionDto {

    @ApiModelProperty(name = "id", value = "会话ID")
    private String id;

    @ApiModelProperty(name = "title", value = "标题")
    private String title;

    @ApiModelProperty(name = "createTime", value = "会话创建时间")
    private Long createTime; // 使用LocalDateTime类型表示日期时间

    @ApiModelProperty(name = "updateTime", value = "会话更新时间")
    private Long updateTime; // 使用LocalDateTime类型表示日期时间
}
