package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: ecos-server
 * @description: 会话表
 * @author: jiahao.jin
 * @create: 2024-05-09 15:13
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_session")
public class ClientSessionDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String chatId;

    private Long userId;

    private String title;

    private Boolean deleted; // 使用Boolean类型表示布尔值

    private LocalDateTime createTime; // 使用LocalDateTime类型表示日期时间

    private LocalDateTime updateTime; // 使用LocalDateTime类型表示日期时间
}
