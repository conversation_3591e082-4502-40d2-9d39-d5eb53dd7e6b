package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "SettingEmailVo")
public class SettingEmailVo {

	@ApiModelProperty(name = "email", value = "邮箱", required = true)
	@NotBlank(message = "err.not.blank")
	private String email;
}
