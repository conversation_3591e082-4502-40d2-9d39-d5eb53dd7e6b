package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-27 10:08
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "设备经纬度更新入参")
public class HomeDeviceCityUpdateVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "lon", value = "经度", required = true)
    @NotNull(message = "err.not.null")
    private Double lon;

    @ApiModelProperty(name = "lat", value = "纬度", required = true)
    @NotNull(message = "err.not.null")
    private Double lat;

}
