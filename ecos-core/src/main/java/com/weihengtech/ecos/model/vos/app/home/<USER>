package com.weihengtech.ecos.model.vos.app.home;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页历史记录入参")
public class HomeHistoryVo extends BaseVO {

	@ApiModelProperty(name = "periodType", value = "0: 自选月; 1: 本周; 2: 本月; 3: 本季度; 4: 本年", required = true)
	@NotNull(message = "err.not.null")
	private Integer periodType;

	@ApiModelProperty(name = "timestamp", value = "月份时间戳")
	private Long timestamp;

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@Override
	public void checkParams() {
		LinkedList<Integer> periodTypeList = ListUtil.toLinkedList(CommonConstants.PERIOD_DAY,
				CommonConstants.PERIOD_WEEK, CommonConstants.PERIOD_MONTH, CommonConstants.PERIOD_SEASON,
				CommonConstants.PERIOD_YEAR
		);
		if (!periodTypeList.contains(periodType)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		if (CommonConstants.PERIOD_DAY == periodType && null == timestamp) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
	}
}
