package com.weihengtech.ecos.model.vos.app.home;

import com.weihengtech.ecos.model.vos.global.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页event-fault入参")
public class HomeEventsFaultVo extends PageInfoVO {

	@ApiModelProperty(name = "start", value = "开始时间戳", required = true)
	@NotNull(message = "err.not.null")
	private Long start;

	@ApiModelProperty(name = "end", value = "结束时间戳", required = true)
	@NotNull(message = "err.not.null")
	private Long end;

	@ApiModelProperty(name = "type", value = "告警:alarm; fault:故障; event:事件")
	private String type;

	@ApiModelProperty(name = "deviceId", value = "用户已绑定设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
