package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页now设备统计信息入参")
public class HomeNowDeviceStatisticsVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "periodType", value = "1: Today; 2: Yesterday; 3: Week; 4: Month", required = true)
	private Integer periodType;
}
