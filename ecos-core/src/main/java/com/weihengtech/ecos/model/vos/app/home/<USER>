package com.weihengtech.ecos.model.vos.app.home;

import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSourceEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 家庭添加设备入参
 * @author: jiahao.jin
 * @create: 2024-01-24 17:02
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭添加设备入参")
public class V2ClientHomeBindDeviceVo {

    @ApiModelProperty(value = "网关SN标识", required = true)
    @NotBlank(message = "err.not.blank")
    private String wifiSn;

    @ApiModelProperty(name = "homeId", value = "家庭ID" , required = true)
    @NotBlank(message = "err.not.blank")
    private String homeId;

    @ApiModelProperty(value = "设备类型，0: 易联 1: 涂鸦 2: OCPP")
    private Integer type;

    @ApiModelProperty(value = "设备类型，0: ELINK_TSDB 1: TUYA_LINDORM 2: ELINK_LINDORM 3: CHARGER_LINDORM")
    private Integer tsdbSource;

    @ApiModelProperty(value = "设备具体类型，100: 易联-储能 200: 涂鸦-储能 201: 涂鸦-单插 300: EN+-充电桩", required = true)
    @NotNull(message = "err.not.null")
    private Integer typeInfo;

    @ApiModelProperty(value = "充电桩联网方式：0：即插即充，1：远程未调用，2：有线联网，3：无线联网")
    private Integer networkType;

    @ApiModelProperty(name = "deviceAliasName", value = "设备别名")
    private String deviceAliasName;

    @ApiModelProperty(name = "lon", value = "地区/城市经度")
    private Double lon = 0.0;

    @ApiModelProperty(name = "lat", value = "地区/城市纬度")
    private Double lat = 0.0;

    @ApiModelProperty(name = "cpFirmwareVersion", value = "固件版本")
    private String cpFirmwareVersion;

    @ApiModelProperty(name = "cpPlugAndChargeMsg", value = "即插即用开关")
    private String cpPlugAndChargeMsg;

    public void buildParam() {
        switch (typeInfo){
            case (CommonConstants.DEVICE_ELINK_CN):
                setType(DeviceTypeInfoEnum.ELINK.getDatasource());
                setTsdbSource(TsdbSourceEnum.ELINK_LINDORM.getCode());
                break;
            case (CommonConstants.DEVICE_TUYA_CN):
                setType(DeviceTypeInfoEnum.TUYA.getDatasource());
                setTsdbSource(TsdbSourceEnum.TUYA_LINDORM.getCode());
                break;
            case (CommonConstants.DEVICE_TUYA_DC):
                setType(DeviceTypeInfoEnum.TUYA.getDatasource());
                setTsdbSource(TsdbSourceEnum.TUYA_LINDORM.getCode());
                break;
            case (CommonConstants.DEVICE_EN_CDZ):
                // EN+充电桩配网绑定
                setType(DeviceTypeInfoEnum.OCPP.getDatasource());
                setTsdbSource(TsdbSourceEnum.CHARGER_LINDORM.getCode());

                if (networkType == null) {
                    throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
                }
                break;
        }
    }
}
