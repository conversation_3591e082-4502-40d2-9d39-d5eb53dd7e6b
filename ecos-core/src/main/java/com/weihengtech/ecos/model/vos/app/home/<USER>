package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @program: ecos-server
 * @description: 用户更新家庭入参
 * @author: jiahao.jin
 * @create: 2024-01-22 15:18
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "用户更新家庭入参")
public class V2HomeUpdateHomeVo {

    @ApiModelProperty(name = "id", value = "家庭Id", required = true)
    @NotBlank(message = "err.not.blank")
    private String id;

    @ApiModelProperty(value = "家庭名称")
    @Size(min = 1, max = 20, message = "err.size.homeName") // 确保homeName的大小在1到20个字符之间
    private String homeName;

    @ApiModelProperty(value = "家庭经度")
    private Double longitude;

    @ApiModelProperty(value = "家庭纬度")
    private Double latitude;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "电价类型")
    private Integer elePriceType;
}
