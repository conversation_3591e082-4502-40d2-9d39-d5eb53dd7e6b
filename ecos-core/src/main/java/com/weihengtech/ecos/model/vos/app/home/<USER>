package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "实时功率入参")
public class HomeNowDeviceRealtimeVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;
}
