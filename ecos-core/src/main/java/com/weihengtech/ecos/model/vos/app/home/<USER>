package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备排序入参")
public class HomeOrderedDeviceListVo {

	@ApiModelProperty(name = "deviceIdList", value = "排好序的设备id列表", required = true)
	@NotNull(message = "err.not.null")
	private List<String> deviceIdList;
}
