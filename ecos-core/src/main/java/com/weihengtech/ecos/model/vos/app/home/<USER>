package com.weihengtech.ecos.model.vos.app.home;

import com.weihengtech.ecos.model.vos.global.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页备电分页入参")
public class HomeEventsBackupPageVo extends PageInfoVO {

	@NotBlank(message = "err.not.blank")
	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	private String deviceId;

	@NotNull(message = "err.not.null")
	@ApiModelProperty(name = "startTime", value = "开始时间时间戳(单位秒)", required = true)
	private Long startTime;

	@NotNull(message = "err.not.null")
	@ApiModelProperty(name = "endTime", value = "结束时间时间戳(单位秒)", required = true)
	private Long endTime;
}
