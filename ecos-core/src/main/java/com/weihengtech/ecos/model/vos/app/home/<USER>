package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "用户绑定设备入参")
public class HomeClientUserBindDeviceVo {

	@ApiModelProperty(value = "wifi棒序列号", required = true)
	@NotBlank(message = "err.not.blank")
	private String wifiSn;

	@ApiModelProperty(value = "0: 易联 1: 涂鸦", required = true)
	@NotNull(message = "err.not.null")
	private Integer type;

	@ApiModelProperty(name = "deviceAliasName", value = "设备别名")
	private String deviceAliasName;

	@ApiModelProperty(name = "lon", value = "地区/城市经度")
	private Double lon = 0.0;

	@ApiModelProperty(name = "lat", value = "地区/城市纬度")
	private Double lat = 0.0;
}
