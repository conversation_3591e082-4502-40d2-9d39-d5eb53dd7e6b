package com.weihengtech.ecos.model.vos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 转移设备家庭入参
 * @author: jiahao.jin
 * @create: 2024-03-13 11:20
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "转移设备家庭入参")
public class V2TransferDeviceFamilyVo {
    @ApiModelProperty(name = "deviceId", value = "设备ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "homeId", value = "要转入的家庭ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String homeId;
}
