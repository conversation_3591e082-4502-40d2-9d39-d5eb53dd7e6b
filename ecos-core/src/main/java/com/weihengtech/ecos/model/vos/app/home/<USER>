package com.weihengtech.ecos.model.vos.app.home;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;

/**
 * @program: ecos-server
 * @description: 家庭中所有储能设备总的数据
 * @author: jiahao.jin
 * @create: 2024-04-01 15:29
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭中所有储能设备总的数据")
public class V2HomeInsightDeviceDataVo extends BaseVO {

    @ApiModelProperty(name = "periodType", value = "0: 天; 2: 月; 4: 年; 5: 总共", required = true)
    @NotNull(message = "err.not.null")
    private Integer periodType;

    @ApiModelProperty(name = "timestamp", value = "时间戳(查询总共可以不用传)")
    private Long timestamp;

    @ApiModelProperty(name = "homeId", value = "家庭ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String homeId;

    @Override
    public void checkParams() {
        LinkedList<Integer> periodTypeList = ListUtil.toLinkedList(CommonConstants.PERIOD_DAY,
                CommonConstants.PERIOD_MONTH, CommonConstants.PERIOD_YEAR, CommonConstants.PERIOD_LIFETIME
        );
        if (!periodTypeList.contains(periodType)) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        if (CommonConstants.PERIOD_DAY == periodType && null == timestamp) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
    }
}
