package com.weihengtech.ecos.model.vos.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 更新别名入参
 * @author: jiahao.jin
 * @create: 2024-03-08 17:25
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "更新别名入参")
public class V2UpdateDeviceRemarkVo {

    @ApiModelProperty(name = "deviceId", value = "设备ID", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "remark", value = "别名", required = true)
    @NotBlank(message = "err.not.blank")
    private String remark;
}
