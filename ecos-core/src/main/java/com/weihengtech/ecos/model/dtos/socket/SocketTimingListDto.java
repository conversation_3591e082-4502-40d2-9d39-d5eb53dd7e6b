package com.weihengtech.ecos.model.dtos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 定时开关任务查询回参
 * @author: jiahao.jin
 * @create: 2024-02-28 19:06
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "定时开关任务查询回参")
public class SocketTimingListDto {

    @ApiModelProperty(name = "taskId", value = "定时任务ID")
    public String taskId;

    @ApiModelProperty(name = "status", value = "定时任务状态")
    public Boolean status;

    @ApiModelProperty(name = "status", value = "单插开关状态(0：关闭，1：开启)")
    public int socketSwitch;

    @ApiModelProperty(name = "week", value = "星期(0：仅一次， 1-7：固定周几)")
    public List<Integer> week;

    @ApiModelProperty(name = "startTime", value = "开关时间")
    public String socketTime;
}
