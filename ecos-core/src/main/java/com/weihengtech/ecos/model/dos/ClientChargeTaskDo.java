package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: client定时充电任务表
 * @author: jiahao.jin
 * @create: 2024-01-04 15:36
 **/
@Getter
@Setter
@ToString
@TableName("client_charge_task")
public class ClientChargeTaskDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long userId;

    private Long deviceId;

    // 开始和结束时间
    private String startTime;
    private String endTime;

    // 周几
    private String week;

    // 充电功率
    private Double power;

    // 任务状态，这里假设是布尔类型（活动/非活动）
    private Boolean status;

    private Long startTaskId;
    private Long endTaskId;

    // 创建时间和更新时间
    private Long createTime;
    private Long updateTime;
}
