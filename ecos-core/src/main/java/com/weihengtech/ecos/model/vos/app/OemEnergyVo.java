package com.weihengtech.ecos.model.vos.app;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.global.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Oem能量页入参")
public class OemEnergyVo extends BaseVO {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@ApiModelProperty(name = "start", value = "开始时间", required = true)
	@NotNull(message = "err.not.null")
	private Long start;

	@ApiModelProperty(name = "end", value = "结束时间", required = true)
	@NotNull(message = "err.not.null")
	private Long end;

	@Override
	public void checkParams() {
		long timeRange = end - start;
		if (timeRange > 30 * 24 * 3600 * 1000L) {
			throw new EcosException(EcosExceptionEnum.TIME_RANGE);
		}
	}
}
