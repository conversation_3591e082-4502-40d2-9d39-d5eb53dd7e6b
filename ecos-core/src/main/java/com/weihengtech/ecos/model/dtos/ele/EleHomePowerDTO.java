package com.weihengtech.ecos.model.dtos.ele;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/31 14:51
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价自动策略数据")
public class EleHomePowerDTO {

    @ApiModelProperty(name = "hour", value = "小时数")
    private Integer hour;

    @ApiModelProperty(name = "homePower", value = "家庭负载功率")
    private Integer homePower;
}
