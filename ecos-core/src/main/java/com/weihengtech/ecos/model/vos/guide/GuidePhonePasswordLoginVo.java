package com.weihengtech.ecos.model.vos.guide;

import com.weihengtech.ecos.annotation.validator.IsAllowedClientType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "手机密码登录入参")
public class GuidePhonePasswordLoginVo implements Serializable {

	@ApiModelProperty(name = "phone", value = "手机号", required = true)
	@NotBlank(message = "err.not.blank")
	private String phone;

	@ApiModelProperty(name = "password", value = "密码", required = true)
	@NotBlank(message = "err.not.blank")
	private String password;

	@ApiModelProperty(name = "clientType", value = "客户端类型", required = true)
	@NotBlank(message = "err.not.blank")
	@IsAllowedClientType(message = "err.valid.client.type")
	private String clientType;

	@ApiModelProperty(name = "clientVersion", value = "客户端版本号", required = true)
	@NotBlank(message = "err.not.blank")
	private String clientVersion;
}
