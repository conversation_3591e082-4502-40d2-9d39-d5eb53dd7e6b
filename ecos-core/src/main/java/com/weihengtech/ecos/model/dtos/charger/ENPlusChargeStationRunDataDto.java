package com.weihengtech.ecos.model.dtos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @program: ecos-server
 * @description: EN+充电桩充电时实时运行数据回参
 * @author: jiahao.jin
 * @create: 2024-02-20 15:13
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "EN+充电桩充电时实时运行数据回参")
public class ENPlusChargeStationRunDataDto {

    @ApiModelProperty(name = "chargeCapacity", value = "充电量(kWh)")
    private BigDecimal chargeCapacity = BigDecimal.ZERO;

    @ApiModelProperty(name = "power", value = "充电功率(W)")
    private BigDecimal power = BigDecimal.ZERO;

    @ApiModelProperty(name = "current1", value = "充电电流1(A)")
    private BigDecimal current1 = BigDecimal.ZERO;
    @ApiModelProperty(name = "current2", value = "充电电流2")
    private BigDecimal current2 = BigDecimal.ZERO;
    @ApiModelProperty(name = "current3", value = "充电电流3")
    private BigDecimal current3 = BigDecimal.ZERO;

    @ApiModelProperty(name = "voltage1", value = "充电电压1(V)")
    private BigDecimal voltage1 = BigDecimal.ZERO;
    @ApiModelProperty(name = "voltage2", value = "充电电压2")
    private BigDecimal voltage2 = BigDecimal.ZERO;
    @ApiModelProperty(name = "voltage3", value = "充电电压3")
    private BigDecimal voltage3 = BigDecimal.ZERO;

    @ApiModelProperty(name = "sysRunMode", value = "-5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检")
    private Integer sysRunMode = -5;

    @ApiModelProperty(name = "startTime", value = "充电开始时间")
    private String startTime;
}
