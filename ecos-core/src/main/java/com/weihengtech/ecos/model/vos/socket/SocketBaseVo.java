package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @program: ecos-server
 * @description: 插座基础入参
 * @author: jiahao.jin
 * @create: 2024-01-28 16:26
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座基础入参")
public abstract class SocketBaseVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    /**
     * 进一步校验参数
     */
    public abstract void checkParams();
}
