package com.weihengtech.ecos.model.vos.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElePriceFixedVO {

	@ApiModelProperty(name = "homeId", value = "家庭Id", required = true)
	@NotNull(message = "err.not.null")
	private String homeId;

	@ApiModelProperty(name = "purchasePrice", value = "购入电价", required = true)
	@NotNull(message = "err.not.null")
	private BigDecimal purchasePrice;

	@ApiModelProperty(name = "purchasePrice", value = "购入税费")
	private BigDecimal purchaseTax;

	@ApiModelProperty(name = "purchasePrice", value = "馈网电价")
	private BigDecimal feedInPrice;
}
