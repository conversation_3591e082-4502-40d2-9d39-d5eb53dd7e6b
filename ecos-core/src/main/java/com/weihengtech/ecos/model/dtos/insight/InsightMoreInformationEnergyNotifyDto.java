package com.weihengtech.ecos.model.dtos.insight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "Insight更多信息能量通知回参")
public class InsightMoreInformationEnergyNotifyDto {

	@ApiModelProperty(name = "open", value = "2: 关 1: 开", required = true)
	private Integer open;

	private Integer threshold;

	private String email;
}
