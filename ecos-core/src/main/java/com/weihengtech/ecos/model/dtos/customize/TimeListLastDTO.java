package com.weihengtech.ecos.model.dtos.customize;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 15:45
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "充放电上次记录")
public class TimeListLastDTO {

	private List<ChargingStructDTO> chargingList;

	private List<ChargingStructDTO> dischargingList;
}
