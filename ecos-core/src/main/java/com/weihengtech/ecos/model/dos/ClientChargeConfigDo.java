package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 充电桩充电配置表
 * @author: jiahao.jin
 * @create: 2024-02-19 16:49
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_charge_config")
public class ClientChargeConfigDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long deviceId;

    // 联网模式（0-即插即充|1-远程未联网|2-有线联网|3-无线联网）
    private Integer mode;

    private Double maxPower;

    private Long createTime;

    private Long updateTime;
}
