package com.weihengtech.ecos.model.vos.socket;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @program: ecos-server
 * @description: 随机定时更新入参
 * @author: jiahao.jin
 * @create: 2024-01-30 18:03
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "随机定时更新入参")
public class SocketRandomTimeMapVo extends SocketBaseVo{

    @ApiModelProperty(name = "socketRandomTimeVoMap", value = "随机定时时间列表")
    @Valid
    private Map<Integer, SocketRandomTimeVo> socketRandomTimeVoMap;


    @Getter
    @Setter
    @ToString
    public static class SocketRandomTimeVo {
        @ApiModelProperty(name = "status", value = "随机定时时间状态(0：关闭，1：开启)")
        @NotNull(message = "err.not.null")
        @Max(value = 1, message = "err.invalid.param")
        @Min(value = 0, message = "err.invalid.param")
        public int status;

        @ApiModelProperty(name = "week", value = "星期(0：仅一次， 1-7：固定周几)")
        @NotEmpty(message = "err.not.empty")
        public List<Integer> week;

        @ApiModelProperty(name = "startTime", value = "开始时间")
        @NotBlank(message = "err.not.blank")
        public String startTime;

        @ApiModelProperty(name = "endTime", value = "结束时间")
        @NotBlank(message = "err.not.blank")
        public String endTime;

    }

    // 正则表达式，确保时间格式为HH:mm，小时00-23，分钟00-59
    private static final Pattern TIME_PATTERN = Pattern.compile("^(?:[01]\\d|2[0-3]):[0-5]\\d$");

    @Override
    public void checkParams() {
        socketRandomTimeVoMap.forEach((i,socketRandomTimeVo) -> {
            if (socketRandomTimeVo.getWeek().size() != 0) {
                for (Integer w : socketRandomTimeVo.getWeek()) {
                    if (w < 0 || w > 7) {
                        throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
                    }
                }
            }


            if (!TIME_PATTERN.matcher(socketRandomTimeVo.startTime).matches() || !TIME_PATTERN.matcher(socketRandomTimeVo.endTime).matches()) {
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        });

    }
}
