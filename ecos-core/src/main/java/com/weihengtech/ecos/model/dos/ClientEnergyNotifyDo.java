package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_energy_notify")
public class ClientEnergyNotifyDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	// 0: 关 1: 开
	private Integer open;

	private Integer threshold;

	private String email;

	private Long deviceId;

	private Long userId;
}
