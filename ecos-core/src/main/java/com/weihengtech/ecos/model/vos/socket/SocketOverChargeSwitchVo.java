package com.weihengtech.ecos.model.vos.socket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: 插座过冲保护开关入参
 * @author: jiahao.jin
 * @create: 2024-02-01 11:53
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "插座过冲保护开关入参")
public class SocketOverChargeSwitchVo extends SocketBaseVo {

    @ApiModelProperty(name = "switchStatus", value = "想要的开关状态", required = true)
    @NotNull(message = "err.not.null")
    private Boolean switchStatus ;

    @Override
    public void checkParams() {

    }
}
