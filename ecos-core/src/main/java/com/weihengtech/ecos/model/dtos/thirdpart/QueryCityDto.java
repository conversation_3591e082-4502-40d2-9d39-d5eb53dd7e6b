package com.weihengtech.ecos.model.dtos.thirdpart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-26 13:04
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "城市搜索回参")
public class QueryCityDto {
    @ApiModelProperty(name = "name", value = "地区/城市名称", required = true)
    private String name;
    @ApiModelProperty(name = "id", value = "地区/城市ID", required = true)
    private String id;
    @ApiModelProperty(name = "lat", value = "地区/城市纬度", required = true)
    private String lat;
    @ApiModelProperty(name = "lon", value = "地区/城市经度", required = true)
    private String lon;
    @ApiModelProperty(name = "adm2", value = "地区/城市的上级行政区划名称", required = true)
    private String adm2;
    @ApiModelProperty(name = "adm1", value = "地区/城市所属一级行政区域", required = true)
    private String adm1;
    @ApiModelProperty(name = "country", value = "地区/城市所属国家名称", required = true)
    private String country;
    @ApiModelProperty(name = "tz", value = "地区/城市所在时区", required = true)
    private String tz;
    @ApiModelProperty(name = "utcOffset", value = "地区/城市目前与UTC时间偏移的小时数", required = true)
    private String utcOffset;
    @ApiModelProperty(name = "isDst", value = "地区/城市是否当前处于夏令时。1 表示当前处于夏令时，0 表示当前不是夏令时。", required = true)
    private String isDst;
    @ApiModelProperty(name = "type", value = "地区/城市的属性", required = true)
    private String type;
    @ApiModelProperty(name = "rank", value = "地区评分,Rank值是表明一个城市或地区排名的数字，基于多种因素综合计算而来，例如：人口、面积、GDP、搜索热度等。", required = true)
    private String rank;
    @ApiModelProperty(name = "fxLink", value = "该地区的天气预报网页链接，便于嵌入你的网站或应用", required = true)
    private String fxLink;
}
