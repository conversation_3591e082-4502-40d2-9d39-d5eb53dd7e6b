package com.weihengtech.ecos.model.dos;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.val;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("client_customize")
public class ClientCustomizeDo {

	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private Long deviceId;

	// 充放电模式
	private Integer chargeMode;
	// 并网预留SOC
	private Integer batteryMin;
	// 馈网功率比例
	private Integer maxFeedIn;
	// 离网预留SOC
	private Integer epsBatteryMin;
	// 电池馈网
	private Integer dischargeToGridFlag;

	// 自发自用SOC
	private Integer selfSoc;
	// 自发自用离网预留SOC
	private Integer selfEpsBat;
	// 自发自用馈网功率比例
	private Integer selfFeedIn;

	// 定时充放SOC
	private Integer regularSoc;
	// 定时充放离网预留SOC
	private Integer regularEpsBat;
	// 定时充放馈网功率比例
	private Integer regularFeedIn;

	// 备电预留SOC
	private Integer backupSoc;
	// 备电离网预留SOC
	private Integer backupEpsBat;
	// 备电预留馈网功率比例
	private Integer backupFeedIn;

	// 电价地区
	private String region;
	// 开启自动策略
	private Integer autoStrategy;
	// 开启联动家庭负载自动策略
	private Integer autoHomeStrategy;
	// 策略模式
	private Integer strategyMode;
	// 自定义充电功率
	private Integer defChargePower;
	// 自定义放电功率
	private Integer defDischargePower;
	// 设备关联时区
	private String timezone;
	// 电价导入类型：0：自定义|1：家庭电价
	private Integer priceImportType;
	// 购电税费
	private BigDecimal purchaseTax;

	// 1充1放
	private Integer chargeStartHour1;
	private Integer chargeStartMinute1;
	private Integer chargeEndHour1;
	private Integer chargeEndMinute1;
	private Integer chargePower1;
	private Integer chargeAbandonPv1;
	private Integer dischargeStartHour1;
	private Integer dischargeStartMinute1;
	private Integer dischargeEndHour1;
	private Integer dischargeEndMinute1;
	private Integer dischargePower1;
	private Integer dischargeAbandonPv1;

	// 2充2放
	private Integer chargeStartHour2;
	private Integer chargeStartMinute2;
	private Integer chargeEndHour2;
	private Integer chargeEndMinute2;
	private Integer chargePower2;
	private Integer chargeAbandonPv2;
	private Integer dischargeStartHour2;
	private Integer dischargeStartMinute2;
	private Integer dischargeEndHour2;
	private Integer dischargeEndMinute2;
	private Integer dischargePower2;
	private Integer dischargeAbandonPv2;

	// 3充3放
	private Integer chargeStartHour3;
	private Integer chargeStartMinute3;
	private Integer chargeEndHour3;
	private Integer chargeEndMinute3;
	private Integer chargePower3;
	private Integer chargeAbandonPv3;
	private Integer dischargeStartHour3;
	private Integer dischargeStartMinute3;
	private Integer dischargeEndHour3;
	private Integer dischargeEndMinute3;
	private Integer dischargePower3;
	private Integer dischargeAbandonPv3;

	// 4充4放
	private Integer chargeStartHour4;
	private Integer chargeStartMinute4;
	private Integer chargeEndHour4;
	private Integer chargeEndMinute4;
	private Integer chargePower4;
	private Integer chargeAbandonPv4;
	private Integer dischargeStartHour4;
	private Integer dischargeStartMinute4;
	private Integer dischargeEndHour4;
	private Integer dischargeEndMinute4;
	private Integer dischargePower4;
	private Integer dischargeAbandonPv4;

	// 5充5放
	private Integer chargeStartHour5;
	private Integer chargeStartMinute5;
	private Integer chargeEndHour5;
	private Integer chargeEndMinute5;
	private Integer chargePower5;
	private Integer chargeAbandonPv5;
	private Integer dischargeStartHour5;
	private Integer dischargeStartMinute5;
	private Integer dischargeEndHour5;
	private Integer dischargeEndMinute5;
	private Integer dischargePower5;
	private Integer dischargeAbandonPv5;

	// 6充6放
	private Integer chargeStartHour6;
	private Integer chargeStartMinute6;
	private Integer chargeEndHour6;
	private Integer chargeEndMinute6;
	private Integer chargePower6;
	private Integer chargeAbandonPv6;
	private Integer dischargeStartHour6;
	private Integer dischargeStartMinute6;
	private Integer dischargeEndHour6;
	private Integer dischargeEndMinute6;
	private Integer dischargePower6;
	private Integer dischargeAbandonPv6;

	// 7充7放
	private Integer chargeStartHour7;
	private Integer chargeStartMinute7;
	private Integer chargeEndHour7;
	private Integer chargeEndMinute7;
	private Integer chargePower7;
	private Integer chargeAbandonPv7;
	private Integer dischargeStartHour7;
	private Integer dischargeStartMinute7;
	private Integer dischargeEndHour7;
	private Integer dischargeEndMinute7;
	private Integer dischargePower7;
	private Integer dischargeAbandonPv7;

	// 8充8放
	private Integer chargeStartHour8;
	private Integer chargeStartMinute8;
	private Integer chargeEndHour8;
	private Integer chargeEndMinute8;
	private Integer chargePower8;
	private Integer chargeAbandonPv8;
	private Integer dischargeStartHour8;
	private Integer dischargeStartMinute8;
	private Integer dischargeEndHour8;
	private Integer dischargeEndMinute8;
	private Integer dischargePower8;
	private Integer dischargeAbandonPv8;

	// 9充9放
	private Integer chargeStartHour9;
	private Integer chargeStartMinute9;
	private Integer chargeEndHour9;
	private Integer chargeEndMinute9;
	private Integer chargePower9;
	private Integer chargeAbandonPv9;
	private Integer dischargeStartHour9;
	private Integer dischargeStartMinute9;
	private Integer dischargeEndHour9;
	private Integer dischargeEndMinute9;
	private Integer dischargePower9;
	private Integer dischargeAbandonPv9;

	// 10充10放
	private Integer chargeStartHour10;
	private Integer chargeStartMinute10;
	private Integer chargeEndHour10;
	private Integer chargeEndMinute10;
	private Integer chargePower10;
	private Integer chargeAbandonPv10;
	private Integer dischargeStartHour10;
	private Integer dischargeStartMinute10;
	private Integer dischargeEndHour10;
	private Integer dischargeEndMinute10;
	private Integer dischargePower10;
	private Integer dischargeAbandonPv10;

	// 11充11放
	private Integer chargeStartHour11;
	private Integer chargeStartMinute11;
	private Integer chargeEndHour11;
	private Integer chargeEndMinute11;
	private Integer chargePower11;
	private Integer chargeAbandonPv11;
	private Integer dischargeStartHour11;
	private Integer dischargeStartMinute11;
	private Integer dischargeEndHour11;
	private Integer dischargeEndMinute11;
	private Integer dischargePower11;
	private Integer dischargeAbandonPv11;

	// 12充12放
	private Integer chargeStartHour12;
	private Integer chargeStartMinute12;
	private Integer chargeEndHour12;
	private Integer chargeEndMinute12;
	private Integer chargePower12;
	private Integer chargeAbandonPv12;
	private Integer dischargeStartHour12;
	private Integer dischargeStartMinute12;
	private Integer dischargeEndHour12;
	private Integer dischargeEndMinute12;
	private Integer dischargePower12;
	private Integer dischargeAbandonPv12;


	/** 转换为自定义持久化对象 */
	public ClientCustomizeDo convertClientCustomizeDo() {

		ClientCustomizeDo clientCustomizeDo2 = new ClientCustomizeDo();
		CglibUtil.copy(this, clientCustomizeDo2);
		Integer mode = this.getChargeMode();
		val selfPowered = 0;
		val loadShifting = 1;
		val backup = 2;

		switch (mode) {
			case selfPowered:
				clientCustomizeDo2.setSelfSoc(this.getBatteryMin());
				clientCustomizeDo2.setSelfEpsBat(this.getEpsBatteryMin());
				clientCustomizeDo2.setSelfFeedIn(this.getMaxFeedIn());
				break;
			case loadShifting:
				clientCustomizeDo2.setRegularSoc(this.getBatteryMin());
				clientCustomizeDo2.setRegularEpsBat(this.getEpsBatteryMin());
				clientCustomizeDo2.setRegularFeedIn(this.getMaxFeedIn());
				break;
			case backup:
				clientCustomizeDo2.setBackupSoc(this.getBatteryMin());
				clientCustomizeDo2.setBackupEpsBat(this.getEpsBatteryMin());
				clientCustomizeDo2.setBackupFeedIn(this.getMaxFeedIn());
				break;
			default:
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		return clientCustomizeDo2;
	}

	/** 构建1-12充放数据 */
	public void buildExtraParams(CustomizeInfoVo customizeInfoVo) {
		Optional.ofNullable(customizeInfoVo.getRegion()).ifPresent(this::setRegion);
		Optional.ofNullable(customizeInfoVo.getAutoStrategy()).ifPresent(this::setAutoStrategy);
		Optional.ofNullable(customizeInfoVo.getAutoHomeStrategy()).ifPresent(this::setAutoHomeStrategy);
		Optional.ofNullable(customizeInfoVo.getStrategyMode()).ifPresent(this::setStrategyMode);
		Optional.ofNullable(customizeInfoVo.getDefChargePower()).ifPresent(this::setDefChargePower);
		Optional.ofNullable(customizeInfoVo.getDefDischargePower()).ifPresent(this::setDefDischargePower);
		Optional.ofNullable(customizeInfoVo.getPriceImportType()).ifPresent(this::setPriceImportType);
		Optional.ofNullable(customizeInfoVo.getPurchaseTax()).ifPresent(this::setPurchaseTax);
		clearItems();
		List<ChargingStructDTO> chargingList = customizeInfoVo.getChargingList();
		List<ChargingStructDTO> dischargingList = customizeInfoVo.getDischargingList();
		Class<ClientCustomizeDo> clazz = ClientCustomizeDo.class;
		if (CollUtil.isNotEmpty(chargingList)) {
			for (int i = 0; i < chargingList.size(); i++) {
				ChargingStructDTO chargingStruct = chargingList.get(i);
				try {
					Method setChargeStartHour = clazz.getMethod("setChargeStartHour" + (i+1), Integer.class);
					setChargeStartHour.invoke(this, chargingStruct.getStartHour());
					Method setChargeStartMinute = clazz.getMethod("setChargeStartMinute" + (i+1), Integer.class);
					setChargeStartMinute.invoke(this, chargingStruct.getStartMinute());
					Method setChargeEndHour = clazz.getMethod("setChargeEndHour" + (i+1), Integer.class);
					setChargeEndHour.invoke(this, chargingStruct.getEndHour());
					Method setChargeEndMinute = clazz.getMethod("setChargeEndMinute" + (i+1), Integer.class);
					setChargeEndMinute.invoke(this, chargingStruct.getEndMinute());
					Method setChargePower = clazz.getMethod("setChargePower" + (i+1), Integer.class);
					setChargePower.invoke(this, chargingStruct.getPower());
					Method setChargeAbandonPv = clazz.getMethod("setChargeAbandonPv" + (i+1), Integer.class);
					setChargeAbandonPv.invoke(this, chargingStruct.getAbandonPv());
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}
		}
		if (CollUtil.isNotEmpty(dischargingList)) {
			for (int i = 0; i < dischargingList.size(); i++) {
				ChargingStructDTO dischargingStruct = dischargingList.get(i);
				try {
					Method setDischargeStartHour = clazz.getMethod("setDischargeStartHour" + (i+1), Integer.class);
					setDischargeStartHour.invoke(this, dischargingStruct.getStartHour());
					Method setDischargeStartMinute = clazz.getMethod("setDischargeStartMinute" + (i+1), Integer.class);
					setDischargeStartMinute.invoke(this, dischargingStruct.getStartMinute());
					Method setDischargeEndHour = clazz.getMethod("setDischargeEndHour" + (i+1), Integer.class);
					setDischargeEndHour.invoke(this, dischargingStruct.getEndHour());
					Method setDischargeEndMinute = clazz.getMethod("setDischargeEndMinute" + (i+1), Integer.class);
					setDischargeEndMinute.invoke(this, dischargingStruct.getEndMinute());
					Method setDischargePower = clazz.getMethod("setDischargePower" + (i+1), Integer.class);
					setDischargePower.invoke(this, dischargingStruct.getPower());
					Method setDischargeAbandonPv = clazz.getMethod("setDischargeAbandonPv" + (i+1), Integer.class);
					setDischargeAbandonPv.invoke(this, dischargingStruct.getAbandonPv());
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}
		}
	}

	/** 无论是否切换模式，都需要清除旧值 */
	private void clearItems() {
		Class<ClientCustomizeDo> clazz = ClientCustomizeDo.class;
		try {
			for (int i = 1; i <= 12; i++) {
				Method setChargeStartHour = clazz.getMethod("setChargeStartHour" + i, Integer.class);
				setChargeStartHour.invoke(this, 0);
				Method setChargeStartMinute = clazz.getMethod("setChargeStartMinute" + i, Integer.class);
				setChargeStartMinute.invoke(this, 0);
				Method setChargeEndHour = clazz.getMethod("setChargeEndHour" + i, Integer.class);
				setChargeEndHour.invoke(this, 0);
				Method setChargeEndMinute = clazz.getMethod("setChargeEndMinute" + i, Integer.class);
				setChargeEndMinute.invoke(this, 0);
				Method setChargePower = clazz.getMethod("setChargePower" + i, Integer.class);
				setChargePower.invoke(this, 0);
				Method setChargeAbandonPv = clazz.getMethod("setChargeAbandonPv" + i, Integer.class);
				setChargeAbandonPv.invoke(this, 0);

				Method setDischargeStartHour = clazz.getMethod("setDischargeStartHour" + i, Integer.class);
				setDischargeStartHour.invoke(this, 0);
				Method setDischargeStartMinute = clazz.getMethod("setDischargeStartMinute" + i, Integer.class);
				setDischargeStartMinute.invoke(this, 0);
				Method setDischargeEndHour = clazz.getMethod("setDischargeEndHour" + i, Integer.class);
				setDischargeEndHour.invoke(this, 0);
				Method setDischargeEndMinute = clazz.getMethod("setDischargeEndMinute" + i, Integer.class);
				setDischargeEndMinute.invoke(this, 0);
				Method setDischargePower = clazz.getMethod("setDischargePower" + i, Integer.class);
				setDischargePower.invoke(this, 0);
				Method setDischargeAbandonPv = clazz.getMethod("setDischargeAbandonPv" + i, Integer.class);
				setDischargeAbandonPv.invoke(this, 0);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}


	/** 3-12充放是否有数据 */
	public boolean isEmptyExtraParam() {
		Class<ClientCustomizeDo> clazz = ClientCustomizeDo.class;
		try {
			for (int i = 3; i <= 12; i++) {
				Method getChargeStartHour = clazz.getMethod("getChargeStartHour" + i);
				Method getChargeStartMinute = clazz.getMethod("getChargeStartMinute" + i);
				Method getChargeEndHour = clazz.getMethod("getChargeEndHour" + i);
				Method getChargeEndMinute = clazz.getMethod("getChargeEndMinute" + i);
				Method getChargePower = clazz.getMethod("getChargePower" + i);
				if ((int) getChargeStartHour.invoke(this) != 0 ||
						(int) getChargeStartMinute.invoke(this) != 0 ||
						(int) getChargeEndHour.invoke(this) != 0 ||
						(int) getChargeEndMinute.invoke(this) != 0 ||
						(int) getChargePower.invoke(this) != 0) {
					return false;
				}
				Method getDischargeStartHour = clazz.getMethod("getDischargeStartHour" + i);
				Method getDischargeStartMinute = clazz.getMethod("getDischargeStartMinute" + i);
				Method getDischargeEndHour = clazz.getMethod("getDischargeEndHour" + i);
				Method getDischargeEndMinute = clazz.getMethod("getDischargeEndMinute" + i);
				Method getDischargePower = clazz.getMethod("getDischargePower" + i);
				if ((int) getDischargeStartHour.invoke(this) != 0 ||
						(int) getDischargeStartMinute.invoke(this) != 0 ||
						(int) getDischargeEndHour.invoke(this) != 0 ||
						(int) getDischargeEndMinute.invoke(this) != 0 ||
						(int) getDischargePower.invoke(this) != 0) {
					return false;
				}
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return true;
	}
}
