package com.weihengtech.ecos.model.vos.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: ecos-server
 * @description: AI问答入参
 * @author: jiahao.jin
 * @create: 2024-05-09 18:36
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "AI问答入参")
public class V2AIChatCompletionsVo {

    @ApiModelProperty(name = "role", value = "角色（0|系统预设问题，1|AI， 2|用户）", required = true)
    @NotNull(message = "角色不能为空")
    private Integer role;

    @ApiModelProperty(name = "content", value = "内容", required = true)
    @NotBlank(message = "内容不能为空")
    private String content;


    @ApiModelProperty(name = "sessionId", value = "会话id(第一次输入传：\"0\")", required = true)
    @NotBlank(message = "会话id不能为空")
    private String sessionId;

    @ApiModelProperty(name = "sessionId", value = "家庭id", required = true)
    @NotBlank(message = "家庭id不能为空")
    private String homeId;
}
