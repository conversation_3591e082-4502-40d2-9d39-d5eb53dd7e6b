package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页now设备统计信息")
public class HomeNowDeviceStatisticsDto {

	@ApiModelProperty(name = "backup", value = "备电次数", required = true)
	private Integer backup;

	@ApiModelProperty(name = "consumptionEnergy", value = "家庭用电量", required = true)
	private BigDecimal consumptionEnergy;

	@ApiModelProperty(name = "fromBattery", value = "电池放电量", required = true)
	private BigDecimal fromBattery;

	@ApiModelProperty(name = "toBattery", value = "电池充电量", required = true)
	private BigDecimal toBattery;

	@ApiModelProperty(name = "fromGrid", value = "电网买电量", required = true)
	private BigDecimal fromGrid;

	@ApiModelProperty(name = "toGrid", value = "电网卖电量", required = true)
	private BigDecimal toGrid;

	@ApiModelProperty(name = "fromBatteryPercent", value = "电池放电百分比", required = true)
	private BigDecimal fromBatteryPercent;

	@ApiModelProperty(name = "fromGridPercent", value = "电网买电量百分比", required = true)
	private BigDecimal fromGridPercent;

	@ApiModelProperty(name = "fromSolarPercent", value = "光伏重点百分比", required = true)
	private BigDecimal fromSolarPercent;

	@ApiModelProperty(name = "fromSolar", value = "光伏充电量", required = true)
	private BigDecimal fromSolar;

	@ApiModelProperty(name = "cycleTimes", value = "循环次数", required = true)
	private Integer recycleTimes;

	@ApiModelProperty(name = "batterySoh", value = "电池健康度", required = true)
	private BigDecimal batterySoh;
}
