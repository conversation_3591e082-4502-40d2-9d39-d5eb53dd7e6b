package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页历史Solar图表回参")
public class HomeHistorySolarDto {

	@ApiModelProperty(name = "carbonReduction", value = "碳减排", required = true)
	private BigDecimal carbonReduction;

	@ApiModelProperty(name = "fromSolar", value = "发电量", required = true)
	private BigDecimal fromSolar;

	@ApiModelProperty(name = "fromSolarDps", value = "发电量统计", required = true)
	private Map<String, BigDecimal> fromSolarDps;
}
