package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页设备实时运行数据回参")
public class HomeNowDeviceRunDataDto {

	@ApiModelProperty(name = "batterySoc", value = "电池剩余电量")
	private BigDecimal batterySoc = BigDecimal.ZERO;

	@ApiModelProperty(name = "batteryPower", value = "电池功率")
	private BigDecimal batteryPower = BigDecimal.ZERO;

	@ApiModelProperty(name = "epsPower", value = "EPS功率")
	private BigDecimal epsPower = BigDecimal.ZERO;

	@ApiModelProperty(name = "gridPower", value = "电网功率(电池功率大于0则指向Home, 反之Home指向电网)")
	private BigDecimal gridPower = BigDecimal.ZERO;

	@ApiModelProperty(name = "homePower", value = "家庭功率")
	private BigDecimal homePower = BigDecimal.ZERO;

	@ApiModelProperty(name = "meterPower", value = "仪表功率")
	private BigDecimal meterPower = BigDecimal.ZERO;

	@ApiModelProperty(name = "solarPower", value = "光伏功率")
	private BigDecimal solarPower = BigDecimal.ZERO;
	// 1: 正常; 2: 故障; 8: 备电; 9: 离线
	@ApiModelProperty(name = "sysRunMode", value = "-5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检")
	private Integer sysRunMode = -5;

	@ApiModelProperty(name = "isExistSolar", value = "是否存在光伏")
	private Boolean isExistSolar = Boolean.TRUE;

	@ApiModelProperty(name = "sysPowerConfig", value = "8: 柴发模式")
	private Integer sysPowerConfig = 0;
}
