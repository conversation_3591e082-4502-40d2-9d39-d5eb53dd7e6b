package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "家庭设备概览")
public class HomeDeviceSketchDto {

	@ApiModelProperty(name = "currSolarPower", value = "当前发电功率", required = true)
	private BigDecimal currentSolarPower;

	@ApiModelProperty(name = "todayGeneratedEnergy", value = "当天发电量", required = true)
	private BigDecimal currentDayGeneratedEnergy;

	@ApiModelProperty(name = "currentMonthGeneratedEnergy", value = "当月发电量", required = true)
	private BigDecimal currentMonthGeneratedEnergy;

	@ApiModelProperty(name = "currentYearGeneratedEnergy", value = "当年发电量", required = true)
	private BigDecimal currentYearGeneratedEnergy;

	@ApiModelProperty(name = "totalEnergy", value = "总发电量", required = true)
	private BigDecimal totalEnergy;

	@ApiModelProperty(name = "totalReduceCarbonEmission", value = "总计碳减排", required = true)
	private BigDecimal totalReduceCarbonEmission;

	@ApiModelProperty(name = "dailyDischargingEnergy", value = "日放电", required = true)
	private BigDecimal dailyDischargingEnergy;

	@ApiModelProperty(name = "monthlyDischargingEnergy", value = "月放电", required = true)
	private BigDecimal monthlyDischargingEnergy;

	@ApiModelProperty(name = "yearlyDischargingEnergy", value = "年放电", required = true)
	private BigDecimal yearlyDischargingEnergy;

	@ApiModelProperty(name = "totalDischargingEnergy", value = "总放电", required = true)
	private BigDecimal totalDischargingEnergy;

	@ApiModelProperty(name = "dailyChargingEnergy", value = "日充电", required = true)
	private BigDecimal dailyChargingEnergy;

	@ApiModelProperty(name = "monthlyChargingEnergy", value = "月充电", required = true)
	private BigDecimal monthlyChargingEnergy;

	@ApiModelProperty(name = "yearlyChargingEnergy", value = "年充电", required = true)
	private BigDecimal yearlyChargingEnergy;

	@ApiModelProperty(name = "totalChargingEnergy", value = "总充电", required = true)
	private BigDecimal totalChargingEnergy;
}
