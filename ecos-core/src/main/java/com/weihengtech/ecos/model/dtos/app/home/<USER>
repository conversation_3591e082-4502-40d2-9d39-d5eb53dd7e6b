package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 最近一周设备光伏、电网能量回参
 * @author: jiahao.jin
 * @create: 2023-11-09 16:34
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "最近一周设备光伏、电网能量回参")
public class HomeDeviceEnergyStatisticsDto {

    @ApiModelProperty(name = "today", value = "周几", required = true)
    private Integer today;

    @ApiModelProperty(name = "lastWeekTotalSolar", value = "最近一周光伏总能量", required = true)
    private BigDecimal lastWeekTotalSolar = BigDecimal.ZERO;

    @ApiModelProperty(name = "lastWeekTotalGrid", value = "最近一周电网总能量", required = true)
    private BigDecimal lastWeekTotalGrid = BigDecimal.ZERO;

    @ApiModelProperty(name = "lastWeekTotalCarbonEmissions", value = "最近一周碳减排", required = true)
    private BigDecimal lastWeekTotalCarbonEmissions = BigDecimal.ZERO;

    @ApiModelProperty(name = "lastWeekTotalSaveStandardCoal", value = "最近一周节约标准煤", required = true)
    private BigDecimal lastWeekTotalSaveStandardCoal = BigDecimal.ZERO;

    @ApiModelProperty(name = "weekEnergy", value = "光伏、电网周能量", required = true)
    private Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> weekEnergy;

    @ApiModelProperty(name = "weekEnergy", value = "碳减排周能量", required = true)
    private Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> carbonEmissionsWeekEnergy;

    @ApiModelProperty(name = "weekEnergy", value = "节约标准煤周能量", required = true)
    private Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> saveStandardCoalWeekEnergy;

    @Getter
    @Setter
    public static class DeviceEnergy {

        @ApiModelProperty(name = "solarEnergy", value = "来自光伏能量", required = true)
        private BigDecimal solarEnergy = BigDecimal.ZERO;

        @ApiModelProperty(name = "gridEnergy", value = "来自电网能量", required = true)
        private BigDecimal gridEnergy = BigDecimal.ZERO;

        @ApiModelProperty(name = "toGrid", value = "馈网能量", required = true)
        private BigDecimal toGrid = BigDecimal.ZERO;

        @ApiModelProperty(name = "homeEnergy", value = "家庭耗能", required = true)
        private BigDecimal homeEnergy = BigDecimal.ZERO;

        @ApiModelProperty(name = "selfPowered", value = "自发自用率", required = true)
        private BigDecimal selfPowered = BigDecimal.ZERO;

    }

    @Getter
    @Setter
    public static class CarbonEmissionsDeviceEnergy {

        @ApiModelProperty(name = "carbonEmissions", value = "碳减排", required = true)
        private BigDecimal carbonEmissions = BigDecimal.ZERO;

    }

    @Getter
    @Setter
    public static class SaveStandardCoalDeviceEnergy {

        @ApiModelProperty(name = "saveStandardCoal", value = "节约标准煤", required = true)
        private BigDecimal saveStandardCoal = BigDecimal.ZERO;

    }


}
