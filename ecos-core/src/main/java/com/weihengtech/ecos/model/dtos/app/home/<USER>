package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @program: ecos-server
 * @description: 家庭设备列表信息
 * @author: jiahao.jin
 * @create: 2024-01-25 11:30
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭设备列表信息")
public class V2HomeDeviceListDto {
    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    private String deviceId;

    @ApiModelProperty(name = "deviceAliasName", value = "设备别名", required = true)
    private String deviceAliasName;

    @ApiModelProperty(name = "state", value = "-5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检；单插：13: 开启 14: 关闭", required = true)
    private Integer state;

    @ApiModelProperty(name = "batterySoc", value = "电池剩余电量")
    private BigDecimal batterySoc = BigDecimal.ZERO;

    @ApiModelProperty(name = "batteryPower", value = "电池当前电压")
    private BigDecimal batteryPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "socketSwitch", value = "插座开关")
    private Boolean socketSwitch;

    @ApiModelProperty(name = "chargeStationMode", value = "充电桩联网方式：0：即插即充，1：远程未联网，2：有线联网，3：无线联网")
    private Integer chargeStationMode;

    @ApiModelProperty(name = "vpp", value = "vpp模式是否开启", required = true)
    private boolean vpp;

    @ApiModelProperty(name = "type", value = "平台，0: 易联; 1: 涂鸦", required = true)
    private Integer type;

    @ApiModelProperty(name = "deviceSn", value = "设备序列号", required = true)
    private String deviceSn;

    @ApiModelProperty(name = "agentId", value = "代理商id", required = true)
    private String agentId;

    @ApiModelProperty(name = "lon", value = "经度", required = true)
    private Double lon;

    @ApiModelProperty(name = "lat", value = "纬度", required = true)
    private Double lat;

    @ApiModelProperty(name = "deviceType", value = "型号", required = true)
    private String deviceType;

    @ApiModelProperty(name = "resourceSeriesId", value = "设备分类ID", required = true)
    private Integer resourceSeriesId;

    @ApiModelProperty(name = "resourceTypeId", value = "设备系列ID", required = true)
    private Integer resourceTypeId;

    @ApiModelProperty(name = "master", value = "操作者的主从状态 0: 从 1: 主", required = true)
    private Integer master;

    @ApiModelProperty(name = "emsSoftwareVersion", value = "ems软件版本", required = true)
    private String emsSoftwareVersion;

    @ApiModelProperty(name = "dsp1SoftwareVersion", value = "dsp软件版本", required = true)
    private String dsp1SoftwareVersion;

    @ApiModelProperty(name = "firstInstall", value = "首次安装时间", required = true)
    private Long firstInstall;

    @ApiModelProperty(name = "isMain", value = "是否主机", required = true)
    private Boolean isMain;
}
