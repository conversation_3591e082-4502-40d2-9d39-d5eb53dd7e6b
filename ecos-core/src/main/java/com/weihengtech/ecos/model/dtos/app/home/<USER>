package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: 家庭中设备模式回参
 * @author: jiahao.jin
 * @create: 2024-02-28 09:33
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭中设备模式回参")
public class V2HomeDeviceModeDto {
    @ApiModelProperty(name = "deviceName", value = "设备名", required = true)
    private String deviceName;

    @ApiModelProperty(name = "chargeUseMode", value = "0: selfPowered; 1: loadShifting; 2:  backup; 3:  vpp", required = true)
    private Integer chargeUseMode;

    @ApiModelProperty(name = "isMain", value = "主从机")
    private Boolean isMain;

}
