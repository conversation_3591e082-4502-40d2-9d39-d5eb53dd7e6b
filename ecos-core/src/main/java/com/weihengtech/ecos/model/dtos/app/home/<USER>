package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页now设备实时energy回参")
public class HomeNowDeviceRealtimeDto {

	@ApiModelProperty(name = "solarPowerDps", value = "光伏功率Dps")
	private Map<Long, Object> solarPowerDps = new HashMap<>();

	@ApiModelProperty(name = "batteryPowerDps", value = "电池功率Dps")
	private Map<Long, Object> batteryPowerDps = new HashMap<>();

	@ApiModelProperty(name = "gridPowerDps", value = "电网功率Dps")
	private Map<Long, Object> gridPowerDps = new HashMap<>();

	@ApiModelProperty(name = "meterPowerDps", value = "仪表功率Dps")
	private Map<Long, Object> meterPowerDps = new HashMap<>();

	@ApiModelProperty(name = "homePowerDps", value = "家庭功率Dps")
	private Map<Long, Object> homePowerDps = new HashMap<>();

	@ApiModelProperty(name = "epsPowerDps", value = "EPS功率Dps")
	private Map<Long, Object> epsPowerDps = new HashMap<>();

	@ApiModelProperty(name = "socDps", value = "电池电量Dps")
	private Map<Long, Object> socDps = new HashMap<>();
}
