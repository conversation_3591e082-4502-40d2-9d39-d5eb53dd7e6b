package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页历史Grid图表回参")
public class HomeHistoryGridDto {

	@ApiModelProperty(name = "fromGrid", value = "买电量", required = true)
	private BigDecimal fromGrid;

	@ApiModelProperty(name = "toGrid", value = "卖点量", required = true)
	private BigDecimal toGrid;

	@ApiModelProperty(name = "fromGridDps", value = "买电量图表", required = true)
	private Map<String, BigDecimal> fromGridDps;

	@ApiModelProperty(name = "toGridDps", value = "卖电量图表", required = true)
	private Map<String, BigDecimal> toGridDps;
}
