package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "设备列表回参")
public class HomeDeviceListDto {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	private String deviceId;

	@ApiModelProperty(name = "deviceAliasName", value = "设备别名", required = true)
	private String deviceAliasName;

	@ApiModelProperty(name = "wifiSn", value = "wifiSn", required = true)
	private String wifiSn;

	@ApiModelProperty(name = "state", value = "-5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检", required = true)
	private Integer state;

	@ApiModelProperty(name = "weight", value = "设备排列顺序", required = true)
	private Integer weight;

	@ApiModelProperty(name = "temp", value = "温度", required = true)
	private String temp;

	@ApiModelProperty(name = "icon", value = "和风天气图标  999: 未知; 100: 晴; 101: 多云; 104: 阴天; 399: 雨; 499: 雪", required = true)
	private String icon;

	@ApiModelProperty(name = "vpp", value = "vpp模式是否开启", required = true)
	private boolean vpp;

	@ApiModelProperty(name = "master", value = "0: 当前是从账号, 1: 当前是主账号", required = true)
	private Integer master;

	@ApiModelProperty(name = "type", value = "平台，0: 易联; 1: 涂鸦", required = true)
	private Integer type;

	@ApiModelProperty(name = "deviceSn", value = "设备序列号", required = true)
	private String deviceSn;

	@ApiModelProperty(name = "agentId", value = "代理商id", required = true)
	private String agentId;

	@ApiModelProperty(name = "lon", value = "经度", required = true)
	private Double lon;

	@ApiModelProperty(name = "lat", value = "纬度", required = true)
	private Double lat;

	@ApiModelProperty(name = "category", value = "品类", required = true)
	private String category;

	@ApiModelProperty(name = "model", value = "型号", required = true)
	private String model;

	@ApiModelProperty(name = "deviceType", value = "设备类型", required = true)
	private String deviceType;

	@ApiModelProperty(name = "isMain", value = "是否主机", required = true)
	private Boolean isMain;
}
