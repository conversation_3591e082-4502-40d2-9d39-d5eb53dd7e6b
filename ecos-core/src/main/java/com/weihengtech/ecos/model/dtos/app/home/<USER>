package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@With
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "首页历史Home图表回参")
public class HomeHistoryHomeDto {

	@ApiModelProperty(name = "energyConsumption", value = "能耗", required = true)
	private BigDecimal energyConsumption = BigDecimal.ZERO;

	@ApiModelProperty(name = "solarPercent", value = "自发自用率", required = true)
	private BigDecimal solarPercent = BigDecimal.ZERO;

	@ApiModelProperty(name = "homeEnergyDps", value = "家庭能耗图", required = true)
	private Map<Long, BigDecimal> homeEnergyDps = new HashMap<>();
}
