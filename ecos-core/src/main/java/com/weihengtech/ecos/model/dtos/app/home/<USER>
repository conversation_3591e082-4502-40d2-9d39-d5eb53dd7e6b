package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "首页历史Battery图表回参")
public class HomeHistoryBatteryDto {

	@ApiModelProperty(name = "toBattery", value = "充电量", required = true)
	private BigDecimal toBattery;

	@ApiModelProperty(name = "fromBattery", value = "放电量", required = true)
	private BigDecimal fromBattery;

	@ApiModelProperty(name = "batterySoh", value = "SOH", required = true)
	private BigDecimal batterySoh;

	@ApiModelProperty(name = "fromBatteryDps", value = "放电量图", required = true)
	private Map<String, BigDecimal> fromBatteryDps;

	@ApiModelProperty(name = "toBatteryDps", value = "充电量图", required = true)
	private Map<String, BigDecimal> toBatteryDps;
}
