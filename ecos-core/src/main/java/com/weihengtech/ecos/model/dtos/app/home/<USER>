package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: ecos-server
 * @description: 家庭添加设备状态回参
 * @author: jiahao.jin
 * @create: 2024-03-05 10:04
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭添加设备状态回参")
public class V2HomeDeviceBindStatusDto {

    @ApiModelProperty(name = "deviceId", value = "设备ID")
    private String deviceId;

    @ApiModelProperty(name = "bindStatus", value = "0 失败 1 成功 2 进行中 3 未进行绑定  4 已被其他账户绑定", required = true)
    private Integer bindStatus;
}
