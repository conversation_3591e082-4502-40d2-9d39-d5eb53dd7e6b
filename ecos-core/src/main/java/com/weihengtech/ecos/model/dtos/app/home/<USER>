package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @program: ecos-server
 * @description: 家庭电池SOC储能
 * @author: jiahao.jin
 * @create: 2024-02-27 10:36
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭储能电池SOC储能")
public class V2HomeDeviceBatterySocDto {

    @ApiModelProperty(name = "deviceSn", value = "设备SN")
    private String deviceSn;

    @ApiModelProperty(name = "batterySoc", value = "电池剩余电量")
    private BigDecimal batterySoc = BigDecimal.ZERO;

    @ApiModelProperty(name = "sysRunMode", value = "-5 未知, -4: 设备已禁用; -3: 设备未激活; -1 设备离线 0: 等待 1: 并网 2: EPS 3: 故障; 4:保留 5: 自检")
    private Integer sysRunMode = -5;

    @ApiModelProperty(name = "isExistSolar", value = "是否存在光伏")
    private Boolean isExistSolar = Boolean.TRUE;

    @ApiModelProperty(name = "sysPowerConfig", value = "8: 柴发模式")
    private Integer sysPowerConfig = 0;

}
