package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 家庭成员详细信息回参
 * @author: jiahao.jin
 * @create: 2024-01-24 11:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class V2HomeMemberInfoDto {

    @ApiModelProperty(name = "id", value = "成员id", required = true)
    private String id;

    @ApiModelProperty(name = "nickname", value = "成员昵称", required = true)
    private String nickname;

    @ApiModelProperty(name = "contactInfo", value = "邮箱或者手机号（优先展示邮箱）", required = true)
    private String contactInfo;

    @ApiModelProperty(name = "relationType", value = "在该家庭中所处角色（0：成员，1：所有者）", required = true)
    private Integer relationType;

    @ApiModelProperty(name = "inviteTime", value = "被邀请时间", required = true)
    private String inviteTime;

    @ApiModelProperty(name = "inviteNickname", value = "邀请人昵称(为空则是自己的家庭)")
    private String inviteNickname;
}
