package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: ecos-server
 * @description: 家庭所有储能设备运行数据
 * @author: jiahao.jin
 * @create: 2024-02-27 10:32
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "家庭所有储能设备运行数据")
public class V2HomeNowDeviceRunDataDto {

    @ApiModelProperty(name = "batteryPower", value = "电池功率")
    private BigDecimal batteryPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "epsPower", value = "EPS功率")
    private BigDecimal epsPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "gridPower", value = "电网功率(电池功率大于0则指向Home, 反之Home指向电网)")
    private BigDecimal gridPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "homePower", value = "家庭功率")
    private BigDecimal homePower = BigDecimal.ZERO;

    @ApiModelProperty(name = "meterPower", value = "仪表功率")
    private BigDecimal meterPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "solarPower", value = "光伏功率")
    private BigDecimal solarPower = BigDecimal.ZERO;

    @ApiModelProperty(name = "chargePower", value = "充电桩功率")
    private BigDecimal chargePower = BigDecimal.ZERO;

    @ApiModelProperty(name = "batterySocList", value = "储能设备电量信息")
    private List<V2HomeDeviceBatterySocDto> batterySocList;
}
