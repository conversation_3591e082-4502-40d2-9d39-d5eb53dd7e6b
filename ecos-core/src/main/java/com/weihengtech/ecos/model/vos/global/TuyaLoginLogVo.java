package com.weihengtech.ecos.model.vos.global;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 涂鸦登录保存入参
 * @author: jiahao.jin
 * @create: 2024-04-13 09:40
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TuyaLoginLogVo {

    @ApiModelProperty("涂鸦账号")
    private String tuyaAccount;

    @ApiModelProperty("登录结果")
    private String loginRes;

    @ApiModelProperty("失败原因")
    private String failReason;
}
