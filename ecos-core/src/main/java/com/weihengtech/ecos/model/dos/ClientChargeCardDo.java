package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 充电桩绑定卡片表
 * @author: jiahao.jin
 * @create: 2024-03-01 10:05
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_charge_card")
public class ClientChargeCardDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long deviceId;

    // 卡片ID
    private String cardId;

    private Long createTime;

    private Long updateTime;
}
