package com.weihengtech.ecos.model.vos.settings;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "配置页个人信息入参")
public class SettingsUserUpdateVo {

	@ApiModelProperty(name = "timeZoneId", value = "时区id")
	private String timeZoneId;

	@ApiModelProperty(name = "nickname", value = "昵称")
	private String nickname;

}
