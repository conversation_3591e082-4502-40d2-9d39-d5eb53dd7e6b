package com.weihengtech.ecos.model.vos.charger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @program: ecos-server
 * @description: 充电桩删除卡片入参
 * @author: jiahao.jin
 * @create: 2024-03-08 10:34
 **/
@Getter
@Setter
@ToString
@ApiModel(value = "充电桩删除卡片入参")
public class DeleteChargeStationCardVo {

    @ApiModelProperty(name = "deviceId", value = "设备id", required = true)
    @NotBlank(message = "err.not.blank")
    private String deviceId;

    @ApiModelProperty(name = "cardId", value = "卡片码", required = true)
    @NotEmpty(message = "err.not.blank")
    private List<String> cardIds;
}
