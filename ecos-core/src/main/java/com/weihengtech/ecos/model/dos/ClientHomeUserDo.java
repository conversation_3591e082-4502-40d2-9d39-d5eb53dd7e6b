package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 家庭-用户关联表
 * @author: jiahao.jin
 * @create: 2024-01-21 11:18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_home_user")
public class ClientHomeUserDo {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long homeId;

    private Long userId;

    // 关系类型（0：成员，1：所有者）
    private Integer relationType;

    private Long inviteUserId;

    private Long createTime;

    private Long updateTime;
}
