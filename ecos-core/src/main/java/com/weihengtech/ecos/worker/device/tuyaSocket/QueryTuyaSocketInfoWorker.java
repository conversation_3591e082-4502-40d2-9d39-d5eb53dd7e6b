package com.weihengtech.ecos.worker.device.tuyaSocket;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketBaseInfoDto;
import com.weihengtech.ecos.model.vos.socket.SocketInfoVo;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.model.dos.ClientUserDo;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 16:47
 **/
public class QueryTuyaSocketInfoWorker extends AbstractTuyaSocketServiceWorker<TuyaSocketBaseInfoDto>{

    private final ClientUserDo clientUserDo;
    private final SocketInfoVo socketInfoVo;

    public QueryTuyaSocketInfoWorker(SinglePlugSocketService singlePlugSocketService, ClientUserDo clientUserDo, SocketInfoVo socketInfoVo) {
        super(singlePlugSocketService);
        this.clientUserDo = clientUserDo;
        this.socketInfoVo = socketInfoVo;
    }

    @Override
    public TuyaSocketBaseInfoDto action(String s, Map<String, WorkerWrapper> map) {
        return singlePlugSocketService.queryTuyaSocketInfo(clientUserDo, socketInfoVo);
    }

    @Override
    public TuyaSocketBaseInfoDto defaultValue() {
        return new TuyaSocketBaseInfoDto();
    }
}
