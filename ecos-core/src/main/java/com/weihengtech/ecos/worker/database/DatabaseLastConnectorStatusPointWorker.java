package com.weihengtech.ecos.worker.database;

import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.jd.platform.async.wrapper.WorkerWrapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DatabaseLastConnectorStatusPointWorker extends AbstractTimeSeriesDatabaseServiceWorker<Map<String, LinkedHashMap<Long, Object>>> {

    private final String deviceSn;
    private final String connectorStatus;
    private final List<String> metricList;
    private final long startTime;
    private final long endTime;

    public DatabaseLastConnectorStatusPointWorker(TimeSeriesDatabaseService timeSeriesDatabaseService, String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime) {
        super(timeSeriesDatabaseService);
        this.deviceSn = deviceSn;
        this.connectorStatus = connectorStatus;
        this.metricList = metricList;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> action(String s, Map<String, WorkerWrapper> map) {
        return timeSeriesDatabaseService.lastConnectorStatusPoint(deviceSn, connectorStatus, metricList, startTime, endTime);
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> defaultValue() {
        return new LinkedHashMap<>();
    }
}