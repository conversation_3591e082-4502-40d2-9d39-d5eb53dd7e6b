package com.weihengtech.ecos.worker.database;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DatabaseWithOutSampleQueryWorker extends AbstractTimeSeriesDatabaseServiceWorker<Map<String, LinkedHashMap<Long, Object>>> {

    private final String deviceSn;
    private final List<String> metricList;
    private final long start;
    private final long end;

    public DatabaseWithOutSampleQueryWorker(TimeSeriesDatabaseService timeSeriesDatabaseService, String deviceSn, List<String> metricList, long start, long end) {
        super(timeSeriesDatabaseService);
        this.deviceSn = deviceSn;
        this.metricList = metricList;
        this.start = start;
        this.end = end;
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> action(String s, Map<String, WorkerWrapper> map) {
        return timeSeriesDatabaseService.withOutSampleQuery(deviceSn, metricList, start, end);
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> defaultValue() {
        return new LinkedHashMap<>();
    }
}