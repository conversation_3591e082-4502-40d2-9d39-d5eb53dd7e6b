package com.weihengtech.ecos.worker.device.tuyaSocket;

import com.jd.platform.async.callback.ICallback;
import com.jd.platform.async.callback.IWorker;
import com.jd.platform.async.executor.timer.SystemClock;
import com.jd.platform.async.worker.WorkResult;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 16:48
 **/
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTuyaSocketServiceWorker<T> implements IWorker<String, T>, ICallback<String, T> {

    protected final SinglePlugSocketService singlePlugSocketService;
    @Override
    public void begin() {
        log.debug("start AbstractTuyaSocketServiceWorker");
    }

    @Override
    public void result(boolean b, String s, WorkResult<T> workResult) {
        if (b) {
            log.debug("callback " + s + " success--" + SystemClock.now()
                    + "-threadName:" + Thread.currentThread().getName() + "----" + workResult.getResult());
        } else {
            log.debug("callback " + s + " failure--" + SystemClock.now()
                    + "-threadName:" + Thread.currentThread().getName() + "----" + workResult.getResult());
        }
    }

    @Override
    public abstract T action(String s, Map<String, WorkerWrapper> map);

    @Override
    public abstract T defaultValue();
}
