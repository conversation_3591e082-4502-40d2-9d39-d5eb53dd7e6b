package com.weihengtech.ecos.worker.device.energyStorage;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 19:14
 **/
public class GetLastWeekSolarAndGridEnergyDataWorker extends AbstractEnergyStorageServiceWorker<HomeDeviceEnergyStatisticsDto>{

    private final ClientUserDo clientUserDo;
    private final String deviceId;
    private final Integer offsetDay;
    private final String timezone;

    public GetLastWeekSolarAndGridEnergyDataWorker(SinglePhaseService singlePhaseService,ClientUserDo clientUserDo, String deviceId, Integer offsetDay, String timezone) {
        super(singlePhaseService);
        this.clientUserDo = clientUserDo;
        this.deviceId = deviceId;
        this.offsetDay = offsetDay;
        this.timezone = timezone;
    }
    @Override
    public HomeDeviceEnergyStatisticsDto action(String s, Map<String, WorkerWrapper> map) {
        return singlePhaseService.getLastWeekSolarAndGridEnergyData(clientUserDo, deviceId, offsetDay, timezone);
    }

    @Override
    public HomeDeviceEnergyStatisticsDto defaultValue() {
        HomeDeviceEnergyStatisticsDto defaultItem = new HomeDeviceEnergyStatisticsDto();
        // 更改原有的Map的定义方式
        Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> map = new HashMap<>();
        Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> map2 = new HashMap<>();
        Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> map3 = new HashMap<>();

        // 使用for循环设置默认的键和值
        for (int i = 1; i <= 7; i++) {
            // 初始化值为0
            map.put(i, new HomeDeviceEnergyStatisticsDto.DeviceEnergy());
            map2.put(i, new HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy());
            map3.put(i, new HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy());
        }
        defaultItem.setWeekEnergy(map);
        defaultItem.setCarbonEmissionsWeekEnergy(map2);
        defaultItem.setSaveStandardCoalWeekEnergy(map3);
        return defaultItem;
    }
}
