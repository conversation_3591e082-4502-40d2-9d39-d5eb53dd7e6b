package com.weihengtech.ecos.worker.hub;

import com.jd.platform.async.callback.ICallback;
import com.jd.platform.async.callback.IWorker;
import com.jd.platform.async.executor.timer.SystemClock;
import com.jd.platform.async.worker.WorkResult;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.service.thirdpart.HubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-25 17:28
 **/
@Slf4j
@RequiredArgsConstructor
public class HubServiceGetAgentsByIdsWork implements IWorker<String, List<BindInfoDTO>>, ICallback<String, List<BindInfoDTO>> {

    private final List<Long> ids;

    private final HubService hubService;

    @Override
    public void begin() {
        log.debug("start HubServiceGetAgentsByIdsWork: {}", ids);
    }

    @Override
    public void result(boolean b, String s, WorkResult<List<BindInfoDTO>> workResult) {
        if (b) {
            log.debug("callback HubServiceGetAgentsByIdsWork success--" + SystemClock.now()
                    + "-threadName:" +Thread.currentThread().getName());
        } else {
            log.debug("callback HubServiceGetAgentsByIdsWork failure--" + SystemClock.now()
                    + "-threadName:" +Thread.currentThread().getName());
        }
    }


    @Override
    public List<BindInfoDTO> action(String s, Map<String, WorkerWrapper> map) {

        return hubService.getAgentsByIds(ids);
    }

    @Override
    public List<BindInfoDTO> defaultValue() {
        return new ArrayList<>();
    }
}
