package com.weihengtech.ecos.worker.device.energyStorage;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 17:06
 **/
public class GetSocWorker extends AbstractEnergyStorageServiceWorker<Map<String, BigDecimal>>{

    private final String userId;
    private final HybridSinglePhaseDO hybridSinglePhaseDO;

    public GetSocWorker(SinglePhaseService singlePhaseService, String userId, HybridSinglePhaseDO hybridSinglePhaseDO) {
        super(singlePhaseService);
        this.userId = userId;
        this.hybridSinglePhaseDO = hybridSinglePhaseDO;
    }
    @Override
    public Map<String, BigDecimal> action(String s, Map<String, WorkerWrapper> map) {
        return singlePhaseService.getSoc(userId, hybridSinglePhaseDO);
    }

    @Override
    public Map<String, BigDecimal> defaultValue() {
        return new HashMap<>();
    }
}
