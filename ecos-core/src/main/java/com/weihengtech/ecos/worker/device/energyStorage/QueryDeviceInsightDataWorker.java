package com.weihengtech.ecos.worker.device.energyStorage;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.insight.InsightConsumptionDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceStatisticsDto;
import com.weihengtech.ecos.model.vos.app.InsightDeviceDataVo;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 19:31
 **/
public class QueryDeviceInsightDataWorker extends AbstractEnergyStorageServiceWorker<InsightDeviceDataDto>{

    private final ClientUserDo clientUserDo;
    private final InsightDeviceDataVo insightDeviceDataVo;

    public QueryDeviceInsightDataWorker(SinglePhaseService singlePhaseService, ClientUserDo clientUserDo, InsightDeviceDataVo insightDeviceDataVo) {
        super(singlePhaseService);
        this.clientUserDo = clientUserDo;
        this.insightDeviceDataVo = insightDeviceDataVo;
    }
    @Override
    public InsightDeviceDataDto action(String s, Map<String, WorkerWrapper> map) {
        return singlePhaseService.queryDeviceInsightData(clientUserDo, insightDeviceDataVo);
    }

    @Override
    public InsightDeviceDataDto defaultValue() {
        InsightDeviceDataDto insightDeviceDataDto = new InsightDeviceDataDto();
        HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = new HomeNowDeviceRealtimeDto();
        InsightDeviceStatisticsDto insightDeviceStatisticsDto = new InsightDeviceStatisticsDto();
        InsightConsumptionDataDto insightConsumptionDataDto = new InsightConsumptionDataDto();
        insightDeviceDataDto.setInsightConsumptionDataDto(insightConsumptionDataDto);
        insightDeviceDataDto.setDeviceRealtimeDto(homeNowDeviceRealtimeDto);
        insightDeviceDataDto.setDeviceStatisticsDto(insightDeviceStatisticsDto);
        return insightDeviceDataDto;
    }
}
