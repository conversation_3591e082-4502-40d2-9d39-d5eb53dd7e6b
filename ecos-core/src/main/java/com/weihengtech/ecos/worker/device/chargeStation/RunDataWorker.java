package com.weihengtech.ecos.worker.device.chargeStation;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationRunDataDto;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.model.dos.ClientUserDo;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 18:25
 **/
public class RunDataWorker extends AbstractChargeStationServiceWorker<ENPlusChargeStationRunDataDto> {

    private final ClientUserDo clientUserDo;
    private final String deviceId;

    public RunDataWorker(ChargeStationService chargeStationService, ClientUserDo clientUserDo, String deviceId) {
        super(chargeStationService);
        this.clientUserDo = clientUserDo;
        this.deviceId = deviceId;
    }
    @Override
    public ENPlusChargeStationRunDataDto action(String s, Map<String, WorkerWrapper> map) {
        return chargeStationService.runData(clientUserDo, deviceId);
    }

    @Override
    public ENPlusChargeStationRunDataDto defaultValue() {
        return new ENPlusChargeStationRunDataDto();
    }
}
