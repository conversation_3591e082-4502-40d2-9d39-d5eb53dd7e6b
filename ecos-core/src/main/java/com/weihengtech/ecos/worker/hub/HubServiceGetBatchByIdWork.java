package com.weihengtech.ecos.worker.hub;

import com.jd.platform.async.callback.ICallback;
import com.jd.platform.async.callback.IWorker;
import com.jd.platform.async.executor.timer.SystemClock;
import com.jd.platform.async.worker.WorkResult;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.service.thirdpart.HubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 获取最后一点点位数据
 * @author: jiahao.jin
 * @create: 2024-06-24 15:39
 **/
@Slf4j
@RequiredArgsConstructor
public class HubServiceGetBatchByIdWork implements IWorker<String, List<HybridSinglePhaseDO>>, ICallback<String, List<HybridSinglePhaseDO>> {

    private final Boolean isNeedExt;

    private final List<Long> ids;

    private final HubService hubService;

    @Override
    public void begin() {
        log.debug("start HubServiceGetBatchByIdWork: {}, {}", isNeedExt, ids);
    }

    @Override
    public void result(boolean b, String s, WorkResult<List<HybridSinglePhaseDO>> workResult) {
        if (b) {
            log.debug("callback HubServiceGetBatchByIdWork success--" + SystemClock.now() + "----" + workResult.getResult().size()
                    + "-threadName:" +Thread.currentThread().getName());
        } else {
            log.debug("callback HubServiceGetBatchByIdWork failure--" + SystemClock.now() + "----"  + workResult.getResult().size()
                    + "-threadName:" +Thread.currentThread().getName());
        }
    }


    @Override
    public List<HybridSinglePhaseDO> action(String s, Map<String, WorkerWrapper> map) {

        return hubService.getBatchById(isNeedExt,ids);
    }

    @Override
    public List<HybridSinglePhaseDO> defaultValue() {
        return new ArrayList<>();
    }
}
