package com.weihengtech.ecos.worker.database;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DatabaseDeltaQueryOneHourPointWorker extends AbstractTimeSeriesDatabaseServiceWorker<Map<String, LinkedHashMap<Long, Object>>> {

    private final String deviceSn;
    private final List<String> metricList;
    private final long startTime;
    private final long endTime;

    public DatabaseDeltaQueryOneHourPointWorker(TimeSeriesDatabaseService timeSeriesDatabaseService, String deviceSn, List<String> metricList, long startTime, long endTime) {
        super(timeSeriesDatabaseService);
        this.deviceSn = deviceSn;
        this.metricList = metricList;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> action(String s, Map<String, WorkerWrapper> map) {
        return timeSeriesDatabaseService.deltaQuery(deviceSn, metricList, startTime, endTime, TsdbSampleEnum.ONE_HOUR_NONE_POINT);
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> defaultValue() {
        return new LinkedHashMap<>();
    }
}