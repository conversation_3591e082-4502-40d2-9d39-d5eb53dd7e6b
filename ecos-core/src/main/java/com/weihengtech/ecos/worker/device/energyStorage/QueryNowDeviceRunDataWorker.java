package com.weihengtech.ecos.worker.device.energyStorage;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 18:22
 **/
public class QueryNowDeviceRunDataWorker extends AbstractEnergyStorageServiceWorker<HomeNowDeviceRunDataDto> {

    private final ClientUserDo clientUserDo;
    private final String deviceId;

    public QueryNowDeviceRunDataWorker(SinglePhaseService singlePhaseService,ClientUserDo clientUserDo, String deviceId) {
        super(singlePhaseService);
        this.clientUserDo = clientUserDo;
        this.deviceId = deviceId;
    }


    @Override
    public HomeNowDeviceRunDataDto action(String s, Map<String, WorkerWrapper> map) {
        return singlePhaseService.queryNowDeviceRunData(clientUserDo, deviceId);
    }

    @Override
    public HomeNowDeviceRunDataDto defaultValue() {
        return new HomeNowDeviceRunDataDto();
    }
}
