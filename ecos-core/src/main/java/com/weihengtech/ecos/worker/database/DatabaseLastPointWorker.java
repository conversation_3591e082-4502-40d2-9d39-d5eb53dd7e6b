package com.weihengtech.ecos.worker.database;

import cn.hutool.core.lang.Dict;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.jd.platform.async.wrapper.WorkerWrapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DatabaseLastPointWorker extends AbstractTimeSeriesDatabaseServiceWorker<Dict> {

    private final String deviceSn;
    private final List<String> metricList;
    private final long endTime;

    public DatabaseLastPointWorker(TimeSeriesDatabaseService timeSeriesDatabaseService, String deviceSn, List<String> metricList, long endTime) {
        super(timeSeriesDatabaseService);
        this.deviceSn = deviceSn;
        this.metricList = metricList;
        this.endTime = endTime;
    }

    @Override
    public Dict action(String s, Map<String, WorkerWrapper> map) {
        return timeSeriesDatabaseService.lastPoint(deviceSn, metricList, endTime);
    }

    @Override
    public Dict defaultValue() {
        return new Dict();
    }
}