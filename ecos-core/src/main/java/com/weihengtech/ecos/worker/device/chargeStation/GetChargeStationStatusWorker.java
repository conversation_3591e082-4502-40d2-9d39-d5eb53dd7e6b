package com.weihengtech.ecos.worker.device.chargeStation;

import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.service.charger.ChargeStationService;

import java.util.Map;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-26 16:58
 **/
public class GetChargeStationStatusWorker extends AbstractChargeStationServiceWorker<Integer>{

    private final String userId;
    private final String deviceId;

    public GetChargeStationStatusWorker(ChargeStationService chargeStationService, String userId, String deviceId) {
        super(chargeStationService);
        this.userId = userId;
        this.deviceId = deviceId;
    }


    @Override
    public Integer action(String s, Map<String, WorkerWrapper> map) {
        return chargeStationService.getChargeStationStatus(deviceId);
    }

    @Override
    public Integer defaultValue() {
        return -1;
    }
}
