package com.weihengtech.ecos.service.global;

import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.service.ele.HomeElePriceService;

/**
 * <AUTHOR>
 */
public interface StrategyService {

	/**
	 * 选择tsdb服务
	 *
	 * @param deviceId 设备id
	 * @return tsdb实例
	 */
	TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(String deviceId);

	/**
	 * 选择tsdb服务
	 *
	 * @param deviceId 设备id
	 * @return tsdb实例
	 */
	TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(Long deviceId);

	/**
	 * 选择tsdb服务
	 *
	 * @param hybridSinglePhaseDO 设备实例
	 * @return tsdb实例
	 */
	TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(HybridSinglePhaseDO hybridSinglePhaseDO);

	/**
	 * 选择tsdb服务
	 *
	 * @param deviceFlag 设备标识
	 * @return tsdb实例
	 */
	TimeSeriesDatabaseService chooseTimeSeriesDatabaseServiceWithDeviceFlag(String deviceFlag);

	/**
	 * 选择电价服务
	 *
	 * @param elePriceType 电价类型
	 * @return
	 */
	HomeElePriceService chooseHomeElePriceService(Integer elePriceType);
}
