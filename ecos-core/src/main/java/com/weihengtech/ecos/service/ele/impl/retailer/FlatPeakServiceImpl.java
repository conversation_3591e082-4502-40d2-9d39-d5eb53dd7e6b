package com.weihengtech.ecos.service.ele.impl.retailer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.enums.ele.RetailerEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.FlatPeakLocationDetailsDTO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.service.ele.RetailerService;
import com.weihengtech.ecos.utils.ElectricityPriceTypeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/22 14:28
 */
@Service
@Slf4j
public class FlatPeakServiceImpl implements RetailerService {

    @Value("${flat-peak.callback-url:https://webhook.site}")
    private String callbackUrl;

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private AuthCacheUtil authCacheUtil;

    /**
     * 查询FlatPeak零售商电价信息
     *
     * @param config   电价配置信息
     * @param time     时间参数（0表示当天，1表示明天）
     * @param timezone 时区
     * @return 电价信息列表
     */
    @Override
    public RetailerElePriceDTO queryRetailerElePrice(ClientElePriceRetailerDO config, Integer time, String timezone) {
        String locationId = config.getToken();

        try {
            String bearerToken = authCacheUtil.getFlatPeakToken();
            String startTime = ZonedDateTime.now(ZoneId.of(timezone))
                    .toLocalDate()
                    .plusDays(time)
                    .atStartOfDay(ZoneId.of(timezone))
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));
            String endTime = ZonedDateTime.now(ZoneId.of(timezone))
                    .toLocalDate()
                    .plusDays(time)
                    .plusDays(1)
                    .atStartOfDay(ZoneId.of(timezone))
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));

            String url = String.format("%s/tariffs/rates/%s?include_tariff=true&direction=IMPORT&start_time=%s&end_time=%s",
                    RetailerEnum.FLAT_PEAK.getUrl(), locationId, URLEncoder.encode(startTime, StandardCharsets.UTF_8.displayName()),
                    URLEncoder.encode(endTime, StandardCharsets.UTF_8.displayName()));

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + bearerToken)
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return parseTariffResponse(responseBody);
                } else {
                    log.error("Failed to get tariff data: {}", response.code());
                    throw new RuntimeException("Failed to get tariff data from FlatPeak API");
                }
            }
        } catch (IOException e) {
            log.error("Error getting tariff data from FlatPeak API", e);
            throw new RuntimeException("Error getting tariff data from FlatPeak API", e);
        }
    }

    /**
     * 获取FlatPeak Connect Token
     *
     * @return connect token
     */
    @Override
    public String getConnectToken() {
        try {
            String bearerToken = authCacheUtil.getFlatPeakToken();

            String jsonBody = "{"
                    + "\"direction\":\"IMPORT\","
                    + "\"type\":\"COMMODITY\","
                    + "\"callback_uri\":\"" + callbackUrl + "\""
                    + "}";

            MediaType JSON = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = new Request.Builder()
                    .url(RetailerEnum.FLAT_PEAK.getUrl() + "/connect/tariff/token")
                    .header("Authorization", "Bearer " + bearerToken)
                    .header("Content-Type", "application/json")
                    .post(body)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return parseConnectTokenFromResponse(responseBody);
                } else {
                    log.error("Failed to get connect token: {}", response.code());
                    throw new RuntimeException("Failed to get connect token from FlatPeak API");
                }
            }
        } catch (IOException e) {
            log.error("Error getting connect token from FlatPeak API", e);
            throw new RuntimeException("Error getting connect token from FlatPeak API", e);
        }
    }

    /**
     * 获取FlatPeak Location ID
     *
     * @param connectToken connect token
     * @return location ID
     */
    @Override
    public String getLocationId(String connectToken) {
        try {
            String bearerToken = authCacheUtil.getFlatPeakToken();

            String url = String.format("%s/connect/tariff/token?connect_token=%s", RetailerEnum.FLAT_PEAK.getUrl(), connectToken);

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + bearerToken)
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return parseLocationIdFromResponse(responseBody);
                } else {
                    log.error("Failed to get location ID: {}", response.code());
                    throw new RuntimeException("Failed to get location ID from FlatPeak API");
                }
            }
        } catch (IOException e) {
            log.error("Error getting location ID from FlatPeak API", e);
            throw new RuntimeException("Error getting location ID from FlatPeak API", e);
        }
    }

    /**
     * 获取FlatPeak Location详情
     *
     * @param locationId location ID
     * @return location详情（仅包含commodity_import信息）
     */
    @Override
    public FlatPeakLocationDetailsDTO getLocationDetails(String locationId) {
        try {
            String bearerToken = authCacheUtil.getFlatPeakToken();

            String url = String.format("%s/locations/%s/status", RetailerEnum.FLAT_PEAK.getUrl(), locationId);

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + bearerToken)
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    return parseLocationDetailsFromResponse(responseBody);
                } else {
                    log.error("Failed to get location details: {}", response.code());
                    throw new RuntimeException("Failed to get location details from FlatPeak API");
                }
            }
        } catch (IOException e) {
            log.error("Error getting location details from FlatPeak API", e);
            throw new RuntimeException("Error getting location details from FlatPeak API", e);
        }
    }

    @Override
    public List<TibberHomeDTO> queryTibberHomes(String token) {
        return Collections.emptyList();
    }

    /**
     * 从响应中解析connect token
     *
     * @param responseBody API响应内容
     * @return connect token
     */
    private String parseConnectTokenFromResponse(String responseBody) {
        try {
            JSONObject responseJson = JSONUtil.parseObj(responseBody);
            String connectToken = responseJson.getStr("connect_token");
            if (connectToken == null) {
                throw new RuntimeException("Connect token not found in response");
            }
            return connectToken;
        } catch (Exception e) {
            log.error("Error parsing connect token from response: {}", responseBody, e);
            throw new RuntimeException("Error parsing connect token from response", e);
        }
    }

    /**
     * 从响应中解析location ID
     *
     * @param responseBody API响应内容
     * @return location ID
     */
    private String parseLocationIdFromResponse(String responseBody) {
        try {
            JSONObject responseJson = JSONUtil.parseObj(responseBody);
            String locationId = responseJson.getStr("location_id");
            if (locationId == null) {
                throw new RuntimeException("Location ID not found in response");
            }
            return locationId;
        } catch (Exception e) {
            log.error("Error parsing location ID from response: {}", responseBody, e);
            throw new RuntimeException("Error parsing location ID from response", e);
        }
    }

    /**
     * 从响应中解析location详情（仅包含commodity_import信息）
     *
     * @param responseBody API响应内容
     * @return location详情
     */
    private FlatPeakLocationDetailsDTO parseLocationDetailsFromResponse(String responseBody) {
        try {
            JSONObject responseJson = JSONUtil.parseObj(responseBody);
            JSONObject tariffStatus = responseJson.getJSONObject("tariff_status");
            if (tariffStatus == null) {
                throw new RuntimeException("tariff_status not found in response");
            }
            
            JSONObject commodityImport = tariffStatus.getJSONObject("commodity_import");
            if (commodityImport == null) {
                throw new RuntimeException("commodity_import not found in response");
            }
            
            return parseLocationDetailsToDTO(commodityImport);
        } catch (Exception e) {
            log.error("Error parsing location details from response: {}", responseBody, e);
            throw new RuntimeException("Error parsing location details from response", e);
        }
    }

    /**
     * 将JSON对象转换为LocationDetails DTO
     *
     * @param commodityImport commodity_import JSON对象
     * @return LocationDetails DTO
     */
    private FlatPeakLocationDetailsDTO parseLocationDetailsToDTO(JSONObject commodityImport) {
        try {
            // 解析provider信息
            JSONObject providerJson = commodityImport.getJSONObject("provider");
            FlatPeakLocationDetailsDTO.ProviderDTO provider = null;
            if (providerJson != null) {
                provider = FlatPeakLocationDetailsDTO.ProviderDTO.builder()
                        .id(providerJson.getStr("id"))
                        .displayName(providerJson.getStr("display_name"))
                        .logoUrl(providerJson.getStr("logo_url"))
                        .build();
            }

            // 解析tariff信息
            JSONObject tariffJson = commodityImport.getJSONObject("tariff");
            FlatPeakLocationDetailsDTO.TariffDTO tariff = null;
            if (tariffJson != null) {
                tariff = FlatPeakLocationDetailsDTO.TariffDTO.builder()
                        .id(tariffJson.getStr("id"))
                        .displayName(tariffJson.getStr("display_name"))
                        .structureType(tariffJson.getStr("structure_type"))
                        .contractEndDate(tariffJson.getStr("contract_end_date"))
                        .build();
            }

            return FlatPeakLocationDetailsDTO.builder()
                    .status(commodityImport.getStr("status"))
                    .connectionType(commodityImport.getStr("connection_type"))
                    .provider(provider)
                    .tariff(tariff)
                    .build();
        } catch (Exception e) {
            log.error("Error converting JSON to DTO: {}", commodityImport, e);
            throw new RuntimeException("Error converting JSON to DTO", e);
        }
    }

    /**
     * 解析FlatPeak电价响应数据
     *
     * @param responseBody API响应内容
     * @return 电价信息
     */
    private RetailerElePriceDTO parseTariffResponse(String responseBody) {
        try {

            JSONObject responseJson = JSONUtil.parseObj(responseBody);
            JSONArray dataArray = responseJson.getJSONArray("data");
            
            if (dataArray == null) {
                throw new RuntimeException("Data not found in response");
            }

            String currency = responseJson.getStr("currency_code");

            List<Long> timeList = dataArray.stream()
                    .map(i -> (JSONObject) i)
                    .map(i -> i.getStr("valid_from"))
                    .map(this::parseDateTimeToUnix)
                    .collect(Collectors.toList());
            TimeIntervalType timeIntervalType = getTimeIntervalTypeByPrice(timeList);
            List<EleDayAheadPriceDto> priceList = processPriceData(dataArray, timeIntervalType);
            priceList.forEach(i -> i.setCurrency(currency != null ? currency : "EUR"));
            ElePriceTypeDetailEnum typeDetailEnum = ElectricityPriceTypeUtil.detectPriceType(priceList);
            return RetailerElePriceDTO.builder()
                    .elePriceDetailType(typeDetailEnum.getCode())
                    .priceList(priceList)
                    .build();

        } catch (Exception e) {
            log.error("Error parsing tariff response: {}", responseBody, e);
            throw new RuntimeException("Error parsing tariff response", e);
        }
    }

    private List<EleDayAheadPriceDto> processPriceData(JSONArray priceList, TimeIntervalType timeIntervalType) {
        if (CollUtil.isEmpty(priceList)) {
            return Collections.emptyList();
        }

        List<EleDayAheadPriceDto> processedList;
        
        // 根据时间间隔类型处理数据
        switch (timeIntervalType) {
            case HOUR:
                processedList = processByInterval(priceList, 3600);
                break;
            case HALF_HOUR:
                processedList = processByInterval(priceList, 1800);
                break;
            case QUARTER_HOUR:
                processedList = processByInterval(priceList, 900);
                break;
            default:
                // 默认不处理
                processedList = Collections.emptyList();
                break;
        }
        
        return processedList;
    }

    /**
     * 按指定时间间隔处理电价数据
     */
    private List<EleDayAheadPriceDto> processByInterval(JSONArray priceList, int intervalSeconds) {
        List<EleDayAheadPriceDto> result = new ArrayList<>();
        int size = 60 * 60 * 24 / intervalSeconds;
        if (priceList.size() == size) {
            for (Object o : priceList) {
                JSONObject priceJson = (JSONObject) o;
                EleDayAheadPriceDto priceDto = EleDayAheadPriceDto.builder()
                        .average(priceJson.getBigDecimal("value"))
                        .startTimeUnix(parseDateTimeToUnix(priceJson.getStr("valid_from")))
                        .build();
                result.add(priceDto);
            }
            return result;
        }
        for (Object o : priceList) {
            JSONObject priceJson = (JSONObject) o;
            long start = parseDateTimeToUnix(priceJson.getStr("valid_from"));
            long end = parseDateTimeToUnix(priceJson.getStr("valid_to"));
            JSONObject tariffObj = priceJson.getJSONObject("tariff");
            String tariffRate = tariffObj != null ? tariffObj.getStr("rate") : null;
            long currentTime = start;
            while (currentTime < end) {
                EleDayAheadPriceDto intervalPrice = EleDayAheadPriceDto.builder()
                        .startTimeUnix(currentTime)
                        .average(new BigDecimal(tariffRate))
                        .build();
                result.add(intervalPrice);
                currentTime += intervalSeconds;
            }
        }
        return result;
    }
    
    /**
     * 查找指定时间点对应的价格
     */
    private EleDayAheadPriceDto findPriceForTime(List<EleDayAheadPriceDto> priceList, long targetTime, long endTime) {
        for (EleDayAheadPriceDto price : priceList) {
            long priceTime = price.getStartTimeUnix();
            // 查找第一个时间大于等于目标时间点的价格
            if (priceTime >= targetTime) {
                return price;
            }
        }
        return null;
    }


    /**
     * 将ISO时间字符串转换为Unix时间戳
     *
     * @param dateTimeStr ISO时间字符串
     * @return Unix时间戳
     */
    private Long parseDateTimeToUnix(String dateTimeStr) {
        try {
            Instant instant = OffsetDateTime.parse(dateTimeStr).toInstant();
            return instant.getEpochSecond();
        } catch (Exception e) {
            log.error("Error parsing datetime: {}", dateTimeStr, e);
            return System.currentTimeMillis() / 1000;
        }
    }
}