package com.weihengtech.ecos.service.global.impl;

import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.ele.impl.ClientElePriceFixedServiceImpl;
import com.weihengtech.ecos.service.ele.impl.ClientElePriceRetailerServiceImpl;
import com.weihengtech.ecos.service.ele.impl.ClientElePriceUseServiceImpl;
import com.weihengtech.ecos.service.ele.impl.ClientElePriceWholesaleServiceImpl;
import com.weihengtech.ecos.utils.BeanUtil;
import com.weihengtech.ecos.utils.InitUtil;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class StrategyServiceImpl implements StrategyService {

	@Resource
	private HubService hubService;

	@Override
	public TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(String deviceId) {
		return chooseTimeSeriesDatabaseService(hubService.getById(Long.parseLong(deviceId)));
	}

	@Override
	public TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(Long deviceId) {
		return chooseTimeSeriesDatabaseService(hubService.getById(deviceId));
	}

	@Override
	public TimeSeriesDatabaseService chooseTimeSeriesDatabaseService(HybridSinglePhaseDO hybridSinglePhaseDO) {
		BeanUtil.assertNotNull(hybridSinglePhaseDO);
		return InitUtil.getBean("tsdbService" + hybridSinglePhaseDO.getTsdbSource(), TimeSeriesDatabaseService.class);
	}

	@Override
	public TimeSeriesDatabaseService chooseTimeSeriesDatabaseServiceWithDeviceFlag(String deviceFlag) {
		return chooseTimeSeriesDatabaseService(hubService.getByDeviceName(deviceFlag));
	}

    @Override
    public HomeElePriceService chooseHomeElePriceService(Integer elePriceType) {
		switch (ElePriceTypeEnum.getEnum(elePriceType)) {
			case FIXED:
				return InitUtil.getBean(ClientElePriceFixedServiceImpl.class);
			case TIME_OF_USE:
				return InitUtil.getBean(ClientElePriceUseServiceImpl.class);
			case WHOLESALE:
				return InitUtil.getBean(ClientElePriceWholesaleServiceImpl.class);
			case RETAIL:
				return InitUtil.getBean(ClientElePriceRetailerServiceImpl.class);
			default:
				return null;
		}
    }
}
