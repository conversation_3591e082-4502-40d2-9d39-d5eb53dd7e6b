package com.weihengtech.ecos.service.app.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.MiddleClientUserDeviceMapper;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MiddleClientUserDeviceServiceImpl
		extends
		ServiceImpl<MiddleClientUserDeviceMapper, MiddleClientUserDeviceDo>
		implements
        MiddleClientUserDeviceService {

	@Autowired
	private SnowFlakeUtil snowFlakeUtil;

	@Override
	@DSTransactional
	public void sortOrder(List<Long> middleIdList) {
		for (int i = 0; i < middleIdList.size(); i++) {
			MiddleClientUserDeviceDo middleClientUserDeviceDo = this.getById(middleIdList.get(i));
			middleClientUserDeviceDo.setWeight(i);
			middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
			ActionFlagUtil.assertTrue(this.updateById(middleClientUserDeviceDo));
		}
	}

	@Override
	public Boolean isUserBindDevice(Long deviceId, Long userId) {
		return null != getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
				.eq(MiddleClientUserDeviceDo::getUserId, userId)
		);
	}

    @Override
	@Transactional(rollbackFor = Exception.class)
    public boolean saveMasterBind(Long userId, List<MiddleClientUserDeviceDo> boundList,
								  List<HybridSinglePhaseDO> deviceList) {
		if (CollUtil.isEmpty(deviceList)) {
			return true;
		}
		// 只需新增尚未绑定主账号关系的设备
		Set<Long> existsSet = boundList == null ? new HashSet<>() : boundList.stream()
				.map(MiddleClientUserDeviceDo::getDeviceId)
				.collect(Collectors.toSet());
		List<MiddleClientUserDeviceDo> itemList = deviceList.stream()
				.filter(i -> !existsSet.contains(i.getId()))
				.map(i -> MiddleClientUserDeviceDo.builder()
						.id(snowFlakeUtil.generateId())
						.userId(userId)
						.deviceId(i.getId())
						.createTime(System.currentTimeMillis())
						.updateTime(System.currentTimeMillis())
						.weight(1000)
						.name(i.getDeviceName())
						.master(1)
						.build())
				.collect(Collectors.toList());
		return saveBatch(itemList);
    }

	@Override
	public boolean saveMasterBind(Long userId, Long deviceId, String deviceName) {
		long middleId = snowFlakeUtil.generateId();
		MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
		middleClientUserDeviceDo.setId(middleId);
		middleClientUserDeviceDo.setUserId(userId);
		middleClientUserDeviceDo.setDeviceId(deviceId);
		middleClientUserDeviceDo.setCreateTime(System.currentTimeMillis());
		middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
		middleClientUserDeviceDo.setWeight(0);
		middleClientUserDeviceDo.setName(deviceName);
		middleClientUserDeviceDo.setMaster(1);
		return this.save(middleClientUserDeviceDo);
	}
}
