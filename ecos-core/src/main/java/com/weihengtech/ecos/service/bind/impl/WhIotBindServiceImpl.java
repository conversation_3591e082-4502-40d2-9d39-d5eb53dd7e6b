package com.weihengtech.ecos.service.bind.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.vos.bind.BindDeviceVO;
import com.weihengtech.ecos.model.vos.bind.WhBindDeviceVO;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.bind.WhIotBindService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * wh iot设备绑定实现逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/16 14:36
 */
@Service
@Slf4j
public class WhIotBindServiceImpl extends BindServiceImpl implements WhIotBindService {

    @Resource
    private HubService hubService;
    @Resource
    private RetryService retryService;

    @Override
    public Long netDeviceBind(WhBindDeviceVO bindParam) {
        return super.netDeviceBind(bindParam);
    }

    @Override
    protected <T extends BindDeviceVO> Long netDeviceBind(T bindParam, ClientUserDo userInfo) {
        WhBindDeviceVO param = (WhBindDeviceVO) bindParam;
        param.buildSourceParam();
        // 1、获取设备详情
        HybridSinglePhaseDO deviceInfo = getDeviceInfoBySn(param.getDeviceSn());
        // 2、校验是否已经被绑定
        checkAlreadyBound(deviceInfo, userInfo);
        // 3、设备加速
        speedupDevice(param.getWifiSn(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
        // 4、根据WiFi Sn查询设备IP
        String ip = getDeviceIpByWifiSn(param.getWifiSn(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
        // 5、执行绑定逻辑
        bindDeviceAction(deviceInfo, param, userInfo, ip, param.getHomeId());
        // 6、异步更新设备状态
        ThreadUtil.execAsync(() -> retryService.syncWhDeviceState(param.getDeviceSn()));
        return deviceInfo.getId();
    }
}
