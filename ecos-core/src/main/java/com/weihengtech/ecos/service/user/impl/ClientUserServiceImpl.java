package com.weihengtech.ecos.service.user.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.dao.ClientUserMapper;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class ClientUserServiceImpl extends ServiceImpl<ClientUserMapper, ClientUserDo> implements ClientUserService {

	@Resource
	private ClientUserMapper clientUserMapper;

	@Override
	public Optional<ClientUserDo> queryOptionalUserByEmail(String email) {
		return Optional.ofNullable(clientUserMapper.selectOne(Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getEmail, email)));
	}

	@Override
	public Optional<ClientUserDo> queryOptionalUserByPhone(String phone) {
		return Optional.ofNullable(clientUserMapper.selectOne(Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getPhone, phone)));
	}

	@Override
	public Optional<ClientUserDo> queryOptionalUserByUsername(String username) {
		return Optional.ofNullable(clientUserMapper.selectOne(Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getUsername, username)));
	}

	@Override
	public void resetPassword(String username, String newPassword) {
		ClientUserDo clientUserDo = queryOptionalUserByUsername(username)
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.INVALID_DATA));
		clientUserDo.setPassword(newPassword);
		clientUserDo.setUpdateTime(System.currentTimeMillis());
		ActionFlagUtil.assertSingleAction(clientUserMapper.updateById(clientUserDo));
	}
}
