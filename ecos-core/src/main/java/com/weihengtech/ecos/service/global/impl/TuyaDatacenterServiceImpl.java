package com.weihengtech.ecos.service.global.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.TuyaDatacenterMapper;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.model.dos.TuyaDatacenterDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TuyaDatacenterServiceImpl extends ServiceImpl<TuyaDatacenterMapper, TuyaDatacenterDo> implements TuyaDatacenterService {
    @Override
    public Boolean exist(Integer datacenterId) {
        return lambdaQuery().eq(TuyaDatacenterDo::getId, datacenterId).count() > 0;
    }

    @Override
    public Boolean isSameDatacenter(Integer firstId, Integer secondId) {
        if (null == firstId || null == secondId) {
            return false;
        }
        String first = getDatacenter(firstId);
        String second = getDatacenter(secondId);
        return first.equals(second) && StrUtil.isNotBlank(first) && StrUtil.isNotBlank(second);
    }

    @Override
    public String getDatacenter(Integer datacenterId) {
        TuyaDatacenterDo datacenterDo = this.getById(datacenterId);
        if (null == datacenterDo) {
            return "";
        }
        return datacenterDo.getDatacenter();
    }
}
