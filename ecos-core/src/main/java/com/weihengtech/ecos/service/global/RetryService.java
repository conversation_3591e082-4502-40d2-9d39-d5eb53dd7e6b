package com.weihengtech.ecos.service.global;


import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;

/**
 * <AUTHOR>
 */
public interface RetryService {

	Pair<String, String> getDeviceSn(Integer type, String wifiSn);

	Pair<String, String> getSocketDeviceSn(Integer type, String wifiSn);

	Boolean checkChargeStationStatus(String deviceId, Integer operateCode);

	Integer getChargeStationStatus(String deviceSn, ChargerSaveVO chargerSaveVO, Integer operateCode);

	Boolean checkSocketStatus(String wifiSn, String cloud, Boolean switchStatus, Integer switchIndex);

	void syncWhDeviceState(String deviceSn);

	void speedUpDevice(String wifiSn, String type);

	void writeCustomizeV2(CustomizeInfoEzV2Vo param);
}
