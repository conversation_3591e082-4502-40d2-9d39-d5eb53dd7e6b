package com.weihengtech.ecos.service.global.impl.tsdb;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(value = "tsdbService9")
@Slf4j
public class EmptyTSDBServiceImpl implements TimeSeriesDatabaseService {

	@Override
	public Dict lastPoint(String deviceSn, List<String> metricList, long endTime) {
		return Dict.create();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> lastConnectorStatusPoint(String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(
            String deviceSn, List<String> metricList, long startTime,
            long endTime, TsdbSampleEnum tsdbSampleEnum
	) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(
			String deviceSn, List<String> metricList,
			long start, long end, long times
	) {
		return MapUtil.empty();
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(
			String deviceSn, List<String> metricList,
			long start, long end
	) {
		return MapUtil.empty();
	}

    @Override
    public Map<String, LinkedHashMap<Long, Object>> meanQuery(String deviceSn, List<String> metricList, long start, long end, long times) {
        return MapUtil.empty();
    }
}
