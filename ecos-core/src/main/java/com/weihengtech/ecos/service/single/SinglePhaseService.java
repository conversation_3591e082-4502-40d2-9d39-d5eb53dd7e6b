package com.weihengtech.ecos.service.single;

import com.weihengtech.ecos.model.dtos.single.SinglePhaseBaseInfoDto;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.vos.app.InsightDeviceDataVo;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import com.weihengtech.ecos.model.vos.app.PowerLimitVO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SinglePhaseService {

    /**
     * 用户绑定设备
     *
     * @param v2ClientHomeBindDeviceVo 设备信息
     * @param clientUserDo               用户信息
     */
    void bindClientUserDevice(V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo, ClientUserDo clientUserDo);

    /**
     * 检测wifi棒是否绑定设备成功
     *
     * @param wifiSn wifi棒序列号
     * @return 0 失败 1 成功 2 进行中 3 未进行绑定  4 已被其他账户绑定
     */
    Integer checkDeviceBindStatus(String wifiSn);

    /**
     * 用户解绑设备
     *
     * @param deviceId 设备id
     */
    void unbindClientUserDevice(String deviceId);

    /**
     * 分页查询event-fault
     *
     * @param homeEventsFaultVo 事件相关参数
     * @return 分页后的数据
     */
    PageInfoDTO<HomeEventsFaultDto> pageEventFault(HomeEventsFaultVo homeEventsFaultVo);

    /**
     * 备电统计信息
     *
     * @param homeEventsBackupStatisticsVo 统计参数
     * @return 统计结果
     */
    HomeEventsBackupStatisticsDto backupStatistics(HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo);

    /**
     * 分页查询备电信息
     *
     * @param homeEventsBackupPageVo 备电参数
     * @return 分页结果
     */
    PageInfoDTO<HomeEventsBackupPageDto> pageBackup(HomeEventsBackupPageVo homeEventsBackupPageVo);


    /**
     * 计算电池循环次数
     *
     * @param deviceSn                  设备序列号
     * @param start                     开始时间
     * @param end                       结束时间
     * @param timeSeriesDatabaseService 时序服务
     * @return 次数
     */
    Integer computeBatteryCycleTimes(
            String deviceSn, long start, long end,
            TimeSeriesDatabaseService timeSeriesDatabaseService
    );

    /**
     * 首页实时能量曲线
     *
     * @param result 查询结果
     * @return 能量曲线数据
     */
    HomeNowDeviceRealtimeDto packageNowDeviceRealtimeResult(Map<String, LinkedHashMap<Long, Object>> result);


    /**
     * 首页设备实时数据
     *
     * @param deviceId 设备id
     * @return 实时数据
     */
    HomeNowDeviceRunDataDto queryNowDeviceRunData(ClientUserDo clientUserDo, String deviceId);

    /**
     * 获取偏移量天数的设备能量热图
     *
     * @param deviceId  设备id
     * @param offsetDay 偏移量天数
     * @return 热图统计
     */
    InsightDeviceEnergyHeatmapStatisticsDto getOffsetDayDeviceEnergyHeatmap(ClientUserDo clientUserDo, String deviceId, Integer offsetDay);

    /**
     * 获取更多信息页面的通知配置
     *
     * @param deviceId 设备id
     * @return 配置信息回参
     */
    InsightMoreInformationEnergyNotifyDto getEnergyNotifyConfig(ClientUserDo clientUserDo, String deviceId);

    /**
     * 更新更多信息页面通知配置
     *
     * @param insightMoreInformationEnergyNotifyVo 更多信息通知
     * @return 配置信息回参
     */
    InsightMoreInformationEnergyNotifyDto updateEnergyNotifyConfig(
            ClientUserDo clientUserDo,
            InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
    );

    /**
     * 计算Oem energy数
     *
     * @param oemEnergyVo 时间与id
     * @return 计算数据
     */
    OemEnergyDto computeOemEnergy(OemEnergyVo oemEnergyVo);

    /**
     * Oem 实时功率曲线
     *
     * @param homeNowDeviceRealtimeVo 设备
     */
    OemRealTimePowerDto oemRealTimePower(HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo);

    /**
     * Oem 历史能量
     *
     * @param oemHistoryEnergyVo 设备
     */
    OemHistoryEnergyDto oemHistoryEnergy(OemHistoryEnergyVo oemHistoryEnergyVo);

    /**
     * 获取最近一周光伏、电网、碳减排、节约标准煤数据
     *
     * @param deviceId 设备ID
     * @param offsetDay 偏移天数
     * @param timezone 时区
     * @return 最近一周光伏、电网能量数据
     */
    HomeDeviceEnergyStatisticsDto getLastWeekSolarAndGridEnergyData(ClientUserDo clientUserDo, String deviceId, Integer offsetDay, String timezone);

    /**
     * 获取获取Insight页面能耗数据
     *
     * @param insightDeviceDataVo 查询入参
     * @return Insight页面能耗数据
     */
    InsightDeviceDataDto queryDeviceInsightData(ClientUserDo clientUserDo, InsightDeviceDataVo insightDeviceDataVo);

    /**
     * 读取 Customize 信息
     *
     * @param deviceId 设备id
     * @return Customize页面信息
     */
    CustomizeInfoDto readCustomize(String deviceId);

    /**
     * 写入 Customize 信息 V2
     *
     * @param customizeInfoEzV2Vo 要配置的信息
     */
    void writeCustomizeV2(CustomizeInfoEzV2Vo customizeInfoEzV2Vo);

    /**
     * 获取储能SOC
     *
     * @param hybridSinglePhaseDO 设备
     */
    Map<String, BigDecimal> getSoc(String userId, HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 获取储能设备详情
     *
     * @param deviceId 设备ID
     */
    SinglePhaseBaseInfoDto queryDeviceDetail(String deviceId);

    /**
     * 读取设备入户侧功率限制
     *
     * @param deviceId 设备id
     * @return 透传结果
     */
    Integer getPowerLimit(Long deviceId);

    /**
     * 保存功率限制
     *
     * @param powerLimit 功率限制参数
     */
    void savePowerLimit(PowerLimitVO powerLimit);
}
