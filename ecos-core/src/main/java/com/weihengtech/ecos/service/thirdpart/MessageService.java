package com.weihengtech.ecos.service.thirdpart;

import com.weihengtech.ecos.enums.global.SmsEnum;
import com.weihengtech.ecos.enums.global.MailModelEnum;

/**
 * <AUTHOR>
 */
public interface MessageService {

	/**
	 * 发送邮件带参数
	 *
	 * @param toEmail       发给谁
	 * @param mailModelEnum 邮件模板
	 * @param params        参数
	 */
	void sendEmail(String toEmail, MailModelEnum mailModelEnum, String... params);

	/**
	 * 发送sms
	 */
    void sendPhone(String phone, String code, String param);

	/**
	 * 带缓存的发送sms
	 */
	void sendPhoneWithCache(SmsEnum smsEnum, String phone, String codeKey, String cacheKey);

	/**
	 * 带缓存的发送email
	 */
	void sendEmailWithCache(String toEmail, String codeKey, String cacheKey, MailModelEnum mailModelEnum, String... params);
}
