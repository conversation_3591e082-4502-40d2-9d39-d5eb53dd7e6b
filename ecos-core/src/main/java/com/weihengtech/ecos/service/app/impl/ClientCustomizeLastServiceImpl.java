package com.weihengtech.ecos.service.app.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.model.dos.ClientCustomizeLastDO;
import com.weihengtech.ecos.dao.ClientCustomizeLastMapper;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;
import com.weihengtech.ecos.service.app.ClientCustomizeLastService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Service
public class ClientCustomizeLastServiceImpl extends ServiceImpl<ClientCustomizeLastMapper, ClientCustomizeLastDO> implements ClientCustomizeLastService {

    @Override
    public void backupLastTimeList(String deviceId,
                                   List<ChargingStructDTO> chargingList,
                                   List<ChargingStructDTO> dischargingList) {
        ClientCustomizeLastDO item = ClientCustomizeLastDO.list2DO(chargingList, dischargingList);
        ClientCustomizeLastDO one = getOne(Wrappers.<ClientCustomizeLastDO>lambdaQuery()
                .eq(ClientCustomizeLastDO::getDeviceId, deviceId));
        if (one != null) {
            item.setId(one.getId());
            item.setDeviceId(one.getDeviceId());
            removeById(one.getId());
            save(item);
        } else {
            item.setDeviceId(Long.parseLong(deviceId));
            save(item);
        }
    }

    @Override
    public TimeListLastDTO queryLastTimeList(String deviceId) {
        ClientCustomizeLastDO item = getOne(Wrappers.<ClientCustomizeLastDO>lambdaQuery()
                .eq(ClientCustomizeLastDO::getDeviceId, deviceId));
        if (item == null) {
            return TimeListLastDTO.builder()
                    .chargingList(Collections.emptyList())
                    .dischargingList(Collections.emptyList())
                    .build();
        }
        return ClientCustomizeLastDO.do2List(item);
    }
}
