package com.weihengtech.ecos.service.global.impl;

import cn.hutool.core.util.ObjectUtil;
import com.weihengtech.ecos.service.global.CacheService;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class CacheServiceImpl implements CacheService {

	@Resource(type = RedissonClient.class)
	private RedissonClient redissonClient;

	@Override
	public void setStrWithSecond(String key, String value, long seconds) {
		RBucket<Object> bucket = redissonClient.getBucket(key);
		bucket.set(value, seconds, TimeUnit.SECONDS);
	}

	@Override
	public Optional<String> getStr(String key) {
		RBucket<Object> bucket = redissonClient.getBucket(key);
		Object o = bucket.get();
		if (ObjectUtil.isNull(o)) {
			return Optional.empty();
		}
		return Optional.of(String.valueOf(o));
	}

	@Override
	public Optional<Boolean> notExistKey(String key) {
		RBucket<Object> bucket = redissonClient.getBucket(key);
		Object o = bucket.get();
		if (ObjectUtil.isNotNull(o)) {
			return Optional.empty();
		}
		return Optional.of(true);
	}

	@Override
	public void delKey(String key) {
		redissonClient.getBucket(key).delete();
	}

}
