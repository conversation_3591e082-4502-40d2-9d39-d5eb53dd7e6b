package com.weihengtech.ecos.service.charger.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientChargeRecordReadMapper;
import com.weihengtech.ecos.model.dos.ClientChargeRecordReadDo;
import com.weihengtech.ecos.service.charger.ClientChargeRecordReadService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @program: ecos-server
 * @description: 充电记录通知服务实现类
 * @author: jiahao.jin
 * @create: 2024-02-29 11:56
 **/
@Service
@RequiredArgsConstructor
public class ClientChargeRecordReadServiceImpl
        extends ServiceImpl<ClientChargeRecordReadMapper, ClientChargeRecordReadDo>
        implements ClientChargeRecordReadService {
}
