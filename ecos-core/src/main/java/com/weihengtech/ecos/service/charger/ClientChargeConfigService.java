package com.weihengtech.ecos.service.charger;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientChargeConfigDo;

import java.util.Optional;

/**
 * @program: ecos-server
 * @description: 充电桩充电配置表服务类
 * @author: jiahao.jin
 * @create: 2024-02-19 16:53
 **/
public interface ClientChargeConfigService extends IService<ClientChargeConfigDo> {


    Optional<ClientChargeConfigDo> queryOptionalChargeConfigByDeviceId(long deviceId);

}
