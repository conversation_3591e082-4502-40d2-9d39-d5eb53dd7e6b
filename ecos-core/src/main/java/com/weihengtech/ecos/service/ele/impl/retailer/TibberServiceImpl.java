package com.weihengtech.ecos.service.ele.impl.retailer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.consts.RequestConstants;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.enums.ele.RetailerEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.FlatPeakLocationDetailsDTO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.service.ele.RetailerService;
import com.weihengtech.ecos.utils.ElectricityPriceTypeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/22 14:27
 */
@Service
@Slf4j
public class TibberServiceImpl implements RetailerService {

    @Resource
    private OkHttpClient okHttpClient;

    @Override
    public List<TibberHomeDTO> queryTibberHomes(String token) {
        // 1. 构建请求体
        RequestBody body = RequestBody.create(RequestConstants.TIBBER_HOME_PARAM,
                MediaType.parse("application/json; charset=utf-8"));
        // 2. 构建请求
        Request request = new Request.Builder()
                .url(RetailerEnum.TIBBER.getUrl())
                .post(body)
                .header("Authorization", "Bearer " + token)
                .build();
        // 3. 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the tibber home list failed");
                return Collections.emptyList();
            }
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(i -> log.error(i));
                return Collections.emptyList();
            }
            return Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("viewer"))
                    .map(i -> i.getJSONArray("homes"))
                    .map(i -> i.toList(TibberHomeDTO.class))
                    .orElse(Collections.emptyList());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public RetailerElePriceDTO queryRetailerElePrice(ClientElePriceRetailerDO config, Integer time, String timezone) {
        // 1. 构建请求体
        RequestBody body = RequestBody.create(String.format(RequestConstants.TIBBER_ELE_PARAM, config.getRetailerHomeId(),
                        1 == time ? "tomorrow" : "today"),
                MediaType.parse("application/json; charset=utf-8"));
        // 2. 构建请求
        Request request = new Request.Builder()
                .url(RetailerEnum.TIBBER.getUrl())
                .post(body)
                .header("Authorization", "Bearer " + config.getToken())
                .build();
        // 3. 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the tibber ele list failed");
                return RetailerElePriceDTO.builder().build();
            }
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(i -> log.error(i));
                return RetailerElePriceDTO.builder().build();
            }
            List<EleDayAheadPriceDto> priceList = Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("viewer"))
                    .map(i -> i.getJSONObject("home"))
                    .map(i -> i.getJSONObject("currentSubscription"))
                    .map(i -> i.getJSONObject("priceInfo"))
                    .map(i -> i.getJSONArray("today"))
                    .map(i -> i.toList(JSONObject.class))
                    .map(i -> i.stream()
                            .map(j -> EleDayAheadPriceDto.builder()
                                    .currency(j.getStr("currency"))
                                    .average(j.getBigDecimal("total"))
                                    .tax(j.getBigDecimal("tax"))
                                    .startTimeUnix(ZonedDateTime.parse(j.getStr("startsAt")).toInstant().getEpochSecond())
                                    .build())
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            ElePriceTypeDetailEnum elePriceTypeDetailEnum = ElePriceTypeDetailEnum.INTERVAL_1H;
            if (CollUtil.isNotEmpty(priceList)) {
                elePriceTypeDetailEnum = ElectricityPriceTypeUtil.detectPriceType(priceList);
            }
            return RetailerElePriceDTO.builder()
                    .elePriceDetailType(elePriceTypeDetailEnum.getCode())
                    .priceList(priceList)
                    .build();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return RetailerElePriceDTO.builder().build();
        }
    }

    @Override
    public String getConnectToken() {
        return "";
    }

    @Override
    public String getLocationId(String connectToken) {
        return "";
    }

    @Override
    public FlatPeakLocationDetailsDTO getLocationDetails(String locationId) {
        return null;
    }
}