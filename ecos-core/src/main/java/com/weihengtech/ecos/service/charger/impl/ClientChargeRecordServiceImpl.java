package com.weihengtech.ecos.service.charger.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientChargeRecordMapper;
import com.weihengtech.ecos.model.dos.ClientChargeRecordDo;
import com.weihengtech.ecos.service.charger.ClientChargeRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @program: ecos-server
 * @description: 充电记录服务实现类
 * @author: jiahao.jin
 * @create: 2024-02-20 17:18
 **/
@Service
@RequiredArgsConstructor
public class ClientChargeRecordServiceImpl
        extends ServiceImpl<ClientChargeRecordMapper, ClientChargeRecordDo>
        implements ClientChargeRecordService {
}
