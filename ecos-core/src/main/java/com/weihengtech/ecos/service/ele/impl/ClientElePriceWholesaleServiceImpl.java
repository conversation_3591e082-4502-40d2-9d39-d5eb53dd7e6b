package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.api.EcosEleApi;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.AheadPriceVo;
import com.weihengtech.ecos.api.pojo.vos.DayAheadPriceVo;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.dao.ClientElePriceWholesaleMapper;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.enums.thirdpart.PriceUnitEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceWholesaleDO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceWholesaleVO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.ClientElePriceWholesaleService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.utils.TimeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class ClientElePriceWholesaleServiceImpl extends ServiceImpl<ClientElePriceWholesaleMapper, ClientElePriceWholesaleDO> implements ClientElePriceWholesaleService, HomeElePriceService {

    @Resource
    private ClientHomeService clientHomeService;
    @Resource
    private EcosEleApi ecosEleApi;

    @Override
    public ClientElePriceWholesaleDO queryWholesalePrice(String homeId) {
        return getOne(Wrappers.<ClientElePriceWholesaleDO>lambdaQuery()
                .eq(ClientElePriceWholesaleDO::getHomeId, homeId));
    }

    @Override
    public void updateWholesalePrice(ElePriceWholesaleVO param) {
        ClientElePriceWholesaleDO exitsOne = queryWholesalePrice(param.getHomeId());
        if (exitsOne == null) {
            ClientElePriceWholesaleDO item = new ClientElePriceWholesaleDO();
            BeanUtils.copyProperties(param, item);
            save(ClientElePriceWholesaleDO.builder()
                    .homeId(Long.parseLong(param.getHomeId()))
                    .region(param.getRegion())
                    .purchaseTax(param.getPurchaseTax())
                    .build());
        } else {
            exitsOne.setRegion(param.getRegion());
            exitsOne.setPurchaseTax(param.getPurchaseTax());
            updateById(exitsOne);
        }
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(param.getHomeId());
        if (homeItem != null && !ElePriceTypeEnum.WHOLESALE.getCode().equals(homeItem.getElePriceType())) {
            homeItem.setElePriceType(ElePriceTypeEnum.WHOLESALE.getCode());
            clientHomeService.updateById(homeItem);
        }
        // 更新家庭币种
        if (homeItem != null && param.getCurrency() != null) {
            homeItem.setCurrency(param.getCurrency());
            clientHomeService.updateById(homeItem);
        }
        // 清除当日成本缓存数据
        clearCurDayCostData(Long.parseLong(param.getHomeId()));
    }

    @Override
    public List<EleDayAheadPriceDto> getEleAheadPrice(AheadPriceVo aheadPriceVo) {
        // 获取用户地区信息，判断用户是否属于德国或者荷兰 查询时间为今天、明天、后天
        Long startTime = TimeUtil.getDayStart(aheadPriceVo.getTime(), aheadPriceVo.getTimezone());
        Long endTime = TimeUtil.getDayEnd(aheadPriceVo.getTime(), aheadPriceVo.getTimezone());
        if (startTime.equals(0L) || endTime.equals(0L)) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        String start = StrUtil.toString(cn.hutool.core.util.NumberUtil.round((double) startTime / 1000, 0));
        String end = StrUtil.toString(cn.hutool.core.util.NumberUtil.round((double) endTime / 1000, 0));

        DayAheadPriceVo dayAheadPriceVo = new DayAheadPriceVo();
        dayAheadPriceVo.setDataSource("Entsoe");
        dayAheadPriceVo.setStartTime(start);
        dayAheadPriceVo.setEndTime(end);
        dayAheadPriceVo.setIntervalSeconds(aheadPriceVo.getIntervalSeconds());
        dayAheadPriceVo.setRegion(aheadPriceVo.getRegion());
        dayAheadPriceVo.setPriceUnit(Optional.ofNullable(aheadPriceVo.getPriceUnit()).orElse(PriceUnitEnum.MWh.name()));
        return getEleDayAheadPrice(dayAheadPriceVo);
    }

    public List<EleDayAheadPriceDto> getEleDayAheadPrice(DayAheadPriceVo dayAheadPriceVo) {
        InResponse<Object> eleDayAheadPrice = ecosEleApi.getEleDayAheadPrice(
                dayAheadPriceVo.getDataSource(),
                dayAheadPriceVo.getCountry(),
                dayAheadPriceVo.getRegion(),
                dayAheadPriceVo.getIntervalSeconds(),
                dayAheadPriceVo.getType(),
                dayAheadPriceVo.getStartTime(),
                dayAheadPriceVo.getEndTime(),
                dayAheadPriceVo.getPriceUnit()
        );
        Object data = eleDayAheadPrice.getData();
        return JSONUtil.toList(JSONUtil.parseArray(data), EleDayAheadPriceDto.class);
    }

    @Override
    public Object queryEleInfoOrConfig(String homeId) {
        return queryWholesalePrice(homeId);
    }

    @Override
    public Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String userTimezone) {
        ClientElePriceWholesaleDO priceInfo = queryWholesalePrice(homeId);
        AheadPriceVo param = AheadPriceVo.builder()
                .time(time)
                .region(Collections.singletonList(priceInfo.getRegion()))
                .intervalSeconds(900)
                .timezone(userTimezone)
                .priceUnit(PriceUnitEnum.kWh.name())
                .build();
        List<EleDayAheadPriceDto> priceList = getEleAheadPrice(param);
        if (CollUtil.isEmpty(priceList)) {
            return Collections.emptyMap();
        }
        return priceList.stream()
                .collect(Collectors.toMap(EleDayAheadPriceDto::getStartTimeUnix,
                        i -> ElePriceDetailDTO.builder()
                                .purchasePrice(i.getAverage())
                                .purchaseTax(priceInfo.getPurchaseTax())
                                .feedInPrice(i.getAverage())
                                .build()));
    }

    @Override
    public List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone) {
        ClientElePriceWholesaleDO config = queryWholesalePrice(homeId);
        return getEleAheadPrice(AheadPriceVo.builder()
                .region(Collections.singletonList(config.getRegion()))
                .intervalSeconds(3600)
                .time(time)
                .timezone(timezone)
                .priceUnit(PriceUnitEnum.kWh.name())
                .build());
    }

    @Override
    public Object queryAllTypeHomePriceInfo() {
        List<ClientHomeDo> homeList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
                .eq(ClientHomeDo::getElePriceType, ElePriceTypeEnum.WHOLESALE.getCode()));
        if (CollUtil.isEmpty(homeList)) {
            return Collections.emptyList();
        }
        List<Long> homeIdList = homeList.stream().map(ClientHomeDo::getId).collect(Collectors.toList());
        return list(Wrappers.<ClientElePriceWholesaleDO>lambdaQuery()
                .in(ClientElePriceWholesaleDO::getHomeId, homeIdList));
    }
}
