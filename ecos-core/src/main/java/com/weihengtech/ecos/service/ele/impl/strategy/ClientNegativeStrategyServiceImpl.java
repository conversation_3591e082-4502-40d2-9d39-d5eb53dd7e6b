package com.weihengtech.ecos.service.ele.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.enums.ele.ChargeTypeEnum;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/1 14:21
 */
@Service()
public class ClientNegativeStrategyServiceImpl implements ClientEleStrategyService {

    @Override
    public List<EleStrategyDTO> calStrategy(List<EleDayAheadPriceDto> elePriceList, EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(elePriceList)) {
            return Collections.emptyList();
        }

        // 计算充电功率：额定功率的一半
        Integer chargePower = param.getRatedPower() / 2;

        // 遍历电价数据，只处理负电价时段
        return elePriceList.stream()
                .filter(priceData -> {
                    // 计算实际电价：average + tax
                    BigDecimal actualPrice = priceData.getAverage();
                    if (priceData.getTax() != null) {
                        actualPrice = actualPrice.add(priceData.getTax());
                    }
                    // 只保留负电价时段
                    return actualPrice.compareTo(BigDecimal.ZERO) < 0;
                })
                .map(priceData -> {
                    // 负电价时段：设置充电，功率为额定功率一半，开启弃光
                    return EleStrategyDTO.builder()
                            .startTimeUnix(priceData.getStartTimeUnix())
                            .price(priceData.getAverage())
                            .power(chargePower)
                            .chargeType(ChargeTypeEnum.CHARGE.getType())
                            .abandonPv(1) // 开启弃光
                            .build();
                })
                .collect(Collectors.toList());
    }
}