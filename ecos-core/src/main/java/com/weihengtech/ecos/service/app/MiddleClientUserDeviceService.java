package com.weihengtech.ecos.service.app;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MiddleClientUserDeviceService extends IService<MiddleClientUserDeviceDo> {

	/**
	 * 将传入的id列表进行排序
	 *
	 * @param middleIdList id列表
	 */
	void sortOrder(List<Long> middleIdList);

	/**
	 * 用户是否绑定设备
	 *
	 * @param deviceId 设备id
	 * @return 绑定标识
	 */
	Boolean isUserBindDevice(Long deviceId, Long userId);

	/**
	 * 保存主账号绑定关系
	 *
	 * @param userId 用户id
	 * @param boundList 需要过滤的主账号关系列表
	 * @param deviceList 设备列表
	 * @return 保存结果
	 */
	boolean saveMasterBind(Long userId, List<MiddleClientUserDeviceDo> boundList, List<HybridSinglePhaseDO> deviceList);

	/**
	 * 保存主账号绑定关系
	 *
	 * @param userId 用户Id
	 * @param deviceId 设备Id
	 * @param deviceName 设备别名
	 * @return
	 */
	boolean saveMasterBind(Long userId, Long deviceId, String deviceName);
}
