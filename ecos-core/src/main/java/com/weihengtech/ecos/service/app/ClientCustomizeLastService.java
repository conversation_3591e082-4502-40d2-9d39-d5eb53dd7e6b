package com.weihengtech.ecos.service.app;

import com.weihengtech.ecos.model.dos.ClientCustomizeLastDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
public interface ClientCustomizeLastService extends IService<ClientCustomizeLastDO> {

    void backupLastTimeList(String deviceId,
                            List<ChargingStructDTO> chargingList,
                            List<ChargingStructDTO> dischargingList);

    TimeListLastDTO queryLastTimeList(String deviceId);
}
