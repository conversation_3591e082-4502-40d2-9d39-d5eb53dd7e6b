package com.weihengtech.ecos.service.thirdpart.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.WorkOrderApi;
import com.weihengtech.ecos.api.pojo.base.WorkOrderResp;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderPageDto;
import com.weihengtech.ecos.api.pojo.vos.*;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.thirdpart.order.AddWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.PageWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.QuizWorkOrderVo;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.service.thirdpart.IWorkOrderService;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements IWorkOrderService {

	@Value("${custom.order.project}")
	private String projectToken;
	@Resource
	private WorkOrderApi workOrderApi;
	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;
	@Resource
	private HubService hubService;
	@Resource
	private V2HomeAdapter v2HomeAdapter;

	@Override
	public void addWorkOrder(AddWorkOrderVo addWorkOrderVo) {
		String deviceSn = addWorkOrderVo.getDeviceSn();
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getByDeviceName(deviceSn);

		Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, String.valueOf(hybridSinglePhaseDO.getId()));
		if (!checkUserHasDevice) {
			Optional.ofNullable(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
							.eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId())
							.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())))
					.orElseThrow(() -> {
						log.warn("未绑定设备");
						return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
					});
		}
		WorkOrderAddVo workOrderAddVo = new WorkOrderAddVo();
		workOrderAddVo.setAttachment(hybridSinglePhaseDO.getDeviceName());
		workOrderAddVo.setLabel(String.valueOf(clientUserDo.getId()));
		workOrderAddVo.setName(clientUserDo.getUsername());
		workOrderAddVo.setNotifyEmail(addWorkOrderVo.getEmail());
		workOrderAddVo.setLevel(1);
		workOrderAddVo.setContent(addWorkOrderVo.getContent());
		workOrderAddVo.setPicList(addWorkOrderVo.getPicList());
		workOrderAddVo.setSubject(addWorkOrderVo.getSubject());

		WorkOrderResp<Object> resp = workOrderApi.addWorkOrder(projectToken, workOrderAddVo);
		if (!resp.getSuccess()) throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
	}

	@Override
	public PageInfoDTO<WorkOrderDetailDto> pageWorkOrder(PageWorkOrderVo pageWorkOrderVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		WorkOrderResp<WorkOrderPageDto> resp = workOrderApi.pageWorkOrder(projectToken,
				new WorkOrderPageVo().setPage(pageWorkOrderVo.getPageNum())
						.setSize(pageWorkOrderVo.getPageSize())
						.setLabels(ListUtil.toList(String.valueOf(clientUserDo.getId())))
		);
		PageInfoDTO<WorkOrderDetailDto> pageInfo = new PageInfoDTO<>();
		if (resp.getSuccess()) {
			WorkOrderPageDto workOrderPageDto = resp.getData();
			pageInfo.setTotalPages(workOrderPageDto.getPages());
			pageInfo.setTotalCount(workOrderPageDto.getTotal().longValue());
			pageInfo.setData(workOrderPageDto.getRecords());
		}
		return pageInfo;
	}

	@Override
	public void closeWorkIOrder(String workOrderId) {
		WorkOrderResp<Object> resp = workOrderApi.closeWorkOrder(projectToken, new WorkOrderCloseVo().setOrderId(workOrderId));
		if (!resp.getSuccess()) throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
	}

	@Override
	public void quizWorkOrder(QuizWorkOrderVo quizWorkOrderVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		WorkOrderResp<Object> resp = workOrderApi.quizWorkOrder(projectToken,
				new WorkOrderQuizVo()
						.setOrderId(quizWorkOrderVo.getWorkOrderId())
						.setName(clientUserDo.getUsername())
						.setContent(quizWorkOrderVo.getContent())
						.setPicList(quizWorkOrderVo.getPicList())
		);
		if (!resp.getSuccess()) throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
	}

	@Override
	public WorkOrderDetailDto detailWorkOrder(String workOrderId) {
		WorkOrderResp<List<WorkOrderDetailDto>> resp = workOrderApi.listWorkOrder(
				projectToken, new WorkOrderListVo().setOrderIdList(ListUtil.toList(workOrderId)));
		List<WorkOrderDetailDto> data = resp.getData();
		if (!resp.getSuccess() && CollUtil.isEmpty(data)) throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
		return data.get(0);
	}
}
