package com.weihengtech.ecos.service.ecos;

import com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo;
import com.weihengtech.ecos.model.bos.ecos.EcosEventBo;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EventService {

	/**
	 * 查询事件列表
	 *
	 * @param deviceFlag 设备标识
	 * @param level      等级
	 * @param start      开始时间
	 * @param end        结束时间
	 * @return 事件列表
	 */
	List<EcosEventBo> listEventByCondition(String deviceFlag, String level, Long start, Long end);

	/**
	 * 统计指定设备的备电次数
	 *
	 * @param deviceFlag 设备标识
	 * @return 备电次数
	 */
	Integer countBackupMode(String deviceFlag);

	/**
	 * 根据设备标识查询备电事件list
	 *
	 * @param deviceFlag 设备标识
	 * @param startTime  开始时间
	 * @param endTime    结束时间
	 * @return 备电事件list
	 */
	List<EcosEpsEventBo> listEpsEventByCondition(String deviceFlag, Long startTime, Long endTime);

	/**
	 * 查找最近的非Eps事件
	 *
	 * @param deviceName 设备标识
	 * @param id         事件id
	 * @return 非Eps事件
	 */
	EcosEpsEventBo findNearlyNotEpsEvent(String deviceName, Integer id);

	/**
	 * 本地事件分页
	 *
	 * @param deviceName
	 * @param homeEventsFaultVo
	 * @return
	 */
	PageInfoDTO<HomeEventsFaultDto> pageEventList(String deviceName, HomeEventsFaultVo homeEventsFaultVo);

	/**
	 * iot事件分页
	 *
	 * @param wifiSn
	 * @param homeEventsFaultVo
	 * @return
	 */
	PageInfoDTO<HomeEventsFaultDto> pageEventListIot(String wifiSn, HomeEventsFaultVo homeEventsFaultVo);
}
