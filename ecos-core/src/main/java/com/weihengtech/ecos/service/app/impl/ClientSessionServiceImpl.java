package com.weihengtech.ecos.service.app.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientSessionMapper;
import com.weihengtech.ecos.model.dos.ClientSessionDo;
import com.weihengtech.ecos.service.app.ClientSessionService;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: ecos-server
 * @description: 绘画表服务实现类
 * @author: jiahao.jin
 * @create: 2024-05-09 15:25
 **/
@Service
public class ClientSessionServiceImpl extends ServiceImpl<ClientSessionMapper, ClientSessionDo> implements ClientSessionService {

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Override
    public ClientSessionDo queryClientSessionByChatId(String chatId) {
        if (StrUtil.isBlank(chatId)) {
            return null;
        }
        // 通过chatId查询会话
        return getOne(Wrappers.<ClientSessionDo>lambdaQuery().eq(ClientSessionDo::getChatId, chatId));
    }

    @Override
    public List<ClientSessionDo> queryClientSessionByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        // 通过userId查询总会话，根据给updateTime排序
        return list(Wrappers.<ClientSessionDo>lambdaQuery()
                        .eq(ClientSessionDo::getUserId, userId)
                        .orderByDesc(ClientSessionDo::getUpdateTime));
    }

    @Override
    public ClientSessionDo handleSession(String title, Long userId, ClientSessionDo clientSessionDo) {
        // 获取会话的chatId
        String chatId = "";
        LocalDateTime now = LocalDateTime.now();
        if (clientSessionDo.getId() == null) {
            // 第一次问答创建会话
            long id = snowFlakeUtil.generateId();
            clientSessionDo.setId(id);
            clientSessionDo.setChatId(chatId);
            clientSessionDo.setTitle(title);
            clientSessionDo.setUserId(userId);
            clientSessionDo.setDeleted(false);
            clientSessionDo.setUpdateTime(now);
            clientSessionDo.setCreateTime(now);
            this.save(clientSessionDo);
        } else if (clientSessionDo.getChatId() == null) {
            // 第一次问答AI更新会话的标题和chatId
            clientSessionDo.setTitle(title);
            clientSessionDo.setChatId(chatId);
            clientSessionDo.setUpdateTime(now);
            this.updateById(clientSessionDo);
        }
        return clientSessionDo;
    }
}
