package com.weihengtech.ecos.service.thirdpart;

import com.weihengtech.ecos.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.thirdpart.order.AddWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.PageWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.QuizWorkOrderVo;

/**
 * <AUTHOR>
 */
public interface IWorkOrderService {

	/**
	 * 新增工单
	 *
	 * @param addWorkOrderVo 新增工单入参
	 */
	void addWorkOrder(AddWorkOrderVo addWorkOrderVo);

	/**
	 * 分页查询用户工单
	 *
	 * @param pageWorkOrderVo 页码
	 * @return 分页数据
	 */
	PageInfoDTO<WorkOrderDetailDto> pageWorkOrder(PageWorkOrderVo pageWorkOrderVo);

	/**
	 * 关闭工单
	 *
	 * @param workOrderId 工单id
	 */
	void closeWorkIOrder(String workOrderId);

	/**
	 * 提问工单
	 *
	 * @param quizWorkOrderVo 提问工单入参
	 */
	void quizWorkOrder(QuizWorkOrderVo quizWorkOrderVo);

	/**
	 * 查询指定工单详情
	 */
	WorkOrderDetailDto detailWorkOrder(String workOrderId);
}
