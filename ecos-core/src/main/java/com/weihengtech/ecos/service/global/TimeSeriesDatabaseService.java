package com.weihengtech.ecos.service.global;

import cn.hutool.core.lang.Dict;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import lombok.val;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TimeSeriesDatabaseService {

	/**
	 * 获取最后一点的数据
	 *
	 * @param deviceSn   设备标识
	 * @param metricList 属性列表
	 * @param endTime    结束时间
	 * @return 属性映射
	 */
	Dict lastPoint(String deviceSn, List<String> metricList, long endTime);

	/**
	 * 查询最后一个指定状态的时间（充电桩专用）
	 *
	 * @param deviceSn
	 * @param connectorStatus
	 * @param metricList
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	Map<String, LinkedHashMap<Long, Object>> lastConnectorStatusPoint(String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime);

	/**
	 * 差值查询
	 *
	 * @param deviceSn       设备标识
	 * @param metricList     属性列表
	 * @param startTime      开始时间
	 * @param endTime        结束时间
	 * @param tsdbSampleEnum 降采样
	 * @return 差值映射结果
	 */
	Map<String, LinkedHashMap<Long, Object>> deltaQuery(
            String deviceSn, List<String> metricList, long startTime,
            long endTime, TsdbSampleEnum tsdbSampleEnum

	);

	/**
	 * 降采样
	 *
	 * @param deviceSn   设备标识
	 * @param metricList 属性列表
	 * @param start      开始时间
	 * @param end        结束时间
	 * @param times      分钟间隔
	 * @return 点位数据
	 */
	Map<String, LinkedHashMap<Long, Object>> withSampleQuery(
			String deviceSn, List<String> metricList, long start,
			long end, long times
	);

	/**
	 * 不降采样
	 *
	 * @param deviceSn   设备标识
	 * @param metricList 属性列表
	 * @param start      开始时间
	 * @param end        结束时间
	 * @return 点位数据
	 */
	Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(
			String deviceSn, List<String> metricList, long start,
			long end
	);

	/**
	 * 降采样（求窗口平均值）
	 *
	 * @param deviceSn   设备标识
	 * @param metricList 属性列表
	 * @param start      开始时间
	 * @param end        结束时间
	 * @return 点位数据
	 */
	Map<String, LinkedHashMap<Long, Object>> meanQuery(String deviceSn, List<String> metricList, long start, long end, long times);


	/**
	 * 图表数据查询，一小时内原始数据，一小时以上降采
	 *
	 * @param deviceSn
	 * @param metricList
	 * @param start
	 * @param end
	 * @param isOem
	 * @return
	 */
	default Map<String, LinkedHashMap<Long, Object>> graphQuery(
			String deviceSn, List<String> metricList, long start,
			long end, boolean isOem
	) {
		val timeRange = end - start;
		long oneHour = 3600 * 1000;
		long oneDay = oneHour * 24L;
		long oneMonth = oneDay * 31;

		Map<String, LinkedHashMap<Long, Object>> result;
		if (timeRange < oneHour) {
			if (isOem) {
				result = withSampleQuery(deviceSn, metricList, start, end, TsdbSampleEnum.ONE_MINUTE_NEAR_POINT.getSample());
			} else {
				result = withOutSampleQuery(deviceSn, metricList, start, end);
			}
		} else if (timeRange < oneDay) {
			result = withSampleQuery(deviceSn, metricList, start, end, TsdbSampleEnum.FIVE_MINUTE_NEAR_POINT.getSample());
		} else if (timeRange < oneMonth) {
			long times = (timeRange / oneDay + 1) * 5;
			result = withSampleQuery(deviceSn, metricList, start, end, times);
		} else {
			throw new EcosException(EcosExceptionEnum.TSDB_OVER_TIME_SPAN);
		}
		return result;
	}

	/** 初始化结果集 */
	default Map<String, LinkedHashMap<Long, Object>> initializeResultMap(List<String> metricList) {
		Map<String, LinkedHashMap<Long, Object>> result = new HashMap<>();
		for (String metric : metricList) {
			result.put(metric, new LinkedHashMap<>());
		}
		return result;
	}
}
