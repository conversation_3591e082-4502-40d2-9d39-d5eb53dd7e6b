package com.weihengtech.ecos.service.charger.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.DeviceBindStatus;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.model.dtos.charger.ChargeStationHistoryCapacityDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeRecordDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeTimeDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationInfoDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationRunDataDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationTimingChargeDto;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.enums.charger.ChargerStatusEnum;
import com.weihengtech.ecos.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.ecos.model.vos.charger.ChargePlugSwitchVO;
import com.weihengtech.ecos.model.vos.charger.ChargeStationHistoryCapacityVo;
import com.weihengtech.ecos.model.vos.charger.ClientCreateChargeTaskVo;
import com.weihengtech.ecos.model.vos.charger.ClientUpdateChargeTaskVo;
import com.weihengtech.ecos.prometheus.BindFailMetrics;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientChargeRecordReadDo;
import com.weihengtech.ecos.model.dos.ClientChargeTaskDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.charger.V2ClientChargeRecordDto;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordSaveVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordUpdateVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.thirdpart.XxlJobInfo;
import com.weihengtech.ecos.service.charger.ClientChargeRecordReadService;
import com.weihengtech.ecos.service.charger.ClientChargeTaskService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.BeanUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TSDBAggUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.utils.XxlJobUtil;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.Connector;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.request.ConfigurationMap;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @program: ecos-server
 * @description: 充电桩服务实现类
 * @author: jiahao.jin
 * @create: 2024-02-18 11:24
 **/
@Slf4j
@Service
public class ChargeStationServiceImpl implements ChargeStationService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private HubService hubService;
    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;
    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private V2HomeAdapter v2HomeAdapter;
    @Resource
    private OssGlobalConfigApi ossGlobalConfigApi;
    @Resource
    private ClientUserService clientUserService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ClientChargeTaskService clientChargeTaskService;
    @Resource
    private ClientChargeRecordReadService  clientChargeRecordReadService;
    @Resource
    private XxlJobUtil xxlJobUtil;
    @Resource
    private RetryService retryService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private BindFailMetrics bindFailMetrics;

    @Override
    public void bindClientUserDevice(V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, ClientUserDo clientUserDo, String ip) {

        Double lon = homeClientUserBindDeviceVo.getLon();
        Double lat = homeClientUserBindDeviceVo.getLat();
        if (lon != null && lat != null) {
            if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                log.warn("lon或lat值不正确");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }
        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        if (StrUtil.isBlank(wifiSn)) {
            log.warn("wifiSn为空");
            throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }
        String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String bindStatus = stringRedisTemplate.opsForValue().get(redisKey);
            if (DeviceBindStatus.OK.equals(bindStatus)) {
                return;
            }
        }
        log.info("开始绑定 {}", JSONUtil.toJsonStr(homeClientUserBindDeviceVo));
        stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.ING, 3, TimeUnit.MINUTES);

        List<HybridSinglePhaseDO> nowBindDeviceList = hubService.nowBindDeviceList(wifiSn);
        if (CollUtil.isNotEmpty(nowBindDeviceList)) {
            for (HybridSinglePhaseDO hybridSinglePhaseDO : nowBindDeviceList) {
                MiddleClientUserDeviceDo nowBindMiddle = middleClientUserDeviceService
                        .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                                .eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId()));
                if (nowBindMiddle == null) {
                    continue;
                }
                Boolean hasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, String.valueOf(hybridSinglePhaseDO.getId()));
                if (!hasDevice) {
                    // 将设备与家庭建立关系
                    v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(hybridSinglePhaseDO.getId()), StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? hybridSinglePhaseDO.getDeviceSn() : homeClientUserBindDeviceVo.getDeviceAliasName());
                }
                ChargerSaveVO chargerSaveVO = new ChargerSaveVO();
                String beforeWifiSn = hybridSinglePhaseDO.getWifiSn();
                stringRedisTemplate.opsForValue().set("NEW-BIND:" + beforeWifiSn, "1", 10, TimeUnit.MINUTES);
                Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
                if (firstInstall == null || firstInstall < 100) {
                    hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
                }
                chargerSaveVO.setDeviceId(hybridSinglePhaseDO.getId());
                chargerSaveVO.setBindMode(homeClientUserBindDeviceVo.getNetworkType());
                chargerSaveVO.setChargerSn(hybridSinglePhaseDO.getDeviceSn());
                chargerSaveVO.setGateSn(hybridSinglePhaseDO.getWifiSn());
                chargerSaveVO.setLon(hybridSinglePhaseDO.getLongitude());
                chargerSaveVO.setLat(hybridSinglePhaseDO.getLatitude());
                chargerSaveVO.setIp(hybridSinglePhaseDO.getIp());
                chargerSaveVO.setDataCenterId(clientUserDo.getDatacenterId());
                chargerSaveVO.setCpFirmwareVersion(homeClientUserBindDeviceVo.getCpFirmwareVersion());
                chargerSaveVO.setCpPlugAndChargeMsg(homeClientUserBindDeviceVo.getCpPlugAndChargeMsg());
                hubService.updCharger(chargerSaveVO);
                stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                return;
            }
        }

        try {
            log.info("{}当前安装绑定ip为：{}", wifiSn, ip);
            if (Objects.equals(ip, "0:0:0:0:0:0:0:1")) {
                ip = "";
            }
            bindDeviceAction(wifiSn, clientUserDo, homeClientUserBindDeviceVo, redisKey, ip);
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
    }

    @Override
    public Integer getChargeStationStatus(String deviceId) {
        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO deviceItem = hubService.getById(Long.parseLong(deviceId));
        String deviceSn = deviceItem.getDeviceSn();

        Integer state = DeviceStatusEnum.UNKNOWN.getDbCode();
        // 没有首次安装时间，就是未知
        if (deviceItem.getFirstInstall() != 0) {
            String wifiSn = deviceItem.getWifiSn();
            if (StrUtil.isEmpty(wifiSn)) {
                state =  DeviceStatusEnum.OFFLINE.getDbCode();
            } else {
                CpStatusResponse chargeStationStatus = ecosIotApi.getChargeStationStatus(deviceSn, String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()));
                List<Connector> connectors = chargeStationStatus.getConnectors();
                if (connectors.size() == 0) {
                    state = DeviceStatusEnum.OFFLINE.getDbCode();
                } else {
                    state = ChargerStatusEnum.getCodeByStatus(connectors.get(0).getStatus());
                }
            }
        }
        return state;
    }

    @Override
    public ENPlusChargeStationInfoDto queryChargeStationInfo(String deviceId) {
        Future<ENPlusChargeStationInfoDto.BindAccount> future = threadPoolTaskExecutor.submit(() -> getBindInstall(deviceId));
        ENPlusChargeStationInfoDto resItem = new ENPlusChargeStationInfoDto();
        ClientUserDo userInfo = SecurityUtil.getClientUserDo();
        HybridSinglePhaseDO deviceItem = v2HomeAdapter.checkHomeAndDevice(String.valueOf(userInfo.getId()), deviceId);
        resItem.setName(StrUtil.isBlank(deviceItem.getAlias()) ? deviceItem.getDeviceModel() : deviceItem.getAlias());

        // 查询设备的子账号
        DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
        OssGlobalConfigBo configBo = globalConfig.getData();
        List<MiddleClientUserDeviceDo> middleList = middleClientUserDeviceService
                .list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                        .orderByDesc(MiddleClientUserDeviceDo::getCreateTime));
        List<ENPlusChargeStationInfoDto.BindAccount> accountList = middleList.stream()
                .filter(i -> isFilterAccount(i, configBo, userInfo, resItem))
                .map(i -> {
                    ClientUserDo bindUser = clientUserService.getById(i.getUserId());
                    return ENPlusChargeStationInfoDto.BindAccount.builder()
                            .seriesId(1)
                            .account(bindUser.getUsername())
                            .bindTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(i.getCreateTime(), userInfo.getTimeZone()))
                            .master(i.getMaster())
                            .accountId(String.valueOf(i.getUserId()))
                            .build();}
                ).collect(Collectors.toList());
        try {
            ENPlusChargeStationInfoDto.BindAccount bindInstall = future.get();
            if (StrUtil.isNotBlank(bindInstall.getAccountId())) {
                accountList.add(bindInstall);
            }
        } catch (Exception e) {
            log.error(String.format("getBindInstall failed: %s", deviceId), e);
        }
        resItem.setAccountList(accountList);
        // 设备相关信息
        String deviceModel = deviceItem.getDeviceModel();
        Integer model = ChargerTypeModelEnum.getTypeCodeByModel(deviceModel);
        resItem.setMinRatedPower(ChargerTypeModelEnum.getMinRatedPowerByCode(model));
        resItem.setRatedPower(ChargerTypeModelEnum.getRatedPowerByCode(model));
        resItem.setRatedCurrent(ChargerTypeModelEnum.getRatedCurrentByCode(model));
        resItem.setVoltageUp(ChargerTypeModelEnum.getVoltageUpByCode(model));
        resItem.setVoltageDown(ChargerTypeModelEnum.getVoltageDownByCode(model));
        ExtInfoDto extInfoDto = cn.hutool.core.bean.BeanUtil.toBean(deviceItem.getExtInfo(), ExtInfoDto.class);
        if (extInfoDto == null) {
            return resItem;
        }
        resItem.setMode(extInfoDto.getMode());
        resItem.setCpPlugAndChargeMsg(extInfoDto.getCpPlugAndChargeMsg());
        resItem.setCpFirmwareVersion(extInfoDto.getCpFirmwareVersion());
        if (extInfoDto.getMode() != 0) {
            Integer chargeStationStatus = getChargeStationStatus(deviceId);
            resItem.setSysRunMode(chargeStationStatus);
        }
        return resItem;
    }

    /** 过滤子账号逻辑 */
    private Boolean isFilterAccount(MiddleClientUserDeviceDo middleUser, OssGlobalConfigBo configBo,
                                    ClientUserDo userInfo, ENPlusChargeStationInfoDto resItem) {
        ClientUserDo curUser = clientUserService.getById(middleUser.getUserId());
        if (null != configBo && configBo.getAftersales().contains(curUser.getUsername())) {
            return false;
        }
        if (!middleUser.getUserId().equals(userInfo.getId())) {
            return true;
        } else {
            resItem.setMaster(middleUser.getMaster());
            return false;
        }
    }

    /** 获取绑定安装商详情 */
    private ENPlusChargeStationInfoDto.BindAccount getBindInstall(String deviceId) {
        InstallBoundInfoDTO bindInstallInfo = hubService.getBindInstallInfo(deviceId);
        return ENPlusChargeStationInfoDto.BindAccount.builder()
                .seriesId(1)
                .master(0)
                .accountId(bindInstallInfo.getInstallId())
                .account(bindInstallInfo.getInstallName())
                .bindTime(bindInstallInfo.getBindTime())
                .countdownTime(bindInstallInfo.getCountdownTime())
                .isInstaller(true)
                .build();
    }

    @Override
    public Double queryMaxChargePower(String userId, String deviceId) {
        HybridSinglePhaseDO deviceItem = hubService.getById(Long.parseLong(deviceId));
        ExtInfoDto extInfoDto = cn.hutool.core.bean.BeanUtil.toBean(deviceItem.getExtInfo(), ExtInfoDto.class);
        return extInfoDto.getMaxPower();
    }

    @Override
    public void updateMaxChargePower(String userId, String deviceId, Double maxPower, Boolean issued) {
        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(userId, deviceId);

        // 最大电流不能超过额定电流
        Integer typeCodeByModel = ChargerTypeModelEnum.getTypeCodeByModel(hybridSinglePhaseDO.getDeviceModel());
        Double ratedPower = Double.valueOf(ChargerTypeModelEnum.getRatedPowerByCode(typeCodeByModel).split("KW")[0]);
        if (maxPower > ratedPower || maxPower < 1) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }

        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(deviceId);
        if (!(chargeStationStatus.equals(ChargerStatusEnum.Available.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.Preparing.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.SuspendedEVSE.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.SuspendedEV.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.Finishing.getDbCode()))) {
            throw new EcosException(EcosExceptionEnum.UNCHANGEABLE_CHARGE_CONFIG);
        }
        if (issued) {
            // 最大功率下发到机器
            Integer typeId = ChargerTypeModelEnum.getTypeIdByCode(typeCodeByModel);
            Boolean res = ecosIotApi.setMaxChargeCurrent(hybridSinglePhaseDO.getDeviceSn(),
                    String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), Double.valueOf(maxPower * 1000).intValue(),
                    typeId == 11 ? 3 : 1);
            ActionFlagUtil.assertTrue(res);
        }
        // 更新
        hubService.updChargerExtInfo(ExtInfoDto.builder().deviceId(Long.valueOf(deviceId)).maxPower(maxPower).build());
    }

    @Override
    public void startCharging(String userId, String deviceId, String power) {
        // 设备是否存在于该家庭中
        HybridSinglePhaseDO deviceItem = v2HomeAdapter.checkHomeAndDevice(userId, deviceId);
        // 检查设备状态是否符合条件
        preCheckStartStatus(deviceItem);
        // 计算充电功率：1、查询最大充电功率；2、如果预约充电功率不为null且小于当前最大功率，则更新充电功率
        Double chargePower = queryMaxChargePower(userId, deviceId);
        if (power != null) {
            double needPower = NumberUtil.toBigDecimal(power).doubleValue();
            if (needPower != chargePower) {
                updateMaxChargePower(userId, deviceId, NumberUtil.toBigDecimal(power).doubleValue(), true);
                chargePower = needPower;
            }
        }
        // 加锁，执行下发充电指令
        RLock lock = redissonClient.getLock(deviceItem.getDeviceSn());
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                doStartCharging(chargePower, deviceItem);
            } else {
                throw new EcosException(EcosExceptionEnum.START_CHARGE_FAIL);
            }
        } catch (Exception e) {
            throw new EcosException(EcosExceptionEnum.START_CHARGE_FAIL);
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /** 预检查充电桩状态 */
    private void preCheckStartStatus(HybridSinglePhaseDO deviceItem) {
        ExtInfoDto extInfoDto = cn.hutool.core.bean.BeanUtil.toBean(deviceItem.getExtInfo(), ExtInfoDto.class);
        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(String.valueOf(deviceItem.getId()));
        if (chargeStationStatus < 0 || Boolean.TRUE.toString().equals(extInfoDto.getCpPlugAndChargeMsg())) {
            throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
        }
        if (chargeStationStatus.equals(ChargerStatusEnum.Charging.getDbCode())) {
            throw new EcosException(EcosExceptionEnum.IS_CHARGING);
        }
        if (!(chargeStationStatus.equals(ChargerStatusEnum.Preparing.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.SuspendedEVSE.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.SuspendedEV.getDbCode()) ||
                chargeStationStatus.equals(ChargerStatusEnum.Finishing.getDbCode()))) {
            throw new EcosException(EcosExceptionEnum.NOT_PLUG_IN);
        }
    }

    /** 执行开始充电的逻辑 */
    private void doStartCharging(Double chargePower, HybridSinglePhaseDO deviceItem) {
        log.info("{}开始执行充电指令下发，充电功率为：{}", deviceItem.getDeviceSn(), chargePower);
        // 下发开始充电指令
        Boolean res = ecosIotApi.startCharging(deviceItem.getDeviceSn(),
                String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), genTransactionId());
        Assert.isTrue(res, String.format("下发开始充电指令失败:%s", deviceItem.getDeviceSn()));
        // 检查是否开始充电了
        try {
            retryService.checkChargeStationStatus(String.valueOf(deviceItem.getId()), 1);
        } catch (Exception e) {
            throw new EcosException(EcosExceptionEnum.START_CHARGE_FAIL);
        }
    }

    /** 生成事务Id */
    private int genTransactionId() {
        long currentTimeMillis = System.currentTimeMillis();
        return Math.toIntExact(currentTimeMillis / 1000);
    }

    @Override
    public void stopCharging(String userId, String deviceId) {
        // 设备是否存在于该家庭中
        HybridSinglePhaseDO deviceItem = v2HomeAdapter.checkHomeAndDevice(userId, deviceId);
        // 检查设备状态是否符合条件
        preCheckStopStatus(deviceItem);
        RLock lock = redissonClient.getLock(deviceItem.getWifiSn());
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                Boolean res = ecosIotApi.stopCharging(deviceItem.getDeviceSn(), String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), -1);
                Assert.isTrue(res, String.format("下发停止充电指令失败:%s", deviceItem.getDeviceSn()));
                // 检查是否已经停止充电了
                try {
                    retryService.checkChargeStationStatus(deviceId, 0);
                } catch (Exception e) {
                    // 所有的重试尝试都失败了，进行相应的处理
                    throw new EcosException(EcosExceptionEnum.STOP_CHARGE_FAIL);
                }
            }
        } catch (Exception e) {
            throw new EcosException(EcosExceptionEnum.STOP_CHARGE_FAIL);
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /** 预检查充电桩状态 */
    private void preCheckStopStatus(HybridSinglePhaseDO deviceItem) {
        ExtInfoDto extInfoDto = cn.hutool.core.bean.BeanUtil.toBean(deviceItem.getExtInfo(), ExtInfoDto.class);
        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(String.valueOf(deviceItem.getId()));
        if (chargeStationStatus < 0 || Boolean.TRUE.toString().equals(extInfoDto.getCpPlugAndChargeMsg())) {
            throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
        }
        if (!(chargeStationStatus.equals(ChargerStatusEnum.Charging.getDbCode()))) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
    }

    @Override
    public ENPlusChargeStationRunDataDto runData(ClientUserDo clientUserDo, String deviceId) {
        ENPlusChargeStationRunDataDto resItem = new ENPlusChargeStationRunDataDto();
        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO deviceItem = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);
        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(deviceId);
        resItem.setSysRunMode(chargeStationStatus);
        if (!chargeStationStatus.equals(ChargerStatusEnum.Charging.getDbCode())) {
            return resItem;
        }
        // 上一次的充电记录
        V2ClientChargeRecordDto lastRecord = hubService.queryLastRecord(Long.valueOf(deviceId), false);
        long startTime = lastRecord.getStartTime();
        long endTime = System.currentTimeMillis();
        // 查询最新实时数据
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(deviceItem);
        Dict lastPointDict = timeSeriesDatabaseService.lastPoint(
                deviceItem.getDeviceSn(),
                CommonConstants.CHARGE_NOW_RUN_DATA,
                endTime
        );
        if (lastPointDict.values().stream().anyMatch(Objects::isNull)) {
            log.warn("充电桩数据存在Null: {}", JSONUtil.toJsonStr(lastPointDict));
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }
        reflectSetRunData("setPower", lastPointDict.getStr(TsdbMetricsConstants.POWER_ACTIVE_IMPORT), resItem);
        reflectSetRunData("setVoltage1", lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L1), resItem);
        reflectSetRunData("setVoltage2", lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L2), resItem);
        reflectSetRunData("setVoltage3", lastPointDict.getStr(TsdbMetricsConstants.VOLTAGE_L3), resItem);
        if (StrUtil.isNotBlank(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L1))) {
            resItem.setCurrent1(NumberUtil.round(new BigDecimal(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L1)), 1, RoundingMode.HALF_UP));
        }
        if (StrUtil.isNotBlank(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L2))) {
            resItem.setCurrent2(NumberUtil.round(new BigDecimal(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L2)), 1, RoundingMode.HALF_UP));
        }
        if (StrUtil.isNotBlank(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L3))) {
            resItem.setCurrent3(NumberUtil.round(new BigDecimal(lastPointDict.getStr(TsdbMetricsConstants.CURRENT_IMPORT_L3)), 1, RoundingMode.HALF_UP));
        }
        resItem.setStartTime(String.valueOf(startTime));
        // 查询实时充电量（开始时间-结束时间之内的充电量充电差值）
        Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(
                deviceItem.getDeviceSn(),
                ListUtil.toLinkedList(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER),
                startTime,
                endTime,
                TsdbSampleEnum.ONE_MINUTE_NEAR_POINT);

        LinkedHashMap<Long, Object> energyMap = metricDataMap.get(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER);
        List<Long> keyList = null == energyMap
                ? ListUtil.empty()
                : energyMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());
        BigDecimal totalEnergy = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(energyMap)) {
            for (Long time : keyList) {
                BigDecimal energy = new BigDecimal(energyMap.getOrDefault(time, "0").toString());
                totalEnergy = NumberUtil.round(NumberUtil.add(totalEnergy, energy), 2, RoundingMode.HALF_UP);
            }
        }
        // 将totalEnergy除以1000并四舍五入到一位小数
        totalEnergy = NumberUtil.round(NumberUtil.div(totalEnergy, 1000), 1, RoundingMode.HALF_UP);
        resItem.setChargeCapacity(totalEnergy);
        return resItem;
    }

    @Override
    public ENPlusChargeStationChargeRecordDto lastChargeRecord(String deviceId) {
        ENPlusChargeStationChargeRecordDto resItem = new ENPlusChargeStationChargeRecordDto();
        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(deviceId);
        // 充电中或者离线时，展示不为空的充电记录
        if (chargeStationStatus < 0 || chargeStationStatus.equals(ChargerStatusEnum.Charging.getDbCode())) {
            V2ClientChargeRecordDto lastRecord = hubService.queryLastRecord(Long.valueOf(deviceId), true);
            if (lastRecord == null) {
                return resItem;
            }
            resItem.setId(lastRecord.getId());
            resItem.setChargeCapacity(lastRecord.getBatCap());
            resItem.setStartTime(lastRecord.getStartTime());
            resItem.setEndTime(lastRecord.getEndTime());
            return resItem;
        }
        // 查询最新充电记录
        V2ClientChargeRecordDto lastRecord = hubService.queryLastRecord(Long.valueOf(deviceId), false);
        // 为空就返回空的数据
        if (lastRecord == null) {
            return resItem;
        }
        resItem.setId(lastRecord.getId());
        resItem.setChargeCapacity(lastRecord.getBatCap());
        resItem.setStartTime(lastRecord.getStartTime());
        resItem.setEndTime(lastRecord.getEndTime());
        return resItem;
    }

    @Override
    public PageInfoDTO<ENPlusChargeStationChargeRecordDto> pageHistoryChargeRecord(V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), v2ClientChargeRecordPageVo.getDeviceId());
        PageInfoDTO<V2ClientChargeRecordDto> v2ClientChargeRecordDtoPageInfoDTO = hubService.pageChargeRecord(v2ClientChargeRecordPageVo);

        PageInfoDTO<ENPlusChargeStationChargeRecordDto> pageInfoDTO = new PageInfoDTO<>();
        List<V2ClientChargeRecordDto> data = v2ClientChargeRecordDtoPageInfoDTO.getData();
        List<ENPlusChargeStationChargeRecordDto> newData = new ArrayList<>();
        for (V2ClientChargeRecordDto v2ClientChargeRecordDto : data) {
            ENPlusChargeStationChargeRecordDto enPlusChargeStationChargeRecordDto = new ENPlusChargeStationChargeRecordDto();
            enPlusChargeStationChargeRecordDto.setId(v2ClientChargeRecordDto.getId());
            enPlusChargeStationChargeRecordDto.setChargeCapacity(v2ClientChargeRecordDto.getBatCap());
            enPlusChargeStationChargeRecordDto.setStartTime(v2ClientChargeRecordDto.getStartTime());
            enPlusChargeStationChargeRecordDto.setEndTime(v2ClientChargeRecordDto.getEndTime());
            newData.add(enPlusChargeStationChargeRecordDto);
        }
        newData.sort((record1, record2) -> record2.getStartTime().compareTo(record1.getStartTime()));

        pageInfoDTO.setData(newData);
        pageInfoDTO.setTotalPages(v2ClientChargeRecordDtoPageInfoDTO.getTotalPages());
        pageInfoDTO.setTotalCount(v2ClientChargeRecordDtoPageInfoDTO.getTotalCount());
        return pageInfoDTO;
    }

    @Override
    public List<ENPlusChargeStationTimingChargeDto> queryNotReadFailChargeRecord(String deviceId, Integer read) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        List<ENPlusChargeStationTimingChargeDto> notReadRecordDtos = new ArrayList<>();

        List<Long> tsakIdList = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, deviceId)).stream().map(ClientChargeTaskDo::getId).collect(Collectors.toList());

        if (tsakIdList.size() == 0) {
            return notReadRecordDtos;
        }

        List<ClientChargeRecordReadDo> recordReadDos = clientChargeRecordReadService.list(Wrappers.<ClientChargeRecordReadDo>lambdaQuery()
                .in(ClientChargeRecordReadDo::getChargeTaskId, tsakIdList)
                .eq(ClientChargeRecordReadDo::getUserId, clientUserDo.getId())
                .eq(ClientChargeRecordReadDo::getStatus, -1)
                .orderByDesc(ClientChargeRecordReadDo::getStartChargeTime));

        List<ClientChargeRecordReadDo> notRecordReadDos = recordReadDos.stream().filter(recordReadDo -> recordReadDo.getReaded() == 0).collect(Collectors.toList());

        // 要查未读的直接返回未读的列表
        if (read == 0) {
            for (ClientChargeRecordReadDo recordReadDo : notRecordReadDos) {
                ENPlusChargeStationTimingChargeDto enPlusChargeStationTimingChargeDto = new ENPlusChargeStationTimingChargeDto();
                CglibUtil.copy(recordReadDo,enPlusChargeStationTimingChargeDto);
                notReadRecordDtos.add(enPlusChargeStationTimingChargeDto);
            }
            return notReadRecordDtos;
        }

        // 已读则先把未读的变成已读，再返回
        // 没有未读，直接返回已读
        if (notRecordReadDos.size() == 0) {
            for (ClientChargeRecordReadDo recordReadDo : recordReadDos) {
                ENPlusChargeStationTimingChargeDto enPlusChargeStationTimingChargeDto = new ENPlusChargeStationTimingChargeDto();
                CglibUtil.copy(recordReadDo,enPlusChargeStationTimingChargeDto);
                notReadRecordDtos.add(enPlusChargeStationTimingChargeDto);
            }
            return notReadRecordDtos;
        }

        // 批量更新未读为已读
        List<ClientChargeRecordReadDo> newNotRecordReadDos = new ArrayList<>();
        for (ClientChargeRecordReadDo recordReadDo : notRecordReadDos) {
            recordReadDo.setReaded(1);
            newNotRecordReadDos.add(recordReadDo);
        }
        ActionFlagUtil.assertTrue(clientChargeRecordReadService.updateBatchById(newNotRecordReadDos));

        for (ClientChargeRecordReadDo recordReadDo : recordReadDos) {
            ENPlusChargeStationTimingChargeDto enPlusChargeStationTimingChargeDto = new ENPlusChargeStationTimingChargeDto();
            CglibUtil.copy(recordReadDo,enPlusChargeStationTimingChargeDto);
            notReadRecordDtos.add(enPlusChargeStationTimingChargeDto);
        }
        return notReadRecordDtos;
    }

    @Override
    public List<ENPlusChargeStationChargeTimeDto> queryChargeScheduledTaskList(String deviceId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        List<ClientChargeTaskDo> clientChargeTaskDos = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, deviceId));
        List<ENPlusChargeStationChargeTimeDto> chargeTimeDtos = new ArrayList<>();
        clientChargeTaskDos.forEach(clientChargeTaskDo -> {
            ENPlusChargeStationChargeTimeDto enPlusChargeStationChargeTimeDto = new ENPlusChargeStationChargeTimeDto();
            CglibUtil.copy(clientChargeTaskDo, enPlusChargeStationChargeTimeDto);
            enPlusChargeStationChargeTimeDto.setId(String.valueOf(clientChargeTaskDo.getId()));
            String week = clientChargeTaskDo.getWeek();
            if (week == null) {
                enPlusChargeStationChargeTimeDto.setWeek(new ArrayList<>());
            } else {
                List<Integer> weekList = Arrays.stream(week.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                enPlusChargeStationChargeTimeDto.setWeek(weekList);
            }
            chargeTimeDtos.add(enPlusChargeStationChargeTimeDto);
        });

        return chargeTimeDtos;

    }

    @Override
    @Transactional
    public void createChargeScheduledTask(ClientCreateChargeTaskVo clientCreateChargeTaskVo) {

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), clientCreateChargeTaskVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());

        // 查询设备是否在线
//        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
//        if (!deviceOnline) {
//            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
//        }

        ClientCreateChargeTaskVo.ChargeTaskTimeVo chargeTimeVoMap = clientCreateChargeTaskVo.getChargeTimeVoMap();
        //  查询所有定时任务
        List<ClientChargeTaskDo> clientChargeTaskDos = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, clientCreateChargeTaskVo.getDeviceId()));

        HashMap<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> map = new HashMap<>();
        IntStream.range(0, clientChargeTaskDos.size())
                .forEach(index -> {
                    SocketRandomTimeMapVo.SocketRandomTimeVo socketRandomTimeVo = new SocketRandomTimeMapVo.SocketRandomTimeVo();
                    ClientChargeTaskDo clientChargeTaskDo = clientChargeTaskDos.get(index);
                    socketRandomTimeVo.setStatus(clientChargeTaskDo.getStatus() ? 1 : 0);
                    String weekString = clientChargeTaskDo.getWeek();
                    List<Integer> weekday = new ArrayList<>();
                    // 检查字符串是否非空
                    if (weekString != null && !weekString.isEmpty()) {
                        // 使用 split 方法按逗号拆分字符串
                        String[] weekParts = weekString.split(",");

                        // 遍历拆分得到的字符串数组
                        for (String weekPart : weekParts) {
                            weekday.add(Integer.parseInt(weekPart.trim()));
                        }
                    }
                    socketRandomTimeVo.setWeek(weekday);
                    socketRandomTimeVo.setStartTime(clientChargeTaskDo.getStartTime());
                    socketRandomTimeVo.setEndTime(clientChargeTaskDo.getEndTime());

                    map.put(index, socketRandomTimeVo);
                });
        map.put(clientChargeTaskDos.size() + 1, chargeTimeVoMap);

        // 判断时间是否有冲突
        if (TimeUtil.hasTimeConflict(map, CommonConstants.DEVICE_EN_CDZ)) {
            throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
        }

        // 保存定时任务
        ClientChargeTaskDo clientChargeTaskDo = new ClientChargeTaskDo();
        CglibUtil.copy(chargeTimeVoMap, clientChargeTaskDo);
        clientChargeTaskDo.setUserId(clientUserDo.getId());
        String startTime = chargeTimeVoMap.getStartTime();
        String endTime = chargeTimeVoMap.getEndTime();
        String offset = clientUserDo.getTimeZone();
        TimeUtil.TimeAndWeekList startTimeAndWeekList = TimeUtil.isoToTimeWithGMT8(startTime, chargeTimeVoMap.getWeek(), offset);
        TimeUtil.TimeAndWeekList endTimeAndWeekList = TimeUtil.isoToTimeWithGMT8(endTime, chargeTimeVoMap.getWeek(), offset);
        clientChargeTaskDo.setStartTime(startTime);
        clientChargeTaskDo.setEndTime(endTime);
        long time = System.currentTimeMillis();
        clientChargeTaskDo.setCreateTime(time);
        clientChargeTaskDo.setUpdateTime(time);
        long id = snowFlakeUtil.generateId();
        clientChargeTaskDo.setId(id);
        clientChargeTaskDo.setDeviceId(Long.valueOf(clientCreateChargeTaskVo.getDeviceId()));

        String weekday = startTimeAndWeekList.getWeekList().stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
        clientChargeTaskDo.setWeek(weekday);
        clientChargeTaskDo.setStatus(chargeTimeVoMap.getStatus() == 1);
        ActionFlagUtil.assertTrue(clientChargeTaskService.save(clientChargeTaskDo));

        // 在xxl-job创建并执行这些定时任务
        ClientChargeTaskDo chargeTaskDo = clientChargeTaskDo;
        LocalTime localStartTime = LocalTime.parse(startTimeAndWeekList.getTime(), DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime localEndTime = LocalTime.parse(endTimeAndWeekList.getTime(), DateTimeFormatter.ofPattern("HH:mm"));

        // 不为空就是重复定时任务
        if (!chargeTimeVoMap.getWeek().contains(0)) {
            String startWeekConf = xxlJobUtil.convertWeekdayForXXLJob(chargeTimeVoMap.getWeek());
            // 然后根据时间调整星期
            String adjustedWeekdayForEndTime = adjustWeekdayForCrossMidnight(localStartTime, localEndTime, chargeTimeVoMap.getWeek());
            String startScheduleConf = String.format("%d %d %d ? * %s", localStartTime.getSecond(), localStartTime.getMinute(), localStartTime.getHour(), startWeekConf);
            String endScheduleConf = String.format("%d %d %d ? * %s", localEndTime.getSecond(), localEndTime.getMinute(), localEndTime.getHour(), adjustedWeekdayForEndTime);
            createXxlJobTask(chargeTaskDo, startScheduleConf, endScheduleConf);
        } else {
            List<String> cronExpressions = generateCronExpressionsForPeriod(localStartTime, localEndTime, offset);
            createXxlJobTask(chargeTaskDo, cronExpressions.get(0), cronExpressions.get(1));
        }

    }

    @Override
    @Transactional
    public void deleteChargeScheduledTask(String userId, String taskId) {
        // 数据库中查到该任务关联的xxl-job信息
        ClientChargeTaskDo chargeTaskDo = clientChargeTaskService.getById(taskId);
        BeanUtil.assertNotNull(chargeTaskDo);

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(userId, String.valueOf(chargeTaskDo.getDeviceId()));

        ActionFlagUtil.assertTrue(clientChargeTaskService.removeById(chargeTaskDo.getId()));

        Long startTaskId = chargeTaskDo.getStartTaskId();
        Long endTaskId = chargeTaskDo.getEndTaskId();

        JSONObject startObject = xxlJobUtil.remove(Math.toIntExact(startTaskId));
        JSONObject endObject = xxlJobUtil.remove(Math.toIntExact(endTaskId));

        if (startObject.getInteger("code") != 200 || endObject.getInteger("code") != 200) {
            throw new EcosException(EcosExceptionEnum.INVALID_CODE);
        }

    }

    @Override
    @Transactional
    public Boolean updateChargeScheduledTask(ClientUpdateChargeTaskVo clientUpdateChargeTaskVo) {
        ClientChargeTaskDo chargeTaskDo = clientChargeTaskService.getById(clientUpdateChargeTaskVo.getTaskId());
        BeanUtil.assertNotNull(chargeTaskDo);
        // 原来的状态
        Boolean beforeStatus = chargeTaskDo.getStatus();

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), String.valueOf(chargeTaskDo.getDeviceId()));

        ClientUpdateChargeTaskVo.ChargeTaskTimeVo chargeTimeVoMap = clientUpdateChargeTaskVo.getChargeTimeVoMap();
        //  查询所有定时任务
        List<ClientChargeTaskDo> clientChargeTaskDos = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, chargeTaskDo.getDeviceId()));

        HashMap<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> map = new HashMap<>();
        IntStream.range(0, clientChargeTaskDos.size())
                .forEach(index -> {
                    SocketRandomTimeMapVo.SocketRandomTimeVo socketRandomTimeVo = new SocketRandomTimeMapVo.SocketRandomTimeVo();
                    ClientChargeTaskDo clientChargeTaskDo = clientChargeTaskDos.get(index);
                    if (Objects.equals(clientChargeTaskDo.getId(), chargeTaskDo.getId())) {
                        return;
                    }
                    socketRandomTimeVo.setStatus(clientChargeTaskDo.getStatus() ? 1 : 0);
                    String weekString = clientChargeTaskDo.getWeek();
                    List<Integer> weekday = new ArrayList<>();
                    // 检查字符串是否非空
                    if (weekString != null && !weekString.isEmpty()) {
                        // 使用 split 方法按逗号拆分字符串
                        String[] weekParts = weekString.split(",");

                        // 遍历拆分得到的字符串数组
                        for (String weekPart : weekParts) {
                            weekday.add(Integer.parseInt(weekPart.trim()));
                        }
                    }
                    socketRandomTimeVo.setWeek(weekday);
                    socketRandomTimeVo.setStartTime(clientChargeTaskDo.getStartTime());
                    socketRandomTimeVo.setEndTime(clientChargeTaskDo.getEndTime());

                    map.put(index, socketRandomTimeVo);
                });
        map.put(clientChargeTaskDos.size() + 1, chargeTimeVoMap);

        // 判断时间是否有冲突
        if (TimeUtil.hasTimeConflict(map, CommonConstants.DEVICE_EN_CDZ)) {
            throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
        }

        // 更新Mysql数据库
        String startTime = chargeTimeVoMap.getStartTime();
        String endTime = chargeTimeVoMap.getEndTime();
        String offset = clientUserDo.getTimeZone();
        TimeUtil.TimeAndWeekList startTimeAndWeekList = TimeUtil.isoToTimeWithGMT8(startTime, chargeTimeVoMap.getWeek(), offset);
        TimeUtil.TimeAndWeekList endTimeAndWeekList = TimeUtil.isoToTimeWithGMT8(endTime, chargeTimeVoMap.getWeek(), offset);
        chargeTaskDo.setPower(chargeTimeVoMap.getPower());
        chargeTaskDo.setStartTime(startTime);
        chargeTaskDo.setEndTime(endTime);
        chargeTaskDo.setStatus(chargeTimeVoMap.getStatus() == 1);
        String weekday = startTimeAndWeekList.getWeekList().stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
        chargeTaskDo.setWeek(weekday);
        chargeTaskDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientChargeTaskService.updateById(chargeTaskDo));

        // 更新Xxl-job任务
        LocalTime localStartTime = LocalTime.parse(startTimeAndWeekList.getTime(), DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime localEndTime = LocalTime.parse(endTimeAndWeekList.getTime(), DateTimeFormatter.ofPattern("HH:mm"));

        // 不为空就是重复定时任务
        if (!chargeTimeVoMap.getWeek().contains(0)) {
            String startWeekConf = xxlJobUtil.convertWeekdayForXXLJob(chargeTimeVoMap.getWeek());
            // 然后根据时间调整星期
            String adjustedWeekdayForEndTime = adjustWeekdayForCrossMidnight(localStartTime, localEndTime, chargeTimeVoMap.getWeek());
            String startScheduleConf = String.format("%d %d %d ? * %s", localStartTime.getSecond(), localStartTime.getMinute(), localStartTime.getHour(), startWeekConf);
            String endScheduleConf = String.format("%d %d %d ? * %s", localEndTime.getSecond(), localEndTime.getMinute(), localEndTime.getHour(), adjustedWeekdayForEndTime);
            return updateXxlJobTask(chargeTaskDo, startScheduleConf, endScheduleConf, beforeStatus);
        } else {
            List<String> cronExpressions = generateCronExpressionsForPeriod(localStartTime, localEndTime, offset);
            return updateXxlJobTask(chargeTaskDo, cronExpressions.get(0), cronExpressions.get(1), beforeStatus);
        }

    }

    @DSTransactional
    private void bindDeviceAction(
            String deviceSn, ClientUserDo clientUserDo,
            V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo,
            String redisKey,
            String ip
    ) {

        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        RLock lock = redissonClient.getLock(wifiSn);
        try {
            if (lock.tryLock(100, TimeUnit.MILLISECONDS)) {
                HybridSinglePhaseDO hybridSinglePhaseDO;
                try {
                    hybridSinglePhaseDO = hubService.getByDeviceName(deviceSn);
                } catch (RuntimeException e) {
                    if (!Objects.equals(e.getMessage(), "err.invalid.data")) {
                        throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
                    }
                    log.info("这是一个新设备：{}", wifiSn);
                    hybridSinglePhaseDO = null;
                }
                Long deviceId;
                if (null == hybridSinglePhaseDO) {
                    deviceId = saveDeviceInfo(deviceSn, wifiSn, homeClientUserBindDeviceVo, clientUserDo.getDatacenterId(), ip);
                    try {
                        hybridSinglePhaseDO = hubService.getById(deviceId);
                    } catch (Exception e) {
                        log.warn("设备不存在");
                        stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FAIL, 1, TimeUnit.MINUTES);
                        throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
                    }
                } else {
                    deviceId = hybridSinglePhaseDO.getId();
                    MiddleClientUserDeviceDo masterDevice = middleClientUserDeviceService.getOne(Wrappers
                            .<MiddleClientUserDeviceDo>lambdaQuery().eq(
                                    MiddleClientUserDeviceDo::getDeviceId,
                                    deviceId
                            )
                            .eq(MiddleClientUserDeviceDo::getMaster, 1));

                    if (masterDevice != null) {
                        if (!masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "从账号绑定错误");
                            log.warn("从账号绑定错误");
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FORBIDDEN, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.SLAVE_BIND_ERROR);
                        }
                    }
                    hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
                    hybridSinglePhaseDO.setIp(ip);
                    updateDeviceInfo(hybridSinglePhaseDO, homeClientUserBindDeviceVo, wifiSn, deviceSn);

                    if (masterDevice != null) {
                        if (masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "已经绑定了该设备");
                            log.warn("{} 已经绑定了该设备：{}", clientUserDo.getEmail(), deviceSn);
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
                        }
                    }
                }
                log.info("设备hub详情：{}", hybridSinglePhaseDO);
                if (homeClientUserBindDeviceVo.getNetworkType() != 1) {
                    // 将设备与操作人建立关系
                    long middleId = snowFlakeUtil.generateId();
                    String deviceName = StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? deviceSn : homeClientUserBindDeviceVo.getDeviceAliasName();
                    MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
                    middleClientUserDeviceDo.setId(middleId);
                    middleClientUserDeviceDo.setUserId(clientUserDo.getId());
                    middleClientUserDeviceDo.setDeviceId(deviceId);
                    middleClientUserDeviceDo.setCreateTime(System.currentTimeMillis());
                    middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
                    middleClientUserDeviceDo.setWeight(0);
                    middleClientUserDeviceDo.setName(deviceName);
                    middleClientUserDeviceDo.setMaster(1);
                    ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(middleClientUserDeviceDo));

                    // 将设备与家庭建立关系
                    v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(deviceId), deviceName);
                }

                // 绑定成功
                stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
            }
        } catch (InterruptedException e) {
            log.warn(e.getMessage());
            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FAIL, 1, TimeUnit.MINUTES);
            throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        } finally {
            lock.unlock();
        }
    }

    private Long saveDeviceInfo(String deviceSn, String wifiSn, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, Integer datacenterId, String ip) {
        Long deviceId = snowFlakeUtil.generateId();
        ChargerSaveVO chargerSaveVO = new ChargerSaveVO();
        chargerSaveVO.setDeviceId(deviceId);
        chargerSaveVO.setBindMode(homeClientUserBindDeviceVo.getNetworkType());
        chargerSaveVO.setChargerSn(deviceSn);
        chargerSaveVO.setGateSn(wifiSn);
        chargerSaveVO.setLon(homeClientUserBindDeviceVo.getLon());
        chargerSaveVO.setLat(homeClientUserBindDeviceVo.getLat());
        chargerSaveVO.setIp(ip);
        chargerSaveVO.setDataCenterId(datacenterId);
        chargerSaveVO.setCpFirmwareVersion(homeClientUserBindDeviceVo.getCpFirmwareVersion());
        chargerSaveVO.setCpPlugAndChargeMsg(homeClientUserBindDeviceVo.getCpPlugAndChargeMsg());
        retryService.getChargeStationStatus(deviceSn, chargerSaveVO, 0);
//        hubService.saveCharger(chargerSaveVO);
        return deviceId;
    }

    private void updateDeviceInfo(HybridSinglePhaseDO hybridSinglePhaseDO, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, String wifiSn, String deviceSn) {
        ChargerSaveVO chargerSaveVO = new ChargerSaveVO();
        String beforeWifiSn = hybridSinglePhaseDO.getWifiSn();
        stringRedisTemplate.opsForValue().set("NEW-BIND:" + beforeWifiSn, "1", 10, TimeUnit.MINUTES);
        Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
        if (firstInstall == null || firstInstall < 100) {
            hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
        }
        chargerSaveVO.setDeviceId(hybridSinglePhaseDO.getId());
        chargerSaveVO.setBindMode(homeClientUserBindDeviceVo.getNetworkType());
        chargerSaveVO.setChargerSn(deviceSn);
        chargerSaveVO.setGateSn(wifiSn);
        chargerSaveVO.setLon(homeClientUserBindDeviceVo.getLon());
        chargerSaveVO.setLat(homeClientUserBindDeviceVo.getLat());
        chargerSaveVO.setIp(hybridSinglePhaseDO.getIp());
        chargerSaveVO.setDataCenterId(hybridSinglePhaseDO.getDatacenterId());
        chargerSaveVO.setCpFirmwareVersion(homeClientUserBindDeviceVo.getCpFirmwareVersion());
        chargerSaveVO.setCpPlugAndChargeMsg(homeClientUserBindDeviceVo.getCpPlugAndChargeMsg());
        retryService.getChargeStationStatus(deviceSn, chargerSaveVO, 1);
//        hubService.updCharger(chargerSaveVO);
    }

    @Override
    public Boolean updChargerConfiguration(ChargePlugSwitchVO request) {
        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(request.getDeviceId()));
        String deviceSn = hybridSinglePhaseDO.getDeviceSn();
        ConfigurationMap map = new ConfigurationMap("plugAndChargeMsg", request.getSwitchStatus().toString());
        Boolean res = ecosIotApi.updConfiguration(deviceSn, String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), map);
        if (res) {
            hubService.updCharger(ChargerSaveVO.builder()
                            .deviceId(Long.parseLong(request.getDeviceId()))
                            .cpPlugAndChargeMsg(request.getSwitchStatus().toString())
                    .build());
        }
        return res;
    }

    private void saveChargeRecord(Long deviceId, long currentTimeMillis) {
        long transactionId = Math.toIntExact(currentTimeMillis / 1000);

        V2ClientChargeRecordSaveVo v2ClientChargeRecordSaveVo = new V2ClientChargeRecordSaveVo();
        v2ClientChargeRecordSaveVo.setDeviceId(deviceId);
        v2ClientChargeRecordSaveVo.setTransactionId(transactionId);
        v2ClientChargeRecordSaveVo.setStartTime(currentTimeMillis);

        hubService.saveChargeRecord(v2ClientChargeRecordSaveVo);
    }

    private V2ClientChargeRecordUpdateVo updateLatestChargeRecord(HybridSinglePhaseDO hybridSinglePhaseDO, V2ClientChargeRecordDto v2ClientChargeRecordDto) {
        long currentTimeMillis = System.currentTimeMillis();
        V2ClientChargeRecordUpdateVo v2ClientChargeRecordUpdateVo = new V2ClientChargeRecordUpdateVo();

        CglibUtil.copy(v2ClientChargeRecordDto, v2ClientChargeRecordUpdateVo);
        v2ClientChargeRecordUpdateVo.setEndTime(currentTimeMillis);
        v2ClientChargeRecordUpdateVo.setBatCap("0wh");
        v2ClientChargeRecordUpdateVo.setDuration(0L);
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

        // 查询最后一个Charging状态的时间
        Map<String, LinkedHashMap<Long, Object>> lastChargePointMap = timeSeriesDatabaseService.lastConnectorStatusPoint(hybridSinglePhaseDO.getDeviceSn(),ChargerStatusEnum.Charging.getStatus(),  ListUtil.toList(TsdbMetricsConstants.CONNECTOR_STATUS), v2ClientChargeRecordDto.getStartTime(), currentTimeMillis);
        LinkedHashMap<Long, Object> lastChargePoint = lastChargePointMap.get(TsdbMetricsConstants.CONNECTOR_STATUS);
        if (lastChargePoint.size() == 0) {
            v2ClientChargeRecordUpdateVo.setEndTime(v2ClientChargeRecordUpdateVo.getStartTime());
            return v2ClientChargeRecordUpdateVo;
        }
        Long endTimestamp = Collections.max(lastChargePoint.keySet()) * 1000;
        v2ClientChargeRecordUpdateVo.setEndTime(endTimestamp);

        // 查询设备的充电量累计
        Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(
                hybridSinglePhaseDO.getDeviceSn(),
                ListUtil.toList(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER),
                v2ClientChargeRecordUpdateVo.getStartTime(),
                v2ClientChargeRecordUpdateVo.getEndTime(),
                TsdbSampleEnum.ONE_MINUTE_NEAR_POINT);

        LinkedHashMap<Long, Object> energyMap = metricDataMap.get(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER);

        List<Long> keyList = null == energyMap
                ? ListUtil.empty()
                : energyMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());

        BigDecimal total = BigDecimal.ZERO;
        for (Long time : keyList) {
            assert energyMap != null;
            BigDecimal energy = new BigDecimal(energyMap.getOrDefault(time, "0").toString());
            total = NumberUtil.round(NumberUtil.add(total, energy), 2, RoundingMode.HALF_UP);
        }
        total = NumberUtil.round(NumberUtil.div(total, 1000), 1, RoundingMode.HALF_UP);
        if (!Objects.equals(total, BigDecimal.ZERO)) {
            v2ClientChargeRecordUpdateVo.setBatCap(total.toString() + "kWh");
            v2ClientChargeRecordUpdateVo.setDuration(v2ClientChargeRecordUpdateVo.getEndTime() - v2ClientChargeRecordUpdateVo.getStartTime());
        }

        hubService.updChargeRecord(v2ClientChargeRecordUpdateVo);

        return v2ClientChargeRecordUpdateVo;
    }

    @Transactional
    public void createXxlJobTask(ClientChargeTaskDo chargeTaskDo, String startScheduleConf, String endScheduleConf) {
        XxlJobInfo xxlJobInfoStart = new XxlJobInfo();
        XxlJobInfo xxlJobInfoEnd = new XxlJobInfo();

        xxlJobInfoStart.setJobDesc("ecos_client_cdz_" + chargeTaskDo.getId() + "_start");
        xxlJobInfoEnd.setJobDesc("ecos_client_cdz_" + chargeTaskDo.getId() + "_end");
        xxlJobInfoStart.setAuthor("ecos_client_auto");
        xxlJobInfoEnd.setAuthor("ecos_client_auto");
        xxlJobInfoStart.setScheduleType("CRON");
        xxlJobInfoEnd.setScheduleType("CRON");

        xxlJobInfoStart.setScheduleConf(startScheduleConf);
        xxlJobInfoEnd.setScheduleConf(endScheduleConf);


        xxlJobInfoStart.setGlueType("BEAN");
        xxlJobInfoEnd.setGlueType("BEAN");

        xxlJobInfoStart.setExecutorHandler("chargeStationChargingTask");
        xxlJobInfoEnd.setExecutorHandler("chargeStationChargingTask");
        xxlJobInfoStart.setExecutorRouteStrategy("RANDOM");
        xxlJobInfoEnd.setExecutorRouteStrategy("RANDOM");
        xxlJobInfoStart.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfoEnd.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfoStart.setMisfireStrategy("DO_NOTHING");
        xxlJobInfoEnd.setMisfireStrategy("DO_NOTHING");

        xxlJobInfoStart.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + "1" + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId() + "//" + chargeTaskDo.getPower());
        xxlJobInfoEnd.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + "0" + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId() + "//" + chargeTaskDo.getPower());


        // 先创建结束子任务，再创建开始主任务，最后更新任务配置表
        JSONObject startObject;
        JSONObject endObject;
        if (chargeTaskDo.getStatus()) {
            startObject = xxlJobUtil.addAndStart(xxlJobInfoStart);
            endObject = xxlJobUtil.addAndStart(xxlJobInfoEnd);
        } else {
            startObject = xxlJobUtil.add(xxlJobInfoStart);
            endObject = xxlJobUtil.add(xxlJobInfoEnd);
        }
        if (startObject.getInteger("code") != 200 || endObject.getInteger("code") != 200) {
            throw new EcosException(EcosExceptionEnum.INVALID_CODE);
        }
        String startTaskId = startObject.getString("content");
        String endTaskId = endObject.getString("content");
        chargeTaskDo.setStartTaskId(Long.valueOf(startTaskId));
        chargeTaskDo.setEndTaskId(Long.valueOf(endTaskId));
        clientChargeTaskService.updateById(chargeTaskDo);
    }

    @Transactional
    public Boolean updateXxlJobTask(ClientChargeTaskDo chargeTaskDo, String startScheduleConf, String endScheduleConf, Boolean beforeStatus) {

        try {

            // 获取要更新的xxl-job任务。更新corn时间
            JSONObject jsonObject = xxlJobUtil.pageList(0, 10, 2, -1, "ecos_client_cdz_" + chargeTaskDo.getId(), "", "");
            BeanUtil.assertNotNull(jsonObject);
            AtomicReference<XxlJobInfo> xxlJobInfoStartA = new AtomicReference<>();
            AtomicReference<XxlJobInfo> xxlJobInfoEndA = new AtomicReference<>();
            jsonObject.getJSONArray("data").forEach(item -> {
                JSONObject j = (JSONObject) item;
                XxlJobInfo jobInfo = j.toJavaObject(XxlJobInfo.class);
                if (jobInfo.getJobDesc().equals("ecos_client_cdz_" + chargeTaskDo.getId() + "_start")) {
                    xxlJobInfoStartA.set(jobInfo);
                }

                if (jobInfo.getJobDesc().equals("ecos_client_cdz_" + chargeTaskDo.getId() + "_end")) {
                    xxlJobInfoEndA.set(jobInfo);
                }
            });
            XxlJobInfo xxlJobInfoStart = xxlJobInfoStartA.get();
            XxlJobInfo xxlJobInfoEnd = xxlJobInfoEndA.get();
            xxlJobInfoStart.setScheduleConf(startScheduleConf);
            xxlJobInfoEnd.setScheduleConf(endScheduleConf);

            xxlJobInfoStart.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + "1" + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId() + "//" + chargeTaskDo.getPower());
            xxlJobInfoEnd.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + "0" + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId() + "//" + chargeTaskDo.getPower());


            JSONObject startObject = xxlJobUtil.update(xxlJobInfoStart);
            JSONObject endObject = xxlJobUtil.update(xxlJobInfoEnd);

            if (startObject.getInteger("code") != 200 || endObject.getInteger("code") != 200) {
                return false;
            }

            // 启动或者暂停xxl-job项目
            if (!Objects.equals(beforeStatus, chargeTaskDo.getStatus()) || "0".equals(chargeTaskDo.getWeek())) {
                JSONObject startObject1;
                JSONObject endObject1;
                if (chargeTaskDo.getStatus()) {
                    startObject1 = xxlJobUtil.start(Math.toIntExact(chargeTaskDo.getStartTaskId()));
                    endObject1 = xxlJobUtil.start(Math.toIntExact(chargeTaskDo.getEndTaskId()));
                } else {
                    startObject1 = xxlJobUtil.pause(Math.toIntExact(chargeTaskDo.getStartTaskId()));
                    endObject1 = xxlJobUtil.pause(Math.toIntExact(chargeTaskDo.getEndTaskId()));
                }

                if (startObject1.getInteger("code") != 200 || endObject1.getInteger("code") != 200) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("连接xxl-job报错：{}",e.getMessage());
            return false;
        }
    }



    public List<String> generateCronExpressionsForPeriod(LocalTime localStartTime, LocalTime localEndTime, String offset) {
        // 解析 startTime 和 endTime
        offset = TimeUtil.getTimezoneCode(offset);
        offset = offset.replace("GMT", "");

        LocalDate today = LocalDate.now(ZoneId.of(offset));
        LocalTime now = LocalTime.now();
        List<String> cronExpressions = new ArrayList<>();

        LocalDate startDate = today;
        LocalDate endDate = startDate;

        // 如果开始时间小于当前时间，将开始和结束时间设置为第二天
        if (localStartTime.isBefore(now)) {
            startDate = startDate.plusDays(1);
            endDate = startDate;
        }

        // 如果开始时间大于结束时间，将结束时间设置为第二天
        if (localStartTime.isAfter(localEndTime)) {
            endDate = startDate.plusDays(1);
        }

        String cronExpressionStart = String.format("%d %d %d %d %d ? %d",
                localStartTime.getSecond(),
                localStartTime.getMinute(),
                localStartTime.getHour(),
                startDate.getDayOfMonth(),
                startDate.getMonthValue(),
                startDate.getYear());

        String cronExpressionEnd = String.format("%d %d %d %d %d ? %d",
                localEndTime.getSecond(),
                localEndTime.getMinute(),
                localEndTime.getHour(),
                endDate.getDayOfMonth(),
                endDate.getMonthValue(),
                endDate.getYear());

        cronExpressions.add(cronExpressionStart);
        cronExpressions.add(cronExpressionEnd);

        return cronExpressions;
    }

    public String adjustWeekdayForCrossMidnight(LocalTime startTime, LocalTime endTime, List<Integer> weekday) {
        if (startTime.isAfter(endTime)) {
            // 如果开始时间晚于结束时间，表示任务跨越了午夜，需要调整星期
            return weekday.stream()
                    .map(day -> ((day % 7) + 1) % 7 + 1) // 星期往后延一天，保持在1到7的范围内
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }
        // 如果没有跨午夜，直接返回未修改的weekday列表的字符串表示
        return weekday.stream()
                .map(day -> (day % 7) + 1) // 星期往后延一天，保持在1到7的范围内
                .map(Object::toString)
                .collect(Collectors.joining(","));
    }

    @Override
    @Transactional
    public void deleteDevicesConfigAndTask(List<Long> deviceIds) {

        // 删除充电定时任务
        List<ClientChargeTaskDo> taskIds = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .in(ClientChargeTaskDo::getDeviceId, deviceIds));

        for (ClientChargeTaskDo task : taskIds) {
            deleteChargeScheduledTask(String.valueOf(task.getUserId()), String.valueOf(task.getId()));
        }
    }

    @Override
    public List<String> queryChargeStationCards(String deviceId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        return ecosIotApi.queryChargeStationCardList(hybridSinglePhaseDO.getDeviceSn(), String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()));


//        return clientChargeCardService.list(Wrappers.<ClientChargeCardDo>lambdaQuery()
//                .eq(ClientChargeCardDo::getDeviceId, deviceId)
//                .orderByDesc(ClientChargeCardDo::getUpdateTime)).stream().map(ClientChargeCardDo::getCardId).collect(Collectors.toList());
    }

    @Override
    public void bindChargeStationCardId(String deviceId, String cardId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        // 判断充电桩状态
        Integer chargeStationStatus = getChargeStationStatus(deviceId);

        if (chargeStationStatus < 0) {
            throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
        }

        // 检测卡片是否已经被绑定
        List<String> cardList = ecosIotApi.queryChargeStationCardList(hybridSinglePhaseDO.getDeviceSn(), String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()));
        cardList.forEach(card -> {
            if (card.equals(cardId)) {
                throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
            }
        });
//        ClientChargeCardDo one = clientChargeCardService.getOne(Wrappers.<ClientChargeCardDo>lambdaQuery()
//                .eq(ClientChargeCardDo::getCardId, cardId));
//
//        if (one != null) {
//            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
//        }

        // 绑定到设备上
        ActionFlagUtil.assertTrue(
                ecosIotApi.addChargeStationCard(hybridSinglePhaseDO.getDeviceSn(), String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), cardId));


//        ClientChargeCardDo clientChargeCardDo = new ClientChargeCardDo();
//        long chargeCardId = snowFlakeUtil.generateId();
//        long currentTimeMillis = System.currentTimeMillis();
//        clientChargeCardDo.setId(chargeCardId);
//        clientChargeCardDo.setCardId(cardId);
//        clientChargeCardDo.setDeviceId(Long.valueOf(deviceId));
//        clientChargeCardDo.setCreateTime(currentTimeMillis);
//        clientChargeCardDo.setUpdateTime(currentTimeMillis);
//        ActionFlagUtil.assertTrue(clientChargeCardService.save(clientChargeCardDo));
    }

    @Override
    public void deleteChargeStationCard(String userId, String deviceId, List<String> cardIds) {
        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(userId, deviceId);

        ActionFlagUtil.assertTrue(
                ecosIotApi.removeChargeStationCards(hybridSinglePhaseDO.getDeviceSn(), String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()), cardIds));

//        ActionFlagUtil.assertTrue(clientChargeCardService.remove(Wrappers.<ClientChargeCardDo>lambdaQuery()
//                .in(ClientChargeCardDo::getCardId, cardIds)
//                .eq(ClientChargeCardDo::getDeviceId, deviceId)));
    }

    @Override
    public ChargeStationHistoryCapacityDto queryChargeStationHistoryCapacity(ChargeStationHistoryCapacityVo chargeStationHistoryCapacityVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), chargeStationHistoryCapacityVo.getDeviceId());

        Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
        String deviceSn = hybridSinglePhaseDO.getDeviceSn();
        String offset = clientUserDo.getTimeZone();

        Pair<Long, Long> timePair = pairStartTimeAndEndTimeForInsight(chargeStationHistoryCapacityVo.getPeriodType(), chargeStationHistoryCapacityVo.getTimestamp(), firstInstall, offset);

        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                .chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
        return sumQueryTSDB(deviceSn, CommonConstants.CHARGE_HISTORY_CAPACITY,
                timePair.getKey(), timePair.getValue(), chargeStationHistoryCapacityVo.getPeriodType(), timeSeriesDatabaseService, offset);
    }

    private void reflectSetRunData(String method, String val, ENPlusChargeStationRunDataDto enPlusChargeStationRunDataDto) {
        log.info("{} {}", method, val);
        if (StrUtil.isNotBlank(val)) {
            BigDecimal data = NumberUtil.round(new BigDecimal(val), 1, RoundingMode.HALF_UP);
            ReflectUtil.invoke(enPlusChargeStationRunDataDto, method,
                    (Math.abs(data.doubleValue()) < 10L) ? BigDecimal.ZERO : data
            );
        } else {
            ReflectUtil.invoke(enPlusChargeStationRunDataDto, method, BigDecimal.ZERO);
        }
    }

    private Pair<Long, Long> pairStartTimeAndEndTimeForInsight(Integer periodType, Long timestamp, Long firstInstall, String offset) {
        long startTime;
        long endTime;
        switch (periodType) {
            case CommonConstants.PERIOD_MONTH:
                startTime = TimeUtil.getAssignMonthStart(timestamp, offset, 0);
                endTime = TimeUtil.getAssignMonthEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_YEAR:
                startTime = TimeUtil.getYearStart(timestamp, offset);
                endTime = TimeUtil.getYearEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_LIFETIME:
                startTime = firstInstall;
                endTime = TimeUtil.getCurrentTime(offset);
                break;
            default:
                startTime = TimeUtil.getAssignDayStart(timestamp, offset);
                endTime = TimeUtil.getAssignDayEnd(timestamp, offset);
        }
        return Pair.of(startTime, endTime);
    }

    private ChargeStationHistoryCapacityDto sumQueryTSDB(
            String deviceName, List<String> metricList,
            long startTime, long endTime, Integer periodType, TimeSeriesDatabaseService timeSeriesDatabaseService,
            String offset
    ) {
        ChargeStationHistoryCapacityDto chargeStationHistoryCapacityDto = new ChargeStationHistoryCapacityDto();

        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(
                deviceName,
                metricList,
                startTime,
                endTime,
                TsdbSampleEnum.FIVE_MINUTE_NEAR_POINT);

        Map<String, LinkedHashMap<Long, Object>> metricDataMap;
        if (5 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryMonthToYear(result, offset);
        } else if (4 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryDayToMonth(result, offset);
        } else if (2 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryHourToDay(result, offset);
        } else {
            Map<String, LinkedHashMap<Long, Object>> aggregateMap = new LinkedHashMap<>();
            for (String m : result.keySet()) {
                LinkedHashMap<Long, Object> aggregateTimeData = new LinkedHashMap<>();
                LinkedHashMap<Long, Object> map = result.get(m);
                for (Long t : map.keySet()) {
                    BigDecimal bigDecimal = new BigDecimal(map.getOrDefault(t, "0").toString());
                    aggregateTimeData.put(t, NumberUtil.round(bigDecimal, 2, RoundingMode.HALF_UP));
                }
                aggregateMap.put(m, aggregateTimeData);
            }
            metricDataMap = aggregateMap;
        }

        if (CollUtil.isNotEmpty(metricDataMap)) {
            LinkedHashMap<Long, Object> addCapacityMap = metricDataMap.get(TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER);

            BigDecimal totalAddaddCapacity = BigDecimal.ZERO;
            LinkedHashMap<Long, Object> aggregateMap = new LinkedHashMap<>();
            if (CollUtil.isNotEmpty(addCapacityMap)) {
                for (Long time : addCapacityMap.keySet()) {
                    BigDecimal addCapacity = new BigDecimal(addCapacityMap.getOrDefault(time, "0").toString());
                    totalAddaddCapacity = NumberUtil.add(addCapacity, totalAddaddCapacity);
                    addCapacity = NumberUtil.round(NumberUtil.div(addCapacity, 1000), 1, RoundingMode.HALF_UP);
                    aggregateMap.put(time, addCapacity);
                }
            }
            totalAddaddCapacity = NumberUtil.round(NumberUtil.div(totalAddaddCapacity, 1000), 1, RoundingMode.HALF_UP);
            chargeStationHistoryCapacityDto.setTotalCapacity(totalAddaddCapacity);
            chargeStationHistoryCapacityDto.setCapacityDps(aggregateMap);
        }
        return chargeStationHistoryCapacityDto;
    }

}
