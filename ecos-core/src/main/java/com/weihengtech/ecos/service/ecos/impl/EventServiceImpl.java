package com.weihengtech.ecos.service.ecos.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.dao.ecos.EcosMapper;
import com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo;
import com.weihengtech.ecos.model.bos.ecos.EcosEventBo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.service.ecos.EventService;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEvent;
import com.weihengtech.sdk.iot.ecos.model.iot.response.IotDeviceEventResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class EventServiceImpl implements EventService {

	@Resource
	private EcosMapper ecosMapper;
	@Resource
	private EcosIotApi ecosIotApi;

	@Override
	public List<EcosEventBo> listEventByCondition(String deviceFlag, String level, Long start, Long end) {
		if ("alarm".equals(level)) {
			return new ArrayList<>();
		}
		return ecosMapper.listEventByCondition(deviceFlag, level, start, end);
	}

	@Override
	public Integer countBackupMode(String deviceFlag) {
		return ecosMapper.countBackupMode(deviceFlag);
	}

	@Override
	public List<EcosEpsEventBo> listEpsEventByCondition(String deviceFlag, Long startTime, Long endTime) {
		startTime = startTime > endTime ? endTime : startTime;
		return ecosMapper.listEpsEventByCondition(deviceFlag, startTime, endTime);
	}

	@Override
	public EcosEpsEventBo findNearlyNotEpsEvent(String deviceName, Integer id) {
		return ecosMapper.findNearlyNotEpsEvent(deviceName, id);
	}

	@Override
	public PageInfoDTO<HomeEventsFaultDto> pageEventList(String deviceName, HomeEventsFaultVo homeEventsFaultVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		PageHelper.startPage(homeEventsFaultVo.getPageNum(), homeEventsFaultVo.getPageSize());
		List<EcosEventBo> ecosEventBos = this.listEventByCondition(deviceName,
				homeEventsFaultVo.getType(), homeEventsFaultVo.getStart(), homeEventsFaultVo.getEnd()
		);
		PageInfo<EcosEventBo> pageInfo = new PageInfo<>(ecosEventBos);

		PageInfoDTO<HomeEventsFaultDto> pageInfoDTO = new PageInfoDTO<>();
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		pageInfoDTO.setData(ecosEventBos.stream().map(bo ->
			HomeEventsFaultDto.builder()
					.errorCode(bo.getSubsystem() + "_" + bo.getCode())
					.eventType(bo.getLevel())
					.eventTypeInt(getEventType(bo.getLevel()))
					.eventContentCn(bo.getChinese())
					.eventContentEn(bo.getEnglish())
					.occurrenceTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(bo.getUploadTime() * 1000L, clientUserDo.getTimeZone()))
					.build()
		).collect(Collectors.toList()));
		return pageInfoDTO;
	}

	@Override
	public PageInfoDTO<HomeEventsFaultDto> pageEventListIot(String wifiSn, HomeEventsFaultVo homeEventsFaultVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		IotDeviceEventResponse response = ecosIotApi.queryCloudIotDeviceEvent(wifiSn,
				String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()), homeEventsFaultVo);
		if (CollUtil.isEmpty(response.getEventOpenVoList())) {
			return new PageInfoDTO<>();
		}
		List<IotDeviceEvent> eventList = response.getEventOpenVoList();
		List<HomeEventsFaultDto> eventInfoList = eventList.stream()
				.map(i -> HomeEventsFaultDto.builder()
						.eventContentCn(i.getChinese())
						.eventContentEn(i.getEnglish())
						.eventType(i.getLevel())
						.eventTypeInt(getEventType(i.getLevel()))
						.errorCode(StrUtil.isBlank(i.getCode()) || StrUtil.isBlank(i.getSubsystem()) ? StrUtil.EMPTY :
										i.getSubsystem() + "_" + i.getCode())
						.occurrenceTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(i.getUploadTime(), clientUserDo.getTimeZone()))
						.eventAction(i.getEventAction())
						.build())
				.collect(Collectors.toList());
		int totalPages = response.getTotal() % response.getPageSize() == 0 ? response.getTotal() / response.getPageSize() :
				response.getTotal() / response.getPageSize() + 1;
		return new PageInfoDTO<>(totalPages, (long) response.getTotal(), eventInfoList);
	}

	/** 事件类型转int */
	private int getEventType(String eventType) {
		int eventTypeInt;
		switch (eventType) {
			case "alarm":
				eventTypeInt = 1;
				break;
			case "fault":
				eventTypeInt = 2;
				break;
			default:
				eventTypeInt = 0;
				break;
		}
		return eventTypeInt;
	}
}
