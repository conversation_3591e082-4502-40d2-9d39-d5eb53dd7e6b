package com.weihengtech.ecos.service.ele;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientHomeSaveCostDO;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface ClientHomeSaveCostService extends IService<ClientHomeSaveCostDO> {

    /**
     * 计算当前家庭中指定时间范围内每个小时的成本收益数据
     * 注意：起始时间、截止时间不能横跨0点
     *
     * @param homeId 家庭
     * @param homeDeviceList 家庭设备列表
     * @param startTime 起始时间
     * @param endTime 截止时间
     * @return
     */
    List<ClientHomeSaveCostDO> costSaving(Long homeId, String userTimezone, List<ClientHomeDeviceDo> homeDeviceList, Long startTime, Long endTime);

    /**
     * 计算当前家庭中指定昨天、今天时段内每个小时的成本收益数据
     *
     * @param homeInfo 家庭
     * @param homeDeviceList 家庭设备列表
     * @param time 昨天：-1，今天：0，明天：1
     * @return
     */
    List<ClientHomeSaveCostDO> costSaving(ClientHomeDo homeInfo, List<ClientHomeDeviceDo> homeDeviceList, Integer time);

    HomeCostSavingDTO calHomeCostInfo(Long homeId);

    void clearHistoryCostInfo(Long homeId);
}
