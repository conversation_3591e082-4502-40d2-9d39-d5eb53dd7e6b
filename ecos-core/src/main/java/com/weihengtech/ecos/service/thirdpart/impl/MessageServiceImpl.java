package com.weihengtech.ecos.service.thirdpart.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.api.NotificationCenterApi;
import com.weihengtech.ecos.api.pojo.base.NotificationResponse;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchMailVo;
import com.weihengtech.ecos.api.pojo.vos.NotificationBatchSmsVo;
import com.weihengtech.ecos.enums.global.SmsEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.consts.MailModel;
import com.weihengtech.ecos.enums.global.MailModelEnum;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.service.global.CacheService;
import com.weihengtech.ecos.service.thirdpart.MessageService;
import com.weihengtech.ecos.utils.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {
	@Resource
	private NotificationCenterApi notificationCenterApi;
	@Resource
	private CacheService cacheService;

	@Override
	public void sendEmail(String toEmail, MailModelEnum mailModelEnum, String... params) {
		MailModel.MailInfoBO mailInfo = MailModel.getMailInfo(mailModelEnum, params);
		try {
			NotificationBatchMailVo notificationBatchMailVo = new NotificationBatchMailVo();
			notificationBatchMailVo.setContent(mailInfo.getContent());
			notificationBatchMailVo.setIsHtml(true);
			notificationBatchMailVo.setSubject(mailInfo.getSubject());
			notificationBatchMailVo.setTo(String.join(",", ListUtil.toList(toEmail)));
			NotificationResponse<String> notificationResponse = notificationCenterApi.v1AsyncMail(notificationBatchMailVo);
			if (notificationResponse == null || !notificationResponse.getSuccess() || notificationResponse.getCode() != HttpStatus.OK.value()) {
				throw new EcosException(EcosExceptionEnum.SEND_EMAIL_ERROR);
			}
		} catch (Exception e) {
			throw new EcosException(EcosExceptionEnum.SEND_EMAIL_ERROR);
		}
	}

	@Override
	public void sendPhone(String phone, String code, String param) {
		NotificationBatchSmsVo notificationBatchSmsVo = new NotificationBatchSmsVo();
		notificationBatchSmsVo.setPhoneNumbers(phone);
		notificationBatchSmsVo.setTemplateCode(code);
		notificationBatchSmsVo.setTemplateParam(param);

		try {
			NotificationResponse<String> response = notificationCenterApi.v1AsyncSms(notificationBatchSmsVo);
			if (response == null || !response.getSuccess() || response.getCode() != HttpStatus.OK.value()) {
				throw new EcosException(EcosExceptionEnum.SEND_EMAIL_ERROR);
			}
		} catch (Exception e) {
			log.warn("MessageServiceImpl#sendPhone, {}, {}, {}", phone, code, param);
			throw new EcosException(EcosExceptionEnum.SEND_EMAIL_ERROR);
		}
	}

	@Override
	public void sendPhoneWithCache(SmsEnum smsEnum, String phone, String codeKey, String cacheKey) {
		cacheService.notExistKey(cacheKey).orElseThrow(() -> new EcosException(EcosExceptionEnum.CODE_SEND_LIMIT));

		Optional<String> opCode = cacheService.getStr(codeKey);
		String code = RandomUtil.generateFixedLengthNumberStr(4);
		log.info("SMS#sendPhoneWithCache generate random code, {} {}", phone, code);
		if (opCode.isPresent()) {
			code = opCode.get();
		} else {
			cacheService.setStrWithSecond(codeKey, code, RedisRefConstants.CODE_REDIS_TIMEOUT);
		}
		Pair<String, String> codeParamPair = SmsEnum.create(smsEnum, phone, code);
		log.info("SMS#sendPhoneWithCache final send, {} {} {}", phone, codeParamPair.getKey(), codeParamPair.getValue());
		sendPhone(phone, codeParamPair.getKey(), codeParamPair.getValue());
		cacheService.setStrWithSecond(cacheKey, code, RedisRefConstants.CACHE_REDIS_TIMEOUT);
	}

	@Override
	public void sendEmailWithCache(String toEmail, String codeKey, String cacheKey, MailModelEnum mailModelEnum, String... params) {
		cacheService.notExistKey(cacheKey).orElseThrow(() -> new EcosException(EcosExceptionEnum.CODE_SEND_LIMIT));

		String code = RandomUtil.generateFixedLengthNumberStr(4);
		String[] p = new String[params.length+1];
		p[0] = code;
		System.arraycopy(params, 0, p, 1, params.length);
		sendEmail(toEmail, mailModelEnum, p);

		cacheService.setStrWithSecond(codeKey, code, RedisRefConstants.CODE_REDIS_TIMEOUT);
		cacheService.setStrWithSecond(cacheKey, code, RedisRefConstants.CACHE_REDIS_TIMEOUT);
	}
}
