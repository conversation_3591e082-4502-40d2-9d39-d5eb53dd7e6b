package com.weihengtech.ecos.service.socket.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ecos.ClientSocketConfigMapper;
import com.weihengtech.ecos.model.dos.ClientSocketConfigDO;
import com.weihengtech.ecos.service.socket.ClientSocketConfigService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/23 14:27
 */
@Service
public class ClientSocketConfigServiceImpl extends ServiceImpl<ClientSocketConfigMapper, ClientSocketConfigDO>
        implements ClientSocketConfigService {

    @Override
    public void updOrSaveCountdown(Long deviceId, Integer second) {
        ClientSocketConfigDO existsItem = getOne(Wrappers.<ClientSocketConfigDO>lambdaQuery().eq(
                ClientSocketConfigDO::getDeviceId, deviceId
        ));
        if (existsItem == null) {
            save(ClientSocketConfigDO.builder()
                    .deviceId(deviceId)
                    .countdownEnd(new Date(System.currentTimeMillis() + second*1000))
                    .build());
        } else {
            existsItem.setCountdownEnd(new Date(System.currentTimeMillis() + second*1000));
            updateById(existsItem);
        }
    }

    @Override
    public ClientSocketConfigDO getByDeviceId(Long deviceId) {
        LambdaQueryWrapper<ClientSocketConfigDO> wrapper = Wrappers.<ClientSocketConfigDO>lambdaQuery()
                .eq(ClientSocketConfigDO::getDeviceId, deviceId);
        return getOne(wrapper);
    }
}
