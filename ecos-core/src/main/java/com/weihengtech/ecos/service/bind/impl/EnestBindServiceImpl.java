package com.weihengtech.ecos.service.bind.impl;

import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.vos.bind.BindDeviceVO;
import com.weihengtech.ecos.model.vos.bind.EnestBindDeviceVO;
import com.weihengtech.ecos.service.bind.EnestBindService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Enest绑定实现逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/16 14:36
 */
@Service
@Slf4j
public class EnestBindServiceImpl extends BindServiceImpl implements EnestBindService {

    @Resource
    private HubService hubService;

    @Override
    public Long netDeviceBind(EnestBindDeviceVO bindParam) {
        return super.netDeviceBind(bindParam);
    }

    /** 配网绑定设备 */
    @Override
    protected <T extends BindDeviceVO> Long netDeviceBind(T bindParam, ClientUserDo userInfo) {
        EnestBindDeviceVO param = (EnestBindDeviceVO) bindParam;
        param.buildSourceParam();
        // 1、设备加速
        speedupDevice(param.getWifiSn(), String.valueOf(param.getType()));
        // 2、获取设备Sn
        String deviceSn = getDeviceSnByWifiSn(param.getWifiSn(), String.valueOf(param.getType()));
        // 3、获取设备详情
        HybridSinglePhaseDO deviceInfo = getDeviceInfoBySn(deviceSn);
        // 4、校验是否已经被绑定
        checkAlreadyBound(deviceInfo, userInfo);
        // 5、根据WiFi Sn查询设备IP
        String ip = getDeviceIpByWifiSn(param.getWifiSn(), String.valueOf(param.getType()));
        // 6、执行绑定逻辑
        bindDeviceAction(deviceInfo, param, userInfo, ip, null);
        return deviceInfo.getId();
    }

}
