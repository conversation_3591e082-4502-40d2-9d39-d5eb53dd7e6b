package com.weihengtech.ecos.service.thirdpart;

import com.weihengtech.ecos.api.pojo.dtos.GlobalDeviceConfigDto;
import com.weihengtech.ecos.api.pojo.dtos.GuideAgreementVersionDto;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundDTO;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.api.pojo.vos.EcosDeviceConfigQueryVo;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.global.ResourceCategoryTreeDto;
import com.weihengtech.ecos.model.dtos.charger.V2ClientChargeRecordDto;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordSaveVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordUpdateVo;

import java.util.List;

public interface HubService {

    /**
     * 通过id获取设备对象
     */
    HybridSinglePhaseDO getById(Long id);

    /**
     * 通过id批量获取设备对象
     */
    List<HybridSinglePhaseDO> getBatchById(Boolean isNeedExt, List<Long> ids);

    /**
     * 通过deviceName获取设备对象
     */
    HybridSinglePhaseDO getByDeviceName(String deviceName);

    /**
     * 获取隐私协议
     */
    GuideAgreementVersionDto getAgreementVersion();

    /**
     * 获取全局配置
     */
    DataResponse<GlobalDeviceConfigDto> queryDeviceConfig(EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo);

    /**
     * 获取代理商id
     */
    DataResponse<String> getAgentId(String deviceId);

    /**
     * 获取代理商id
     */
    List<BindInfoDTO> getAgentsByIds(List<Long> ids);

    /**
     * 加速设备
     */
    void speedupOnce(String deviceId);

    /**
     * 其他绑定的设备
     */
    List<HybridSinglePhaseDO> listOtherBindDevice(String wifiSn, String deviceName);

    /**
     * 保存新设备
     */
    void save(HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 更新设备
     */
    void updateById(HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 保存新插座
     */
    void saveSocket(HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 更新插座
     */
    void updSocket(HybridSinglePhaseDO hybridSinglePhaseDO);

    /**
     * 保存新充电桩
     */
    void saveCharger(ChargerSaveVO chargerSaveVO);

    /**
     * 更新充电桩
     */
    void updCharger(ChargerSaveVO chargerSaveVO);

    /**
     * 现在绑定的设备
     */
    List<HybridSinglePhaseDO> nowBindDeviceList(String wifiSn);

    /**
     * ecos最新版本
     */
    GlobalVersionDto ecosLatestVersion();

    /**
     * enest最新版本
     */
    GlobalEnestLatestVersionDto enestLatestVersion();

    /**
     * 更新系统转移时间
     *
     * @param systemId 系统id
     */
    void updateSysTransTime(Long systemId, Integer saveDeviceTime);

    /**
     * 获取资源类型树
     *
     */
    List<ResourceCategoryTreeDto> resourceCategoryTree();

    PageInfoDTO<V2ClientChargeRecordDto> pageChargeRecord(V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo);

    V2ClientChargeRecordDto queryLastRecord(Long deviceId, Boolean isFilterCharing);

    void saveChargeRecord(V2ClientChargeRecordSaveVo v2ClientChargeRecordSaveVo);

    void updChargeRecord(V2ClientChargeRecordUpdateVo v2ClientChargeRecordUpdateVo);

    void updChargerExtInfo(ExtInfoDto param);

    void boundInstall(InstallBoundDTO item);

    InstallBoundInfoDTO getBindInstallInfo(String deviceId);

    DynamicExportDTO dynamicExport(String deviceName);

    DynamicDesignDTO designInfo(String deviceName);

    void dynamicSave(DynamicSaveVO param);

    DynamicTestDTO dynamicTest(String deviceName);

    void dynamicSwitch(DynamicSwitchVO param);
}
