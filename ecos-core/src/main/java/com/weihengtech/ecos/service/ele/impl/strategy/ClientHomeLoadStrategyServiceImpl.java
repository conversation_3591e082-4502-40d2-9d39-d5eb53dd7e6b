package com.weihengtech.ecos.service.ele.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleHomeStrategyPreviewVO;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.enums.ele.ChargeTypeEnum;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.AsyncResultUtil;
import com.weihengtech.ecos.utils.EleStrategyUtil;
import com.weihengtech.ecos.utils.ElectricityPriceTypeUtil;
import com.weihengtech.ecos.utils.InitUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/1 14:21
 */
@Service
public class ClientHomeLoadStrategyServiceImpl implements ClientEleStrategyService {

    @Resource
    private HubService hubService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public List<EleStrategyDTO> calStrategy(List<EleDayAheadPriceDto> elePriceList, EleStrategyPreviewVO param) {
        ClientDefStrategyServiceImpl defStrategyService = InitUtil.getBean(ClientDefStrategyServiceImpl.class);
        List<EleStrategyDTO> eleStrategyRes = defStrategyService.calStrategy(elePriceList, param);
        // 如果开启了家庭负载优化策略，则加入家庭负载功率逻辑
        Map<Integer, Integer> eleHomePowerRes = homePowerGraph(
                EleHomeStrategyPreviewVO.builder()
                        .deviceId(param.getDeviceId())
                        .time(param.getTime())
                        .timezone(param.getTimezone())
                        .dayType(param.getDayType()).build());
        // 判断电价类型，决定不同的负电价阈值
        ElePriceTypeDetailEnum typeDetailEnum = ElectricityPriceTypeUtil.detectPriceType(elePriceList);
        int continuousPeriodThreshold = 3;
        if (ElePriceTypeDetailEnum.INTERVAL_15MIN == typeDetailEnum) {
            continuousPeriodThreshold = 12;
        } else if (ElePriceTypeDetailEnum.INTERVAL_30MIN == typeDetailEnum) {
            continuousPeriodThreshold = 6;
        }
        calStrategyWithHomePower(eleStrategyRes, eleHomePowerRes, param.getTimezone(), continuousPeriodThreshold);
        return eleStrategyRes;
    }

    public Map<Integer, Integer> homePowerGraph(EleHomeStrategyPreviewVO param) {
        HybridSinglePhaseDO deviceInfo = hubService.getById(param.getDeviceId());
        // 1、计算起始时间，今天和明天都按照今天起始算（由于今日没结束，计算明日的策略无法将今天的数据纳入）
        String timezoneStr = param.getTimezone();
        Long startTime = -1 == param.getTime() ? TimeUtil.getLastDayStart(timezoneStr) : TimeUtil.getDayStart(0, timezoneStr);
        // 2、计算过去10个工作日时间区间，如果是周末，则计算过去5个周末
        List<Pair<Long, Long>> backDaysList;
        if (0 == param.getDayType()) {
            backDaysList = TimeUtil.calWeekendDays(startTime, timezoneStr, 5);
        } else {
            backDaysList = TimeUtil.calWorkDays(startTime, timezoneStr, 10);
        }
        // 3、获取各个时间区间的家庭功率数据，每个时间区间24个点（降采样取平均值）
        List<Map<Integer, Integer>> allDayPowers = AsyncResultUtil.multiThreadDone(backDaysList,
                k -> queryHomeLoaderPower(deviceInfo, k, timezoneStr), threadPoolTaskExecutor);
        if (allDayPowers.stream().allMatch(CollUtil::isEmpty)) {
            return Collections.emptyMap();
        }
        // 4、每个点按照10个工作日中同一时间的点计算平均值
        return EleStrategyUtil.calAverage(allDayPowers);
    }

    private void calStrategyWithHomePower(List<EleStrategyDTO> eleStrategyRes,
                                          Map<Integer, Integer> eleHomePowerRes,
                                          String timezone,
                                          int continuousPeriodThreshold) {
        if (CollUtil.isEmpty(eleHomePowerRes)) {
            return;
        }
        // 找出连续2小时放电以上的时间段
        List<List<EleStrategyDTO>> dischargeList = new ArrayList<>();
        List<EleStrategyDTO> partial = new ArrayList<>();
        int i = 0, j = 0;
        while (i < eleStrategyRes.size() && j <= eleStrategyRes.size()) {
            if (j == eleStrategyRes.size()) {
                if (j - i > continuousPeriodThreshold) {
                    dischargeList.add(partial);
                }
                if (!partial.isEmpty()) {
                    partial.clear();
                }
                i = j;
                continue;
            }
            EleStrategyDTO point = eleStrategyRes.get(j);
            if (point.getChargeType() == ChargeTypeEnum.DISCHARGE.getType()) {
                partial.add(point);
                j++;
            }else {
                if (j != i) {
                    if (j - i > continuousPeriodThreshold) {
                        dischargeList.add(new ArrayList<>(partial));
                    }
                    if (!partial.isEmpty()) {
                        partial.clear();
                    }
                    i = j;
                } else {
                    i ++;
                    j ++;
                }
            }
        }
        if (CollUtil.isEmpty(dischargeList)) {
            return;
        }
        Map<Long, Boolean> dischargeMap = calDischargeFlag(dischargeList, eleHomePowerRes, timezone);
        eleStrategyRes.forEach(k -> {
            if (dischargeMap.containsKey(k.getStartTimeUnix()) && !dischargeMap.get(k.getStartTimeUnix())) {
                k.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                k.setPower(0);
            }
            k.setHomePower(eleHomePowerRes.get(TimeUtil.calCurHour(k.getStartTimeUnix(), timezone)));
        });
    }

    /** 计算放电区间中，满足家庭负载条件的区间，满足条件的设置true，其他false */
    private Map<Long, Boolean> calDischargeFlag(List<List<EleStrategyDTO>> dischargeList,
                                                Map<Integer, Integer> homePowerMap, String timezone) {
        Map<Long, Boolean> dischargeMap = new HashMap<>();
        for (List<EleStrategyDTO> eleStrategyList : dischargeList) {
            boolean allContains = eleStrategyList.stream().allMatch(i ->
                    homePowerMap.containsKey(TimeUtil.calCurHour(i.getStartTimeUnix(), timezone)));
            if (!allContains) {
                continue;
            }
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                eleStrategy.setHomePower(homePowerMap.get(TimeUtil.calCurHour(eleStrategy.getStartTimeUnix(), timezone)));
            }
            eleStrategyList.sort(Comparator.comparing(EleStrategyDTO::getHomePower));
            for (int i = 0; i < eleStrategyList.size(); i++) {
                EleStrategyDTO eleStrategy = eleStrategyList.get(i);
                if (i == eleStrategyList.size() - 2 || i == eleStrategyList.size() - 1) {
                    dischargeMap.put(eleStrategy.getStartTimeUnix(), true);
                } else {
                    dischargeMap.put(eleStrategy.getStartTimeUnix(), false);
                }
            }
        }
        return dischargeMap;
    }

    /** 查询每日的家庭负载功率<小时数，功率值> */
    private Map<Integer, Integer> queryHomeLoaderPower(HybridSinglePhaseDO deviceInfo, Pair<Long, Long> timePair, String timezone) {
        // 查询24小时降采样的相关点位的功率数据
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(deviceInfo);
        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.meanQuery(deviceInfo.getDeviceSn(), CommonConstants.TH_REALTIME_POWER_V2,
                timePair.getKey(), timePair.getValue(), 60);
        // 多个点位计算出家庭负载功率
        return packageNowDeviceRealtimeResult(result, timezone);

    }

    /** 多个点位计算出家庭负载功率<小时数，功率值> */
    public Map<Integer, Integer> packageNowDeviceRealtimeResult(Map<String, LinkedHashMap<Long, Object>> metricMap, String timezone) {

        List<Long> sortedTimeList = new ArrayList<>();
        List<Object> meterValueList = new ArrayList<>();
        List<Object> solarValueList1 = new ArrayList<>();
        List<Object> solarValueList2 = new ArrayList<>();
        List<Object> batteryValueList = new ArrayList<>();
        for (String metric : metricMap.keySet()) {
            LinkedHashMap<Long, Object> resultMap = metricMap.get(metric);
            switch (metric) {
                case TsdbMetricsConstants.METER_P_PV:
                    solarValueList1 = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_PV_P:
                    solarValueList2 = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.BAT_P:
                    batteryValueList = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_P:
                    sortedTimeList = resultMap.keySet().stream().sorted().collect(Collectors.toList());
                    meterValueList = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                default:
            }
        }
        if (CollUtil.isEmpty(sortedTimeList)) {
            return Collections.emptyMap();
        }
        Map<Integer, Integer> homePowerDps = new HashMap<>();
        // 家庭用电功率:meter_p(电网) + meter_p_pv(光伏) - bat_p(电池)
        for (int i = 0; i < sortedTimeList.size(); i++) {
            Long time = sortedTimeList.get(i);
            BigDecimal solarValue1 = parseListValue(i, solarValueList1);
            BigDecimal solarValue2 = parseListValue(i, solarValueList2);
            BigDecimal solarValue = cn.hutool.core.util.NumberUtil.add(solarValue1, solarValue2);
            BigDecimal meterValue = parseListValue(i, meterValueList);
            BigDecimal batteryValue = parseListValue(i, batteryValueList);
            BigDecimal homePower = cn.hutool.core.util.NumberUtil.sub(cn.hutool.core.util.NumberUtil.add(meterValue, solarValue), batteryValue);
            // 时序数据库算出的数据是从1：00-23:59，只需要减去1s，求出当前小时数，即可调整为00:00-23:00
            if (homePower.intValue() > 0) {
                homePowerDps.put(TimeUtil.calCurHour(time-1, timezone), homePower.intValue());
            }
        }
        return homePowerDps;
    }

    /** 解析点位值数据 */
    private BigDecimal parseListValue(Integer i, List<Object> list) {
        return new BigDecimal(i > list.size() - 1 ? "0" : Optional.ofNullable(list.get(i)).orElse("0").toString());
    }
}