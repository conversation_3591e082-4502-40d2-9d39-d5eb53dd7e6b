package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.consts.RequestConstants;
import com.weihengtech.ecos.dao.ClientElePriceRetailerMapper;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;
import com.weihengtech.ecos.model.vos.price.TibberEleVO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.ele.ClientElePriceRetailerService;
import okhttp3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class ClientElePriceRetailerServiceImpl extends ServiceImpl<ClientElePriceRetailerMapper, ClientElePriceRetailerDO> implements ClientElePriceRetailerService, HomeElePriceService {

    @Value("${custom.url.tibber:https://api.tibber.com/v1-beta/gql}")
    private String tibberUrl;

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private ClientHomeService clientHomeService;
    @Resource
    private RedisTemplate<String, List<EleDayAheadPriceDto>> redisTemplate;

    @Override
    public ClientElePriceRetailerDO queryRetailerPrice(String homeId) {
        return getOne(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .eq(ClientElePriceRetailerDO::getHomeId, homeId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRetailerPrice(ElePriceRetailerVO param) {
        ClientElePriceRetailerDO exitsOne = queryRetailerPrice(param.getHomeId());
        if (exitsOne == null) {
            save(ClientElePriceRetailerDO.builder()
                    .homeId(Long.parseLong(param.getHomeId()))
                    .token(param.getToken())
                    .retailer(param.getRetailer())
                    .retailerHomeId(param.getRetailerHomeId())
                    .build());
        } else {
            BeanUtils.copyProperties(param, exitsOne);
            updateById(exitsOne);
        }
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(param.getHomeId());
        if (homeItem != null && !ElePriceTypeEnum.RETAIL.getCode().equals(homeItem.getElePriceType())) {
            homeItem.setElePriceType(ElePriceTypeEnum.RETAIL.getCode());
            clientHomeService.updateById(homeItem);
        }
        // 更新家庭币种
        if (homeItem != null && param.getCurrency() != null) {
            homeItem.setCurrency(param.getCurrency());
            clientHomeService.updateById(homeItem);
        }
        // 清除当日成本缓存数据
        clearCurDayCostData(Long.parseLong(param.getHomeId()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRetailerPrice(String homeId) {
        remove(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .eq(ClientElePriceRetailerDO::getHomeId, homeId));
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(homeId);
        if (homeItem != null && ElePriceTypeEnum.RETAIL.getCode().equals(homeItem.getElePriceType())) {
            clientHomeService.update(Wrappers.<ClientHomeDo>lambdaUpdate()
                    .set(ClientHomeDo::getElePriceType, null)
                    .eq(ClientHomeDo::getId, homeItem.getId()));
        }


    }

    @Override
    public List<TibberHomeDTO> queryTibberHomes(String token) {
        // 1. 构建请求体
        RequestBody body = RequestBody.create(RequestConstants.TIBBER_HOME_PARAM,
                MediaType.parse("application/json; charset=utf-8"));
        // 2. 构建请求
        Request request = new Request.Builder()
                .url(tibberUrl)
                .post(body)
                .header("Authorization", "Bearer " + token)
                .build();
        // 3. 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the tibber home list failed");
                return Collections.emptyList();
            }
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(i -> log.error(i));
                return Collections.emptyList();
            }
            return Optional.ofNullable(resJson.getJSONObject("data"))
                            .map(i -> i.getJSONObject("viewer"))
                                    .map(i -> i.getJSONArray("homes"))
                                            .map(i -> i.toList(TibberHomeDTO.class))
                                                    .orElse(Collections.emptyList());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<EleDayAheadPriceDto> queryTibberEle(TibberEleVO param) {
        // 1. 构建请求体
        RequestBody body = RequestBody.create(String.format(RequestConstants.TIBBER_ELE_PARAM, param.getTibberHomeId(),
                        1 == param.getTime() ? "tomorrow" : "today"),
                MediaType.parse("application/json; charset=utf-8"));
        // 2. 构建请求
        Request request = new Request.Builder()
                .url(tibberUrl)
                .post(body)
                .header("Authorization", "Bearer " + param.getToken())
                .build();
        // 3. 发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the tibber ele list failed");
                return Collections.emptyList();
            }
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(i -> log.error(i));
                return Collections.emptyList();
            }
            return Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("viewer"))
                    .map(i -> i.getJSONObject("home"))
                    .map(i -> i.getJSONObject("currentSubscription"))
                    .map(i -> i.getJSONObject("priceInfo"))
                    .map(i -> i.getJSONArray("today"))
                    .map(i -> i.toList(JSONObject.class))
                    .map(i -> i.stream()
                            .map(j -> EleDayAheadPriceDto.builder()
                                    .currency(j.getStr("currency"))
                                    .average(j.getBigDecimal("total"))
                                    .tax(j.getBigDecimal("tax"))
                                    .startTimeUnix(ZonedDateTime.parse(j.getStr("startsAt")).toInstant().getEpochSecond())
                                    .build())
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ClientElePriceRetailerDO> queryAllTypeHomePriceInfo() {
        List<ClientHomeDo> homeList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
                .eq(ClientHomeDo::getElePriceType, ElePriceTypeEnum.RETAIL.getCode()));
        if (CollUtil.isEmpty(homeList)) {
            return Collections.emptyList();
        }
        List<Long> homeIdList = homeList.stream().map(ClientHomeDo::getId).collect(Collectors.toList());
        return list(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .in(ClientElePriceRetailerDO::getHomeId, homeIdList));
    }

    @Override
    public Object queryEleInfoOrConfig(String homeId) {
        return queryRetailerPrice(homeId);
    }

    @Override
    public List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone) {
        ClientElePriceRetailerDO config = queryRetailerPrice(homeId);
        return queryTibberEle(TibberEleVO.builder()
                .token(config.getToken())
                .tibberHomeId(config.getRetailerHomeId())
                .time(time)
                .build());
    }

    @Override
    public Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String userTimezone) {
        List<EleDayAheadPriceDto> priceList;
        if (time == -1) {
            priceList = redisTemplate.opsForValue().get(homeId);
        } else if (time == 0) {
            priceList = queryOriginalElePrice(homeId, time, userTimezone);
        } else {
            return Collections.emptyMap();
        }
        if (CollUtil.isEmpty(priceList)) {
            return Collections.emptyMap();
        }
        if (priceList.size() == 96) {
            return priceList.stream().collect(Collectors.toMap(EleDayAheadPriceDto::getStartTimeUnix,
                    i -> ElePriceDetailDTO.builder()
                            .purchasePrice(i.getAverage())
                            .purchaseTax(i.getTax())
                            .feedInPrice(i.getAverage())
                            .build()));
        }
        Map<Long, ElePriceDetailDTO> resMap = new HashMap<>();
        for (EleDayAheadPriceDto hourPrice : priceList) {
            resMap.put(hourPrice.getStartTimeUnix(), ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 15, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 30, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 45, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
        }
        return resMap;
    }
}
