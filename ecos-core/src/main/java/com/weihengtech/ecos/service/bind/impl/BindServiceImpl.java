package com.weihengtech.ecos.service.bind.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.pojo.vos.TuyaDeviceSpeedupVo;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.vos.bind.BindDeviceVO;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 通用绑定逻辑实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/7 14:04
 */
@Slf4j
public abstract class BindServiceImpl {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private RetryService retryService;
    @Resource
    private HubService hubService;
    @Resource
    private ClientHomeUserService clientHomeUserService;
    @Resource
    private ClientHomeDeviceService clientHomeDeviceService;
    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;

    /**
     * 绑定通用加锁逻辑，防止高并发重复操作
     *
     * @param bindParam 绑定入参
     * @param <T> homeId
     * @return 设备Id
     */
    public <T extends BindDeviceVO> Long netDeviceBind(T bindParam) {
        ClientUserDo userInfo = SecurityUtil.getClientUserDo();
        String wifiSn = bindParam.getWifiSn();

        RLock lock = redissonClient.getLock(wifiSn);
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    return netDeviceBind(bindParam, userInfo);
                } catch (Exception e) {
                    log.error(String.format("wifiSn:%s, userId:%s, lon:%s, lat:%s bind failure",
                            wifiSn, userInfo.getUsername(), bindParam.getLon(), bindParam.getLat()), e);
                    throw e;
                } finally {
                    lock.unlock();
                }
            }else {
                throw new EcosException(EcosExceptionEnum.DEVICE_BIND_TIME_LIMIT_ERROR);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 绑定逻辑模板方法
     *
     * @param bindParam 绑定入参
     * @param userInfo 用户信息
     * @param <T> 继承自通用入参
     * @return 设备Id
     */
    protected abstract <T extends BindDeviceVO> Long netDeviceBind(T bindParam, ClientUserDo userInfo);

    /** 设备加速 */
    protected void speedupDevice(String wifiSn, String type) {
        if (DeviceTypeInfoEnum.ELINK.getDatasource() == Integer.parseInt(type)) {
            // 易联设备加速是必要条件
            try {
                ecosIotApi.speedupDevice(TuyaDeviceSpeedupVo.builder().deviceId(wifiSn).build(), type);
            } catch (Exception e) {
                log.error("device speedup failed: {}", wifiSn);
                throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
            }
        } else {
            // 其他类型设备异步加速
            ThreadUtil.execAsync(() -> retryService.speedUpDevice(wifiSn, type));
        }
    }

    /** 根据设备Sn获取设备信息 */
    protected HybridSinglePhaseDO getDeviceInfoBySn(String deviceSn) {
        HybridSinglePhaseDO deviceInfo;
        try {
            deviceInfo = hubService.getByDeviceName(deviceSn);
        } catch (Exception e) {
            log.error("device not exists: {}", deviceSn);
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
        return deviceInfo;
    }

    /** 根据wifiSn获取设备IP */
    protected String getDeviceIpByWifiSn(String wifiSn, String type) {
        try {
            return ecosIotApi.getDeviceIpByWifiSn(wifiSn, String.valueOf(type));
        } catch (Exception e) {
            log.error("getDeviceIpByWifiSn failed: {}, type: {}", wifiSn, type);
            return StrUtil.EMPTY;
        }
    }

    /** 透传获取逆变器Sn */
    protected String getDeviceSnByWifiSn(String wifiSn, String type) {
        String deviceSn = ecosIotApi.getDeviceSnByWifiSn(wifiSn, type);
        if (StrUtil.isBlank(deviceSn)) {
            log.error("getDeviceSnByWifiSn is blank : {}", wifiSn);
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
        log.info("bind installer UserDevice deviceSn: {}", deviceSn);
        return deviceSn;
    }

    /** 校验是否已经被绑定 */
    protected void checkAlreadyBound(HybridSinglePhaseDO deviceInfo, ClientUserDo userInfo) {
        MiddleClientUserDeviceDo masterDevice = middleClientUserDeviceService.getOne(
                Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, deviceInfo.getId())
                        .eq(MiddleClientUserDeviceDo::getMaster, 1));
        if (masterDevice == null) {
            return;
        }
        if (masterDevice.getUserId().equals(userInfo.getId())) {
            log.error("{} 已经绑定了该设备：{}", userInfo.getEmail(), deviceInfo.getDeviceSn());
            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
        } else {
            log.error("{} 从账号绑定错误: {}", userInfo.getEmail(), deviceInfo.getDeviceSn());
            throw new EcosException(EcosExceptionEnum.SLAVE_BIND_ERROR);
        }
    }

    /** 执行绑定逻辑 */
    protected <T extends BindDeviceVO> void bindDeviceAction(HybridSinglePhaseDO deviceInfo, T bindParam, ClientUserDo userInfo, String ip, String homeId) {
        try {
            // 更新设备信息
            updateDeviceInfo(deviceInfo, bindParam, userInfo, ip);
            // 将设备与操作人建立关系
            middleClientUserDeviceService.saveMasterBind(userInfo.getId(), deviceInfo.getId(), deviceInfo.getDeviceName());
            // 将设备与家庭建立关系
            buildHomeDeviceRel(homeId, String.valueOf(userInfo.getId()), String.valueOf(deviceInfo.getId()), deviceInfo.getDeviceName());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
    }

    /** 将设备与家庭建立关系 */
    private void buildHomeDeviceRel(String homeId, String userId, String deviceId, String deviceName) {
        if (StrUtil.isBlank(homeId)) {
            return;
        }
        clientHomeUserService.checkOwner(userId, homeId);
        clientHomeDeviceService.saveRelation(homeId, deviceId, deviceName);
    }

    /** 更新设备信息 */
    private <T extends BindDeviceVO> void updateDeviceInfo(HybridSinglePhaseDO deviceInfo, T bindParam, ClientUserDo userInfo,
                                                           String ip) {
        deviceInfo.setDatacenterId(userInfo.getDatacenterId());
        deviceInfo.setIp(ip);
        deviceInfo.setWifiSn(bindParam.getWifiSn());
        Long firstInstall = deviceInfo.getFirstInstall();
        if (firstInstall == null || firstInstall < 100) {
            deviceInfo.setFirstInstall(System.currentTimeMillis());
        }
        deviceInfo.setDataSource(bindParam.getDataSource());
        deviceInfo.setTsdbSource(bindParam.getTsdbSource());
        deviceInfo.setLongitude(bindParam.getLon());
        deviceInfo.setLatitude(bindParam.getLat());
        deviceInfo.setUpdateTime(LocalDateTime.now());
        hubService.updateById(deviceInfo);

        List<HybridSinglePhaseDO> beforeWifiBindDevice = hubService.listOtherBindDevice(bindParam.getWifiSn(), deviceInfo.getDeviceSn());
        if (CollUtil.isNotEmpty(beforeWifiBindDevice)) {
            beforeWifiBindDevice.forEach(deviceDo -> {
                deviceDo.setWifiSn("");
                deviceDo.setUpdateTime(LocalDateTime.now());
                hubService.updateById(deviceDo);
            });
        }
    }
}
