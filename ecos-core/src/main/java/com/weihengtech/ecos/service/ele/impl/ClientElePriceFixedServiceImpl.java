package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.dao.ClientElePriceFixedMapper;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceFixedDO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceFixedVO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.ele.ClientElePriceFixedService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
public class ClientElePriceFixedServiceImpl extends ServiceImpl<ClientElePriceFixedMapper, ClientElePriceFixedDO> implements ClientElePriceFixedService, HomeElePriceService {

    @Resource
    private ClientHomeService clientHomeService;

    @Override
    public ClientElePriceFixedDO queryFixedPrice(String homeId) {
        return getOne(Wrappers.<ClientElePriceFixedDO>lambdaQuery()
                .eq(ClientElePriceFixedDO::getHomeId, homeId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFixedPrice(ElePriceFixedVO param) {
        ClientElePriceFixedDO exitsOne = queryFixedPrice(param.getHomeId());
        if (exitsOne == null) {
            // 保存电价
            ClientElePriceFixedDO item = new ClientElePriceFixedDO();
            BeanUtils.copyProperties(param, item);
            item.setHomeId(Long.valueOf(param.getHomeId()));
            save(item);
        } else {
            BeanUtils.copyProperties(param, exitsOne);
            updateById(exitsOne);
        }
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(param.getHomeId());
        if (homeItem != null && !ElePriceTypeEnum.FIXED.getCode().equals(homeItem.getElePriceType())) {
            // 如果原来的家庭电价类型为批发电价、零售商电价，则关联的设备都需要关闭自动策略
            closeAutoStrategy(homeItem);
            homeItem.setElePriceType(ElePriceTypeEnum.FIXED.getCode());
            clientHomeService.updateById(homeItem);
        }
        // 清除当日成本缓存数据
        clearCurDayCostData(Long.parseLong(param.getHomeId()));
    }

    @Override
    public Object queryEleInfoOrConfig(String homeId) {
        return queryFixedPrice(homeId);
    }

    @Override
    public Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String userTimezone) {
        if (StrUtil.isBlank(userTimezone)) {
            return Collections.emptyMap();
        }
        ClientElePriceFixedDO priceInfo = queryFixedPrice(homeId);
        if (priceInfo == null) {
            return Collections.emptyMap();
        }
        ElePriceDetailDTO singlePriceInfo = ElePriceDetailDTO.builder()
                .purchasePrice(priceInfo.getPurchasePrice())
                .purchaseTax(priceInfo.getPurchaseTax())
                .feedInPrice(priceInfo.getFeedInPrice())
                .build();
        List<Long> timeList = cal15MinDayTime(time, userTimezone);
        return timeList.stream()
                .collect(Collectors.toMap(Function.identity(), i -> singlePriceInfo));
    }

    @Override
    public List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone) {
        return Collections.emptyList();
    }

    @Override
    public List<ClientElePriceFixedDO> queryAllTypeHomePriceInfo() {
        List<ClientHomeDo> homeList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
                .eq(ClientHomeDo::getElePriceType, ElePriceTypeEnum.FIXED.getCode()));
        if (CollUtil.isEmpty(homeList)) {
            return Collections.emptyList();
        }
        List<Long> homeIdList = homeList.stream().map(ClientHomeDo::getId).collect(Collectors.toList());
        return list(Wrappers.<ClientElePriceFixedDO>lambdaQuery()
                .in(ClientElePriceFixedDO::getHomeId, homeIdList));
    }
}
