package com.weihengtech.ecos.service.charger.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientChargeCardMapper;
import com.weihengtech.ecos.model.dos.ClientChargeCardDo;
import com.weihengtech.ecos.service.charger.ClientChargeCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @program: ecos-server
 * @description: 充电桩绑定卡片服务实现类
 * @author: jiahao.jin
 * @create: 2024-03-01 10:09
 **/
@Service
@RequiredArgsConstructor
public class ClientChargeCardServiceImpl
        extends ServiceImpl<ClientChargeCardMapper, ClientChargeCardDo>
        implements ClientChargeCardService {
}
