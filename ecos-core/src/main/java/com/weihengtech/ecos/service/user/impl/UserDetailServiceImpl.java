package com.weihengtech.ecos.service.user.impl;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.dao.ClientUserMapper;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.model.bos.user.ClientUserDetails;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserDetailServiceImpl implements UserDetailsService {

	@Resource
	private ClientUserMapper clientUserMapper;

	@Resource
	private ClientUserRoleService clientUserRoleService;

	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		return Optional
				.ofNullable(clientUserMapper
						.selectOne(Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getUsername, username)))
				.map(user -> {
					List<String> roles = clientUserRoleService.queryRoleListByClientUserId(user.getId());
					ClientUserDetails clientUserDetails = new ClientUserDetails();
					CglibUtil.copy(user, clientUserDetails);
					clientUserDetails.setAuthorities(roles);
					return clientUserDetails;
				}).orElseThrow(() -> {
					log.warn("账号名密码错误");
					return new EcosException(EcosExceptionEnum.INVALID_USERNAME_OR_PASSWORD);
				});
	}
}
