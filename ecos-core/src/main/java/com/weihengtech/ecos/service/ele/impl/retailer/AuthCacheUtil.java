package com.weihengtech.ecos.service.ele.impl.retailer;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.model.vos.price.OctopusAuthVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/22 14:28
 */
@Service
@Slf4j
public class AuthCacheUtil {

    @Value("${custom.url.octopus:https://api.octopus.energy/v1/graphql/}")
    private String octopusUrl;
    @Value("${custom.url.flat-peak:https://api.flatpeak.com}")
    private String flatPeakUrl;
    @Value("${flat-peak.account:acc_685e60871ef3a0c6b25282a7}")
    private String flatPeakAccount;
    @Value("${flat-peak.apikey:****************************************}")
    private String flatPeakApikey;

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取Octopus认证token
     * @param apiKey API密钥
     * @return 认证token
     */
    public String getOctopusToken(String apiKey) {
        if (StrUtil.isBlank(apiKey)) {
            return null;
        }
        
        String cacheKey = "octopus:token:" + apiKey;
        
        try {
            String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotBlank(cachedToken)) {
                return cachedToken;
            }
            
            Pair<String, Long> tokenPair = getOctopusTokenRemote(apiKey);
            if (tokenPair == null || StrUtil.isBlank(tokenPair.getKey())) {
                return null;
            }
            
            String token = tokenPair.getKey();
            Long expiryTimestamp = tokenPair.getValue();
            
            if (expiryTimestamp != null && expiryTimestamp > 0) {
                long currentTime = ZonedDateTime.now(ZoneId.systemDefault()).toEpochSecond();
                long ttl = Math.max(0, expiryTimestamp - currentTime);
                
                if (ttl > 0) {
                    stringRedisTemplate.opsForValue().set(cacheKey, token, ttl, TimeUnit.SECONDS);
                } else {
                    log.warn("Octopus token has already expired, expiry time: {}", expiryTimestamp);
                }
            } else {
                stringRedisTemplate.opsForValue().set(cacheKey, token, 3600, TimeUnit.SECONDS);
            }
            
            return token;
        } catch (Exception e) {
            log.error("Error getting octopus token for apiKey: {}", apiKey, e);
            return null;
        }
    }

    public String getFlatPeakToken() {
        String cacheKey = "flatpeak:token:" + flatPeakAccount;
        
        try {
            String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotBlank(cachedToken)) {
                return cachedToken;
            }
            
            Pair<String, Long> tokenPair = getAuthToken();
            if (tokenPair == null || StrUtil.isBlank(tokenPair.getKey())) {
                return null;
            }
            
            String token = tokenPair.getKey();
            Long expiresIn = tokenPair.getValue();
            
            if (expiresIn != null && expiresIn > 0) {
                stringRedisTemplate.opsForValue().set(cacheKey, token, expiresIn, TimeUnit.SECONDS);
            } else {
                stringRedisTemplate.opsForValue().set(cacheKey, token, 3600, TimeUnit.SECONDS);
            }
            
            return token;
        } catch (Exception e) {
            log.error("Error getting flatpeak token", e);
            return null;
        }
    }


    /**
     * 获取Octopus认证token，过期时间（秒级时间戳）
     * @param apiKey API密钥
     * @return 认证token
     */
    private Pair<String, Long> getOctopusTokenRemote(String apiKey) {
        // 步骤1：构建认证请求的输入参数
        OctopusAuthVO.Variable.Input input = OctopusAuthVO.Variable.Input.builder().APIKey(apiKey).build();
        OctopusAuthVO.Variable variable = OctopusAuthVO.Variable.builder().input(input).build();
        OctopusAuthVO authVO = new OctopusAuthVO();
        authVO.setVariables(variable);
        
        // 步骤2：构建请求体
        RequestBody body = RequestBody.create(JSONUtil.toJsonStr(authVO),
                MediaType.parse("application/json; charset=utf-8"));
        
        // 步骤3：构建HTTP请求
        Request request = new Request.Builder()
                .url(octopusUrl)
                .post(body)
                .build();
        
        // 步骤4：发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            // 步骤5：检查响应是否成功
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the octopus token failed");
                return null;
            }
            
            // 步骤6：解析响应JSON数据
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            // 步骤7：检查是否有错误信息
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(log::error);
                return null;
            }
            
            // 步骤8：提取并返回token
            String token = Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("obtainKrakenToken"))
                    .map(i -> i.getStr("token"))
                    .orElse(null);

            Long exp = Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("obtainKrakenToken"))
                    .map(i -> i.getJSONObject("payload"))
                    .map(i -> i.getLong("exp"))
                    .orElse(null);
            return Pair.of(token, exp);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取FlatPeak API认证token，过期时间段（单位秒，例如3600）
     *
     * @return bearer token
     */
    public Pair<String, Long> getAuthToken() {
        try {
            String credentials = flatPeakAccount + ":" + flatPeakApikey;
            String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

            Request request = new Request.Builder()
                    .url(flatPeakUrl + "/login")
                    .header("Authorization", "Basic " + encodedCredentials)
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject responseJson = JSONUtil.parseObj(responseBody);
                    return Pair.of(responseJson.getStr("bearer_token"), responseJson.getLong("expiresIn"));
                } else {
                    log.error("Failed to get token: {}", response.code());
                    throw new RuntimeException("Failed to get token from FlatPeak API");
                }
            }
        } catch (IOException e) {
            log.error("Error getting token from FlatPeak API", e);
            throw new RuntimeException("Error getting token from FlatPeak API", e);
        }
    }
}