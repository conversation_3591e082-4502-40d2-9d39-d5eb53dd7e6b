package com.weihengtech.ecos.service.global.impl.tsdb;

import cn.hutool.core.lang.Dict;
import com.influxdb.query.FluxTable;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.dtos.app.TsdbQueryDTO;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.influx.pojo.ResultWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * wh iot的tsdb服务实现
 *
 * <AUTHOR>
 * @date 2024/5/21 15:29
 * @version 1.0
 */
@Service(value = "tsdbService4")
@Slf4j
public class WhInfluxServiceImpl implements TimeSeriesDatabaseService {

	private static final List<String> EXTRA_COLUMN = Arrays.asList("result",
			"table", "_start", "_stop", "_time", "_measurement", "device_id", "device_sn", "gateway_sn");

	@Resource
	private EcosIotApi ecosIotApi;

	@Override
	public Dict lastPoint(String deviceSn, List<String> metricList, long endTime) {
		FluxTable fluxTable = ecosIotApi.queryIotLastPoint(TsdbQueryDTO.builder()
				.cloudId(deviceSn)
				.startTime(endTime / 1000 - 600)
				.endTime(endTime / 1000)
				.metricList(metricList)
				.build(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		Map<String, Object> map = ResultWrapper.convertFirstMapData(fluxTable);
		Dict dict = Dict.create();
		dict.putAll(map);
		return dict;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> lastConnectorStatusPoint(String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime) {
		return null;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(
			String deviceSn, List<String> metricList, long start,
			long end, TsdbSampleEnum tsdbSampleEnum
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);

		FluxTable fluxTable = ecosIotApi.queryIotDelta(TsdbQueryDTO.builder()
				.cloudId(deviceSn)
				.startTime(start / 1000)
				.endTime(end / 1000)
				.metricList(metricList)
				.times(tsdbSampleEnum.getDelta())
				.build(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		Map<String, LinkedHashMap<Long, Object>> map = ResultWrapper.convertMapData(fluxTable);
		EXTRA_COLUMN.forEach(map::remove);
		result.putAll(map);
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(
			String deviceSn, List<String> metricList, long start, long end, long times
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
		FluxTable fluxTable = ecosIotApi.queryIotWithSample(TsdbQueryDTO.builder()
				.cloudId(deviceSn)
				.startTime(start / 1000)
				.endTime(end / 1000)
				.metricList(metricList)
				.aggregateFunction("first")
				.times(times + "m")
				.build(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		result.putAll(ResultWrapper.convertMapData(fluxTable));
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(
			String deviceSn, List<String> metricList, long start, long end
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);

		FluxTable fluxTable = ecosIotApi.queryIotWithoutSample(TsdbQueryDTO.builder()
				.cloudId(deviceSn)
				.startTime(start / 1000)
				.endTime(end / 1000)
				.metricList(metricList)
				.build(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		result.putAll(ResultWrapper.convertMapData(fluxTable));
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> meanQuery(String deviceSn, List<String> metricList, long start, long end, long times) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
		FluxTable fluxTable = ecosIotApi.queryIotWithSample(TsdbQueryDTO.builder()
				.cloudId(deviceSn)
				.startTime(start / 1000)
				.endTime(end / 1000)
				.metricList(metricList)
				.aggregateFunction("mean")
				.times(times + "m")
				.build(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		result.putAll(ResultWrapper.convertMapData(fluxTable));
		return result;
	}
}
