package com.weihengtech.ecos.service.global.impl.tsdb;

import cn.hutool.core.lang.Dict;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.influx.pojo.req.BasicParamDTO;
import com.weihengtech.influx.pojo.req.SampleParamDTO;
import com.weihengtech.influx.service.FluxQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(value = "tsdbService2")
@Slf4j
public class ElinkInfluxServiceImpl implements TimeSeriesDatabaseService {

	private static final List<String> EXTRA_COLUMN = Arrays.asList("result",
			"table", "_start", "_stop", "_time", "_measurement", "device_id");

	@Value("${influxdb.elink.database}")
	private String influxdbDatabase;

	@Value("${influxdb.elink.table}")
	private String influxdbTable;

	@Resource
	private FluxQueryWrapper elinkFluxQueryWrapper;

	@Override
	public Dict lastPoint(String deviceSn, List<String> metricList, long endTime) {
		try {
			Map<String, Object> map = elinkFluxQueryWrapper.lastPoint(BasicParamDTO.builder()
					.database(influxdbDatabase)
					.table(influxdbTable)
					.deviceName(deviceSn)
					.metricList(metricList)
					.start(endTime / 1000L - 60*10)
					.end(endTime / 1000L)
					.build());
			Dict dict = Dict.create();
			dict.putAll(map);
			return dict;
		} catch (Exception e) {
			log.warn("Error querying InfluxDB: " + e.getMessage());
			return Dict.create();
		}
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> lastConnectorStatusPoint(String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime) {
		return null;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> deltaQuery(
            String deviceSn, List<String> metricList, long start,
            long end, TsdbSampleEnum tsdbSampleEnum
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
		try {
			Map<String, LinkedHashMap<Long, Object>> map = elinkFluxQueryWrapper.deltaQuery(SampleParamDTO.builder()
					.database(influxdbDatabase)
					.table(influxdbTable)
					.deviceName(deviceSn)
					.metricList(metricList)
					.start(start / 1000L)
					.end(end / 1000L)
					.timesStr(tsdbSampleEnum.getDelta())
					.isCreateEmpty(false)
					.build());
			EXTRA_COLUMN.forEach(map::remove);
			result.putAll(map);
		} catch (Exception e) {
			// 处理异常，如日志记录
			log.warn("Error querying InfluxDB: " + e.getMessage());
		}
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(
			String deviceSn, List<String> metricList,
			long start, long end, long times
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
		try {
			Map<String, LinkedHashMap<Long, Object>> map = elinkFluxQueryWrapper.withSampleQuery(SampleParamDTO.builder()
					.database(influxdbDatabase)
					.table(influxdbTable)
					.deviceName(deviceSn)
					.metricList(metricList)
					.start(start / 1000L)
					.end(end / 1000L)
					.times(times)
					.isCreateEmpty(false)
					.build());
			result.putAll(map);
		} catch (Exception e) {
			log.warn("Error querying InfluxDB: " + e.getMessage());
		}
		return result;
	}

	@Override
	public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(
			String deviceSn, List<String> metricList,
			long start, long end
	) {
		Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
		try {
			Map<String, LinkedHashMap<Long, Object>> map = elinkFluxQueryWrapper.withoutSampleQuery(BasicParamDTO.builder()
					.database(influxdbDatabase)
					.table(influxdbTable)
					.deviceName(deviceSn)
					.metricList(metricList)
					.start(start / 1000L)
					.end(end / 1000L)
					.build());
			result.putAll(map);
		} catch (Exception e) {
			log.warn("Error querying InfluxDB: " + e.getMessage());
		}
		return result;
	}

    @Override
    public Map<String, LinkedHashMap<Long, Object>> meanQuery(String deviceSn, List<String> metricList, long start, long end, long times) {
		return elinkFluxQueryWrapper.meanQuery(SampleParamDTO.builder()
				.database(influxdbDatabase)
				.table(influxdbTable)
				.deviceName(deviceSn)
				.metricList(metricList)
				.start(start / 1000L)
				.end(end / 1000L)
				.times(times)
				.isCreateEmpty(false)
				.build());
    }
}
