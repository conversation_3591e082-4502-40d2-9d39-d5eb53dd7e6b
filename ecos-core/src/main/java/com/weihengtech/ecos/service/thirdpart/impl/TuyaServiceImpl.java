package com.weihengtech.ecos.service.thirdpart.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.settings.SettingTransferDTO;
import com.weihengtech.ecos.model.vos.thirdpart.HomeMemberVo;
import com.weihengtech.ecos.model.vos.thirdpart.TuyaAddHomeMemberVo;
import com.weihengtech.ecos.service.global.TuyaDatacenterService;
import com.weihengtech.ecos.service.thirdpart.TuyaService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.sdk.iot.ecos.model.response.CloudHomeMembersResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对接tuya接口服务
 *
 * <AUTHOR>
 * @date 2023/8/28 9:25
 * @version 1.0
 */
@Service
@Slf4j
public class TuyaServiceImpl implements TuyaService {

    private final String cloud = "1";

    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private TuyaDatacenterService tuyaDatacenterService;

    @Override
    public void transTuyaHome(ClientUserDo userInfo, SettingTransferDTO param) {
        // 根据ecos用户账号获取涂鸦用户账号 数据中心代码+ecos账号
        String tuyaUsername = getTuyaUsername(userInfo);
        // 查询家庭信息
        List<CloudHomeMembersResponse> homeUserList = ecosIotApi.getHomeUserList(param.getHomeId(),cloud);
        log.info("homeUserList is {}", JSONUtil.toJsonStr(homeUserList));
        ActionFlagUtil.assertTrue(CollUtil.isNotEmpty(homeUserList));
        CloudHomeMembersResponse owner = homeUserList.stream()
                .filter(CloudHomeMembersResponse::getOwner)
                .findFirst()
                .orElseThrow(() -> new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE));
        if (tuyaUsername.equals(owner.getName())) {
            // 已经绑定成功，当前ecos用户已经是家庭owner
            return;
        }
        HomeMemberVo homeMember = HomeMemberVo.builder()
                .countryCode(param.getCountryCode())
                .memberAccount(tuyaUsername)
                .name(tuyaUsername)
                .admin(true)
                .build();
        TuyaAddHomeMemberVo addHomeMember = TuyaAddHomeMemberVo.builder()
                .appSchema(param.getAppSchema())
                .member(homeMember)
                .build();
        // 将当前用户添加到家庭成员
        Boolean addRes = ecosIotApi.addHomeMember(param.getHomeId(), addHomeMember, cloud);
        // 删除已有家庭所有者
        Boolean delRes = ecosIotApi.deleteHomeUser(param.getHomeId(), owner.getUid(), cloud);
        if (!addRes || !delRes) {
            // 如果执行失败，查询是否已经存在，如果已经存在则放行
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
    }


    /**
     * 根据ecos用户账号获取涂鸦用户账号 数据中心代码+ecos账号
     *
     * @param userInfo
     * @return
     */
    private String getTuyaUsername(ClientUserDo userInfo) {
        String datacenter = tuyaDatacenterService.getDatacenter(userInfo.getDatacenterId());
        ActionFlagUtil.assertTrue(StrUtil.isNotBlank(datacenter));
        return datacenter + userInfo.getUsername();
    }
}
