package com.weihengtech.ecos.service.app;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDTO;

import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 家庭表服务类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:22
 **/
public interface ClientHomeService extends IService<ClientHomeDo> {

    /**
     * 获取家庭电价信息
     *
     * @param homeId 家庭ID
     * @return
     */
    HomeElePriceDTO familyPriceInfo(String homeId);

    /**
     * 计算家庭15分钟电价数据
     *
     * @param homeInfo 家庭信息
     * @param time 查询时间，昨天：-1，今天：0，明天：1
     * @return
     */
    Map<Long, ElePriceDetailDTO> calHomeElePrice(ClientHomeDo homeInfo, Integer time, String userTimezone);

    /**
     * 查询开启了家庭电价的家庭
     *
     * @return
     */
    List<ClientHomeDo> queryHomePriceHomeList();
}
