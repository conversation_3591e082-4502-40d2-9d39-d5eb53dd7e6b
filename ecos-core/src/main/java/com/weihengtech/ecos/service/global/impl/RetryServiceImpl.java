package com.weihengtech.ecos.service.global.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.adapter.CustomizeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.pojo.dtos.DeviceBasicInfoDto;
import com.weihengtech.ecos.api.pojo.vos.TuyaDeviceSpeedupVo;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.global.DeviceTypeInfoEnum;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.RetryException;
import com.weihengtech.ecos.enums.charger.ChargerStatusEnum;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.sdk.iot.ecos.model.ocpp.response.CpStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDevicePropertyStatusResponse;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RetryServiceImpl implements RetryService {

	@Resource
	private EcosIotApi ecosIotApi;
	@Resource
	private ChargeStationService chargeStationService;
	@Resource
	private HubService hubService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private CustomizeAdapter customizeAdapter;

	@Override
	@Retryable(value = {RetryException.class}, backoff = @Backoff(delay = 10000, multiplier = 1))
	public Pair<String, String> getDeviceSn(Integer type, String wifiSn) {
		Pair<String, String> pair;
		String deviceSn;
		try {

			if (1 == type) {
				TuyaDeviceSpeedupVo tuyaDeviceSpeedupVo = new TuyaDeviceSpeedupVo();
				tuyaDeviceSpeedupVo.setDeviceId(wifiSn);
				ecosIotApi.speedupDevice(tuyaDeviceSpeedupVo, "1");
			}

			// 根据WiFi Sn查询设备IP
			String ip = ecosIotApi.getDeviceIpByWifiSn(wifiSn, String.valueOf(type));

			// 根据WiFi Sn查询设备SN
			deviceSn = ecosIotApi.getDeviceSnByWifiSn(wifiSn, String.valueOf(type));
			pair = new Pair<>(deviceSn, ip);
			log.info("bindClientUserDevice deviceSn: {}", deviceSn);
		} catch (Exception e) {
			throw new RetryException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
		}
		if (StrUtil.isBlank(deviceSn)) {
			throw new RetryException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
		}
		return pair;
	}

	@Override
	@Retryable(maxAttempts = 20, value = {RetryException.class}, backoff = @Backoff(delay =10000, multiplier = 1))
	public Pair<String, String> getSocketDeviceSn(Integer type, String wifiSn) {
		Pair<String, String> pair;
		String deviceSn;
		try {

			// 根据WiFi Sn查询设备IP
			String ip = ecosIotApi.getDeviceIpByWifiSn(wifiSn, String.valueOf(type));

			// 涂鸦插座
			DeviceBasicInfoDto deviceBasicInfo = ecosIotApi.getDeviceBasicInfo(wifiSn, String.valueOf(type));
			deviceSn = deviceBasicInfo.getUuid();
			pair = new Pair<>(deviceSn, ip);
			log.info("bindClientUserDevice deviceSn: {}", deviceSn);
		} catch (Exception e) {
			throw new RetryException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
		}
		if (StrUtil.isBlank(deviceSn)) {
			throw new RetryException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
		}
		return pair;
	}

	@Override
	@Retryable(maxAttempts = 20, value = {RetryException.class}, backoff = @Backoff(delay = 2000, multiplier = 1))
	public Boolean checkChargeStationStatus(String deviceId, Integer operateCode) {
		Integer status;
		try {
			status = chargeStationService.getChargeStationStatus(deviceId);
			if (operateCode == 1 && status == ChargerStatusEnum.Charging.getDbCode()) {
				return true;
			}

			if (operateCode == 0 && status != ChargerStatusEnum.Charging.getDbCode()) {
				return true;
			}
			log.info("充电桩 {} 状态: {}", deviceId, status);
		} catch (Exception e) {
			throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
		}
		throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
	}

	@Override
	@Retryable(maxAttempts = 20, value = {RetryException.class}, backoff = @Backoff(delay = 10000, multiplier = 1))
	public Integer getChargeStationStatus(String deviceSn, ChargerSaveVO chargerSaveVO, Integer operateCode) {
		try {
			if (chargerSaveVO.getBindMode().equals(0)) {
				processChargeStationInfo(operateCode, chargerSaveVO);
				return DeviceStatusEnum.OFFLINE.getDbCode();
			}

			Integer status = fetchChargeStationStatus(deviceSn);
			if (status != null && status > 0) {
				log.info("充电桩 {} 状态: {}", deviceSn, status);
				processChargeStationInfo(operateCode, chargerSaveVO);
				return status;
			}
		} catch (Exception e) {
			log.error("Error fetching charge station status for device: {}", deviceSn, e);
		}
		throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
	}

	private Integer fetchChargeStationStatus(String deviceSn) {
		CpStatusResponse chargeStationStatus = ecosIotApi.getChargeStationStatus(deviceSn, String.valueOf(DeviceTypeInfoEnum.OCPP.getDatasource()));
		if (chargeStationStatus == null || chargeStationStatus.getConnectors().isEmpty()) {
			return DeviceStatusEnum.OFFLINE.getDbCode();
		}
		return ChargerStatusEnum.getCodeByStatus(chargeStationStatus.getConnectors().get(0).getStatus());
	}

	private void processChargeStationInfo(Integer operateCode, ChargerSaveVO chargerSaveVO) {
		switch (operateCode) {
			case 0:
				log.info("保存充电桩信息: {}", chargerSaveVO);
				hubService.saveCharger(chargerSaveVO);
				break;
			case 1:
				log.info("更新充电桩信息: {}", chargerSaveVO);
				hubService.updCharger(chargerSaveVO);
				break;
			default:
				log.warn("未知操作代码: {}", operateCode);
				break;
		}
	}


	@Retryable(maxAttempts = 5, value = {RetryException.class}, backoff = @Backoff(delay = 500, multiplier = 1))
	public Boolean checkSocketStatus(String wifiSn, String cloud, Boolean switchStatus, Integer switchIndex) {
		try {
			// 查询设备相关属性
			List<CloudDevicePropertyStatusResponse> deviceInfo = ecosIotApi.getDevicePropertyInfo(wifiSn, cloud);
			for (CloudDevicePropertyStatusResponse response : deviceInfo) {
				String code = "switch_" + switchIndex;
				if (code.equals(response.getCode()) && switchStatus.equals((Boolean) response.getValue())) {
					return true;
				}
			}
		} catch (Exception e) {
			throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
		}
		throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
	}

	@Override
	@Retryable(maxAttempts = 10, value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void syncWhDeviceState(String deviceSn) {
		HybridSinglePhaseDO deviceInfo = hubService.getByDeviceName(deviceSn);
		if (null == deviceInfo) {
			log.error("whIot device not exists: {}", deviceSn);
			throw new RetryException(9998, String.format("whIot device not exists: %s", deviceSn));
		}
		if (deviceInfo.getState() > 0) {
			log.info("whIot device already online: {}", deviceSn);
			return;
		}
		Boolean isOnline = ecosIotApi.checkDeviceOnline(deviceInfo.getWifiSn(), String.valueOf(DeviceTypeInfoEnum.WH.getDatasource()));
		if (!isOnline) {
			log.error("whIot device sync state offline: {}", deviceSn);
			throw new RetryException(9998, String.format("whIot device sync state offline: %s", deviceSn));
		}
		TimeSeriesDatabaseService tsdbService = strategyService.chooseTimeSeriesDatabaseService(deviceInfo);
		Dict lastPointDict = tsdbService.lastPoint(deviceInfo.getDeviceName(),
				ListUtil.toLinkedList(TsdbMetricsConstants.SYS_RUN_MODE), System.currentTimeMillis()
		);

		Integer sysRunMode = Optional.ofNullable(lastPointDict)
				.map(i -> i.get(TsdbMetricsConstants.SYS_RUN_MODE))
				.map(String::valueOf)
				.map(i -> NumberUtil.round(i, 0))
				.map(BigDecimal::intValue)
				.orElseThrow(() -> {
					log.error("whIot device read last state failed: {}", deviceSn);
					return new RetryException(9998, String.format("whIot device read last state failed: %s", deviceSn));
				});
		deviceInfo.setState(sysRunMode);
		hubService.updateById(deviceInfo);
		log.info("whIot device {} sync state success: {}", deviceSn, sysRunMode);
	}

	@Override
	@Retryable(value = {RetryException.class}, backoff = @Backoff(delay = 5000, multiplier = 1))
	public void speedUpDevice(String wifiSn, String type) {
		try {
			ecosIotApi.speedupDevice(TuyaDeviceSpeedupVo.builder().deviceId(wifiSn).build(), type);
		} catch (Exception e) {
			throw new RetryException(9998, String.format("whIot device speedup failed: %s", wifiSn));
		}
	}

	@Override
	@Retryable(value = {RetryException.class}, backoff = @Backoff(delay = 1000, multiplier = 1))
	public void writeCustomizeV2(CustomizeInfoEzV2Vo param) {
		try {
			customizeAdapter.writeCustomizeV2(param);
			XxlJobHelper.log("{} writeCustomizeV2 success", param.getDeviceId());
		} catch (Exception e) {
			XxlJobHelper.log("{} writeCustomizeV2 failed: {}", param.getDeviceId(), e.getMessage());
			throw new RetryException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
		}
	}
}
