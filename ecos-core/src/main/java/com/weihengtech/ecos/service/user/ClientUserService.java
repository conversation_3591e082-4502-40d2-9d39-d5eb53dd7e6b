package com.weihengtech.ecos.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientUserDo;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ClientUserService extends IService<ClientUserDo> {

	/**
	 * 通过邮箱查询用户
	 *
	 * @param email 邮箱
	 * @return Optional用户
	 */
	Optional<ClientUserDo> queryOptionalUserByEmail(String email);

	/**
	 * 通过phone查询用户
	 *
	 * @param phone 账号名
	 * @return Optional用户
	 */
	Optional<ClientUserDo> queryOptionalUserByPhone(String phone);

	/**
	 * 通过username查询用户
	 *
	 * @param username 账号名
	 * @return Optional用户
	 */
	Optional<ClientUserDo> queryOptionalUserByUsername(String username);

	/**
	 * 重置密码
	 *
	 * @param username       账号
	 * @param newPassword 新密码
	 */
	void resetPassword(String username, String newPassword);
}
