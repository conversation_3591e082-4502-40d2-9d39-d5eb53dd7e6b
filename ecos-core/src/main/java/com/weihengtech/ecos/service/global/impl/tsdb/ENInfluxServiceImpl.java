package com.weihengtech.ecos.service.global.impl.tsdb;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.influx.pojo.req.BasicParamDTO;
import com.weihengtech.influx.pojo.req.SampleParamDTO;
import com.weihengtech.influx.service.FluxQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @program: ecos-server
 * @description: EN+充电桩Lindorm服务实现类
 * @author: jiahao.jin
 * @create: 2024-02-21 09:41
 **/
@Service(value = "tsdbService3")
@Slf4j
public class ENInfluxServiceImpl implements TimeSeriesDatabaseService {

    private static final List<String> EXTRA_COLUMN = Arrays.asList("result",
            "table", "_start", "_stop", "_time", "_measurement", "device_id");

    @Value("${influxdb.charger.database}")
    private String influxdbDatabase;

    @Value("${influxdb.charger.table}")
    private String influxdbTable;

    @Resource
    private FluxQueryWrapper chargerFluxQueryWrapper;


    @Override
    public Dict lastPoint(String deviceSn, List<String> metricList, long endTime) {
        try {
            Map<String, Object> map = chargerFluxQueryWrapper.lastPoint(BasicParamDTO.builder()
                    .database(influxdbDatabase)
                    .table(influxdbTable)
                    .deviceName(deviceSn)
                    .metricList(metricList)
                    .start(endTime / 1000L - 60*10)
                    .end(endTime / 1000L)
                    .build());
            Dict dict = Dict.create();
            dict.putAll(map);
            return dict;
        } catch (Exception e) {
            log.warn("Error querying InfluxDB: " + e.getMessage());
            return Dict.create();
        }
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> lastConnectorStatusPoint(
            String deviceSn, String connectorStatus, List<String> metricList, long startTime, long endTime) {
        Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
        try {
            Map<String, LinkedHashMap<Long, Object>> map = chargerFluxQueryWrapper.withoutSampleQuery(BasicParamDTO.builder()
                    .database(influxdbDatabase)
                    .table(influxdbTable)
                    .deviceName(deviceSn)
                    .metricList(metricList)
                    .start(startTime / 1000L)
                    .end(endTime / 1000L)
                    .build());
            // 筛选出指定状态的数据
            LinkedHashMap<Long, Object> linkedHashMap = map.get(TsdbMetricsConstants.CONNECTOR_STATUS);
            if (linkedHashMap == null) {
                return result;
            }
            LinkedHashMap<Long, Object> filteredResult = new LinkedHashMap<>();
            linkedHashMap.forEach((k, v) -> {
                if (connectorStatus.equals(v)) {
                    filteredResult.put(k, v);
                }
            });
            result.put(TsdbMetricsConstants.CONNECTOR_STATUS, filteredResult);
        } catch (Exception e) {
            // 处理异常，例如日志记录
            log.warn("Error querying InfluxDB: " + e.getMessage());
        }
        return result;
    }


    @Override
    public Map<String, LinkedHashMap<Long, Object>> deltaQuery(
            String deviceSn, List<String> metricList, long start,
            long end, TsdbSampleEnum tsdbSampleEnum
    ) {
        Map<String, LinkedHashMap<Long, Object>> result = initializeResultMap(metricList);
        try {
            Map<String, LinkedHashMap<Long, Object>> map = chargerFluxQueryWrapper.deltaQuery(SampleParamDTO.builder()
                    .database(influxdbDatabase)
                    .table(influxdbTable)
                    .deviceName(deviceSn)
                    .metricList(metricList)
                    .start(start / 1000L)
                    .end(end / 1000L)
                    .timesStr(tsdbSampleEnum.getDelta())
                    .isCreateEmpty(false)
                    .build());
            EXTRA_COLUMN.forEach(map::remove);
            result.putAll(map);
        } catch (Exception e) {
            // 处理异常，如日志记录
            log.warn("Error querying InfluxDB: " + e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> withSampleQuery(
            String deviceSn, List<String> metricList,
            long start, long end, long times
    ) {
        return MapUtil.empty();
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> withOutSampleQuery(
            String deviceSn, List<String> metricList,
            long start, long end
    ) {
        return MapUtil.empty();
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> meanQuery(String deviceSn, List<String> metricList, long start, long end, long times) {
        return MapUtil.empty();
    }
}
