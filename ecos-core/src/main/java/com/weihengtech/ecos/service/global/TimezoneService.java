package com.weihengtech.ecos.service.global;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dtos.global.TimezoneDto;
import com.weihengtech.ecos.model.dos.TimezoneDo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TimezoneService extends IService<TimezoneDo> {

	/**
	 * 根据国际化返回所有的时区列表
	 *
	 * @return 时区列表
	 */
	List<TimezoneDto> allTimezoneWithLocale();

	/**
	 * 获取国际化时区名字
	 *
	 * @return 时区名
	 */
	String getTimezoneName(TimezoneDo timezoneDo);
}
