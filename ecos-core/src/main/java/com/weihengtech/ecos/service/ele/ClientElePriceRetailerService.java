package com.weihengtech.ecos.service.ele;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;
import com.weihengtech.ecos.model.vos.price.TibberEleVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ClientElePriceRetailerService extends IService<ClientElePriceRetailerDO> {

    ClientElePriceRetailerDO queryRetailerPrice(String homeId);

    void updateRetailerPrice(ElePriceRetailerVO param);

    void deleteRetailerPrice(String homeId);

    List<TibberHomeDTO> queryTibberHomes(String token);

    List<EleDayAheadPriceDto> queryTibberEle(TibberEleVO param);
}
