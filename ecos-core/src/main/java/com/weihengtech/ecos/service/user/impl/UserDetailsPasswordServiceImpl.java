package com.weihengtech.ecos.service.user.impl;

import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.dao.ClientUserMapper;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.model.bos.user.ClientUserDetails;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsPasswordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class UserDetailsPasswordServiceImpl implements UserDetailsPasswordService {

	@Resource
	private ClientUserMapper clientUserMapper;

	@Resource
	private ClientUserRoleService clientUserRoleService;

	@Override
	public UserDetails updatePassword(UserDetails user, String newPassword) {
		return Optional
				.ofNullable(clientUserMapper.selectOne(
						Wrappers.<ClientUserDo>lambdaQuery().eq(ClientUserDo::getUsername, user.getUsername())))
				.map(u -> {
					ClientUserDo newUser = u.withPassword(newPassword);
					ActionFlagUtil.assertSingleAction(clientUserMapper.updateById(newUser));
					List<String> roles = clientUserRoleService.queryRoleListByClientUserId(u.getId());
					ClientUserDetails clientUserDetails = new ClientUserDetails();
					CglibUtil.copy(newUser, clientUserDetails);
					clientUserDetails.setAuthorities(roles);
					return (UserDetails) newUser;
				}).orElse(user);
	}
}
