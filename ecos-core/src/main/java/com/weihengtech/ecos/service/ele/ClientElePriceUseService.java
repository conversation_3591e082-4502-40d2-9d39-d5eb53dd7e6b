package com.weihengtech.ecos.service.ele;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientElePriceUseDO;
import com.weihengtech.ecos.model.vos.price.ElePriceUseVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface ClientElePriceUseService extends IService<ClientElePriceUseDO> {

    ElePriceUseVO queryUsePrice(String homeId);

    void updateUsePrice(ElePriceUseVO param);

    void deleteUsePrice(String homeId);
}
