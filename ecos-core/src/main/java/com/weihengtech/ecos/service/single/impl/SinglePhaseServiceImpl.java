package com.weihengtech.ecos.service.single.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.jd.platform.async.executor.Async;
import com.jd.platform.async.wrapper.WorkerWrapper;
import com.weihengtech.ecos.adapter.HomeAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.consts.*;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.model.dtos.single.SinglePhaseBaseInfoDto;
import com.weihengtech.ecos.prometheus.BindFailMetrics;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo;
import com.weihengtech.ecos.model.bos.ecos.EcosEventBo;
import com.weihengtech.ecos.model.bos.app.HistoryHomeBo;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientEnergyNotifyDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightConsumptionDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzVo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeHistoryVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.InsightDeviceDataVo;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import com.weihengtech.ecos.model.vos.app.PowerLimitVO;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.app.ClientEnergyNotifyService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.ecos.EventService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.OperationUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TSDBAggUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.utils.TimezoneUtil;
import com.weihengtech.ecos.utils.TransformUtil;
import com.weihengtech.ecos.worker.database.DatabaseDeltaQueryOneHourPointWorker;
import com.weihengtech.ecos.worker.database.DatabaseLastPointWorker;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description cnsb 相关产品业务实现
 * @create 2023-10-27 10:43
 **/
@Slf4j
@Service
public class SinglePhaseServiceImpl implements SinglePhaseService {

    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private EventService eventService;
    @Resource
    private HubService hubService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ClientEnergyNotifyService clientEnergyNotifyService;
    @Resource
    private RetryService retryService;
    @Resource
    private EcosIotApi  ecosIotApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private HomeAdapter homeAdapter;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ClientCustomizeService clientCustomizeService;
    @Resource
    private V2HomeAdapter v2HomeAdapter;
    @Resource
    private OssGlobalConfigApi ossGlobalConfigApi;
    @Resource
    private ClientUserService clientUserService;
    @Resource
    private BindFailMetrics bindFailMetrics;


    @Override
    public void bindClientUserDevice(V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, ClientUserDo clientUserDo) {
        Integer type = homeClientUserBindDeviceVo.getType();
        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        Double lon = homeClientUserBindDeviceVo.getLon();
        Double lat = homeClientUserBindDeviceVo.getLat();
        if (lon != null && lat != null) {
            if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                log.warn("lon或lat值不正确");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }

        String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
        if (stringRedisTemplate.hasKey(redisKey)) {
            String bindStatus = stringRedisTemplate.opsForValue().get(redisKey);
            if (DeviceBindStatus.OK.equals(bindStatus)) {
                return;
            }
        }
        log.info("开始绑定 {}", JSONUtil.toJsonStr(homeClientUserBindDeviceVo));
        stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.ING, 3, TimeUnit.MINUTES);

        List<HybridSinglePhaseDO> nowBindDeviceList = hubService.nowBindDeviceList(wifiSn);
        if (CollUtil.isNotEmpty(nowBindDeviceList)) {
            for (HybridSinglePhaseDO hybridSinglePhaseDO : nowBindDeviceList) {
                MiddleClientUserDeviceDo nowBindMiddle = middleClientUserDeviceService
                        .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                                .eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId()));
                if (nowBindMiddle != null) {
                    Boolean hasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, String.valueOf(hybridSinglePhaseDO.getId()));
                    if (!hasDevice) {
                        // 将设备与家庭建立关系
                        v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(hybridSinglePhaseDO.getId()), StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? hybridSinglePhaseDO.getDeviceSn() : homeClientUserBindDeviceVo.getDeviceAliasName());
                    }
                    hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
                    hubService.updateById(hybridSinglePhaseDO);
                    stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                    return;
                }
            }
        }

        try {
            Pair<String, String> pair = retryService.getDeviceSn(type, wifiSn);
            if (pair == null || pair.getKey() == null) {
                bindFailMetrics.recordFailure(wifiSn, "获取设备sn失败");
            }
            bindDeviceAction(pair.getKey(), clientUserDo, homeClientUserBindDeviceVo, redisKey, pair.getValue());
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
    }

    @Override
    public Integer checkDeviceBindStatus(String wifiSn) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
        return Integer.parseInt(Optional.ofNullable(stringRedisTemplate.opsForValue().get(redisKey)).orElse(DeviceBindStatus.MISS));
    }

    @Override
    public void unbindClientUserDevice(String deviceId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService.getOne(
                Wrappers.<MiddleClientUserDeviceDo>lambdaQuery().eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                        .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId()));

        if (null == middleClientUserDeviceDo) {
            log.warn("无权操作设备");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }
        int master = middleClientUserDeviceDo.getMaster();
        if (1 == master) {
            middleClientUserDeviceService.remove(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                    .eq(MiddleClientUserDeviceDo::getDeviceId, Long.parseLong(deviceId)));
        } else {
            middleClientUserDeviceService.removeById(middleClientUserDeviceDo.getId());
        }
    }

    @Override
    public PageInfoDTO<HomeEventsFaultDto> pageEventFault(HomeEventsFaultVo homeEventsFaultVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        String deviceId = homeEventsFaultVo.getDeviceId();
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo, deviceId);
//        MiddleClientUserDeviceDo middleClientUserDeviceDo = middleClientUserDeviceService.getOne(Wrappers
//                .<MiddleClientUserDeviceDo>lambdaQuery()
//                .eq(
//                        MiddleClientUserDeviceDo::getUserId,
//                        clientUserDo.getId()
//                )
//                .eq(
//                        MiddleClientUserDeviceDo::getDeviceId,
//                        deviceId
//                ));
//        Optional.ofNullable(middleClientUserDeviceDo).orElseThrow(() -> {
//            log.warn("未绑定的设备");
//            return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
//        });
        Long start = homeEventsFaultVo.getStart();
        Long end = homeEventsFaultVo.getEnd();

        HybridSinglePhaseDO hybridSinglePhaseDO = userPair.getValue();
        PageHelper.startPage(homeEventsFaultVo.getPageNum(), homeEventsFaultVo.getPageSize());
        List<EcosEventBo> ecosEventBos = eventService.listEventByCondition(hybridSinglePhaseDO.getDeviceName(),
                homeEventsFaultVo.getType(), start, end
        );
        PageInfo<EcosEventBo> pageInfo = new PageInfo<>(ecosEventBos);

        PageInfoDTO<HomeEventsFaultDto> pageInfoDTO = new PageInfoDTO<>();
        pageInfoDTO.setTotalPages(pageInfo.getPages());
        pageInfoDTO.setTotalCount(pageInfo.getTotal());
        pageInfoDTO.setData(ecosEventBos.stream().map(bo -> {
            HomeEventsFaultDto homeEventsFaultDto = new HomeEventsFaultDto();
            homeEventsFaultDto.setErrorCode(bo.getSubsystem() + "_" + bo.getCode());
            homeEventsFaultDto.setEventType(bo.getLevel());
            int eventTypeInt;
            switch (bo.getLevel()) {
                case "alarm":
                    eventTypeInt = 1;
                    break;
                case "fault":
                    eventTypeInt = 2;
                    break;
                default:
                    eventTypeInt = 0;
                    break;
            }
            homeEventsFaultDto.setEventTypeInt(eventTypeInt);
            homeEventsFaultDto.setEventContentEn(bo.getEnglish());
            homeEventsFaultDto.setEventContentCn(bo.getChinese());
            homeEventsFaultDto.setOccurrenceTime(TimeUtil
                    .longTimestampToSerialStringOffsetGMT8(
                            bo.getUploadTime() * 1000L, clientUserDo.getTimeZone()));
            return homeEventsFaultDto;
        }).collect(Collectors.toList()));

        return pageInfoDTO;
    }

    @Override
    public HomeEventsBackupStatisticsDto backupStatistics(HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo,
                homeEventsBackupStatisticsVo.getDeviceId());
        List<EcosEpsEventBo> ecosEpsEventBos = eventService.listEpsEventByCondition(
                userPair.getValue().getDeviceSn(),
                homeEventsBackupStatisticsVo.getStartTime(),
                homeEventsBackupStatisticsVo.getEndTime()
        );

        BigDecimal totalEnergy = BigDecimal.ZERO;
        BigDecimal totalDuration = BigDecimal.ZERO;
        for (EcosEpsEventBo ecosEpsEventBo : ecosEpsEventBos) {
            String deviceName = ecosEpsEventBo.getDeviceName();
            EcosEpsEventBo notEpsEventBo = eventService.findNearlyNotEpsEvent(deviceName, ecosEpsEventBo.getId());
            long start = ecosEpsEventBo.getUploadTime();
            long end = notEpsEventBo == null ? System.currentTimeMillis() / 1000 : notEpsEventBo.getUploadTime();

            totalDuration = NumberUtil.add(totalDuration, new BigDecimal(end - start));

            TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                    .chooseTimeSeriesDatabaseService(userPair.getValue());
            Dict startResult = timeSeriesDatabaseService.lastPoint(
                    deviceName,
                    ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
                    start * 1000
            );
            Dict endResult = timeSeriesDatabaseService.lastPoint(
                    deviceName,
                    ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
                    end * 1000
            );
            totalEnergy = NumberUtil.add(NumberUtil.round(NumberUtil.sub(
                            new BigDecimal(
                                    endResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString()),
                            new BigDecimal(
                                    startResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString())
                    ),
                    2, RoundingMode.HALF_UP
            ), totalEnergy);
        }

        HomeEventsBackupStatisticsDto homeEventsBackupStatisticsDto = new HomeEventsBackupStatisticsDto();
        homeEventsBackupStatisticsDto.setBackupCount(ecosEpsEventBos.size());
        homeEventsBackupStatisticsDto
                .setBackupEnergy(totalEnergy.compareTo(BigDecimal.ZERO) > 0 ? totalEnergy : BigDecimal.ZERO);
        homeEventsBackupStatisticsDto.setBackupDuration(
                NumberUtil.round(NumberUtil.div(totalDuration, new BigDecimal("3600")), 2, RoundingMode.HALF_UP));
        return homeEventsBackupStatisticsDto;
    }

    @Override
    public PageInfoDTO<HomeEventsBackupPageDto> pageBackup(HomeEventsBackupPageVo homeEventsBackupPageVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo, homeEventsBackupPageVo.getDeviceId());
        List<EcosEpsEventBo> ecosEpsEventBos = eventService.listEpsEventByCondition(
                userPair.getValue().getDeviceSn(),
                homeEventsBackupPageVo.getStartTime(),
                homeEventsBackupPageVo.getEndTime()
        );

        Integer pageNum = homeEventsBackupPageVo.getPageNum();
        Integer pageSize = homeEventsBackupPageVo.getPageSize();
        int count = ecosEpsEventBos.size();

        PageInfoDTO<HomeEventsBackupPageDto> pageInfoDto = new PageInfoDTO<>();
        pageInfoDto.setTotalPages(count == 0 ? 0 : count / pageSize + 1);
        pageInfoDto.setTotalCount((long) count);

        List<List<EcosEpsEventBo>> splitList = ListUtil.split(ecosEpsEventBos, pageSize);
        if (pageNum > splitList.size()) {
            pageInfoDto.setData(ListUtil.empty());
        } else {
            List<EcosEpsEventBo> pageData = splitList.get(pageNum - 1);
            List<HomeEventsBackupPageDto> pageDtoList = new ArrayList<>();
            for (EcosEpsEventBo epsEventBo : pageData) {
                String deviceName = epsEventBo.getDeviceName();
                EcosEpsEventBo notEpsEventBo = eventService.findNearlyNotEpsEvent(deviceName, epsEventBo.getId());
                long start = epsEventBo.getUploadTime();
                long end = notEpsEventBo == null ? System.currentTimeMillis() / 1000 : notEpsEventBo.getUploadTime();

                HomeEventsBackupPageDto homeEventsBackupPageDto = new HomeEventsBackupPageDto();
                homeEventsBackupPageDto.setStartTime(
                        TimeUtil.longTimestampToSerialStringOffsetGMT8(start * 1000L, userPair.getKey().getTimeZone()));

                TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                        .chooseTimeSeriesDatabaseService(userPair.getValue());
                Dict startResult = timeSeriesDatabaseService.lastPoint(
                        deviceName,
                        ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
                        start * 1000
                );
                Dict endResult = timeSeriesDatabaseService.lastPoint(
                        deviceName,
                        ListUtil.toLinkedList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS),
                        end * 1000
                );
                homeEventsBackupPageDto
                        .setMinutes(NumberUtil.round(new BigDecimal((end - start) / 60), 2, RoundingMode.HALF_UP));
                BigDecimal deltaEnergy = NumberUtil.round(NumberUtil.sub(
                                new BigDecimal(
                                        endResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString()),
                                new BigDecimal(
                                        startResult.getOrDefault(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS, "0").toString())
                        ),
                        2, RoundingMode.HALF_UP
                );
                homeEventsBackupPageDto
                        .setEnergy(deltaEnergy.compareTo(BigDecimal.ZERO) > 0 ? deltaEnergy : BigDecimal.ZERO);
                pageDtoList.add(homeEventsBackupPageDto);
            }
            pageInfoDto.setData(pageDtoList);
        }
        return pageInfoDto;
    }

    @Override
    public Integer computeBatteryCycleTimes(
            String deviceFlag, long start, long end,
            TimeSeriesDatabaseService timeSeriesDatabaseService
    ) {
        Dict startCycleTimes = timeSeriesDatabaseService.lastPoint(deviceFlag,
                ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), start
        );
        Dict endCycleTimes = timeSeriesDatabaseService.lastPoint(deviceFlag,
                ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), end
        );

        BigDecimal before = new BigDecimal(
                startCycleTimes.getOrDefault(TsdbMetricsConstants.BAT_CYCLE_TIME, "0").toString());
        BigDecimal after = new BigDecimal(
                endCycleTimes.getOrDefault(TsdbMetricsConstants.BAT_CYCLE_TIME, "0").toString());

        int times;
        try {
            times = NumberUtil.sub(after, before).intValue();
        } catch (Exception e) {
            times = 0;
        }
        return Math.max(times, 0);
    }

    @Override
    public HomeNowDeviceRealtimeDto packageNowDeviceRealtimeResult(
            Map<String, LinkedHashMap<Long, Object>> metricMap
    ) {
        HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = new HomeNowDeviceRealtimeDto();

        List<Long> sortedTimeList = new ArrayList<>();
        List<Object> meterValueList = new ArrayList<>();
        List<Object> solarValueList1 = new ArrayList<>();
        List<Object> solarValueList2 = new ArrayList<>();
        List<Object> batteryValueList = new ArrayList<>();
        List<Object> epsRList = new ArrayList<>();
        List<Object> epsSList = new ArrayList<>();
        List<Object> epsTList = new ArrayList<>();
        List<Object> socList = new ArrayList<>();

        for (String metric : metricMap.keySet()) {
            LinkedHashMap<Long, Object> resultMap = metricMap.get(metric);
            switch (metric) {
                case TsdbMetricsConstants.METER_P_PV:
                    solarValueList1 = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_PV_P:
                    solarValueList2 = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.AC_P:
                    homeNowDeviceRealtimeDto.setGridPowerDps(resultMap);
                    break;
                case TsdbMetricsConstants.BAT_P:
                    batteryValueList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_P:
                    sortedTimeList = resultMap.keySet().stream().sorted().collect(Collectors.toList());
                    meterValueList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.EPS_P:
                    epsRList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.EPS_PS:
                    epsSList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.EPS_PT:
                    epsTList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                case TsdbMetricsConstants.BAT_SOC:
                    socList = resultMap.keySet().stream().sorted().map(resultMap::get)
                            .collect(Collectors.toList());
                    break;
                default:
            }
        }

        if (CollUtil.isEmpty(sortedTimeList)
                || CollUtil.isEmpty(meterValueList)
                || CollUtil.isEmpty(solarValueList1)
                || CollUtil.isEmpty(batteryValueList)
                || CollUtil.isEmpty(epsRList)
                || CollUtil.isEmpty(socList)
        ) {
            return homeNowDeviceRealtimeDto;
        }

        Map<Long, Object> homePowerDps = new LinkedHashMap<>();
        Map<Long, Object> meterPowerDps = new LinkedHashMap<>();
        Map<Long, Object> solarPowerDps = new LinkedHashMap<>();
        Map<Long, Object> batteryPowerDps = new LinkedHashMap<>();
        Map<Long, Object> epsPowerDps = new LinkedHashMap<>();
        Map<Long, Object> socDps = new LinkedHashMap<>();

        // 家庭用电功率:meter_p(电网) + meter_p_pv(光伏) - eps_p(应急负载) - bat_p(电池)
        for (int i = 0; i < sortedTimeList.size(); i++) {
            Long time = sortedTimeList.get(i);
            BigDecimal meterValue = parseListValue(i, meterValueList);
            BigDecimal solarValue1 = parseListValue(i, solarValueList1);
            BigDecimal solarValue2 = parseListValue(i, solarValueList2);
            BigDecimal solarValue = NumberUtil.add(solarValue1, solarValue2);
            BigDecimal batteryValue = parseListValue(i, batteryValueList);

            BigDecimal epsRValue = parseListValue(i, epsRList);
            BigDecimal epsSValue = parseListValue(i, epsSList);
            BigDecimal epsTValue = parseListValue(i, epsTList);

            meterPowerDps.put(time, meterValue);
            solarPowerDps.put(time, solarValue);
            batteryPowerDps.put(time, batteryValue);

            BigDecimal epsValue = NumberUtil.add(epsRValue, epsSValue, epsTValue);
            epsPowerDps.put(time, epsValue);

            homePowerDps.put(time, NumberUtil.sub(NumberUtil.add(meterValue, solarValue), batteryValue, epsValue));
            socDps.put(time, parseListValue(i, socList));
        }

        homeNowDeviceRealtimeDto.setHomePowerDps(homePowerDps);
        homeNowDeviceRealtimeDto.setSolarPowerDps(solarPowerDps);
        homeNowDeviceRealtimeDto.setBatteryPowerDps(batteryPowerDps);
        homeNowDeviceRealtimeDto.setMeterPowerDps(meterPowerDps);
        homeNowDeviceRealtimeDto.setEpsPowerDps(epsPowerDps);
        homeNowDeviceRealtimeDto.setSocDps(socDps);
        return homeNowDeviceRealtimeDto;
    }

    private BigDecimal parseListValue(Integer i, List<Object> list) {
        return new BigDecimal(i > list.size() - 1
                ? "0"
                : Optional.ofNullable(list.get(i)).orElse("0").toString());
    }

    @DSTransactional
    private void bindDeviceAction(
            String deviceSn, ClientUserDo clientUserDo,
            V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo,
            String redisKey,
            String ip
    ) {

        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        RLock lock = redissonClient.getLock(wifiSn);
        try {
            if (lock.tryLock(100, TimeUnit.MILLISECONDS)) {
                HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getByDeviceName(deviceSn);
                Long deviceId;
                if (null == hybridSinglePhaseDO) {
                    deviceId = saveDeviceInfo(deviceSn, wifiSn, homeClientUserBindDeviceVo, clientUserDo.getDatacenterId());
                } else {
                    deviceId = hybridSinglePhaseDO.getId();
                    MiddleClientUserDeviceDo masterDevice = middleClientUserDeviceService.getOne(Wrappers
                            .<MiddleClientUserDeviceDo>lambdaQuery().eq(
                                    MiddleClientUserDeviceDo::getDeviceId,
                                    deviceId
                            )
                            .eq(MiddleClientUserDeviceDo::getMaster, 1));

                    if (masterDevice != null) {
                        if (!masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "从账号绑定错误");
                            log.warn("从账号绑定错误");
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FORBIDDEN, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.SLAVE_BIND_ERROR);
                        }
                    }
                    hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
                    hybridSinglePhaseDO.setIp(ip);
                    updateDeviceInfo(hybridSinglePhaseDO, homeClientUserBindDeviceVo, wifiSn, deviceSn);

                    if (masterDevice != null) {
                        if (masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "已经绑定了该设备");
                            log.warn("{} 已经绑定了该设备：{}", clientUserDo.getEmail(), deviceSn);
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
                        }
                    }
                }

                // 将设备与操作人建立关系
                long middleId = snowFlakeUtil.generateId();
                String deviceName = StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? deviceSn : homeClientUserBindDeviceVo.getDeviceAliasName();
                MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
                middleClientUserDeviceDo.setId(middleId);
                middleClientUserDeviceDo.setUserId(clientUserDo.getId());
                middleClientUserDeviceDo.setDeviceId(deviceId);
                middleClientUserDeviceDo.setCreateTime(System.currentTimeMillis());
                middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
                middleClientUserDeviceDo.setWeight(0);
                middleClientUserDeviceDo.setName(deviceName);
                middleClientUserDeviceDo.setMaster(1);
                ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(middleClientUserDeviceDo));

                // 将设备与家庭建立关系
                v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(deviceId), deviceName);

                // 绑定成功
                stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
            }
        } catch (InterruptedException e) {
            log.warn(e.getMessage());
            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FAIL, 1, TimeUnit.MINUTES);
            throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public HomeNowDeviceRunDataDto queryNowDeviceRunData(ClientUserDo clientUserDo, String deviceId) {
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo, deviceId);
//        LinkedList<String> metricListPv = new LinkedList<>(CommonConstants.METER_PV_P);

        HomeNowDeviceRunDataDto homeNowDeviceRunDataDto = new HomeNowDeviceRunDataDto();

        HybridSinglePhaseDO hybridSinglePhaseDO = userPair.getValue();
        Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);
        homeNowDeviceRunDataDto.setSysRunMode(sysRunMode);

        if (sysRunMode.equals(DeviceStatusEnum.OFFLINE.getDbCode())) {
            log.info("设备id： " + deviceId + " 离线");
            return homeNowDeviceRunDataDto;
        }

        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
        Dict lastPointDict = timeSeriesDatabaseService.lastPoint(userPair.getValue().getDeviceSn(), CommonConstants.NOW_RUN_DATA, System.currentTimeMillis());
//        Dict lastPointDictPv = timeSeriesDatabaseService.lastDataPoint(userPair.getValue().getKey(), metricListPv, System.currentTimeMillis());

        if (((int) lastPointDict.values().stream().filter(Objects::isNull).count()) > 0) {
            log.warn("首页数据存在Null: {}", JSONUtil.toJsonStr(lastPointDict));
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }

        String solarPower = StrUtil.toString(NumberUtil.add(lastPointDict.getStr(TsdbMetricsConstants.METER_P_PV), lastPointDict.getStr(TsdbMetricsConstants.METER_PV_P)));
        reflectSetRunData("setSolarPower", solarPower, homeNowDeviceRunDataDto);
        reflectSetRunData("setGridPower", lastPointDict.getStr(TsdbMetricsConstants.AC_P), homeNowDeviceRunDataDto);
        reflectSetRunData("setBatteryPower", lastPointDict.getStr(TsdbMetricsConstants.BAT_P), homeNowDeviceRunDataDto);
        reflectSetRunData("setMeterPower", lastPointDict.getStr(TsdbMetricsConstants.METER_P), homeNowDeviceRunDataDto);
        reflectSetRunData("setEpsPower", lastPointDict.getStr(TsdbMetricsConstants.EPS_P_1), homeNowDeviceRunDataDto);
        setDeviceRunDataBatterySoc(lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC), homeNowDeviceRunDataDto);
        val sysPowerConfig = lastPointDict.getOrDefault(TsdbMetricsConstants.SYS_POWER_CONFIG, "-1").toString();
        homeNowDeviceRunDataDto.setSysPowerConfig(new BigDecimal(sysPowerConfig).intValue());

        homeNowDeviceRunDataDto.setHomePower(NumberUtil.round(
                NumberUtil.sub(NumberUtil.sub(NumberUtil.add(
                        homeNowDeviceRunDataDto.getSolarPower(),
                        homeNowDeviceRunDataDto.getMeterPower()
                ), homeNowDeviceRunDataDto.getBatteryPower()), homeNowDeviceRunDataDto.getEpsPower()),
                0, RoundingMode.HALF_UP
        ));

        return homeNowDeviceRunDataDto;
    }


    @Override
    public InsightDeviceEnergyHeatmapStatisticsDto getOffsetDayDeviceEnergyHeatmap(ClientUserDo clientUserDo, String deviceId, Integer offsetDay) {
        Pair<HybridSinglePhaseDO, String> pair = validateUserDevice(clientUserDo, deviceId, null);
        Pair<Long, Long> timePair = TimeUtil.computeStartAndEndByOffsetDay(offsetDay, pair.getValue());
        return computeDeviceEnergyHeatmap(pair.getKey(), timePair.getKey(), timePair.getValue(), pair.getValue()
        );
    }

    @Override
    @DSTransactional
    public InsightMoreInformationEnergyNotifyDto getEnergyNotifyConfig(ClientUserDo clientUserDo, String deviceId) {
        validateUserDevice(clientUserDo, deviceId, "");

        InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto = new InsightMoreInformationEnergyNotifyDto();
        saveOrGetEnergyNotify(insightMoreInformationEnergyNotifyDto, Long.parseLong(deviceId), clientUserDo.getId());

        return insightMoreInformationEnergyNotifyDto;
    }

    private void saveOrGetEnergyNotify(
            InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto,
            Long deviceId, Long userId
    ) {
        ClientEnergyNotifyDo clientEnergyNotifyDo = clientEnergyNotifyService
                .getOne(Wrappers.<ClientEnergyNotifyDo>lambdaQuery().eq(ClientEnergyNotifyDo::getUserId, userId)
                        .eq(ClientEnergyNotifyDo::getDeviceId, deviceId));
        if (null == clientEnergyNotifyDo) {
            clientEnergyNotifyDo = new ClientEnergyNotifyDo();
            clientEnergyNotifyDo.setId(snowFlakeUtil.generateId());
            clientEnergyNotifyDo.setDeviceId(deviceId);
            clientEnergyNotifyDo.setUserId(userId);
            ActionFlagUtil.assertTrue(clientEnergyNotifyService.save(clientEnergyNotifyDo));
            insightMoreInformationEnergyNotifyDto.setOpen(2);
            insightMoreInformationEnergyNotifyDto.setThreshold(0);
            insightMoreInformationEnergyNotifyDto.setEmail("");
        } else {
            CglibUtil.copy(clientEnergyNotifyDo, insightMoreInformationEnergyNotifyDto);
        }
    }

    @Override
    public InsightMoreInformationEnergyNotifyDto updateEnergyNotifyConfig(
            ClientUserDo clientUserDo,
            InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
    ) {
        String deviceId = insightMoreInformationEnergyNotifyVo.getDeviceId();
        validateUserDevice(clientUserDo, deviceId, "");

        InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto = new InsightMoreInformationEnergyNotifyDto();
        updateOrGetEnergyNotify(insightMoreInformationEnergyNotifyDto, Long.parseLong(deviceId), clientUserDo.getId(),
                insightMoreInformationEnergyNotifyVo
        );

        return insightMoreInformationEnergyNotifyDto;
    }

    @Override
    public OemEnergyDto computeOemEnergy(OemEnergyVo oemEnergyVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Pair<ClientUserDo, HybridSinglePhaseDO> pair = homeAdapter.validateDeviceOwner(clientUserDo, oemEnergyVo.getDeviceId());
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                .chooseTimeSeriesDatabaseService(pair.getValue());

        OemEnergyDto oemEnergyDto = new OemEnergyDto();
        oemEnergyDto.setToBattery(new BigDecimal("0"));
        oemEnergyDto.setFromBattery(new BigDecimal("0"));
        oemEnergyDto.setFromGrid(new BigDecimal("0"));
        oemEnergyDto.setToGrid(new BigDecimal("0"));
        oemEnergyDto.setFromSolar(new BigDecimal("0"));
        oemEnergyDto.setHomeEnergy(new BigDecimal("0"));
        oemEnergyDto.setSelfPowered(new BigDecimal("0"));
        oemEnergyDto.setCycleTimes(0);
        oemEnergyDto.setHomeEnergyDps(Maps.newHashMap());
        oemEnergyDto.setFromSolarDps(Maps.newHashMap());
        oemEnergyDto.setFromBatteryDps(Maps.newHashMap());
        oemEnergyDto.setToBatteryDps(Maps.newHashMap());
        oemEnergyDto.setFromGridDps(Maps.newHashMap());
        oemEnergyDto.setToGridDps(Maps.newHashMap());

        Long start = oemEnergyVo.getStart();
        Long end = oemEnergyVo.getEnd();
        if (start > end) {
            return oemEnergyDto;
        }

        List<String> metricList = ListUtil.toList(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
                TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID,
                TsdbMetricsConstants.BAT_E_TOTAL_CHARGE, TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE, TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID
        );

        DatabaseLastPointWorker lastPointWorker1 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
                ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), start);
        DatabaseLastPointWorker lastPointWorker2 = new DatabaseLastPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(),
                ListUtil.toLinkedList(TsdbMetricsConstants.BAT_CYCLE_TIME), end);
        DatabaseDeltaQueryOneHourPointWorker deltaQueryOneHourPointWorker = new DatabaseDeltaQueryOneHourPointWorker(timeSeriesDatabaseService, pair.getValue().getDeviceSn(), metricList, start, end);

        WorkerWrapper<String, Dict> lastPointWorker1Wrapper = new WorkerWrapper.Builder<String, Dict>()
                .worker(lastPointWorker1)
                .callback(lastPointWorker1)
                .param("DatabaseLastPointWorker")
                .build();
        WorkerWrapper<String, Dict> lastPointWorker2Wrapper = new WorkerWrapper.Builder<String, Dict>()
                .worker(lastPointWorker2)
                .callback(lastPointWorker2)
                .param("DatabaseLastPointWorker")
                .build();
        WorkerWrapper<String, Map<String, LinkedHashMap<Long, Object>>> deltaQueryOneHourPointWorkerWrapper = new WorkerWrapper.Builder<String, Map<String, LinkedHashMap<Long, Object>>>()
                .worker(deltaQueryOneHourPointWorker)
                .callback(deltaQueryOneHourPointWorker)
                .param("DatabaseDeltaQueryOneHourPointWorker")
                .build();

        try {
            Async.beginWork(RequestConstants.DATABASE_REQUEST_MILLISECOND_TIMES, lastPointWorker1Wrapper, lastPointWorker2Wrapper, deltaQueryOneHourPointWorkerWrapper);
        } catch (ExecutionException | InterruptedException e) {
            log.warn(e.getMessage());
        }

        Map<String, LinkedHashMap<Long, Object>> metricDataMap = TSDBAggUtil.aggregateDeltaQueryHourToDay(deltaQueryOneHourPointWorkerWrapper.getWorkResult().getResult(), pair.getKey().getTimeZone());

        LinkedHashMap<Long, Object> homeEnergyDps = new LinkedHashMap<>(1024);

        if (CollUtil.isNotEmpty(metricDataMap)) {
            LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
            LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);

            if (fromSolarMap2 != null) {
                for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
                    Long time = entry.getKey();
                    Object value = entry.getValue();
                    if (fromSolarMap.containsKey(time)) {
                        // 如果键已存在，则将值相加
                        BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
                        BigDecimal additionalValue = new BigDecimal(value.toString());
                        BigDecimal sum = originalValue.add(additionalValue);
                        fromSolarMap.put(time, sum);
                    } else {
                        // 如果键不存在，则添加键值对
                        fromSolarMap.put(time, value);
                    }
                }
            }

            LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
                    .get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
            LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
                    .get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
            LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
            LinkedHashMap<Long, Object> fromGridMap = metricDataMap
                    .get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);

            List<Long> keyList = null == fromSolarMap
                    ? ListUtil.empty()
                    : fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                    .collect(Collectors.toList());
            BigDecimal totalFromSolar = BigDecimal.ZERO;
            BigDecimal totalFromGrid = BigDecimal.ZERO;
            BigDecimal totalToGrid = BigDecimal.ZERO;
            BigDecimal totalFromBattery = BigDecimal.ZERO;
            BigDecimal totalToBattery = BigDecimal.ZERO;
            BigDecimal totalHomeEnergy = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(fromSolarMap)) {
                for (Long time : keyList) {
                    BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
                    fromSolar = fromSolar.signum() == -1 ? new BigDecimal("0") : fromSolar;
                    totalFromSolar = NumberUtil.add(totalFromSolar, fromSolar);

                    BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
                    BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
                    totalFromBattery = NumberUtil.add(totalFromBattery, fromBattery);
                    totalToBattery = NumberUtil.add(totalToBattery, toBattery);

                    BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());
                    BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
                    totalFromGrid = NumberUtil.add(totalFromGrid, fromGrid);
                    totalToGrid = NumberUtil.add(totalToGrid, toGrid);

                    BigDecimal homeEnergy = NumberUtil.round(
                            NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid, fromBattery), toGrid, toBattery), 2,
                            RoundingMode.HALF_UP
                    );
                    homeEnergyDps.put(time, homeEnergy);
                    totalHomeEnergy = NumberUtil.add(totalHomeEnergy, homeEnergy);
                }
            }

            oemEnergyDto.setFromBattery(totalFromBattery);
            oemEnergyDto.setToBattery(totalToBattery);
            oemEnergyDto.setFromGrid(totalFromGrid);
            oemEnergyDto.setToGrid(totalToGrid);
            oemEnergyDto.setFromSolar(totalFromSolar);
            oemEnergyDto.setFromSolarDps(fromSolarMap);
            oemEnergyDto.setFromBatteryDps(fromBatteryMap);
            oemEnergyDto.setToBatteryDps(toBatteryMap);
            oemEnergyDto.setFromGridDps(fromGridMap);
            oemEnergyDto.setToGridDps(toGridMap);

            oemEnergyDto.setHomeEnergyDps(homeEnergyDps);
            oemEnergyDto.setHomeEnergy(NumberUtil.round(totalHomeEnergy, 2, RoundingMode.HALF_UP));

            if (totalFromSolar.compareTo(BigDecimal.ZERO) == 0 || totalHomeEnergy.compareTo(BigDecimal.ZERO) == 0) {
                oemEnergyDto.setSelfPowered(BigDecimal.ZERO);
            } else {
                BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(totalFromSolar, totalHomeEnergy), 2,
                        RoundingMode.HALF_UP
                );
                BigDecimal percentData = NumberUtil.mul(
                        solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                        new BigDecimal(100)
                );
                oemEnergyDto.setSelfPowered(percentData);
            }

            oemEnergyDto.setCycleTimes(homeAdapter.computeBatteryCycleTimes(lastPointWorker1Wrapper.getWorkResult().getResult(), lastPointWorker2Wrapper.getWorkResult().getResult()));

        }

        return oemEnergyDto;
    }

    @Override
    public OemRealTimePowerDto oemRealTimePower(HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo) {
        String deviceId = homeNowDeviceRealtimeVo.getDeviceId();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo, deviceId);
        String offset = userPair.getKey().getTimeZone();
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(userPair.getValue());

        Map<String, LinkedHashMap<Long, Object>> metricMap = timeSeriesDatabaseService.graphQuery(
                userPair.getValue().getDeviceSn(), CommonConstants.SH_REALTIME_POWER_V2, TimeUtil.getDayStart(0, offset),
                TimeUtil.getCurrentTime(offset), true
        );

        HomeNowDeviceRealtimeDto homeNowDeviceRealtimeDto = homeAdapter.packageNowDeviceRealtimeResult(metricMap);
        OemRealTimePowerDto oemRealTimePowerDto = new OemRealTimePowerDto();
        CglibUtil.copy(homeNowDeviceRealtimeDto, oemRealTimePowerDto);
        oemRealTimePowerDto.setDate(TimeUtil.longTimestampToSerialStringOffsetGMT8(System.currentTimeMillis(), offset, "dd/MM/yyyy"));
        return oemRealTimePowerDto;
    }

    @Override
    public OemHistoryEnergyDto oemHistoryEnergy(OemHistoryEnergyVo oemHistoryEnergyVo) {
        HomeHistoryVo homeHistoryVo = new HomeHistoryVo();
        CglibUtil.copy(oemHistoryEnergyVo, homeHistoryVo);
        Integer periodType = oemHistoryEnergyVo.getPeriodType();
        if (CommonConstants.PERIOD_DAY == periodType) {
            String choiceMonth = oemHistoryEnergyVo.getChoiceMonth();
            String day15OfMonth = choiceMonth + "-15";
            try {
                long l = LocalDateTimeUtil.parse(day15OfMonth, "yyyy-MM-dd").toEpochSecond(ZoneOffset.ofHours(8));
                homeHistoryVo.setTimestamp(l);
            } catch (Exception e) {
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }
        HistoryHomeBo historyHomeBo = homeAdapter.queryHistoryHomeBo(homeHistoryVo);
        OemHistoryEnergyDto oemHistoryEnergyDto = new OemHistoryEnergyDto();
        CglibUtil.copy(historyHomeBo, oemHistoryEnergyDto);
        return oemHistoryEnergyDto;
    }

    private Map<String, LinkedHashMap<Long, Object>> deltaQueryTSDB(
            String deviceName, List<String> metricList,
            long startTime, long endTime, TimeSeriesDatabaseService timeSeriesDatabaseService, String offset
    ) {
        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(deviceName,
                metricList, startTime, endTime, TsdbSampleEnum.ONE_HOUR_NONE_POINT
        );
        return TSDBAggUtil.aggregateDeltaQueryHourToDay(result, offset);
    }

    @Override
    public HomeDeviceEnergyStatisticsDto getLastWeekSolarAndGridEnergyData(ClientUserDo clientUserDo, String deviceId, Integer offsetDay, String timezone) {
        Pair<HybridSinglePhaseDO, String> pair = validateUserDevice(clientUserDo, deviceId, timezone);
        return computeDeviceLastWeekEnergy(pair.getKey(), pair.getValue(), offsetDay);

    }

    private HomeDeviceEnergyStatisticsDto computeDeviceLastWeekEnergy(
            HybridSinglePhaseDO hybridSinglePhaseDO, String offset, Integer offsetDay
    ) {

        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
        Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(
                hybridSinglePhaseDO.getDeviceSn(), CommonConstants.HOME_ENERGY, TimeUtil.getDayEnd(offsetDay, offset),
                System.currentTimeMillis(), TsdbSampleEnum.ONE_HOUR_NONE_POINT
        );
        LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
        LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
        LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);

        LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
                .get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
        LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
                .get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
        LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);

        if (fromSolarMap2 != null) {
            for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
                Long time = entry.getKey();
                Object value = entry.getValue();
                if (fromSolarMap.containsKey(time)) {
                    // 如果键已存在，则将值相加
                    BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
                    BigDecimal additionalValue = new BigDecimal(value.toString());
                    BigDecimal sum = originalValue.add(additionalValue);
                    fromSolarMap.put(time, sum);
                } else {
                    // 如果键不存在，则添加键值对
                    fromSolarMap.put(time, value);
                }
            }
        }

        // 更改原有的Map的定义方式
        Map<Integer, HomeDeviceEnergyStatisticsDto.DeviceEnergy> map = new HashMap<>();
        Map<Integer, HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy> map2 = new HashMap<>();
        Map<Integer, HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy> map3 = new HashMap<>();

        // 使用for循环设置默认的键和值
        for (int i = 1; i <= 7; i++) {
            // 初始化值为0
            map.put(i, new HomeDeviceEnergyStatisticsDto.DeviceEnergy());
            map2.put(i, new HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy());
            map3.put(i, new HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy());
        }
        List<Long> keyList = null == fromSolarMap
                ? ListUtil.empty()
                : fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());

        HomeDeviceEnergyStatisticsDto homeDeviceEnergyStatisticsDto = new HomeDeviceEnergyStatisticsDto();


        for (Long time : keyList) {
            assert fromSolarMap != null;
            BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
            BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
            BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());

            Integer dayOfWeekend = TimeUtil.getDayOfWeekend(time * 1000, offset);
            BigDecimal solar = fromSolar.signum() == -1 ? new BigDecimal(0) : fromSolar;

            BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
            BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
            BigDecimal homeEnergy = NumberUtil.round(
                    NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid, fromBattery), toGrid, toBattery), 3,
                    RoundingMode.HALF_UP
            );


            HomeDeviceEnergyStatisticsDto.DeviceEnergy deviceEnergy;
            HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy carbonEmissionsDeviceEnergy;
            HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy saveStandardCoalDeviceEnergy;
            if (!map.containsKey(dayOfWeekend)) {
                deviceEnergy = new HomeDeviceEnergyStatisticsDto.DeviceEnergy();
                carbonEmissionsDeviceEnergy = new HomeDeviceEnergyStatisticsDto.CarbonEmissionsDeviceEnergy();
                saveStandardCoalDeviceEnergy = new HomeDeviceEnergyStatisticsDto.SaveStandardCoalDeviceEnergy();
                deviceEnergy.setSolarEnergy(NumberUtil.round(solar, 3, RoundingMode.HALF_UP));
                carbonEmissionsDeviceEnergy.setCarbonEmissions(NumberUtil.round(NumberUtil.mul(solar, 0.997), 3, RoundingMode.HALF_UP));
                saveStandardCoalDeviceEnergy.setSaveStandardCoal(NumberUtil.round(NumberUtil.mul(solar, 0.404), 3, RoundingMode.HALF_UP));
                deviceEnergy.setGridEnergy(NumberUtil.round(fromGrid, 3, RoundingMode.HALF_UP));
                deviceEnergy.setToGrid(NumberUtil.round(toGrid, 3, RoundingMode.HALF_UP));
                deviceEnergy.setHomeEnergy(homeEnergy);
            } else {
                deviceEnergy = map.get(dayOfWeekend);
                carbonEmissionsDeviceEnergy = map2.get(dayOfWeekend);
                saveStandardCoalDeviceEnergy = map3.get(dayOfWeekend);
                deviceEnergy.setSolarEnergy(NumberUtil.round(NumberUtil.add(deviceEnergy.getSolarEnergy(), solar), 3, RoundingMode.HALF_UP));
                carbonEmissionsDeviceEnergy.setCarbonEmissions(NumberUtil.round(NumberUtil.add(carbonEmissionsDeviceEnergy.getCarbonEmissions(), NumberUtil.mul(solar, 0.997)), 3, RoundingMode.HALF_UP));
                saveStandardCoalDeviceEnergy.setSaveStandardCoal(NumberUtil.round(NumberUtil.add(saveStandardCoalDeviceEnergy.getSaveStandardCoal(), NumberUtil.mul(solar, 0.404)), 3, RoundingMode.HALF_UP));
                deviceEnergy.setGridEnergy(NumberUtil.round(NumberUtil.add(deviceEnergy.getGridEnergy(), fromGrid), 3, RoundingMode.HALF_UP));
                deviceEnergy.setToGrid(NumberUtil.round(NumberUtil.add(deviceEnergy.getToGrid(), toGrid), 3, RoundingMode.HALF_UP));
                deviceEnergy.setHomeEnergy(NumberUtil.round(NumberUtil.add(deviceEnergy.getHomeEnergy(), homeEnergy), 3, RoundingMode.HALF_UP));
            }

            if (deviceEnergy.getSolarEnergy().compareTo(BigDecimal.ZERO) == 0 || deviceEnergy.getHomeEnergy().compareTo(BigDecimal.ZERO) == 0) {
                deviceEnergy.setSelfPowered(BigDecimal.ZERO);
            } else {
                BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(deviceEnergy.getSolarEnergy(), deviceEnergy.getToGrid()), deviceEnergy.getHomeEnergy()), 2, RoundingMode.HALF_UP);
                // 如果solarPercent小于等于0，则将其设置为0
                if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                    solarPercent = BigDecimal.ZERO;
                }
                BigDecimal percentData = NumberUtil.mul(
                        solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                        new BigDecimal(100)
                );
                deviceEnergy.setSelfPowered(percentData);
            }

            map.put(dayOfWeekend, deviceEnergy);
            map2.put(dayOfWeekend, carbonEmissionsDeviceEnergy);
            map3.put(dayOfWeekend, saveStandardCoalDeviceEnergy);
        }

        // 转换成 double
        // 计算总和
        homeDeviceEnergyStatisticsDto.setLastWeekTotalSolar(NumberUtil.round(map.size() != 0
                ? BigDecimal.valueOf(map.values().stream()
                .mapToDouble(deviceEnergy -> deviceEnergy.getSolarEnergy().doubleValue()) // 转换成 double
                .sum()) // 计算总和
                : BigDecimal.ZERO, 3, RoundingMode.HALF_UP));

        homeDeviceEnergyStatisticsDto.setLastWeekTotalGrid(NumberUtil.round(map.size() != 0
                ? BigDecimal.valueOf(map.values().stream()
                .mapToDouble(deviceEnergy -> deviceEnergy.getGridEnergy().doubleValue()) // 转换成 double
                .sum()) // 计算总和
                : BigDecimal.ZERO, 3, RoundingMode.HALF_UP));

        homeDeviceEnergyStatisticsDto.setLastWeekTotalCarbonEmissions(NumberUtil.round(map.size() != 0
                ? BigDecimal.valueOf(map2.values().stream()
                .mapToDouble(deviceEnergy -> deviceEnergy.getCarbonEmissions().doubleValue()) // 转换成 double
                .sum()) // 计算总和
                : BigDecimal.ZERO, 3, RoundingMode.HALF_UP));

        homeDeviceEnergyStatisticsDto.setLastWeekTotalSaveStandardCoal(NumberUtil.round(map.size() != 0
                ? BigDecimal.valueOf(map3.values().stream()
                .mapToDouble(deviceEnergy -> deviceEnergy.getSaveStandardCoal().doubleValue()) // 转换成 double
                .sum()) // 计算总和
                : BigDecimal.ZERO, 3, RoundingMode.HALF_UP));

        Integer dayOfWeekend = TimeUtil.getDayOfWeekend(System.currentTimeMillis() , offset);
        homeDeviceEnergyStatisticsDto.setToday(dayOfWeekend);
        homeDeviceEnergyStatisticsDto.setWeekEnergy(map);
        homeDeviceEnergyStatisticsDto.setCarbonEmissionsWeekEnergy(map2);
        homeDeviceEnergyStatisticsDto.setSaveStandardCoalWeekEnergy(map3);

        return homeDeviceEnergyStatisticsDto;
    }


    @Override
    public InsightDeviceDataDto queryDeviceInsightData(ClientUserDo clientUserDo, InsightDeviceDataVo insightDeviceDataVo) {
        // 校验是否是用户本人
        Pair<ClientUserDo, HybridSinglePhaseDO> pair = homeAdapter.validateDeviceOwner(clientUserDo, insightDeviceDataVo.getDeviceId());
        String offset = pair.getKey().getTimeZone();
        String deviceId = insightDeviceDataVo.getDeviceId();
        HybridSinglePhaseDO hybridSinglePhaseDO = pair.getValue();
        long firstInstallTime = hybridSinglePhaseDO.getFirstInstall();
        InsightDeviceDataDto insightDeviceDataDto = new InsightDeviceDataDto();
        Pair<Long, Long> timePair = pairStartTimeAndEndTimeForInsight(insightDeviceDataVo.getPeriodType(),insightDeviceDataVo.getTimestamp(),firstInstallTime, offset);

        // 检测是否是按天去查询
        if (insightDeviceDataVo.getPeriodType() == CommonConstants.PERIOD_DAY) {
            // 查询一天的功率曲线
            HomeNowDeviceRealtimeDto deviceRealtimeDto = this.queryDayDeviceRealtime(clientUserDo, deviceId, timePair.getKey(), timePair.getValue());

            // 查询一天的总功耗
            InsightDeviceStatisticsDto insightDeviceStatisticsDto = new InsightDeviceStatisticsDto();
            TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                    .chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
            setHomeEnergyRefStatistics(insightDeviceStatisticsDto, pair.getValue().getDeviceSn(), timePair.getKey(),
                    timePair.getValue(), timeSeriesDatabaseService, pair.getKey().getTimeZone()
            );
            if (insightDeviceStatisticsDto.getFromSolar().compareTo(BigDecimal.ZERO) == 0 || insightDeviceStatisticsDto.getConsumptionEnergy().compareTo(BigDecimal.ZERO) == 0) {
                insightDeviceDataDto.setSelfPowered(BigDecimal.ZERO);
            } else {
                BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(insightDeviceStatisticsDto.getFromSolar(), insightDeviceStatisticsDto.getToGrid()), insightDeviceStatisticsDto.getConsumptionEnergy()), 2, RoundingMode.HALF_UP);
                // 如果solarPercent小于等于0，则将其设置为0
                if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                    solarPercent = BigDecimal.ZERO;
                }
                BigDecimal percentData = NumberUtil.mul(
                        solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                        new BigDecimal(100)
                );
                insightDeviceDataDto.setSelfPowered(percentData);
            }

            insightDeviceDataDto.setDeviceRealtimeDto(deviceRealtimeDto);
            insightDeviceDataDto.setDeviceStatisticsDto(insightDeviceStatisticsDto);
            return insightDeviceDataDto;
        }

        HistoryHomeBo historyHomeBo = queryHomeBo(clientUserDo, insightDeviceDataVo, timePair.getKey(), timePair.getValue());
        InsightConsumptionDataDto consumptionDataDto = new InsightConsumptionDataDto();
        CglibUtil.copy(historyHomeBo, consumptionDataDto);
        consumptionDataDto.setSelfPoweredDps(getSelfPoweredDps(consumptionDataDto));
        insightDeviceDataDto.setInsightConsumptionDataDto(consumptionDataDto);

        InsightDeviceStatisticsDto insightDeviceStatisticsDto = new InsightDeviceStatisticsDto();
        CglibUtil.copy(historyHomeBo, insightDeviceStatisticsDto);
        insightDeviceDataDto.setSelfPowered(historyHomeBo.getSelfPowered());
        insightDeviceStatisticsDto.setConsumptionEnergy(historyHomeBo.getHomeEnergy());
        insightDeviceDataDto.setDeviceStatisticsDto(insightDeviceStatisticsDto);

        return insightDeviceDataDto;
    }

    @Override
    public CustomizeInfoDto readCustomize(String deviceId) {
        Pair<String, HybridSinglePhaseDO> pair = authenticateDevice(deviceId);
        CustomizeInfoDto customizeInfoDto = new CustomizeInfoDto();
        OperationUtil
                .of(clientCustomizeService
                        .getOne(Wrappers.<ClientCustomizeDo>lambdaQuery().eq(ClientCustomizeDo::getDeviceId, deviceId)))
                .ifPresentOrElse(
                        clientCustomizeDo -> this.queryDataFromDb(clientCustomizeDo, customizeInfoDto),
                        () -> this.queryFromDevice(pair.getValue(), customizeInfoDto)
                );
        TimezoneUtil.convertGMTToUserTimezone(customizeInfoDto, pair.getKey());
        return customizeInfoDto;
    }

    private Pair<String, HybridSinglePhaseDO> authenticateDevice(String deviceId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 如果家庭中有这台设备就不做校验
        Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, deviceId);
        if (checkUserHasDevice) {
            HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));
            return Pair.of(clientUserDo.getTimeZone(), hybridSinglePhaseDO);
        }

        List<MiddleClientUserDeviceDo> bindList = middleClientUserDeviceService.list(Wrappers
                .<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId));

        if (bindList.size() == 0) {
            log.warn("无权限对设备进行操作");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        }
        HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));
        return Pair.of(clientUserDo.getTimeZone(), hybridSinglePhaseDO);
    }

    private void queryDataFromDb(ClientCustomizeDo clientCustomizeDo, CustomizeInfoDto customizeInfoDto) {
        customizeInfoDto.setMinCapacity(clientCustomizeDo.getBatteryMin());
        customizeInfoDto.setChargeUseMode(clientCustomizeDo.getChargeMode());
        customizeInfoDto.setCharge1StartTimeHour(clientCustomizeDo.getChargeStartHour1());
        customizeInfoDto.setCharge1StartTimeMinute(clientCustomizeDo.getChargeStartMinute1());
        customizeInfoDto.setCharge1EndTimeHour(clientCustomizeDo.getChargeEndHour1());
        customizeInfoDto.setCharge1EndTimeMinute(clientCustomizeDo.getChargeEndMinute1());
        customizeInfoDto.setCharge2StartTimeHour(clientCustomizeDo.getChargeStartHour2());
        customizeInfoDto.setCharge2StartTimeMinute(clientCustomizeDo.getChargeStartMinute2());
        customizeInfoDto.setCharge2EndTimeHour(clientCustomizeDo.getChargeEndHour2());
        customizeInfoDto.setCharge2EndTimeMinute(clientCustomizeDo.getChargeEndMinute2());
        customizeInfoDto.setDischarge1StartTimeHour(clientCustomizeDo.getDischargeStartHour1());
        customizeInfoDto.setDischarge1StartTimeMinute(clientCustomizeDo.getDischargeStartMinute1());
        customizeInfoDto.setDischarge1EndTimeHour(clientCustomizeDo.getDischargeEndHour1());
        customizeInfoDto.setDischarge1EndTimeMinute(clientCustomizeDo.getDischargeEndMinute1());
        customizeInfoDto.setDischarge2StartTimeHour(clientCustomizeDo.getDischargeStartHour2());
        customizeInfoDto.setDischarge2StartTimeMinute(clientCustomizeDo.getDischargeStartMinute2());
        customizeInfoDto.setDischarge2EndTimeHour(clientCustomizeDo.getDischargeEndHour2());
        customizeInfoDto.setDischarge2EndTimeMinute(clientCustomizeDo.getDischargeEndMinute2());
        customizeInfoDto.setMaxFeedIn(clientCustomizeDo.getMaxFeedIn());
        customizeInfoDto.setEpsBatteryMin(clientCustomizeDo.getEpsBatteryMin());
        customizeInfoDto.setDischargeToGridFlag(clientCustomizeDo.getDischargeToGridFlag());
        customizeInfoDto.setSelfSoc(clientCustomizeDo.getSelfSoc());
        customizeInfoDto.setSelfFeedIn(clientCustomizeDo.getSelfFeedIn());
        customizeInfoDto.setRegularSoc(clientCustomizeDo.getRegularSoc());
        customizeInfoDto.setRegularFeedIn(clientCustomizeDo.getRegularFeedIn());
        customizeInfoDto.setBackupSoc(clientCustomizeDo.getBackupSoc());
        customizeInfoDto.setBackupFeedIn(clientCustomizeDo.getBackupFeedIn());
    }

    @DSTransactional
    private void queryFromDevice(HybridSinglePhaseDO hybridSinglePhaseDO, CustomizeInfoDto customizeInfoDto) {
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        List<Integer> valList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41001, 27, cloud);
        if (CollUtil.isEmpty(valList)) {
            log.warn("读取设备长度为0");
            throw new EcosException(EcosExceptionEnum.READ_DEVICE_CONFIG_ERROR);
        }

        setCustomizeInfoDto(customizeInfoDto, valList);

        ClientCustomizeDo clientCustomizeDo = new ClientCustomizeDo();
        clientCustomizeDo.setId(snowFlakeUtil.generateId());
        clientCustomizeDo.setDeviceId(hybridSinglePhaseDO.getId());

        clientCustomizeDo.setBatteryMin(valList.get(1));
        clientCustomizeDo.setChargeMode(valList.get(2));
        clientCustomizeDo.setChargeStartHour1(valList.get(7));
        clientCustomizeDo.setChargeStartMinute1(valList.get(8));
        clientCustomizeDo.setChargeEndHour1(valList.get(9));
        clientCustomizeDo.setChargeEndMinute1(valList.get(10));

        clientCustomizeDo.setDischargeStartHour1(valList.get(12));
        clientCustomizeDo.setDischargeStartMinute1(valList.get(13));
        clientCustomizeDo.setDischargeEndHour1(valList.get(14));
        clientCustomizeDo.setDischargeEndMinute1(valList.get(15));

        clientCustomizeDo.setChargeStartHour2(valList.get(17));
        clientCustomizeDo.setChargeStartMinute2(valList.get(18));
        clientCustomizeDo.setChargeEndHour2(valList.get(19));
        clientCustomizeDo.setChargeEndMinute2(valList.get(20));

        clientCustomizeDo.setDischargeStartHour2(valList.get(22));
        clientCustomizeDo.setDischargeStartMinute2(valList.get(23));
        clientCustomizeDo.setDischargeEndHour2(valList.get(24));
        clientCustomizeDo.setDischargeEndMinute2(valList.get(25));

        List<Integer> feedInList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41037, 1, cloud);
        List<Integer> epsBatteryMinList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41042, 1, cloud);
        List<Integer> dischargeToGridFlagList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 40046, 1, cloud);

        if (feedInList.size() > 0) {
            customizeInfoDto.setMaxFeedIn(feedInList.get(0));
            clientCustomizeDo.setMaxFeedIn(feedInList.get(0));
        }

        if (epsBatteryMinList.size() > 0) {
            customizeInfoDto.setEpsBatteryMin(epsBatteryMinList.get(0));
            clientCustomizeDo.setEpsBatteryMin(epsBatteryMinList.get(0));
        }

        if (dischargeToGridFlagList.size() > 0) {
            customizeInfoDto.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
            clientCustomizeDo.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
        }

        ClientCustomizeDo clientCustomizeDo2 = convertClientCustomizeDo(clientCustomizeDo);

        ActionFlagUtil.assertTrue(clientCustomizeService.save(clientCustomizeDo2));
    }

    private void setCustomizeInfoDto(CustomizeInfoDto customizeInfoDto, List<Integer> valList) {
        val resLen = 27;
        if (valList.size() != resLen) {
            log.warn("参数长度错误");
            throw new EcosException(EcosExceptionEnum.INVALID_RESULT_LEN);
        }
        customizeInfoDto.setMinCapacity(valList.get(1));
        customizeInfoDto.setChargeUseMode(valList.get(2));

        customizeInfoDto.setCharge1StartTimeHour(valList.get(7));
        customizeInfoDto.setCharge1StartTimeMinute(valList.get(8));
        customizeInfoDto.setCharge1EndTimeHour(valList.get(9));
        customizeInfoDto.setCharge1EndTimeMinute(valList.get(10));

        customizeInfoDto.setDischarge1StartTimeHour(valList.get(12));
        customizeInfoDto.setDischarge1StartTimeMinute(valList.get(13));
        customizeInfoDto.setDischarge1EndTimeHour(valList.get(14));
        customizeInfoDto.setDischarge1EndTimeMinute(valList.get(15));

        customizeInfoDto.setCharge2StartTimeHour(valList.get(17));
        customizeInfoDto.setCharge2StartTimeMinute(valList.get(18));
        customizeInfoDto.setCharge2EndTimeHour(valList.get(19));
        customizeInfoDto.setCharge2EndTimeMinute(valList.get(20));

        customizeInfoDto.setDischarge2StartTimeHour(valList.get(22));
        customizeInfoDto.setDischarge2StartTimeMinute(valList.get(23));
        customizeInfoDto.setDischarge2EndTimeHour(valList.get(24));
        customizeInfoDto.setDischarge2EndTimeMinute(valList.get(25));
    }

    private ClientCustomizeDo convertClientCustomizeDo(ClientCustomizeDo clientCustomizeDo) {

        ClientCustomizeDo clientCustomizeDo2 = new ClientCustomizeDo();
        CglibUtil.copy(clientCustomizeDo,clientCustomizeDo2);
        Integer mode = clientCustomizeDo.getChargeMode();
        val selfPowered = 0;
        val loadShifting = 1;
        val backup = 2;

        switch (mode) {
            case selfPowered:
                clientCustomizeDo2.setSelfSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setSelfFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            case loadShifting:
                clientCustomizeDo2.setRegularSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setRegularFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            case backup:
                clientCustomizeDo2.setBackupSoc(clientCustomizeDo.getBatteryMin());
                clientCustomizeDo2.setBackupFeedIn(clientCustomizeDo.getMaxFeedIn());
                break;
            default:
                log.warn("参数错误");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }


        return clientCustomizeDo2;
    }

    @Override
    public void writeCustomizeV2(CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {
        // 转换参数 customizeInfoEzV2Vo --> customizeInfoEzVo
        CustomizeInfoEzVo customizeInfoEzVo = convertCustomizeInfoEzVo(customizeInfoEzV2Vo);
        writeCustomize(TransformUtil.ezToCustomizeInfoVo(customizeInfoEzVo));
    }

    @Override
    public Map<String, BigDecimal> getSoc(String userId,HybridSinglePhaseDO hybridSinglePhaseDO) {

        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);
        Dict lastPointDict = timeSeriesDatabaseService.lastPoint(hybridSinglePhaseDO.getDeviceSn(), CommonConstants.BAT_SOC, System.currentTimeMillis());

        if (((int) lastPointDict.values().stream().filter(Objects::isNull).count()) > 0) {
            log.warn("首页数据存在Null: {}", JSONUtil.toJsonStr(lastPointDict));
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }

        String pointDictStr = lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC);
        String batteryPower = lastPointDict.getStr(TsdbMetricsConstants.BAT_P);

        Map<String, BigDecimal> map = new HashMap<>();
        map.put(TsdbMetricsConstants.BAT_SOC, BigDecimal.ZERO);
        map.put(TsdbMetricsConstants.BAT_P, BigDecimal.ZERO);
        if (StrUtil.isNotBlank(pointDictStr)) {
            map.put(TsdbMetricsConstants.BAT_SOC, NumberUtil.div(new BigDecimal(pointDictStr), new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        }

        if (StrUtil.isNotBlank(batteryPower)) {
            BigDecimal data = NumberUtil.round(new BigDecimal(batteryPower), 0, RoundingMode.HALF_UP);
            map.put(TsdbMetricsConstants.BAT_P, (Math.abs(data.doubleValue()) < 10L) ? BigDecimal.ZERO : data);
        }

        return map;
    }

    @Override
    public SinglePhaseBaseInfoDto queryDeviceDetail(String deviceId) {
        Future<SinglePhaseBaseInfoDto.BindAccount> future = ThreadUtil.execAsync(() -> getBindInstall(deviceId));
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);
        String deviceName = hybridSinglePhaseDO.getAlias();

        if (Objects.equals(deviceName, "")) {
            deviceName = hybridSinglePhaseDO.getDeviceModel();
        }

        SinglePhaseBaseInfoDto baseInfoDto = new SinglePhaseBaseInfoDto();
        baseInfoDto.setName(deviceName);
        String deviceModel = hybridSinglePhaseDO.getDeviceModel();
        if (deviceModel != null && deviceModel.contains("Wifo_pro")) {
            deviceModel = "";
        }
        baseInfoDto.setDeviceModel(deviceModel);
        baseInfoDto.setType(hybridSinglePhaseDO.getDataSource());
        baseInfoDto.setDeviceSn(hybridSinglePhaseDO.getDeviceName());
        baseInfoDto.setWifiSn(hybridSinglePhaseDO.getWifiSn());
        baseInfoDto.setEmsSoftwareVersion(hybridSinglePhaseDO.getEmsSoftwareVersion());
        baseInfoDto.setDsp1SoftwareVersion(hybridSinglePhaseDO.getDsp1SoftwareVersion());

        // 查询设备的子账号
        DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
        OssGlobalConfigBo configBo = globalConfig.getData();
        List<SinglePhaseBaseInfoDto.BindAccount> bindAccountList = middleClientUserDeviceService
                .list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                        .orderByDesc(MiddleClientUserDeviceDo::getCreateTime))
                .stream().filter(middleClientUserDeviceDo -> {
                    Long userId = middleClientUserDeviceDo.getUserId();
                    ClientUserDo userDo = clientUserService.getById(userId);
                    if (null != configBo && configBo.getAftersales().contains(userDo.getUsername())) {
                        return false;
                    }
                    if (!userId.equals(clientUserDo.getId())) {
                        return true;
                    } else {
                        baseInfoDto.setMaster(middleClientUserDeviceDo.getMaster());
                        return false;
                    }
                }).map(middleClientUserDeviceDo -> {
                    SinglePhaseBaseInfoDto.BindAccount bindAccount = new SinglePhaseBaseInfoDto.BindAccount();
                    bindAccount.setSeriesId(1);
                    ClientUserDo bindUser = clientUserService.getById(middleClientUserDeviceDo.getUserId());
                    bindAccount.setAccount(bindUser.getUsername());
                    bindAccount.setBindTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(
                            middleClientUserDeviceDo.getCreateTime(), clientUserDo.getTimeZone()));
                    bindAccount.setMaster(middleClientUserDeviceDo.getMaster());
                    bindAccount.setAccountId(String.valueOf(middleClientUserDeviceDo.getUserId()));
                    return bindAccount;
                }).collect(Collectors.toList());
        try {
            SinglePhaseBaseInfoDto.BindAccount bindInstall = future.get();
            if (StrUtil.isNotBlank(bindInstall.getAccountId())) {
                bindAccountList.add(bindInstall);
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error(String.format("getBindInstall failed: %s", deviceId), e);
        }
        baseInfoDto.setAccountList(bindAccountList);
        return baseInfoDto;
    }

    @Override
    public Integer getPowerLimit(Long deviceId) {
        HybridSinglePhaseDO deviceInfo = hubService.getById(deviceId);
        List<Integer> powerLimit = ecosIotApi.readDevice(deviceInfo.getWifiSn(), 1, 40049, 1, String.valueOf(deviceInfo.getDataSource()));
        if (CollUtil.isEmpty(powerLimit)) {
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }
        return powerLimit.get(0);
    }

    @Override
    public void savePowerLimit(PowerLimitVO powerLimit) {
        HybridSinglePhaseDO deviceInfo = hubService.getById(powerLimit.getDeviceId());
        ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 40049, 1,
                Collections.singletonList(powerLimit.getPowerLimit()), String.valueOf(deviceInfo.getDataSource()));
    }

    /** 获取绑定安装商详情 */
    private SinglePhaseBaseInfoDto.BindAccount getBindInstall(String deviceId) {
        InstallBoundInfoDTO bindInstallInfo = hubService.getBindInstallInfo(deviceId);
        return SinglePhaseBaseInfoDto.BindAccount.builder()
                .seriesId(1)
                .master(0)
                .accountId(bindInstallInfo.getInstallId())
                .account(bindInstallInfo.getInstallName())
                .bindTime(bindInstallInfo.getBindTime())
                .countdownTime(bindInstallInfo.getCountdownTime())
                .isInstaller(true)
                .build();
    }

    private CustomizeInfoEzVo convertCustomizeInfoEzVo(CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {

        CustomizeInfoEzVo customizeInfoEzVo = new CustomizeInfoEzVo();
        CglibUtil.copy(customizeInfoEzV2Vo,customizeInfoEzVo);
        Integer mode = customizeInfoEzV2Vo.getChargeUseMode();
        val selfPowered = 0;
        val loadShifting = 1;
        val backup = 2;

        switch (mode) {
            case selfPowered:
                customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getSelfSoc());
                customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getSelfFeedIn());
                break;
            case loadShifting:
                customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getRegularSoc());
                customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getRegularFeedIn());
                break;
            case backup:
                customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getBackupSoc());
                customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getBackupFeedIn());
                break;
            default:
                log.warn("参数错误");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }

        return customizeInfoEzVo;
    }

    public void writeCustomize(CustomizeInfoVo customizeInfoVo) {

        Pair<String, HybridSinglePhaseDO> pair = authenticateDevice(customizeInfoVo.getDeviceId());
        TimezoneUtil.convertUserTimezoneToGMT(customizeInfoVo, pair.getKey());

        ClientCustomizeDo clientCustomizeDo = Optional
                .ofNullable(clientCustomizeService.getOne(Wrappers.<ClientCustomizeDo>lambdaQuery()
                        .eq(ClientCustomizeDo::getDeviceId, customizeInfoVo.getDeviceId())))
                .orElseThrow(() -> new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR));

        List<Integer> params = buildPostParams(customizeInfoVo, clientCustomizeDo);

        HybridSinglePhaseDO hybridSinglePhaseDO = pair.getValue();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        ecosIotApi.writeDevice(pair.getValue().getWifiSn(), 1, 41001, 27, params, cloud);
        Optional.ofNullable(customizeInfoVo.getMaxFeedIn()).ifPresent(feedIn -> {
            Integer param = com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(feedIn, 0, 100, 0, false);
            ecosIotApi.writeDevice(pair.getValue().getWifiSn(), 1, 41037, 1, ListUtil.toLinkedList(param), cloud);
            clientCustomizeDo.setMaxFeedIn(param);
        });
        Optional.ofNullable(customizeInfoVo.getEpsBatteryMin()).ifPresent(epsBatteryMin -> {
            Integer param = com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(epsBatteryMin, 10, 100, 10, false);
            ecosIotApi.writeDevice(pair.getValue().getWifiSn(), 1, 41042, 1, ListUtil.toLinkedList(param), cloud);
            clientCustomizeDo.setEpsBatteryMin(param);
        });
        Optional.ofNullable(customizeInfoVo.getDischargeToGridFlag()).ifPresent(dischargeFlag -> {
            Integer param = com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(dischargeFlag, 0, 1, 0, false);
            ecosIotApi.writeDevice(pair.getValue().getWifiSn(), 1, 40046, 1, ListUtil.toLinkedList(param), cloud);
            clientCustomizeDo.setDischargeToGridFlag(param);
        });
        // 转换一下再去更新
        ClientCustomizeDo clientCustomizeDo2 = convertClientCustomizeDo(clientCustomizeDo);
        ActionFlagUtil.assertTrue(clientCustomizeService.updateById(clientCustomizeDo2));
    }

    private List<Integer> buildPostParams(CustomizeInfoVo customizeInfoVo, ClientCustomizeDo clientCustomizeDo) {
        Integer chargeUseMode = customizeInfoVo.getChargeUseMode();
        Integer minCapacity = customizeInfoVo.getMinCapacity();

        val paramLen = 27;
        List<Integer> params = new ArrayList<>(paramLen);
        for (int i = 0; i < paramLen; i++) {
            params.add(0);
        }

        val selfPowered = 0;
        val loadShifting = 1;
        val backup = 2;

        switch (chargeUseMode) {
            case selfPowered:
                params.set(0, 4);
                params.set(1, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, true));
                params.set(2, selfPowered);
                clientCustomizeDo.setBatteryMin(com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, true));
                clientCustomizeDo.setChargeMode(selfPowered);
                break;
            case loadShifting:
                com.weihengtech.ecos.utils.NumberUtil.validChargeTimeCross(customizeInfoVo);
                setListParam(params, minCapacity, customizeInfoVo);
                setClientCustomizeDo(clientCustomizeDo, minCapacity, customizeInfoVo);
                break;
            case backup:
                params.set(0, 4);
                params.set(1, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
                params.set(2, backup);
                clientCustomizeDo.setBatteryMin(com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
                clientCustomizeDo.setChargeMode(backup);
                break;
            default:
                log.warn("参数错误");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        return params;
    }

    private void setListParam(List<Integer> params, Integer minCapacity, CustomizeInfoVo customizeInfoVo) {
        params.set(0, 0);
        params.set(1, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
        params.set(2, 1);
        params.set(7, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeHour(), 0, 23, 0, true));
        params.set(8, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeMinute(), 0, 59, 0, true));
        params.set(9, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeHour(), 0, 23, 0, true));
        params.set(10, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeMinute(), 0, 59, 0, true));
        params.set(11, 6000);
        params.set(12, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeHour(), 0, 23, 0, true));
        params.set(13, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeMinute(), 0, 59, 0, true));
        params.set(14, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeHour(), 0, 23, 0, true));
        params.set(15, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeMinute(), 0, 59, 0, true));
        params.set(16, 6000);
        params.set(17, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeHour(), 0, 23, 0, true));
        params.set(18, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeMinute(), 0, 59, 0, true));
        params.set(19, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeHour(), 0, 23, 0, true));
        params.set(20, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeMinute(), 0, 59, 0, true));
        params.set(21, 6000);
        params.set(22, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeHour(), 0, 23, 0, true));
        params.set(23, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeMinute(), 0, 59, 0, true));
        params.set(24, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeHour(), 0, 23, 0, true));
        params.set(25, com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeMinute(), 0, 59, 0, true));
        params.set(26, 6000);
    }

    private void setClientCustomizeDo(
            ClientCustomizeDo clientCustomizeDo, Integer minCapacity,
            CustomizeInfoVo customizeInfoVo
    ) {
        clientCustomizeDo.setBatteryMin(com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));

        clientCustomizeDo.setChargeMode(1);
        clientCustomizeDo.setChargeStartHour1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setChargeStartMinute1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeMinute(), 0, 59, 0, true));
        clientCustomizeDo.setChargeEndHour1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setChargeEndMinute1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeMinute(), 0, 59, 0, true));

        clientCustomizeDo.setChargeStartHour2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setChargeStartMinute2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeMinute(), 0, 59, 0, true));
        clientCustomizeDo.setChargeEndHour2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setChargeEndMinute2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeMinute(), 0, 59, 0, true));

        clientCustomizeDo.setDischargeStartHour1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setDischargeStartMinute1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeMinute(), 0, 59, 0, true));
        clientCustomizeDo.setDischargeEndHour1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setDischargeEndMinute1(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeMinute(), 0, 59, 0, true));

        clientCustomizeDo.setDischargeStartHour2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setDischargeStartMinute2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeMinute(), 0, 59, 0, true));
        clientCustomizeDo.setDischargeEndHour2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeHour(), 0, 23, 0, true));
        clientCustomizeDo.setDischargeEndMinute2(
                com.weihengtech.ecos.utils.NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeMinute(), 0, 59, 0, true));
    }

    public HomeNowDeviceRealtimeDto queryDayDeviceRealtime(ClientUserDo clientUserDo, String deviceId, long startTime, long endTime) {
        Pair<ClientUserDo, HybridSinglePhaseDO> userPair = homeAdapter.validateDeviceOwner(clientUserDo, deviceId);
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(userPair.getValue());
        String deviceName = userPair.getValue().getDeviceSn();
        Map<String, LinkedHashMap<Long, Object>> metricMap;
        metricMap = timeSeriesDatabaseService.graphQuery(
                deviceName,
                CommonConstants.SH_REALTIME_POWER_V2,
                startTime, endTime, false
        );
        return packageNowDeviceRealtimeResult(metricMap);
    }

    public HistoryHomeBo queryHomeBo(ClientUserDo clientUserDo, InsightDeviceDataVo insightDeviceDataVo, long startTime, long endTime) {
        HistoryHomeBo historyHomeBo = new HistoryHomeBo();
        Pair<ClientUserDo, HybridSinglePhaseDO> pair = homeAdapter.validateDeviceOwner(clientUserDo, insightDeviceDataVo.getDeviceId());
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(pair.getValue());

        LinkedHashMap<Long, BigDecimal> homeEnergyDps = new LinkedHashMap<>(1024);

        if (startTime > endTime) {
            return historyHomeBo;
        }

        try {
            historyHomeBo.setCycleTimes(computeBatteryCycleTimes(pair.getValue().getDeviceSn(), startTime,
                    endTime, timeSeriesDatabaseService
            ));
        } catch (Exception e) {
            log.warn(e.getMessage());
        }

        List<String> metricList = buildMetricListByGraph(CommonConstants.GRAPH_HOME_V2);
        Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(pair.getValue().getDeviceSn(), metricList,
                startTime, endTime,
                insightDeviceDataVo.getPeriodType(),
                timeSeriesDatabaseService,
                pair.getKey().getTimeZone()
        );
        if (CollUtil.isNotEmpty(metricDataMap)) {
            LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
            LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
            LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
            LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
            LinkedHashMap<Long, Object> toBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
            LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
            LinkedHashMap<Long, Object> epsMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS);

            if (fromSolarMap2 != null) {
                for (Map.Entry<Long, Object> entry : fromSolarMap2.entrySet()) {
                    Long time = entry.getKey();
                    Object value = entry.getValue();
                    if (fromSolarMap.containsKey(time)) {
                        // 如果键已存在，则将值相加
                        BigDecimal originalValue = new BigDecimal(fromSolarMap.get(time).toString());
                        BigDecimal additionalValue = new BigDecimal(value.toString());
                        BigDecimal sum = originalValue.add(additionalValue);
                        fromSolarMap.put(time, sum);
                    } else {
                        // 如果键不存在，则添加键值对
                        fromSolarMap.put(time, value);
                    }
                }
            }

            historyHomeBo.setFromBatteryDps(fromBatteryMap);
            historyHomeBo.setToBatteryDps(toBatteryMap);
            historyHomeBo.setFromGridDps(fromGridMap);
            historyHomeBo.setToGridDps(toGridMap);
            historyHomeBo.setFromSolarDps(fromSolarMap);
            historyHomeBo.setEpsDps(epsMap);

            List<Long> keyList = null == fromSolarMap
                    ? ListUtil.empty()
                    : fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                    .collect(Collectors.toList());

            BigDecimal totalSolar = BigDecimal.ZERO;
            BigDecimal totalFromGrid = BigDecimal.ZERO;
            BigDecimal totalToGrid = BigDecimal.ZERO;
            BigDecimal totalFromBattery = BigDecimal.ZERO;
            BigDecimal totalToBattery = BigDecimal.ZERO;
            BigDecimal totalHomeEnergy = BigDecimal.ZERO;
            BigDecimal totalEps = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(fromSolarMap)) {
                for (Long time : keyList) {
                    BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
                    fromSolar = fromSolar.signum() == -1 ? new BigDecimal("0") : fromSolar;

                    BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());
                    BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());

                    BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
                    BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
                    BigDecimal homeEnergy = NumberUtil.round(
                            NumberUtil.sub(NumberUtil.add(fromSolar, fromGrid, fromBattery), toGrid, toBattery), 2,
                            RoundingMode.HALF_UP
                    );
                    BigDecimal eps = new BigDecimal(epsMap.getOrDefault(time, "0").toString());

                    homeEnergyDps.put(time, homeEnergy);

                    totalSolar = NumberUtil.add(totalSolar, fromSolar);
                    totalFromGrid = NumberUtil.add(totalFromGrid, fromGrid);
                    totalToGrid = NumberUtil.add(totalToGrid, toGrid);
                    totalFromBattery = NumberUtil.add(totalFromBattery, fromBattery);
                    totalToBattery = NumberUtil.add(totalToBattery, toBattery);
                    totalHomeEnergy = NumberUtil.add(totalHomeEnergy, homeEnergy);
                    totalEps = NumberUtil.add(totalEps, eps);
                }
            }

            historyHomeBo.setHomeEnergyDps(homeEnergyDps);
            historyHomeBo.setFromBattery(totalFromBattery);
            historyHomeBo.setToBattery(totalToBattery);
            historyHomeBo.setFromGrid(totalFromGrid);
            historyHomeBo.setToGrid(totalToGrid);
            historyHomeBo.setFromSolar(totalSolar);
            historyHomeBo.setHomeEnergy(totalHomeEnergy);
            historyHomeBo.setEps(totalEps);
            if (totalSolar.compareTo(BigDecimal.ZERO) == 0 || totalHomeEnergy.compareTo(BigDecimal.ZERO) == 0) {
                historyHomeBo.setSelfPowered(BigDecimal.ZERO);
            } else {
                BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(totalSolar, totalToGrid), totalHomeEnergy), 2, RoundingMode.HALF_UP);
                // 如果solarPercent小于等于0，则将其设置为0
                if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                    solarPercent = BigDecimal.ZERO;
                }
                BigDecimal percentData = NumberUtil.mul(
                        solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                        new BigDecimal(100)
                );
                historyHomeBo.setSelfPowered(percentData);
            }
        }
        return historyHomeBo;
    }

    private Pair<Long, Long> pairStartTimeAndEndTimeForInsight(Integer periodType, Long timestamp, Long firstInstall, String offset) {
        long startTime;
        long endTime;
        switch (periodType) {
            case CommonConstants.PERIOD_MONTH:
                startTime = TimeUtil.getAssignMonthStart(timestamp, offset, 0);
                endTime = TimeUtil.getAssignMonthEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_YEAR:
                startTime = TimeUtil.getYearStart(timestamp, offset);
                endTime = TimeUtil.getYearEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_LIFETIME:
                startTime = firstInstall;
                endTime = TimeUtil.getCurrentTime(offset);
                break;
            default:
                startTime = TimeUtil.getAssignDayStart(timestamp, offset);
                endTime = TimeUtil.getAssignDayEnd(timestamp, offset);
        }
        return Pair.of(startTime, endTime);
    }

    private void updateOrGetEnergyNotify(
            InsightMoreInformationEnergyNotifyDto insightMoreInformationEnergyNotifyDto,
            Long deviceId, Long userId, InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
    ) {
        ClientEnergyNotifyDo clientEnergyNotifyDo = clientEnergyNotifyService
                .getOne(Wrappers.<ClientEnergyNotifyDo>lambdaQuery().eq(ClientEnergyNotifyDo::getUserId, userId)
                        .eq(ClientEnergyNotifyDo::getDeviceId, deviceId));

        if (null == clientEnergyNotifyDo) {
            clientEnergyNotifyDo = new ClientEnergyNotifyDo();
            clientEnergyNotifyDo.setId(snowFlakeUtil.generateId());
            clientEnergyNotifyDo.setDeviceId(deviceId);
            clientEnergyNotifyDo.setUserId(userId);
            CglibUtil.copy(insightMoreInformationEnergyNotifyVo, clientEnergyNotifyDo);
            ActionFlagUtil.assertTrue(clientEnergyNotifyService.save(clientEnergyNotifyDo));
        } else {
            CglibUtil.copy(insightMoreInformationEnergyNotifyVo, clientEnergyNotifyDo);
            ActionFlagUtil.assertTrue(clientEnergyNotifyService.updateById(clientEnergyNotifyDo));
        }
        CglibUtil.copy(clientEnergyNotifyDo, insightMoreInformationEnergyNotifyDto);
    }

    private void reflectSetRunData(String method, String val, HomeNowDeviceRunDataDto homeNowDeviceRunDataDto) {
        log.info("{} {}", method, val);
        if (StrUtil.isNotBlank(val)) {
            BigDecimal data = NumberUtil.round(new BigDecimal(val), 0, RoundingMode.HALF_UP);
            ReflectUtil.invoke(homeNowDeviceRunDataDto, method,
                    (Math.abs(data.doubleValue()) < 10L) ? BigDecimal.ZERO : data
            );
        } else {
            ReflectUtil.invoke(homeNowDeviceRunDataDto, method, BigDecimal.ZERO);
        }
    }

    private void setDeviceRunDataBatterySoc(String val, HomeNowDeviceRunDataDto homeNowDeviceRunDataDto) {
        if (StrUtil.isNotBlank(val)) {
            BigDecimal soc = NumberUtil.div(new BigDecimal(val), new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            homeNowDeviceRunDataDto.setBatterySoc(soc);
        } else {
            homeNowDeviceRunDataDto.setBatterySoc(new BigDecimal("0"));
        }
    }

    private Long saveDeviceInfo(String deviceSn, String wifiSn, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, Integer datacenterId) {
        Long deviceId = snowFlakeUtil.generateId();
        HybridSinglePhaseDO hybridSinglePhaseDO = new HybridSinglePhaseDO();
        hybridSinglePhaseDO.setId(deviceId);
        hybridSinglePhaseDO.setDeviceName(deviceSn);
        hybridSinglePhaseDO.setDeviceSn(deviceSn);
        hybridSinglePhaseDO.setWifiSn(wifiSn);
        hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
        hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getTsdbSource());
        hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
        hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
        hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
        hybridSinglePhaseDO.setDatacenterId(datacenterId);
        initHybridSinglePhase(hybridSinglePhaseDO);
        hubService.save(hybridSinglePhaseDO);
        return deviceId;
    }

    private void updateDeviceInfo(HybridSinglePhaseDO hybridSinglePhaseDO, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, String wifiSn, String deviceSn) {
        String beforeWifiSn = hybridSinglePhaseDO.getWifiSn();
        stringRedisTemplate.opsForValue().set("NEW-BIND:" + beforeWifiSn, "1", 10, TimeUnit.MINUTES);
        hybridSinglePhaseDO.setWifiSn(wifiSn);
        Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
        if (firstInstall == null || firstInstall < 100) {
            hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
        }
        hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
        hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getTsdbSource());
        hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
        hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
        hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
        hubService.updateById(hybridSinglePhaseDO);

        List<HybridSinglePhaseDO> beforeWifiBindDevice = hubService.listOtherBindDevice(wifiSn, deviceSn);

        if (CollUtil.isNotEmpty(beforeWifiBindDevice)) {
            beforeWifiBindDevice.forEach(deviceDo -> {
                deviceDo.setWifiSn("");
                deviceDo.setUpdateTime(LocalDateTime.now());
                hubService.updateById(deviceDo);
            });
        }
    }

    static void initHybridSinglePhase(HybridSinglePhaseDO hybridSinglePhaseDO) {
        hybridSinglePhaseDO.setAlias("");
        hybridSinglePhaseDO.setDeviceModel("");
        hybridSinglePhaseDO.setBrand("");
        hybridSinglePhaseDO.setFactory("");
        hybridSinglePhaseDO.setPowerBoardHardwareVersion("");
        hybridSinglePhaseDO.setDsp1SoftwareVersion("");
        hybridSinglePhaseDO.setDsp2SoftwareVersion("");
        hybridSinglePhaseDO.setEmsSoftwareVersion("");
        hybridSinglePhaseDO.setEmsHardwareVersion("");
        hybridSinglePhaseDO.setBmsGaugeVersion("");
        hybridSinglePhaseDO.setBmsSn("");
        hybridSinglePhaseDO.setBmsVendor("");
        hybridSinglePhaseDO.setBmsSoftwareVersion("");
        hybridSinglePhaseDO.setBmsHardwareVersion("");
        hybridSinglePhaseDO.setState(0);
        hybridSinglePhaseDO.setLongitude(0.0D);
        hybridSinglePhaseDO.setLatitude(0.0D);
        hybridSinglePhaseDO.setVppMode(false);
        hybridSinglePhaseDO.setCreateTime(LocalDateTime.now());
        hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
    }

    private List<String> buildMetricListByGraph(Integer graphType) {
        List<String> metricList = new ArrayList<>();
        switch (graphType) {
            case CommonConstants.GRAPH_HOME:
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
                break;
            case CommonConstants.GRAPH_SOLAR:
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
                break;
            case CommonConstants.GRAPH_SOLAR_2:
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
                break;
            case CommonConstants.GRAPH_BATTERY:
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
                break;
            case CommonConstants.GRAPH_GRID:
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
                break;
            case CommonConstants.GRAPH_HOME_V2:
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
                metricList.add(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
                metricList.add(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS);
                break;
            default:
        }
        return metricList;
    }

    private Map<String, LinkedHashMap<Long, Object>> deltaQueryTSDB(
            String deviceName, List<String> metricList,
            long startTime, long endTime, Integer periodType, TimeSeriesDatabaseService timeSeriesDatabaseService,
            String offset
    ) {
        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(
                deviceName,
                metricList,
                startTime,
                endTime,
                TsdbSampleEnum.ONE_HOUR_NONE_POINT
        );
        if (5 == periodType) {
            return TSDBAggUtil.aggregateDeltaQueryMonthToYear(result, offset);
        } else if (4 == periodType) {
            return TSDBAggUtil.aggregateDeltaQueryDayToMonth(result, offset);
        } else {
            return TSDBAggUtil.aggregateDeltaQueryHourToDay(result, offset);
        }
    }

    private void setHomeEnergyRefStatistics(
            InsightDeviceStatisticsDto insightDeviceStatisticsDto, String deviceFlag,
            long start, long end, TimeSeriesDatabaseService timeSeriesDatabaseService, String offset
    ) {

        Map<String, LinkedHashMap<Long, Object>> metricDataMap = deltaQueryTSDB(deviceFlag, CommonConstants.METRIC_LIST_V2,
                start, end, 1, timeSeriesDatabaseService, offset
        );
        Dict currSocDict = timeSeriesDatabaseService.lastPoint(deviceFlag, Collections.singletonList(TsdbMetricsConstants.BAT_SOC),
                System.currentTimeMillis());

        if (CollUtil.isNotEmpty(metricDataMap)) {
            LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
                    .get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
            LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap
                    .get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
            LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
            LinkedHashMap<Long, Object> fromSolarMap2 = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
            LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
            LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
            LinkedHashMap<Long, Object> epsMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS);

            BigDecimal totalToBattery = BigDecimal.ZERO;
            BigDecimal totalFromBattery = BigDecimal.ZERO;
            BigDecimal totalFromSolar = BigDecimal.ZERO;
            BigDecimal totalToGrid = BigDecimal.ZERO;
            BigDecimal totalFromGrid = BigDecimal.ZERO;
            BigDecimal totalEps = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(fromSolarMap)) {
                for (Long time : fromSolarMap.keySet()) {
                    BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
                    BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
                    BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());
                    BigDecimal fromSolar2 = new BigDecimal(fromSolarMap2.getOrDefault(time, "0").toString());
                    BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
                    BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());
                    BigDecimal eps = new BigDecimal(epsMap.getOrDefault(time, "0").toString());

                    totalToBattery = NumberUtil.add(toBattery, totalToBattery);
                    totalFromBattery = NumberUtil.add(fromBattery, totalFromBattery);
                    totalFromSolar = NumberUtil.add(fromSolar, totalFromSolar);
                    totalFromSolar = NumberUtil.add(fromSolar2, totalFromSolar);
                    totalToGrid = NumberUtil.add(toGrid, totalToGrid);
                    totalFromGrid = NumberUtil.add(fromGrid, totalFromGrid);
                    totalEps = NumberUtil.add(eps, totalEps);
                }
            }

            BigDecimal totalConsumption = NumberUtil.add(totalFromSolar, totalFromBattery, totalFromGrid);
            insightDeviceStatisticsDto.setConsumptionEnergy(NumberUtil
                    .round(
                            NumberUtil.sub(totalConsumption, totalToBattery, totalToGrid), 2,
                            RoundingMode.HALF_UP
                    ));
            insightDeviceStatisticsDto.setFromBattery(NumberUtil.round(totalFromBattery, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setToBattery(NumberUtil.round(totalToBattery, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setFromGrid(NumberUtil.round(totalFromGrid, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setToGrid(NumberUtil.round(totalToGrid, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setFromSolar(NumberUtil.round(totalFromSolar, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setEps(NumberUtil.round(totalEps, 2, RoundingMode.HALF_UP));
            insightDeviceStatisticsDto.setSoc(currSocDict.getBigDecimal(TsdbMetricsConstants.BAT_SOC));
        }
    }

    private Pair<HybridSinglePhaseDO, String> validateUserDevice(ClientUserDo clientUserDo, String deviceId, String timezone) {
        HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));

        if (StrUtil.isBlank(timezone)) {
            // 先判断当前操作的用户下是否有家庭，家庭中是否有该设备
//            ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
            Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, deviceId);
            if (!checkUserHasDevice) {
                Optional.ofNullable(middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())))
                        .orElseThrow(() -> {
                            log.warn("未绑定设备");
                            return new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
                        });
            }
            return Pair.of(hybridSinglePhaseDO, clientUserDo.getTimeZone());
        }
        return Pair.of(hybridSinglePhaseDO, timezone);
    }

    private InsightDeviceEnergyHeatmapStatisticsDto computeDeviceEnergyHeatmap(
            HybridSinglePhaseDO hybridSinglePhaseDO, long start, long end,
            String offset
    ) {

        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

        Map<String, LinkedHashMap<Long, Object>> metricDataMap = timeSeriesDatabaseService.deltaQuery(hybridSinglePhaseDO.getDeviceSn(),
                CommonConstants.METRIC_LIST, start, end,
                TsdbSampleEnum.TWO_HOUR_NONE_POINT
        );
        LinkedHashMap<Long, Object> fromSolarMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
        LinkedHashMap<Long, Object> fromBatteryMap = metricDataMap.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
        LinkedHashMap<Long, Object> fromGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
        LinkedHashMap<Long, Object> toBatteryMap = metricDataMap
                .get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
        LinkedHashMap<Long, Object> toGridMap = metricDataMap.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);

        InsightDeviceEnergyHeatmapStatisticsDto insightDeviceEnergyHeatmapStatisticsDto = new InsightDeviceEnergyHeatmapStatisticsDto();
        insightDeviceEnergyHeatmapStatisticsDto.setMaxEnergyOfHour(new BigDecimal("0"));
        insightDeviceEnergyHeatmapStatisticsDto.setMinEnergyOfHour(new BigDecimal("0"));

        Map<Integer, Map<Integer, List<BigDecimal>>> map = new HashMap<>(TimeUtil.getDaysInStartAndEnd(start, end));
        List<Long> keyList = null == fromSolarMap
                ? ListUtil.empty()
                : fromSolarMap.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());

        BigDecimal max = new BigDecimal(Integer.MIN_VALUE);
        BigDecimal min = new BigDecimal(Integer.MAX_VALUE);

        if (CollUtil.isNotEmpty(fromSolarMap)) {
            for (Long time : keyList) {
                Integer hourOfDay = TimeUtil.getHourOfDay(time * 1000, offset);
                Integer hourKey = hourOfDay / 2;
                Integer dayOfWeekend = TimeUtil.getDayOfWeekend(time * 1000, offset);
                BigDecimal toBattery = new BigDecimal(toBatteryMap.getOrDefault(time, "0").toString());
                BigDecimal toGrid = new BigDecimal(toGridMap.getOrDefault(time, "0").toString());
                BigDecimal fromSolar = new BigDecimal(fromSolarMap.getOrDefault(time, "0").toString());

                BigDecimal solar = fromSolar.signum() == -1 ? new BigDecimal(0) : fromSolar;
                BigDecimal fromBattery = new BigDecimal(fromBatteryMap.getOrDefault(time, "0").toString());
                BigDecimal fromGrid = new BigDecimal(fromGridMap.getOrDefault(time, "0").toString());

                Map<Integer, List<BigDecimal>> innerMap;
                BigDecimal curr = NumberUtil.round(
                        NumberUtil.sub(NumberUtil.add(solar, fromGrid, fromBattery), toGrid, toBattery), 2,
                        RoundingMode.HALF_UP
                );

                if (!map.containsKey(dayOfWeekend)) {
                    innerMap = new HashMap<>(12);
                    innerMap.put(hourKey, ListUtil.toList(curr));
                } else {
                    innerMap = map.get(dayOfWeekend);
                    if (!innerMap.containsKey(hourKey)) {
                        innerMap.put(hourKey, ListUtil.toList(curr));
                    } else {
                        List<BigDecimal> bigDecimalList = innerMap.get(hourKey);
                        bigDecimalList.add(curr);
                        innerMap.put(hourKey, bigDecimalList);
                    }
                }

                max = max.compareTo(curr) > 0 ? max : curr;
                min = min.compareTo(curr) > 0 ? curr : min;
                map.put(dayOfWeekend, innerMap);
            }
        }

        Map<Integer, Map<Integer, BigDecimal>> result = new HashMap<>(map.size());

        for (Integer weekend : map.keySet()) {
            Map<Integer, List<BigDecimal>> innerMap = map.get(weekend);
            Map<Integer, BigDecimal> resultInnerMap = new HashMap<>(innerMap.size());
            for (Integer hour : innerMap.keySet()) {
                BigDecimal sum = innerMap.get(hour).stream().reduce(BigDecimal.ZERO, NumberUtil::add);
                resultInnerMap.put(hour, NumberUtil.div(sum, innerMap.get(hour).size(), 2));
            }
            result.put(weekend, resultInnerMap);
        }

        insightDeviceEnergyHeatmapStatisticsDto.setMaxEnergyOfHour(keyList.size() == 0 ? BigDecimal.ZERO : max);
        insightDeviceEnergyHeatmapStatisticsDto.setMinEnergyOfHour(keyList.size() == 0 ? BigDecimal.ZERO : min);
        insightDeviceEnergyHeatmapStatisticsDto.setWeekEnergyOfHour(result);
        Integer dayOfWeekend = TimeUtil.getDayOfWeekend(System.currentTimeMillis() , offset);
        insightDeviceEnergyHeatmapStatisticsDto.setToday(dayOfWeekend);
        return insightDeviceEnergyHeatmapStatisticsDto;
    }

    public Map<Long, Object> getSelfPoweredDps(InsightConsumptionDataDto consumptionDataDto) {
        LinkedHashMap<Long, Object> selfPoweredDps = new LinkedHashMap<>(1024);
        Map<Long, Object> fromSolarDps = consumptionDataDto.getFromSolarDps();
        Map<Long, Object> toGridDps = consumptionDataDto.getToGridDps();
        Map<Long, Object> homeEnergyDps = consumptionDataDto.getHomeEnergyDps();

        List<Long> keyList = null == fromSolarDps
                ? ListUtil.empty()
                : fromSolarDps.keySet().stream().sorted(Comparator.comparingLong(Long::longValue))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(fromSolarDps)) {
            for (Long time : keyList) {
                BigDecimal fromSolar = new BigDecimal(fromSolarDps.getOrDefault(time, "0").toString());
                BigDecimal toGrid = new BigDecimal(toGridDps.getOrDefault(time, "0").toString());
                BigDecimal homeEnergy = new BigDecimal(homeEnergyDps.getOrDefault(time, "0").toString());

                if (fromSolar.compareTo(BigDecimal.ZERO) == 0 || homeEnergy.compareTo(BigDecimal.ZERO) == 0) {
                    selfPoweredDps.put(time, BigDecimal.ZERO);
                } else {
                    BigDecimal solarPercent = NumberUtil.round(NumberUtil.div(NumberUtil.sub(fromSolar, toGrid), homeEnergy), 2, RoundingMode.HALF_UP);
                    // 如果solarPercent小于等于0，则将其设置为0
                    if (solarPercent.compareTo(BigDecimal.ZERO) <= 0) {
                        solarPercent = BigDecimal.ZERO;
                    }
                    BigDecimal percentData = NumberUtil.mul(
                            solarPercent.compareTo(new BigDecimal(1)) > 0 ? new BigDecimal(1) : solarPercent,
                            new BigDecimal(100)
                    );
                    selfPoweredDps.put(time, percentData);
                }

            }
        }

        return selfPoweredDps;
    }

}
