package com.weihengtech.ecos.service.app;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.enums.ChatRoleEnum;
import com.weihengtech.ecos.model.dos.ClientSessionDo;
import com.weihengtech.ecos.model.dos.ClientSessionMessageDo;

import java.time.LocalDateTime;

/**
 * @program: ecos-server
 * @description: 绘画消息表服务接口
 * @author: jiahao.jin
 * @create: 2024-05-09 15:28
 **/
public interface ClientSessionMessageService extends IService<ClientSessionMessageDo> {

    /**
     * @description: 通过sessionId查询最后一条消息类型非用户的消息
     * @param sessionId 会话id
     * @return com.weihengtech.ecos.model.dos.ClientSessionMessageDo
     */
    ClientSessionMessageDo queryLastMessageBySessionId(Long sessionId);

    ClientSessionMessageDo createMessage(ClientSessionDo session, Long userId, ChatRoleEnum role, String content,
                                         Long parentId, LocalDateTime timestamp);
}
