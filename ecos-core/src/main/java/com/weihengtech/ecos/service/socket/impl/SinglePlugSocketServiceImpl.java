package com.weihengtech.ecos.service.socket.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.DeviceBindStatus;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dtos.socket.SocketHistoryEleDto;
import com.weihengtech.ecos.model.dtos.socket.SocketSwitchLogsDto;
import com.weihengtech.ecos.model.dtos.socket.SocketTimingListDto;
import com.weihengtech.ecos.model.dtos.socket.SwitchLogDto;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketBaseInfoDto;
import com.weihengtech.ecos.enums.socket.SinglePlugSocketPropertyEnum;
import com.weihengtech.ecos.enums.socket.SinglePlugSocketRelayStatusEnum;
import com.weihengtech.ecos.enums.socket.SocketStatusEnum;
import com.weihengtech.ecos.model.vos.socket.SocketAddTimingVo;
import com.weihengtech.ecos.model.vos.socket.SocketCountdownVo;
import com.weihengtech.ecos.model.vos.socket.SocketHistoryEleVo;
import com.weihengtech.ecos.model.vos.socket.SocketInfoVo;
import com.weihengtech.ecos.model.vos.socket.SocketOverChargeSwitchVo;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import com.weihengtech.ecos.model.vos.socket.SocketSwitchLogsVo;
import com.weihengtech.ecos.model.vos.socket.SocketUpdateTimingVo;
import com.weihengtech.ecos.model.vos.socket.SwitchSocketVo;
import com.weihengtech.ecos.prometheus.BindFailMetrics;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientChargeTaskDo;
import com.weihengtech.ecos.model.dos.ClientSocketConfigDO;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.thirdpart.XxlJobInfo;
import com.weihengtech.ecos.service.charger.ClientChargeTaskService;
import com.weihengtech.ecos.service.socket.ClientSocketConfigService;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.BeanUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TSDBAggUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import com.weihengtech.ecos.utils.XxlJobUtil;
import com.weihengtech.sdk.iot.ecos.model.response.CloudDevicePropertyStatusResponse;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLog;
import com.weihengtech.sdk.iot.ecos.model.tuya.response.TuyaDeviceLogsResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: Tuya 单插插座服务实现类
 * @author: jiahao.jin
 * @create: 2024-01-27 16:20
 **/
@Slf4j
@Service
public class SinglePlugSocketServiceImpl implements SinglePlugSocketService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private HubService hubService;
    @Resource
    private MiddleClientUserDeviceService middleClientUserDeviceService;
    @Resource
    private EcosIotApi ecosIotApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private V2HomeAdapter v2HomeAdapter;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ClientUserService clientUserService;
    @Resource
    private OssGlobalConfigApi ossGlobalConfigApi;
    @Resource
    private ClientChargeTaskService clientChargeTaskService;
    @Resource
    private XxlJobUtil xxlJobUtil;
    @Resource
    private RetryService retryService;
    @Resource
    private ClientSocketConfigService clientSocketConfigService;
    @Resource
    private BindFailMetrics bindFailMetrics;

    @Override
    public void bindClientUserDevice(V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, ClientUserDo clientUserDo) {

        Integer type = homeClientUserBindDeviceVo.getType();
        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        Double lon = homeClientUserBindDeviceVo.getLon();
        Double lat = homeClientUserBindDeviceVo.getLat();
        if (lon != null && lat != null) {
            if (lon < -180 || lon > 180 || lat < -90 || lat > 90) {
                log.warn("lon或lat值不正确");
                throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
            }
        }
        String redisKey = RedisRefConstants.buildDeviceBindKey(clientUserDo.getUsername(), wifiSn);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String bindStatus = stringRedisTemplate.opsForValue().get(redisKey);
            if (DeviceBindStatus.OK.equals(bindStatus)) {
                return;
            }
        }
        log.info("开始绑定 {}", JSONUtil.toJsonStr(homeClientUserBindDeviceVo));
        stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.ING, 3, TimeUnit.MINUTES);

        List<HybridSinglePhaseDO> nowBindDeviceList = hubService.nowBindDeviceList(wifiSn);
        if (CollUtil.isNotEmpty(nowBindDeviceList)) {
            for (HybridSinglePhaseDO hybridSinglePhaseDO : nowBindDeviceList) {
                MiddleClientUserDeviceDo nowBindMiddle = middleClientUserDeviceService
                        .getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                                .eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
                                .eq(MiddleClientUserDeviceDo::getDeviceId, hybridSinglePhaseDO.getId()));
                if (nowBindMiddle != null) {
                    Boolean hasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, String.valueOf(hybridSinglePhaseDO.getId()));
                    if (!hasDevice) {
                        // 将设备与家庭建立关系
                        v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(hybridSinglePhaseDO.getId()), StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? hybridSinglePhaseDO.getDeviceSn() : homeClientUserBindDeviceVo.getDeviceAliasName());
                    }
                    hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
                    hubService.updSocket(hybridSinglePhaseDO);
                    stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                    return;
                }
            }
        }

        try {
            Pair<String, String> pair = retryService.getSocketDeviceSn(type, wifiSn);
            if (pair == null || pair.getKey() == null) {
                bindFailMetrics.recordFailure(wifiSn, "获取设备sn失败");
            }
            bindDeviceAction(pair.getKey(), clientUserDo, homeClientUserBindDeviceVo, redisKey, pair.getValue());
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new EcosException(EcosExceptionEnum.DEVICE_BIND_FAILURE);
        }
    }

    @Override
    public void switchSocket(SwitchSocketVo switchSocketVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), switchSocketVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());

        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
        if (!deviceOnline) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }

        Boolean aBoolean = ecosIotApi.switchTuyaSocket(wifiSn, cloud, switchSocketVo.getSwitchStatus(), switchSocketVo.getSwitchIndex());
        if (!aBoolean) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }

        // 检查是否已经开启对应的开关了
        try {
            retryService.checkSocketStatus(wifiSn, cloud, switchSocketVo.getSwitchStatus(), switchSocketVo.getSwitchIndex());
        } catch (Exception e) {
            // 所有的重试尝试都失败了，进行相应的处理
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
    }

    @Override
    public TuyaSocketBaseInfoDto queryTuyaSocketInfo(ClientUserDo clientUserDo, SocketInfoVo socketInfoVo) {
        Future<TuyaSocketBaseInfoDto.BindAccount> future = ThreadUtil.execAsync(() -> getBindInstall(socketInfoVo.getDeviceId()));

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketInfoVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
        String deviceName = hybridSinglePhaseDO.getAlias();

        if (Objects.equals(deviceName, "")) {
            deviceName = hybridSinglePhaseDO.getDeviceModel();
        }

        TuyaSocketBaseInfoDto tuyaSocketBaseInfoDto = new TuyaSocketBaseInfoDto();
        tuyaSocketBaseInfoDto.setName(deviceName);
        tuyaSocketBaseInfoDto.setSwitch1(false);

        // 查询设备的子账号
        DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
        OssGlobalConfigBo configBo = globalConfig.getData();
        List<TuyaSocketBaseInfoDto.BindAccount> bindAccountList = middleClientUserDeviceService
                .list(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                        .eq(MiddleClientUserDeviceDo::getDeviceId, socketInfoVo.getDeviceId())
                        .orderByDesc(MiddleClientUserDeviceDo::getCreateTime))
                .stream().filter(middleClientUserDeviceDo -> {
                    Long userId = middleClientUserDeviceDo.getUserId();
                    ClientUserDo userDo = clientUserService.getById(userId);
                    if (null != configBo && configBo.getAftersales().contains(userDo.getUsername())) {
                        return false;
                    }
                    if (!userId.equals(clientUserDo.getId())) {
                        return true;
                    } else {
                        tuyaSocketBaseInfoDto.setMaster(middleClientUserDeviceDo.getMaster());
                        return false;
                    }
                }).map(middleClientUserDeviceDo -> {
                    TuyaSocketBaseInfoDto.BindAccount bindAccount = new TuyaSocketBaseInfoDto.BindAccount();
                    bindAccount.setSeriesId(1);
                    ClientUserDo bindUser = clientUserService.getById(middleClientUserDeviceDo.getUserId());
                    bindAccount.setAccount(bindUser.getUsername());
                    bindAccount.setBindTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(
                            middleClientUserDeviceDo.getCreateTime(), clientUserDo.getTimeZone()));
                    bindAccount.setMaster(middleClientUserDeviceDo.getMaster());
                    bindAccount.setAccountId(String.valueOf(middleClientUserDeviceDo.getUserId()));
                    return bindAccount;
                }).collect(Collectors.toList());
        try {
            TuyaSocketBaseInfoDto.BindAccount bindInstall = future.get();
            if (StrUtil.isNotBlank(bindInstall.getAccountId())) {
                bindAccountList.add(bindInstall);
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error(String.format("getBindInstall failed: %s", socketInfoVo.getDeviceId()), e);
        }
        tuyaSocketBaseInfoDto.setAccountList(bindAccountList);

        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);

        if (!deviceOnline) {
            log.info("设备id： " + socketInfoVo.getDeviceId() + " 离线");
            tuyaSocketBaseInfoDto.setSysRunMode(DeviceStatusEnum.OFFLINE.getDbCode());
            return tuyaSocketBaseInfoDto;
        }

        // 查询设备相关属性
        List<CloudDevicePropertyStatusResponse> deviceInfo = ecosIotApi.getDevicePropertyInfo(wifiSn, cloud);
        deviceInfo.forEach(response -> {
            SinglePlugSocketPropertyEnum propertyEnum = SinglePlugSocketPropertyEnum.fromIdentifier(response.getCode());
            switch (propertyEnum) {
                case SWITCH_1:
                    tuyaSocketBaseInfoDto.setSwitch1((Boolean) response.getValue());
                    break;
                case CUR_CURRENT:
                    tuyaSocketBaseInfoDto.setCurCurrent((Double) response.getValue() / 1000);
                    break;
                case CUR_POWER:
                    tuyaSocketBaseInfoDto.setCurPower(NumberUtil.div((Double) response.getValue(), Double.valueOf(10)));
                    break;
                case CUR_VOLTAGE:
                    tuyaSocketBaseInfoDto.setCurVoltage(NumberUtil.div((Double) response.getValue(), Double.valueOf(10)));
                    break;
                case RELAY_STATUS:
                    SinglePlugSocketRelayStatusEnum statusEnum = SinglePlugSocketRelayStatusEnum.valueOf((String) response.getValue());
                    tuyaSocketBaseInfoDto.setRelayStatus(statusEnum.getValue());
                    break;
                case OVERCHARGE_SWITCH:
                    tuyaSocketBaseInfoDto.setOverchargeSwitch((Boolean) response.getValue());
                    break;
                case COUNTDOWN_1:
                    tuyaSocketBaseInfoDto.setCountdown1((Double) response.getValue());
                    break;
                case RANDOM_TIME:
                    tuyaSocketBaseInfoDto.setRandomTime(TimeUtil.parseRandomTime((String) response.getValue()));

            }
        });
        tuyaSocketBaseInfoDto.setSysRunMode(SocketStatusEnum.OFF.getDbCode());
        if (tuyaSocketBaseInfoDto.getSwitch1()) {
            tuyaSocketBaseInfoDto.setSysRunMode(SocketStatusEnum.ON.getDbCode());
        }
        ClientSocketConfigDO socketConfigInfo = clientSocketConfigService.getByDeviceId(hybridSinglePhaseDO.getId());
        if (socketConfigInfo != null) {
            tuyaSocketBaseInfoDto.setCountdownEnd(socketConfigInfo.getCountdownEnd().getTime());
        }

        return tuyaSocketBaseInfoDto;

    }

    /** 获取绑定安装商详情 */
    private TuyaSocketBaseInfoDto.BindAccount getBindInstall(String deviceId) {
        InstallBoundInfoDTO bindInstallInfo = hubService.getBindInstallInfo(deviceId);
        return TuyaSocketBaseInfoDto.BindAccount.builder()
                .seriesId(1)
                .master(0)
                .accountId(bindInstallInfo.getInstallId())
                .account(bindInstallInfo.getInstallName())
                .bindTime(bindInstallInfo.getBindTime())
                .countdownTime(bindInstallInfo.getCountdownTime())
                .isInstaller(true)
                .build();
    }

    @Override
    public void updateTuyaSocketCountdown(SocketCountdownVo socketCountdownVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketCountdownVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());

        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
        if (!deviceOnline) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }

        Boolean aBoolean = ecosIotApi.updateTuyaSocketCountdown(wifiSn, cloud, socketCountdownVo.getSecond());
        if (!aBoolean) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
        // 保存更新倒计时
        clientSocketConfigService.updOrSaveCountdown(hybridSinglePhaseDO.getId(), socketCountdownVo.getSecond());
    }

    @Override
    public void updateTuyaSocketRandomTime(SocketRandomTimeMapVo socketRandomTimeMapVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketRandomTimeMapVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());

        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
        if (!deviceOnline) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
        Map<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> map = socketRandomTimeMapVo.getSocketRandomTimeVoMap();
        // 不能超过16个定时任务
        if (map.size() > 16) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM_RANGE);
        }

        // 判断时间是否有冲突
        if (TimeUtil.hasTimeConflict(map, CommonConstants.DEVICE_TUYA_DC)) {
            throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
        }

        // 加密成Base64字符串
        String code = TimeUtil.encodeRandomTime(map);

        ecosIotApi.updateTuyaSocketRandomData(wifiSn, cloud, code);
    }

    @Override
    @Transactional
    public void addSocketTiming(SocketAddTimingVo socketAddTimingVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketAddTimingVo.getDeviceId());

        // 如果不是单插直接返回
        if (hybridSinglePhaseDO.getResourceSeriesId() != 105) {
            return;
        }

        // 校验启用的定时任务是否冲突
        List<SocketAddTimingVo> socketAddTimingVoList = new ArrayList<>();
        List<ClientChargeTaskDo> taskDoList = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, socketAddTimingVo.getDeviceId())
                .eq(ClientChargeTaskDo::getStatus, 1));
        for (ClientChargeTaskDo chargeTaskDo : taskDoList) {
            SocketAddTimingVo socketAddTimingVo1 = new SocketAddTimingVo();
            socketAddTimingVo1.setDeviceId(String.valueOf(chargeTaskDo.getDeviceId()));
            socketAddTimingVo1.setStatus(chargeTaskDo.getStatus());
            String weekString = chargeTaskDo.getWeek();
            List<Integer> weekday = new ArrayList<>();
            // 检查字符串是否非空
            if (weekString != null && !weekString.isEmpty()) {
                // 使用 split 方法按逗号拆分字符串
                String[] weekParts = weekString.split(",");

                // 遍历拆分得到的字符串数组
                for (String weekPart : weekParts) {
                    weekday.add(Integer.parseInt(weekPart.trim()));
                }
            }
            socketAddTimingVo1.setWeek(weekday);
            socketAddTimingVo1.setSocketTime(chargeTaskDo.getStartTime());
            socketAddTimingVo1.setSocketSwitch(Objects.equals(chargeTaskDo.getEndTime(), "0") ? 0 : 1);
            socketAddTimingVoList.add(socketAddTimingVo1);
        }
        if (socketAddTimingVo.getStatus()) {
            socketAddTimingVoList.add(socketAddTimingVo);
        }

        // 判断时间是否有冲突
        if (TimeUtil.hasTimeConflictForTiming(socketAddTimingVoList, CommonConstants.DEVICE_TUYA_DC)) {
            throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
        }

        // 保存定时任务到数据库
        ClientChargeTaskDo chargeTaskDo = saveTimingTask(clientUserDo, socketAddTimingVo);
        TimeUtil.TimeAndWeekList timeAndWeekList = TimeUtil.isoToTimeWithGMT8(socketAddTimingVo.getSocketTime(), socketAddTimingVo.getWeek(), clientUserDo.getTimeZone());
        String time = timeAndWeekList.getTime();

        // 在xxl-job创建并执行这些定时任务
        LocalTime localStartTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));

        // 包含0就是重复定时任务
        if (!socketAddTimingVo.getWeek().contains(0)) {
            String startWeekConf = xxlJobUtil.convertWeekdayForXXLJob(socketAddTimingVo.getWeek());
            // 然后根据时间调整星期
            String startScheduleConf = String.format("%d %d %d ? * %s", localStartTime.getSecond(), localStartTime.getMinute(), localStartTime.getHour(), startWeekConf);
            createXxlJobTask(chargeTaskDo, startScheduleConf);
        } else {
            ZonedDateTime zonedDateTime = TimeUtil.isoToLocalTimeWithGMT8(socketAddTimingVo.getSocketTime(), clientUserDo.getTimeZone());
            String cronExpressionStart = String.format("%d %d %d %d %d ? %d",
                    zonedDateTime.getSecond(),
                    zonedDateTime.getMinute(),
                    zonedDateTime.getHour(),
                    zonedDateTime.getDayOfMonth(),
                    zonedDateTime.getMonthValue(),
                    zonedDateTime.getYear());
            createXxlJobTask(chargeTaskDo, cronExpressionStart);
        }

    }

    @Override
    public List<SocketTimingListDto> querySocketTimingList(String deviceId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        // 如果不是单插直接返回
        if (hybridSinglePhaseDO.getResourceSeriesId() != 105) {
            return new ArrayList<>();
        }

        List<ClientChargeTaskDo> taskDoList = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, deviceId));
        List<SocketTimingListDto> socketTimingListDtoList = new ArrayList<>();
        for (ClientChargeTaskDo taskDo : taskDoList) {
            SocketTimingListDto socketTimingListDto = new SocketTimingListDto();
            socketTimingListDto.setStatus(taskDo.getStatus());
            socketTimingListDto.setSocketSwitch(Objects.equals(taskDo.getEndTime(), "0") ? 0 : 1);
            String weekString = taskDo.getWeek();
            List<Integer> weekday = new ArrayList<>();
            // 检查字符串是否非空
            if (weekString != null && !weekString.isEmpty()) {
                // 使用 split 方法按逗号拆分字符串
                String[] weekParts = weekString.split(",");

                // 遍历拆分得到的字符串数组
                for (String weekPart : weekParts) {
                    weekday.add(Integer.parseInt(weekPart.trim()));
                }
            }
            socketTimingListDto.setWeek(weekday);
            socketTimingListDto.setSocketTime(taskDo.getStartTime());
            socketTimingListDto.setTaskId(String.valueOf(taskDo.getId()));

            socketTimingListDtoList.add(socketTimingListDto);
        }

        return socketTimingListDtoList;
    }

    @Override
    @Transactional
    public Boolean updateSocketTiming(SocketUpdateTimingVo socketUpdateTimingVo) {
        ClientChargeTaskDo taskDo = clientChargeTaskService.getById(socketUpdateTimingVo.getTaskId());
        BeanUtil.assertNotNull(taskDo);
        // 原来的状态
        Boolean beforeStatus = taskDo.getStatus();

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), String.valueOf(taskDo.getDeviceId()));

        // 如果不是单插直接返回
        if (hybridSinglePhaseDO.getResourceSeriesId() != 105) {
            return true;
        }

        // 校验启用的定时任务是否冲突
        List<SocketAddTimingVo> socketAddTimingVoList = new ArrayList<>();
        List<ClientChargeTaskDo> taskDoList = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                .eq(ClientChargeTaskDo::getDeviceId, taskDo.getDeviceId())
                .eq(ClientChargeTaskDo::getStatus, 1));
        for (ClientChargeTaskDo chargeTaskDo : taskDoList) {
            if (Objects.equals(taskDo.getId(), chargeTaskDo.getId())) {
                continue;
            }
            SocketAddTimingVo socketAddTimingVo1 = new SocketAddTimingVo();
            socketAddTimingVo1.setDeviceId(String.valueOf(chargeTaskDo.getDeviceId()));
            socketAddTimingVo1.setStatus(chargeTaskDo.getStatus());
            String weekString = chargeTaskDo.getWeek();
            List<Integer> weekday = new ArrayList<>();
            // 检查字符串是否非空
            if (weekString != null && !weekString.isEmpty()) {
                // 使用 split 方法按逗号拆分字符串
                String[] weekParts = weekString.split(",");

                // 遍历拆分得到的字符串数组
                for (String weekPart : weekParts) {
                    weekday.add(Integer.parseInt(weekPart.trim()));
                }
            }
            socketAddTimingVo1.setWeek(weekday);
            socketAddTimingVo1.setSocketTime(chargeTaskDo.getStartTime());
            socketAddTimingVo1.setSocketSwitch(Objects.equals(chargeTaskDo.getEndTime(), "0") ? 0 : 1);
            socketAddTimingVoList.add(socketAddTimingVo1);
        }
        SocketAddTimingVo socketAddTimingVo = new SocketAddTimingVo();
        socketAddTimingVo.setDeviceId(String.valueOf(taskDo.getDeviceId()));
        CglibUtil.copy(socketUpdateTimingVo,socketAddTimingVo);
        if (socketUpdateTimingVo.getStatus()) {
            socketAddTimingVoList.add(socketAddTimingVo);
        }

        // 判断时间是否有冲突
        if (TimeUtil.hasTimeConflictForTiming(socketAddTimingVoList, CommonConstants.DEVICE_TUYA_DC)) {
            throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
        }

        // 更新数据库中的数据
        ClientChargeTaskDo chargeTaskDo = updateTimingTask(clientUserDo, taskDo, socketAddTimingVo);
        TimeUtil.TimeAndWeekList timeAndWeekList = TimeUtil.isoToTimeWithGMT8(socketAddTimingVo.getSocketTime(), socketAddTimingVo.getWeek(), clientUserDo.getTimeZone());
        String time = timeAndWeekList.getTime();

        // 更新Xxl-job任务
        LocalTime localStartTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));

        // 不为空就是重复定时任务
        if (!socketAddTimingVo.getWeek().contains(0)) {
            String startWeekConf = xxlJobUtil.convertWeekdayForXXLJob(socketAddTimingVo.getWeek());

            String startScheduleConf = String.format("%d %d %d ? * %s", localStartTime.getSecond(), localStartTime.getMinute(), localStartTime.getHour(), startWeekConf);
            return updateXxlJobTask(chargeTaskDo, startScheduleConf, beforeStatus);
        } else {
            ZonedDateTime zonedDateTime = TimeUtil.isoToLocalTimeWithGMT8(socketAddTimingVo.getSocketTime(), clientUserDo.getTimeZone());
            String cronExpressionStart = String.format("%d %d %d %d %d ? %d",
                    zonedDateTime.getSecond(),
                    zonedDateTime.getMinute(),
                    zonedDateTime.getHour(),
                    zonedDateTime.getDayOfMonth(),
                    zonedDateTime.getMonthValue(),
                    zonedDateTime.getYear());
            updateXxlJobTask(chargeTaskDo, cronExpressionStart, beforeStatus);
        }

        return true;
    }

    @Override
    @Transactional
    public void deleteSocketTimingTask(String userId, String taskId) {
        // 数据库中查到该任务关联的xxl-job信息
        ClientChargeTaskDo chargeTaskDo = clientChargeTaskService.getById(taskId);
        BeanUtil.assertNotNull(chargeTaskDo);

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(userId, String.valueOf(chargeTaskDo.getDeviceId()));

        // 如果不是单插直接返回
        if (hybridSinglePhaseDO.getResourceSeriesId() != 105) {
            return;
        }

        ActionFlagUtil.assertTrue(clientChargeTaskService.removeById(chargeTaskDo.getId()));

        Long startTaskId = chargeTaskDo.getStartTaskId();

        JSONObject startObject = xxlJobUtil.remove(Math.toIntExact(startTaskId));

        if (startObject.getInteger("code") != 200) {
            throw new EcosException(EcosExceptionEnum.INVALID_CODE);
        }
    }

    @Override
    public SocketSwitchLogsDto querySocketSwitchLogs(SocketSwitchLogsVo socketSwitchLogsVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketSwitchLogsVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());


        String offset = clientUserDo.getTimeZone();
        String type = "7";
        Long endTime = TimeUtil.getCurrentTime(offset);
        Long startTime = endTime - 7 * 24 * 3600 * 1000;
        String codes = "switch_1";
        Integer queryType = 1;
        TuyaDeviceLogsResponse tuyaDeviceLogs = ecosIotApi.queryTuyaDeviceLogs(wifiSn, cloud, type, startTime, endTime, codes, queryType, socketSwitchLogsVo.getSize(), socketSwitchLogsVo.getPageStartRow());
        SocketSwitchLogsDto socketSwitchLogsDto = new SocketSwitchLogsDto();
        socketSwitchLogsDto.setNextRowKey(tuyaDeviceLogs.getNextRowKey() == null ? "" : tuyaDeviceLogs.getNextRowKey());
        socketSwitchLogsDto.setHasNext(tuyaDeviceLogs.getHasNext());
        ArrayList<SwitchLogDto> switchLogDtos = new ArrayList<>();
        List<TuyaDeviceLog> logs = tuyaDeviceLogs.getLogs();
        logs.forEach(tuyaDeviceLog -> {
            SwitchLogDto switchLogDto = new SwitchLogDto();
            switchLogDto.setCode(tuyaDeviceLog.getCode());
            switchLogDto.setValue(tuyaDeviceLog.getValue());
            switchLogDto.setTime(TimeUtil.longTimestampToSerialStringOffsetGMT8(tuyaDeviceLog.getEventTime(), offset));
            switchLogDtos.add(switchLogDto);
        });
        socketSwitchLogsDto.setLogs(switchLogDtos);

        return socketSwitchLogsDto;


    }

    @Override
    public SocketHistoryEleDto querySocketHistoryEle(SocketHistoryEleVo socketHistoryEleVo) {

        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketHistoryEleVo.getDeviceId());

//        Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
        String deviceSn = hybridSinglePhaseDO.getDeviceSn();
        String deviceId = socketHistoryEleVo.getDeviceId();
        String offset = clientUserDo.getTimeZone();

        MiddleClientUserDeviceDo clientUserDeviceDo = middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                .eq(MiddleClientUserDeviceDo::getMaster, 1));

        Long firstInstall = clientUserDeviceDo.getCreateTime();

        Pair<Long, Long> timePair = pairStartTimeAndEndTimeForInsight(socketHistoryEleVo.getPeriodType(), socketHistoryEleVo.getTimestamp(), firstInstall, offset);
        Long startTime = timePair.getKey();
        Long endTime = timePair.getValue();
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
                .chooseTimeSeriesDatabaseService(hybridSinglePhaseDO);

        if (startTime < hybridSinglePhaseDO.getFirstInstall()) {
            startTime = hybridSinglePhaseDO.getFirstInstall();
        }
        return sumQueryTSDB(deviceSn, CommonConstants.SOCKET_HISTORY_ELE,
                startTime, endTime, socketHistoryEleVo.getPeriodType(), timeSeriesDatabaseService, offset);
    }

    @Override
    public void switchSocketOverCharge(SocketOverChargeSwitchVo socketOverChargeSwitchVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        HybridSinglePhaseDO hybridSinglePhaseDO = v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), socketOverChargeSwitchVo.getDeviceId());

        String wifiSn = hybridSinglePhaseDO.getWifiSn();
        String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());

        // 查询设备是否在线
        Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
        if (!deviceOnline) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }

        Boolean aBoolean = ecosIotApi.switchTuyaSocketOverCharge(wifiSn, cloud, socketOverChargeSwitchVo.getSwitchStatus());
        if (!aBoolean) {
            throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
        }
    }

    @Override
    public void deleteDevicesConfigAndTask(List<HybridSinglePhaseDO> devices) {
        List<Long> deviceIds = devices.stream().map(HybridSinglePhaseDO::getId).collect(Collectors.toList());

        // 删除充电定时任务
        List<ClientChargeTaskDo> taskIds = clientChargeTaskService.list(Wrappers.<ClientChargeTaskDo>lambdaQuery()
                        .in(ClientChargeTaskDo::getDeviceId, deviceIds));

        for (ClientChargeTaskDo task : taskIds) {
            deleteSocketTimingTask(String.valueOf(task.getUserId()), String.valueOf(task.getId()));
        }

        for (HybridSinglePhaseDO device : devices) {
            String wifiSn = device.getWifiSn();
            String cloud = String.valueOf(device.getDataSource());

            // 查询设备是否在线
            Boolean deviceOnline = ecosIotApi.checkDeviceOnline(wifiSn, cloud);
            if (!deviceOnline) {
                continue;
            }

            //
            Boolean aBoolean1 = ecosIotApi.updateTuyaSocketCountdown(wifiSn, cloud, 0);
            Boolean aBoolean2 = ecosIotApi.updateTuyaSocketRandomData(wifiSn, cloud, "");
            if (!(aBoolean1 && aBoolean2)) {
                throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
            }
        }


    }

    @DSTransactional
    @Override
    public void clearSocketPowerData(String deviceId) {
        // 先判断该设备是否在该用户下面的
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

        // 设备是否存在 设备是否存在于该家庭中
        v2HomeAdapter.checkHomeAndDevice(String.valueOf(clientUserDo.getId()), deviceId);

        // 更新该设备绑定主账号的createTime
        MiddleClientUserDeviceDo middleClientUserDeviceServiceOne = middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
                .eq(MiddleClientUserDeviceDo::getDeviceId, deviceId)
                .eq(MiddleClientUserDeviceDo::getMaster, 1));

        if (middleClientUserDeviceServiceOne == null) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }
        middleClientUserDeviceServiceOne.setCreateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(middleClientUserDeviceService.updateById(middleClientUserDeviceServiceOne));
    }

    @DSTransactional
    private void bindDeviceAction(
            String deviceSn, ClientUserDo clientUserDo,
            V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo,
            String redisKey,
            String ip
    ) {

        String wifiSn = homeClientUserBindDeviceVo.getWifiSn();
        RLock lock = redissonClient.getLock(wifiSn);
        try {
            if (lock.tryLock(100, TimeUnit.MILLISECONDS)) {
                HybridSinglePhaseDO hybridSinglePhaseDO;
                try {
                    hybridSinglePhaseDO = hubService.getByDeviceName(deviceSn);
                } catch (RuntimeException e) {
                    if (!Objects.equals(e.getMessage(), "err.invalid.data")) {
                        throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
                    }
                    log.info("这是一个新设备：{}", wifiSn);
                    hybridSinglePhaseDO = null;
                }
                Long deviceId;
                if (null == hybridSinglePhaseDO) {
                    deviceId = saveDeviceInfo(deviceSn, wifiSn, homeClientUserBindDeviceVo, clientUserDo.getDatacenterId(), ip);
                } else {
                    deviceId = hybridSinglePhaseDO.getId();
                    MiddleClientUserDeviceDo masterDevice = middleClientUserDeviceService.getOne(Wrappers
                            .<MiddleClientUserDeviceDo>lambdaQuery().eq(
                                    MiddleClientUserDeviceDo::getDeviceId,
                                    deviceId
                            )
                            .eq(MiddleClientUserDeviceDo::getMaster, 1));

                    if (masterDevice != null) {
                        if (!masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "从账号绑定错误");
                            log.warn("从账号绑定错误");
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FORBIDDEN, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.SLAVE_BIND_ERROR);
                        }
                    }
                    hybridSinglePhaseDO.setDatacenterId(clientUserDo.getDatacenterId());
                    hybridSinglePhaseDO.setIp(ip);
                    updateDeviceInfo(hybridSinglePhaseDO, homeClientUserBindDeviceVo, wifiSn, deviceSn);

                    if (masterDevice != null) {
                        if (masterDevice.getUserId().equals(clientUserDo.getId())) {
                            bindFailMetrics.recordFailure(wifiSn, "已经绑定了该设备");
                            log.warn("{} 已经绑定了该设备：{}", clientUserDo.getEmail(), deviceSn);
                            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
                            throw new EcosException(EcosExceptionEnum.DONT_BIND_DEVICE_TWICE);
                        }
                    }
                }

                // 将设备与操作人建立关系
                long middleId = snowFlakeUtil.generateId();
                String deviceName = StrUtil.isBlank(homeClientUserBindDeviceVo.getDeviceAliasName()) ? deviceSn : homeClientUserBindDeviceVo.getDeviceAliasName();                MiddleClientUserDeviceDo middleClientUserDeviceDo = new MiddleClientUserDeviceDo();
                middleClientUserDeviceDo.setId(middleId);
                middleClientUserDeviceDo.setUserId(clientUserDo.getId());
                middleClientUserDeviceDo.setDeviceId(deviceId);
                middleClientUserDeviceDo.setCreateTime(System.currentTimeMillis());
                middleClientUserDeviceDo.setUpdateTime(System.currentTimeMillis());
                middleClientUserDeviceDo.setWeight(0);
                middleClientUserDeviceDo.setName(deviceName);
                middleClientUserDeviceDo.setMaster(1);
                ActionFlagUtil.assertTrue(middleClientUserDeviceService.save(middleClientUserDeviceDo));

                // 将设备与家庭建立关系
                v2HomeAdapter.homeBindDevice(clientUserDo, homeClientUserBindDeviceVo.getHomeId(), String.valueOf(deviceId), deviceName);

                // 绑定成功
                stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.OK, 1, TimeUnit.MINUTES);
            }
        } catch (InterruptedException e) {
            log.warn(e.getMessage());
            stringRedisTemplate.opsForValue().set(redisKey, DeviceBindStatus.FAIL, 1, TimeUnit.MINUTES);
            throw new EcosException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
        } finally {
            lock.unlock();
        }
    }

    private Long saveDeviceInfo(String deviceSn, String wifiSn, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, Integer datacenterId, String ip) {
        Long deviceId = snowFlakeUtil.generateId();
        HybridSinglePhaseDO hybridSinglePhaseDO = new HybridSinglePhaseDO();
        hybridSinglePhaseDO.setId(deviceId);
        hybridSinglePhaseDO.setDeviceName(deviceSn);
        hybridSinglePhaseDO.setDeviceSn(deviceSn);
        hybridSinglePhaseDO.setWifiSn(wifiSn);
        hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
        hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getTsdbSource());
        hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
        hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
        hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
        hybridSinglePhaseDO.setDatacenterId(datacenterId);
        initHybridSinglePhase(hybridSinglePhaseDO);
        hubService.saveSocket(hybridSinglePhaseDO);
        return deviceId;
    }

    private void updateDeviceInfo(HybridSinglePhaseDO hybridSinglePhaseDO, V2ClientHomeBindDeviceVo homeClientUserBindDeviceVo, String wifiSn, String deviceSn) {
        String beforeWifiSn = hybridSinglePhaseDO.getWifiSn();
        stringRedisTemplate.opsForValue().set("NEW-BIND:" + beforeWifiSn, "1", 10, TimeUnit.MINUTES);
        hybridSinglePhaseDO.setWifiSn(wifiSn);
        Long firstInstall = hybridSinglePhaseDO.getFirstInstall();
        if (firstInstall == null || firstInstall < 100) {
            hybridSinglePhaseDO.setFirstInstall(System.currentTimeMillis());
        }
        hybridSinglePhaseDO.setDataSource(homeClientUserBindDeviceVo.getType());
        hybridSinglePhaseDO.setTsdbSource(homeClientUserBindDeviceVo.getTsdbSource());
        hybridSinglePhaseDO.setLongitude(homeClientUserBindDeviceVo.getLon());
        hybridSinglePhaseDO.setLatitude(homeClientUserBindDeviceVo.getLat());
        hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
        hubService.updSocket(hybridSinglePhaseDO);

        List<HybridSinglePhaseDO> beforeWifiBindDevice = hubService.listOtherBindDevice(wifiSn, deviceSn);

        if (CollUtil.isNotEmpty(beforeWifiBindDevice)) {
            beforeWifiBindDevice.forEach(deviceDo -> {
                deviceDo.setWifiSn("");
                deviceDo.setUpdateTime(LocalDateTime.now());
                hubService.updSocket(deviceDo);
            });
        }
    }

    static void initHybridSinglePhase(HybridSinglePhaseDO hybridSinglePhaseDO) {
        hybridSinglePhaseDO.setAlias("");
        hybridSinglePhaseDO.setDeviceModel("");
        hybridSinglePhaseDO.setBrand("");
        hybridSinglePhaseDO.setFactory("");
        hybridSinglePhaseDO.setPowerBoardHardwareVersion("");
        hybridSinglePhaseDO.setDsp1SoftwareVersion("");
        hybridSinglePhaseDO.setDsp2SoftwareVersion("");
        hybridSinglePhaseDO.setEmsSoftwareVersion("");
        hybridSinglePhaseDO.setEmsHardwareVersion("");
        hybridSinglePhaseDO.setBmsGaugeVersion("");
        hybridSinglePhaseDO.setBmsSn("");
        hybridSinglePhaseDO.setBmsVendor("");
        hybridSinglePhaseDO.setBmsSoftwareVersion("");
        hybridSinglePhaseDO.setBmsHardwareVersion("");
        hybridSinglePhaseDO.setState(0);
        hybridSinglePhaseDO.setLongitude(0.0D);
        hybridSinglePhaseDO.setLatitude(0.0D);
        hybridSinglePhaseDO.setVppMode(false);
        hybridSinglePhaseDO.setCreateTime(LocalDateTime.now());
        hybridSinglePhaseDO.setUpdateTime(LocalDateTime.now());
    }

    private Pair<Long, Long> pairStartTimeAndEndTimeForInsight(Integer periodType, Long timestamp, Long firstInstall, String offset) {
        long startTime;
        long endTime;
        switch (periodType) {
            case CommonConstants.PERIOD_MONTH:
                startTime = TimeUtil.getAssignMonthStart(timestamp, offset, 0);
                endTime = TimeUtil.getAssignMonthEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_YEAR:
                startTime = TimeUtil.getYearStart(timestamp, offset);
                endTime = TimeUtil.getYearEnd(timestamp, offset);
                break;
            case CommonConstants.PERIOD_LIFETIME:
                startTime = firstInstall;
                endTime = TimeUtil.getCurrentTime(offset);
                break;
            default:
                // 针对日级别的时间范围，起始时间设为前一天的23：55，经过降采样和差值计算后，才能算出1：00的值
                startTime = TimeUtil.getAssignDayStart(timestamp, offset) - 5*60*1000;
                endTime = TimeUtil.getAssignDayEnd(timestamp, offset);
        }
        return Pair.of(startTime, endTime);
    }

    private SocketHistoryEleDto sumQueryTSDB(
            String deviceName, List<String> metricList,
            long startTime, long endTime, Integer periodType, TimeSeriesDatabaseService timeSeriesDatabaseService,
            String offset
    ) {
        SocketHistoryEleDto socketHistoryEleDto = new SocketHistoryEleDto();

        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.deltaQuery(
                deviceName,
                metricList,
                startTime,
                endTime,
                TsdbSampleEnum.ONE_HOUR_NONE_POINT
        );

        Map<String, LinkedHashMap<Long, Object>> metricDataMap;
        if (5 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryMonthToYear(result, offset);
        } else if (4 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryDayToMonth(result, offset);
        } else if (2 == periodType) {
            metricDataMap = TSDBAggUtil.aggregateDeltaQueryHourToDay(result, offset);
        } else {
            Map<String, LinkedHashMap<Long, Object>> aggregateMap = new LinkedHashMap<>();
            for (String m : result.keySet()) {
                LinkedHashMap<Long, Object> aggregateTimeData = new LinkedHashMap<>();
                LinkedHashMap<Long, Object> map = result.get(m);
                for (Long t : map.keySet()) {
                    BigDecimal bigDecimal = new BigDecimal(map.getOrDefault(t, "0").toString());
                    aggregateTimeData.put(t, NumberUtil.round(bigDecimal, 2, RoundingMode.HALF_UP));
                }
                aggregateMap.put(m, aggregateTimeData);
            }
            metricDataMap = aggregateMap;
        }

        if (CollUtil.isNotEmpty(metricDataMap)) {
            LinkedHashMap<Long, Object> addEleMap = metricDataMap.get(TsdbMetricsConstants.ADD_ELE_SUM);

            BigDecimal totalAddEle = BigDecimal.ZERO;

            if (CollUtil.isNotEmpty(addEleMap)) {
                for (Long time : addEleMap.keySet()) {
                    BigDecimal addEle = new BigDecimal(addEleMap.getOrDefault(time, "0").toString());
                    totalAddEle = NumberUtil.add(addEle, totalAddEle);
                }
            }
            socketHistoryEleDto.setTotalEle(NumberUtil.round(totalAddEle, 2, RoundingMode.HALF_UP));
            socketHistoryEleDto.setEleDps(addEleMap);
        }
        return socketHistoryEleDto;
    }

    public ClientChargeTaskDo saveTimingTask(ClientUserDo clientUserDo, SocketAddTimingVo socketAddTimingVo) {
        ClientChargeTaskDo clientChargeTaskDo = new ClientChargeTaskDo();
        CglibUtil.copy(socketAddTimingVo, clientChargeTaskDo);
        clientChargeTaskDo.setUserId(clientUserDo.getId());
        String startTime = socketAddTimingVo.getSocketTime();
        String offset = clientUserDo.getTimeZone();
        TimeUtil.TimeAndWeekList timeAndWeekList = TimeUtil.isoToTimeWithGMT8(startTime, socketAddTimingVo.getWeek(), offset);
        clientChargeTaskDo.setStartTime(startTime);
        clientChargeTaskDo.setEndTime(String.valueOf(socketAddTimingVo.getSocketSwitch()));
        long time = System.currentTimeMillis();
        clientChargeTaskDo.setCreateTime(time);
        clientChargeTaskDo.setUpdateTime(time);
        long id = snowFlakeUtil.generateId();
        clientChargeTaskDo.setId(id);
        clientChargeTaskDo.setDeviceId(Long.valueOf(socketAddTimingVo.getDeviceId()));
        clientChargeTaskDo.setPower(0D);

        String weekday = timeAndWeekList.getWeekList().stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
        clientChargeTaskDo.setWeek(weekday);
        clientChargeTaskDo.setStatus(socketAddTimingVo.getStatus());
        ActionFlagUtil.assertTrue(clientChargeTaskService.save(clientChargeTaskDo));

        return clientChargeTaskDo;
    }

    public ClientChargeTaskDo updateTimingTask(ClientUserDo clientUserDo,ClientChargeTaskDo chargeTaskDo, SocketAddTimingVo socketAddTimingVo) {
        String startTime = socketAddTimingVo.getSocketTime();
        String offset = clientUserDo.getTimeZone();
        TimeUtil.TimeAndWeekList timeAndWeekList = TimeUtil.isoToTimeWithGMT8(startTime, socketAddTimingVo.getWeek(), offset);
        chargeTaskDo.setStartTime(startTime);
        chargeTaskDo.setEndTime(String.valueOf(socketAddTimingVo.getSocketSwitch()));
        chargeTaskDo.setStatus(socketAddTimingVo.getStatus());
        String weekday = timeAndWeekList.getWeekList().stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
        chargeTaskDo.setWeek(weekday);
        chargeTaskDo.setUpdateTime(System.currentTimeMillis());
        ActionFlagUtil.assertTrue(clientChargeTaskService.updateById(chargeTaskDo));

        chargeTaskDo.setStartTime(timeAndWeekList.getTime());
        return chargeTaskDo;
    }


    public void createXxlJobTask(ClientChargeTaskDo chargeTaskDo, String startScheduleConf) {
        XxlJobInfo xxlJobInfoStart = new XxlJobInfo();

        xxlJobInfoStart.setJobDesc("ecos_client_dc_" + chargeTaskDo.getId() + "_timing");
        xxlJobInfoStart.setAuthor("ecos_client_auto");
        xxlJobInfoStart.setScheduleType("CRON");

        xxlJobInfoStart.setScheduleConf(startScheduleConf);


        xxlJobInfoStart.setGlueType("BEAN");

        xxlJobInfoStart.setExecutorHandler("socketSwitchTask");
        xxlJobInfoStart.setExecutorRouteStrategy("RANDOM");
        xxlJobInfoStart.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfoStart.setMisfireStrategy("DO_NOTHING");

        xxlJobInfoStart.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + chargeTaskDo.getEndTime() + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId());


        // 先创建结束子任务，再创建开始主任务，最后更新任务配置表
        JSONObject startObject;
        if (chargeTaskDo.getStatus()) {
            startObject = xxlJobUtil.addAndStart(xxlJobInfoStart);
        } else {
            startObject = xxlJobUtil.add(xxlJobInfoStart);
        }
        if (startObject.getInteger("code") != 200) {
            throw new EcosException(EcosExceptionEnum.INVALID_CODE);
        }
        String startTaskId = startObject.getString("content");
        chargeTaskDo.setStartTaskId(Long.valueOf(startTaskId));
        chargeTaskDo.setEndTaskId(Long.valueOf(startTaskId));
        clientChargeTaskService.updateById(chargeTaskDo);
    }

    public Boolean updateXxlJobTask(ClientChargeTaskDo chargeTaskDo, String startScheduleConf, Boolean beforeStatus) {
        try {
            // 获取要更新的xxl-job任务。更新corn时间
            JSONObject jsonObject = xxlJobUtil.pageList(0, 10, 2, -1, "ecos_client_dc_" + chargeTaskDo.getId(), "", "");
            BeanUtil.assertNotNull(jsonObject);
            AtomicReference<XxlJobInfo> xxlJobInfoStartA = new AtomicReference<>();
            jsonObject.getJSONArray("data").forEach(item -> {
                JSONObject j = (JSONObject) item;
                XxlJobInfo jobInfo = j.toJavaObject(XxlJobInfo.class);
                if (jobInfo.getJobDesc().equals("ecos_client_dc_" + chargeTaskDo.getId() + "_timing")) {
                    xxlJobInfoStartA.set(jobInfo);
                }
            });
            XxlJobInfo xxlJobInfoStart = xxlJobInfoStartA.get();
            xxlJobInfoStart.setScheduleConf(startScheduleConf);
            xxlJobInfoStart.setExecutorParam(chargeTaskDo.getDeviceId() + "//" + chargeTaskDo.getEndTime() + "//" + chargeTaskDo.getWeek() + "//" + chargeTaskDo.getId());

            JSONObject startObject = xxlJobUtil.update(xxlJobInfoStart);

            if (startObject.getInteger("code") != 200) {
                return false;
            }

            // 启动或者暂停xxl-job项目
            if (!Objects.equals(beforeStatus, chargeTaskDo.getStatus()) || "0".equals(chargeTaskDo.getWeek())) {
                JSONObject startObject1;
                if (chargeTaskDo.getStatus()) {
                    startObject1 = xxlJobUtil.start(Math.toIntExact(chargeTaskDo.getStartTaskId()));
                } else {
                    startObject1 = xxlJobUtil.pause(Math.toIntExact(chargeTaskDo.getStartTaskId()));
                }

                if (startObject1.getInteger("code") != 200) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("连接xxl-job报错：{}",e.getMessage());
            return false;
        }
    }
}
