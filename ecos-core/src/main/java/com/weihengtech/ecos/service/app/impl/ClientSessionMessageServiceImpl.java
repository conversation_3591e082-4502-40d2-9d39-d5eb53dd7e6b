package com.weihengtech.ecos.service.app.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.enums.ChatRoleEnum;
import com.weihengtech.ecos.dao.ClientSessionMessageMapper;
import com.weihengtech.ecos.model.dos.ClientSessionDo;
import com.weihengtech.ecos.model.dos.ClientSessionMessageDo;
import com.weihengtech.ecos.service.app.ClientSessionMessageService;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @program: ecos-server
 * @description: 绘画消息表服务实现类
 * @author: jiahao.jin
 * @create: 2024-05-09 15:28
 **/
@Service
public class ClientSessionMessageServiceImpl extends
        ServiceImpl<ClientSessionMessageMapper, ClientSessionMessageDo>
        implements ClientSessionMessageService {

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Override
    public ClientSessionMessageDo queryLastMessageBySessionId(Long sessionId) {

        return getOne(Wrappers.<ClientSessionMessageDo>lambdaQuery()
                .eq(ClientSessionMessageDo::getSessionId, sessionId)
                .ne(ClientSessionMessageDo::getType, ChatRoleEnum.USER.getCode())
                .orderByDesc(ClientSessionMessageDo::getTimestamp)
                .last("limit 1"));
    }

    @Override
    public ClientSessionMessageDo createMessage(ClientSessionDo session, Long userId, ChatRoleEnum role, String content, Long parentId, LocalDateTime timestamp) {
        ClientSessionMessageDo message = new ClientSessionMessageDo();
        message.setId(snowFlakeUtil.generateId());
        message.setSessionId(session.getId());
        message.setUserId(userId);
        message.setContent(content);
        message.setParentId(parentId);
        message.setType(role.getCode());
        message.setTimestamp(timestamp);
        return message;
    }
}
