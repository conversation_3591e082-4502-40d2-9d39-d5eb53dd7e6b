package com.weihengtech.ecos.service.app.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientCustomizeMapper;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ClientCustomizeServiceImpl extends ServiceImpl<ClientCustomizeMapper, ClientCustomizeDo>
		implements
        ClientCustomizeService {

	@Override
	public List<StrategyCustomizeDTO> getAutoStrategyDevices() {
        return this.baseMapper.getAutoStrategyDevices();
	}

    @Override
    public List<ClientCustomizeDo> getAutoStrategyDevices(List<Long> deviceIds) {
        return this.baseMapper.getAutoStrategyDevicesByIds(deviceIds);
    }
}
