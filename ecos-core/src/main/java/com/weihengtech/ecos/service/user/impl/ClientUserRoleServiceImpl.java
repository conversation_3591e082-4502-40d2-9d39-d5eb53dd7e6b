package com.weihengtech.ecos.service.user.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.dao.ClientUserRoleMapper;
import com.weihengtech.ecos.service.user.ClientUserRoleService;
import com.weihengtech.ecos.model.dos.ClientUserRoleDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ClientUserRoleServiceImpl extends ServiceImpl<ClientUserRoleMapper, ClientUserRoleDo>
		implements
        ClientUserRoleService {

	@Resource
	private ClientUserRoleMapper clientUserRoleMapper;

	@Override
	public List<String> queryRoleListByClientUserId(Long id) {
		return Optional
				.ofNullable(clientUserRoleMapper
						.selectList(Wrappers.<ClientUserRoleDo>lambdaQuery().eq(ClientUserRoleDo::getClientUserId, id)))
				.map(list -> list.stream().map(ClientUserRoleDo::getRoleName).collect(Collectors.toList()))
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.UNLICENSED_USER));
	}
}
