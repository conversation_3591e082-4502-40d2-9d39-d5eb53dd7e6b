package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.dao.ClientElePriceUseMapper;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.enums.ele.UseDayTypeEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceUseDO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceUseVO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.ele.ClientElePriceUseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ClientElePriceUseServiceImpl extends ServiceImpl<ClientElePriceUseMapper, ClientElePriceUseDO> implements ClientElePriceUseService, HomeElePriceService {

    @Resource
    private ClientHomeService clientHomeService;

    @Override
    public ElePriceUseVO queryUsePrice(String homeId) {
        List<ClientElePriceUseDO> itemList = list(Wrappers.<ClientElePriceUseDO>lambdaQuery()
                .eq(ClientElePriceUseDO::getHomeId, homeId));
        if (CollUtil.isEmpty(itemList)) {
            return ElePriceUseVO.builder().homeId(homeId).build();
        }
        Map<String, List<ClientElePriceUseDO>> monthGroup = itemList.stream()
                .collect(Collectors.groupingBy(i -> String.format("%d-%d", i.getStartMonth(), i.getEndMonth())));
        List<ClientElePriceUseDO.MonthUsePrice> monthPriceList = new ArrayList<>();
        for (Map.Entry<String, List<ClientElePriceUseDO>> entry : monthGroup.entrySet()) {
            String monthRange = entry.getKey();
            List<ClientElePriceUseDO> priceList = entry.getValue();
            monthPriceList.add(ClientElePriceUseDO.MonthUsePrice.builder()
                            .startMonth(Integer.parseInt(monthRange.split("-")[0]))
                            .endMonth(Integer.parseInt(monthRange.split("-")[1]))
                            .weekendSame(priceList.get(0).getWeekendSame())
                            .monthUsePrices(priceList.stream()
                                    .map(ClientElePriceUseDO :: toUsePriceDetail)
                                    .collect(Collectors.toList()))
                    .build());
        }
        return ElePriceUseVO.builder().homeId(homeId).monthUsePriceList(monthPriceList).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUsePrice(ElePriceUseVO param) {
        deleteUsePrice(param.getHomeId());
        addUsePrice(param);
        // 清除当日成本缓存数据
        clearCurDayCostData(Long.parseLong(param.getHomeId()));
    }

    private void addUsePrice(ElePriceUseVO param) {
        int count = count(Wrappers.<ClientElePriceUseDO>lambdaQuery()
                .eq(ClientElePriceUseDO::getHomeId, param.getHomeId()));
        Assert.isTrue(count == 0, "Use electricity prices have been set for current home");
        // 校验时间交叉
        Assert.notEmpty(param.getMonthUsePriceList(), "month use price list is empty");
        checkTimeRange(param.getMonthUsePriceList());
        // 处理电价
        List<ClientElePriceUseDO> itemList = param.getMonthUsePriceList().stream()
                .map(i -> i.getMonthUsePrices().stream()
                        .map(j -> j.toUsePriceDetail(param.getHomeId(), i.getStartMonth(), i.getEndMonth(),
                                i.getWeekendSame()))
                        .collect(Collectors.toList())
                )
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 保存电价
        saveBatch(itemList);
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(param.getHomeId());
        if (homeItem != null && !ElePriceTypeEnum.TIME_OF_USE.getCode().equals(homeItem.getElePriceType())) {
            // 如果原来的家庭电价类型为批发电价、零售商电价，则关联的设备都需要关闭自动策略
            closeAutoStrategy(homeItem);
            homeItem.setElePriceType(ElePriceTypeEnum.TIME_OF_USE.getCode());
            clientHomeService.updateById(homeItem);
        }
    }

    @Override
    public void deleteUsePrice(String homeId) {
        remove(Wrappers.<ClientElePriceUseDO>lambdaQuery()
                .eq(ClientElePriceUseDO::getHomeId, homeId));
    }

    @Override
    public Object queryEleInfoOrConfig(String homeId) {
        return queryUsePrice(homeId);
    }

    @Override
    public Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String userTimezone) {
        if (StrUtil.isBlank(userTimezone)) {
            return Collections.emptyMap();
        }
        ElePriceUseVO priceInfo = queryUsePrice(homeId);
        if (priceInfo == null) {
            return Collections.emptyMap();
        }
        // 获取当前时间对应分时电价
        List<ClientElePriceUseDO.UsePriceDetail> priceDetailList = calPriceInfo(priceInfo, userTimezone);
        // 切分计算15分钟电价
        return cal15HomeElePrice(userTimezone, priceDetailList);
    }

    @Override
    public List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone) {
        return Collections.emptyList();
    }

    @Override
    public Object queryAllTypeHomePriceInfo() {
        return Collections.emptyList();
    }

    /** 计算15分钟电价 */
    private Map<Long, ElePriceDetailDTO> cal15HomeElePrice(String userTimezone, List<ClientElePriceUseDO.UsePriceDetail> priceDetailList) {
        ZoneId zoneId = ZoneId.of(userTimezone);
        // 使用当前日期作为基准日期
        ZonedDateTime base = ZonedDateTime.now(zoneId).toLocalDate().atStartOfDay(zoneId);
        Map<Long, ElePriceDetailDTO> resMap = new HashMap<>();
        for (ClientElePriceUseDO.UsePriceDetail usePriceDetail : priceDetailList) {
            for (ClientElePriceUseDO.Period period : usePriceDetail.getTimeList()) {
                int current = period.getStart();
                while (current <= period.getEnd()) {
                    ZonedDateTime dateTime = base.plusMinutes(current);
                    long timestamp = dateTime.toEpochSecond(); // 秒级时间戳
                    resMap.put(timestamp, ElePriceDetailDTO.builder().purchasePrice(usePriceDetail.getPurchasePrice())
                            .purchaseTax(usePriceDetail.getPurchaseTax())
                            .feedInPrice(usePriceDetail.getFeedInPrice())
                            .build());
                    current += 15;
                }
            }
        }
        return resMap;
    }

    /** 获取当前时间对应分时电价 */
    private List<ClientElePriceUseDO.UsePriceDetail> calPriceInfo(ElePriceUseVO priceInfo, String userTimezone) {
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(userTimezone));
        int month = zonedDateTime.getMonth().getValue();
        int weekday = zonedDateTime.getDayOfWeek().getValue();
        ClientElePriceUseDO.MonthUsePrice monthUsePrice = priceInfo.getMonthUsePriceList().stream()
                .filter(i -> month >= i.getStartMonth() && month < i.getEndMonth())
                .findFirst()
                .orElse(null);
        if (monthUsePrice == null) {
            return Collections.emptyList();
        }
        Boolean weekendSame = monthUsePrice.getWeekendSame();
        if (weekday >= 1 && weekday <= 5) {
            return monthUsePrice.getMonthUsePrices().stream()
                    .filter(i -> UseDayTypeEnum.isWork(i.getDayType()))
                    .collect(Collectors.toList());
        } else {
            return monthUsePrice.getMonthUsePrices().stream()
                    .filter(i -> weekendSame ? UseDayTypeEnum.isWork(i.getDayType()) :
                            UseDayTypeEnum.isWeek(i.getDayType()))
                    .collect(Collectors.toList());
        }
    }

    private void checkTimeRange(List<ClientElePriceUseDO.MonthUsePrice> monthUsePriceList) {
        List<ClientElePriceUseDO.MonthUsePrice> checkList = new ArrayList<>(monthUsePriceList);
        Boolean monthCheckRes = checkMonthRange(checkList);
        Assert.isTrue(monthCheckRes, "The monthly range data is illegal");
        for (ClientElePriceUseDO.MonthUsePrice monthUsePrice : checkList) {
            List<ClientElePriceUseDO.Period> weekendRangeList = monthUsePrice.getMonthUsePrices().stream()
                    .filter(i -> UseDayTypeEnum.isWeek(i.getDayType()))
                    .map(ClientElePriceUseDO.UsePriceDetail::getTimeList)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            List<ClientElePriceUseDO.Period> workRangeList = monthUsePrice.getMonthUsePrices().stream()
                    .filter(i -> UseDayTypeEnum.isWork(i.getDayType()))
                    .map(ClientElePriceUseDO.UsePriceDetail::getTimeList)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            Boolean weekendCheckRes = checkMinuteRange(weekendRangeList);
            Boolean workCheckRes = checkMinuteRange(workRangeList);
            Assert.isTrue(weekendCheckRes, "The weekend minutely range data is illegal");
            Assert.isTrue(workCheckRes, "The workday minutely range data is illegal");
        }
    }

    private Boolean checkMinuteRange(List<ClientElePriceUseDO.Period> minuteRangeList) {
        if (CollUtil.isEmpty(minuteRangeList)) {
            return true;
        }
        // 检查每个范围是否合法
        for (ClientElePriceUseDO.Period range : minuteRangeList) {
            int start = range.getStart();
            int end = range.getEnd();
            if (start < 0 || start > 24*60 || end < 0 || end > 24*60 || start > end) {
                return false;
            }
        }
        // 根据起始分钟进行排序
        minuteRangeList.sort(Comparator.comparingInt(ClientElePriceUseDO.Period::getStart));
        // 检查起始和截止
        if (minuteRangeList.get(0).getStart() != 0 || minuteRangeList.get(minuteRangeList.size() - 1).getEnd() != 24*60) {
            return false;
        }
        // 检查连续性，无重叠
        int prevEnd = minuteRangeList.get(0).getEnd();
        for (int i = 1; i < minuteRangeList.size(); i++) {
            int currentStart = minuteRangeList.get(i).getStart();
            int currentEnd = minuteRangeList.get(i).getEnd();
            if (currentStart != prevEnd) {
                return false;
            }
            prevEnd = currentEnd;
        }
        return true;
    }

    private Boolean checkMonthRange(List<ClientElePriceUseDO.MonthUsePrice> monthUsePriceList) {
        if (CollUtil.isEmpty(monthUsePriceList)) {
            return true;
        }
        // 根据起始月份进行排序
        monthUsePriceList.sort(Comparator.comparingInt(ClientElePriceUseDO.MonthUsePrice::getStartMonth));
        int totalMonths = 0;
        // 初始化预期下一个起始月份
        int expectedNextStart = -1;
        // 检查每个范围是否合法
        for (ClientElePriceUseDO.MonthUsePrice range : monthUsePriceList) {
            int start = range.getStartMonth();
            int end = range.getEndMonth();
            // 校验月份值合法 (1-12)
            if (start < 1 || start > 12 || end < 1 || end > 12) {
                return false;
            }
            // 计算当前区间长度（处理跨年）
            int rangeLength = calculateRangeLength(start, end);
            if (rangeLength < 1 || rangeLength > 12) {
                return false; // 单个区间长度必须在1-12个月
            }

            // 检查连续性（第一个区间跳过连续性检查）
            if (expectedNextStart != -1) {
                if (start != expectedNextStart) {
                    return false; // 不连续
                }
            }

            // 更新预期下一个起始月份：当前结束月份的下一个月（跨年处理）
            expectedNextStart = (end == 12) ? 1 : end + 1;

            // 累加总月份
            totalMonths += rangeLength;
            if (totalMonths > 12) {
                return false; // 总长度超过12个月
            }
        }
        return true;
    }

    // 计算区间长度（支持跨年）
    private static int calculateRangeLength(int start, int end) {
        if (start <= end) {
            // 非跨年
            return end - start + 1;
        } else {
            // 跨年：从start到年底 + 年初到end
            return (12 - start + 1) + end;
        }
    }
}
