package com.weihengtech.ecos.service.charger;

import com.weihengtech.ecos.model.vos.charger.ChargePlugSwitchVO;
import com.weihengtech.ecos.model.vos.charger.ChargeStationHistoryCapacityVo;
import com.weihengtech.ecos.model.vos.charger.ClientCreateChargeTaskVo;
import com.weihengtech.ecos.model.vos.charger.ClientUpdateChargeTaskVo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.charger.ChargeStationHistoryCapacityDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeRecordDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeTimeDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationInfoDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationRunDataDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationTimingChargeDto;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 充电桩服务类
 * @author: jiahao.jin
 * @create: 2024-02-18 11:24
 **/
public interface ChargeStationService {

    /**
     * 用户绑定设备
     *
     * @param v2ClientHomeBindDeviceVo 设备信息
     * @param clientUserDo               用户信息
     */
    void bindClientUserDevice(V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo, ClientUserDo clientUserDo, String ip);

    /**
     * 查询充电桩状态
     *
     * @param deviceId 设备Id
     */
    Integer getChargeStationStatus(String deviceId);

    /**
     * 查询充电桩详情
     *
     * @param deviceId 设备Id
     */
    ENPlusChargeStationInfoDto queryChargeStationInfo(String deviceId);

    /**
     * 获取最大充电功率
     *
     * @param deviceId 设备Id
     */
    Double queryMaxChargePower(String userId, String deviceId);

    /**
     * 更新最大充电功率
     *
     * @param deviceId 设备Id
     */
    void updateMaxChargePower(String userId, String deviceId, Double maxPower, Boolean issued);

    /**
     * 充电桩开始充电
     *
     * @param deviceId 设备Id
     * @param power 充电功率
     */
    void startCharging(String userId, String deviceId, String power);

    /**
     * 充电桩停止充电
     *
     * @param deviceId 设备Id
     */
    void stopCharging(String userId, String deviceId);

    /**
     * 充电桩充电实时数据
     *
     * @param deviceId 设备Id
     */
    ENPlusChargeStationRunDataDto runData(ClientUserDo clientUserDo, String deviceId);

    /**
     * 充电桩最新充电记录
     *
     * @param deviceId 设备Id
     */
    ENPlusChargeStationChargeRecordDto lastChargeRecord(String deviceId);

    /**
     * 充电桩历史充电记录
     *
     * @param v2ClientChargeRecordPageVo 查询入参
     */
    PageInfoDTO<ENPlusChargeStationChargeRecordDto> pageHistoryChargeRecord(V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo);

    /**
     * 充电桩未读失败充电记录列表
     *
     * @param deviceId 设备ID
     * @param read 是否已读（0：未读，1：已读）
     */
    List<ENPlusChargeStationTimingChargeDto> queryNotReadFailChargeRecord(String deviceId, Integer read);

    /**
     * 查询充电定时任务
     *
     * @param deviceId 充电桩id
     */
    List<ENPlusChargeStationChargeTimeDto> queryChargeScheduledTaskList(String deviceId);

    /**
     * 创建充电定时任务
     *
     * @param clientCreateChargeTaskVo 入参
     */
    void createChargeScheduledTask(ClientCreateChargeTaskVo clientCreateChargeTaskVo);

    /**
     * 删除充电定时任务
     *
     * @param taskId 任务ID
     */
    void deleteChargeScheduledTask(String userId, String taskId);

    /**
     * 更新充电定时任务
     *
     * @param clientUpdateChargeTaskVo 更新入参
     */
    Boolean updateChargeScheduledTask(ClientUpdateChargeTaskVo clientUpdateChargeTaskVo);

    /**
     * 删除充电定时任务和充电配置
     *
     * @param deviceIds 设备ID列表
     */
    void deleteDevicesConfigAndTask(List<Long> deviceIds);

    /**
     * 查询充电桩绑定卡片
     *
     * @param deviceId 设备ID
     */
    List<String> queryChargeStationCards(String deviceId);

    /**
     * 充电桩绑定卡片
     *
     * @param deviceId 设备ID
     * @param cardId 卡片ID
     */
    void bindChargeStationCardId(String deviceId, String cardId);

    /**
     * 充电桩批量移除绑定卡片
     *
     * @param deviceId 设备ID
     * @param cardIds 卡片ID
     */
    void deleteChargeStationCard(String userId, String deviceId, List<String> cardIds);

    /**
     * 查询充电桩历史充电量
     *
     * @param chargeStationHistoryCapacityVo 查询入参
     * @return 历史充电量数据
     */
    ChargeStationHistoryCapacityDto queryChargeStationHistoryCapacity(ChargeStationHistoryCapacityVo chargeStationHistoryCapacityVo);

    /**
     * 更新充电桩配置:即插即充开关
     *
     * @param request
     * @return
     */
    Boolean updChargerConfiguration(ChargePlugSwitchVO request);
}
