package com.weihengtech.ecos.service.ele.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.enums.ele.ChargeTypeEnum;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import com.weihengtech.ecos.utils.ElectricityPriceTypeUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/1 14:21
 */
@Service
public class ClientDefStrategyServiceImpl implements ClientEleStrategyService {

    @Override
    public List<EleStrategyDTO> calStrategy(List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(priceDataList)) {
            return Collections.emptyList();
        }
        // 判断电价类型，决定不同的负电价阈值
        ElePriceTypeDetailEnum typeDetailEnum = ElectricityPriceTypeUtil.detectPriceType(priceDataList);
        long negativeHourThreshold = 4L;
        if (ElePriceTypeDetailEnum.INTERVAL_15MIN == typeDetailEnum) {
            negativeHourThreshold = 16L;
        } else if (ElePriceTypeDetailEnum.INTERVAL_30MIN == typeDetailEnum) {
            negativeHourThreshold = 8L;
        }

        // 根据电价数据计算平均值、标准差、变异系数
        List<BigDecimal> priceList = priceDataList.stream()
                .map(EleDayAheadPriceDto::getAverage)
                .collect(Collectors.toList());
        double average = priceList.stream().collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double standardDeviation = calStandardDeviation(priceList, average);
        double variation = Math.abs(standardDeviation / average);
        double chargeThreshold = average * (1 - calThreshold(variation));
        double dischargeThreshold = average * (1 + calThreshold(variation));
        long negativeHourCount = priceDataList.stream()
                .filter(i -> i.getAverage().compareTo(BigDecimal.ZERO) < 0)
                .count();

        // 生成初始策略
        List<EleStrategyDTO> initialStrategy = calEleStrategyPreviewData(priceDataList, chargeThreshold, dischargeThreshold,
                param, negativeHourCount, negativeHourThreshold);

        // 应用优化逻辑
        return optimizeStrategy(initialStrategy, priceDataList, negativeHourCount, param);
    }

    /** 策略优化逻辑 */
    private List<EleStrategyDTO> optimizeStrategy(List<EleStrategyDTO> initialStrategy, List<EleDayAheadPriceDto> priceDataList, long negativeHourCount, EleStrategyPreviewVO param) {
        // 复制初始策略避免修改原列表
        List<EleStrategyDTO> optimizedStrategy = initialStrategy.stream()
                .map(strategy -> EleStrategyDTO.builder()
                        .startTimeUnix(strategy.getStartTimeUnix())
                        .price(strategy.getPrice())
                        .power(strategy.getPower())
                        .chargeType(strategy.getChargeType())
                        .abandonPv(strategy.getAbandonPv())
                        .build())
                .collect(Collectors.toList());

        // 1. 放电策略优化
        optimizeDischargeStrategy(optimizedStrategy, priceDataList, param);

        // 2. 充电策略优化（前提条件：24小时内负电价时段不大于4个小时）
        if (negativeHourCount <= 4) {
            optimizeChargeStrategy(optimizedStrategy, priceDataList, param);
        }

        return optimizedStrategy;
    }

    /** 放电策略优化 */
    private void optimizeDischargeStrategy(List<EleStrategyDTO> strategy, List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param) {
        // 找出所有连续放电时段
        List<List<EleStrategyDTO>> continuousDischargePeriods = findContinuousPeriods(strategy, ChargeTypeEnum.DISCHARGE.getType());

        for (List<EleStrategyDTO> period : continuousDischargePeriods) {
            if (period.size() > 4) {
                // 连续放电时段超过4小时，取连续时段中电价最高的4个时段
                period.sort((a, b) -> b.getPrice().compareTo(a.getPrice()));
                List<EleStrategyDTO> top4Periods = period.subList(0, 4);

                // 将不在top4的时段设置为无充放电
                for (EleStrategyDTO dischargePeriod : period) {
                    if (!top4Periods.contains(dischargePeriod)) {
                        EleStrategyDTO targetStrategy = strategy.stream()
                                .filter(s -> s.getStartTimeUnix().equals(dischargePeriod.getStartTimeUnix()))
                                .findFirst()
                                .orElse(null);
                        if (targetStrategy != null) {
                            targetStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                            targetStrategy.setPower(0);
                        }
                    }
                }
            }
            // 连续放电时段不超过4小时，则放电策略不变
        }
    }

    /** 充电策略优化 */
    private void optimizeChargeStrategy(List<EleStrategyDTO> strategy, List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param) {
        // 找出所有连续充电时段
        List<List<EleStrategyDTO>> continuousChargePeriods = findContinuousPeriods(strategy, ChargeTypeEnum.CHARGE.getType());

        for (List<EleStrategyDTO> period : continuousChargePeriods) {
            if (period.size() > 4) {
                // 连续充电时段超过4小时，取连续时段中电价最低的4个小时充电
                period.sort(Comparator.comparing(EleStrategyDTO::getPrice));
                List<EleStrategyDTO> top4Periods = period.subList(0, 4);

                // 将不在top4的时段设置为无充放电
                for (EleStrategyDTO chargePeriod : period) {
                    if (!top4Periods.contains(chargePeriod)) {
                        EleStrategyDTO targetStrategy = strategy.stream()
                                .filter(s -> s.getStartTimeUnix().equals(chargePeriod.getStartTimeUnix()))
                                .findFirst()
                                .orElse(null);
                        if (targetStrategy != null) {
                            targetStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                            targetStrategy.setPower(0);
                        }
                    }
                }
            }
            // 连续充电时段不超过4小时，则充电策略不变
        }
    }

    /** 找出连续的充放电时段 */
    private List<List<EleStrategyDTO>> findContinuousPeriods(List<EleStrategyDTO> strategy, int chargeType) {
        List<List<EleStrategyDTO>> continuousPeriods = new ArrayList<>();
        List<EleStrategyDTO> currentPeriod = new ArrayList<>();

        for (EleStrategyDTO s : strategy) {
            if (s.getChargeType() == chargeType) {
                currentPeriod.add(s);
            } else {
                if (!currentPeriod.isEmpty()) {
                    continuousPeriods.add(new ArrayList<>(currentPeriod));
                    currentPeriod.clear();
                }
            }
        }

        // 添加最后一个时段
        if (!currentPeriod.isEmpty()) {
            continuousPeriods.add(currentPeriod);
        }

        return continuousPeriods;
    }
}