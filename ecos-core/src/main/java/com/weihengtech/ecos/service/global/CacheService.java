package com.weihengtech.ecos.service.global;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface CacheService {

	/**
	 * 缓存字符串 单位秒
	 *
	 * @param key     redis key
	 * @param value   要缓存的字符串
	 * @param seconds 缓存时间
	 */
	void setStrWithSecond(String key, String value, long seconds);

	/**
	 * 获取字符串数据
	 *
	 * @param key redis key
	 * @return Optional字符串
	 */
	Optional<String> getStr(String key);

	/**
	 * 存在返回空 不存在返回有
	 *
	 * @param key redis key
	 * @return 存在Optional.empty()
	 */
	Optional<Boolean> notExistKey(String key);

	/**
	 * 删除指定缓存
	 *
	 * @param key 删除的key
	 */
	void delKey(String key);

}
