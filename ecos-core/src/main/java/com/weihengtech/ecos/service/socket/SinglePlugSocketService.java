package com.weihengtech.ecos.service.socket;

import com.weihengtech.ecos.model.dtos.socket.SocketHistoryEleDto;
import com.weihengtech.ecos.model.dtos.socket.SocketSwitchLogsDto;
import com.weihengtech.ecos.model.dtos.socket.SocketTimingListDto;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketBaseInfoDto;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.socket.SocketAddTimingVo;
import com.weihengtech.ecos.model.vos.socket.SocketCountdownVo;
import com.weihengtech.ecos.model.vos.socket.SocketHistoryEleVo;
import com.weihengtech.ecos.model.vos.socket.SocketInfoVo;
import com.weihengtech.ecos.model.vos.socket.SocketOverChargeSwitchVo;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import com.weihengtech.ecos.model.vos.socket.SocketSwitchLogsVo;
import com.weihengtech.ecos.model.vos.socket.SocketUpdateTimingVo;
import com.weihengtech.ecos.model.vos.socket.SwitchSocketVo;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 单插插座服务类
 * @author: jiahao.jin
 * @create: 2024-01-27 16:20
 **/
public interface SinglePlugSocketService {

    /**
     * 用户绑定设备
     *
     * @param v2ClientHomeBindDeviceVo 设备信息
     * @param clientUserDo               用户信息
     */
    void bindClientUserDevice(V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo, ClientUserDo clientUserDo);

    /**
     * 开关插座
     *
     * @param switchSocketVo 开关插座参数
     */
    void switchSocket(SwitchSocketVo switchSocketVo);

    /**
     * 查询单插插座详情
     *
     * @param socketInfoVo 单插插座详情参数
     * @return 插座详情
     */
    TuyaSocketBaseInfoDto queryTuyaSocketInfo(ClientUserDo clientUserDo, SocketInfoVo socketInfoVo);

    /**
     * 更新单插插座倒计时
     *
     * @param socketCountdownVo 单插插座倒计时入参
     */
    void updateTuyaSocketCountdown(SocketCountdownVo socketCountdownVo);

    /**
     * 更新单插插座随机定时
     *
     * @param socketRandomTimeMapVo 单插插座随机定时入参
     */
    void updateTuyaSocketRandomTime(SocketRandomTimeMapVo socketRandomTimeMapVo);

    /**
     * 添加定时开关任务
     *
     * @param socketAddTimingVo 入参
     */
    void addSocketTiming(SocketAddTimingVo socketAddTimingVo);

    /**
     * 查询单插定时开关任务列表
     *
     * @param deviceId 单插ID
     */
    List<SocketTimingListDto> querySocketTimingList(String deviceId);

    /**
     * 更新定时开关任务
     *
     * @param socketUpdateTimingVo 入参
     */
    Boolean updateSocketTiming(SocketUpdateTimingVo socketUpdateTimingVo);

    /**
     * 删除单插定时开关任务
     *
     * @param taskId 任务ID
     */
    void deleteSocketTimingTask(String userId, String taskId);

    /**
     * 查询单插开关日志（分页）
     *
     * @param socketSwitchLogsVo 查询入参
     * @return 日志列表
     */
    SocketSwitchLogsDto querySocketSwitchLogs(SocketSwitchLogsVo socketSwitchLogsVo);

    /**
     * 查询单插历史用电量
     *
     * @param socketHistoryEleVo 查询入参
     * @return 历史用电量数据
     */
    SocketHistoryEleDto querySocketHistoryEle(SocketHistoryEleVo socketHistoryEleVo);

    /**
     * 开关过冲保护功能
     *
     * @param socketOverChargeSwitchVo 入参
     * @return 是否执行成功
     */
    void switchSocketOverCharge(SocketOverChargeSwitchVo socketOverChargeSwitchVo);

    /**
     * 删除定时开关任务和恢复倒计时和随机定时
     *
     * @param devices 设备列表
     */
    void deleteDevicesConfigAndTask(List<HybridSinglePhaseDO> devices);

    /**
     * 清除电量数据
     *
     * @param deviceId 设备ID
     */
    void clearSocketPowerData(String deviceId);

}
