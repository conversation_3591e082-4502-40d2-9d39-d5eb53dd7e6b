package com.weihengtech.ecos.service.global;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.TuyaDatacenterDo;

/**
 * <AUTHOR>
 */
public interface TuyaDatacenterService extends IService<TuyaDatacenterDo> {

    /**
     * 判断数据中心是否存在
     * @param datacenterId 数据中心id
     * @return 是否存在
     */
    Boolean exist(Integer datacenterId);

    /**
     * 是否是相同的数据中心
     * @param firstId 第一个数据中心id
     * @param secondId 第二个数据中心id
     * @return 是否相同
     */
    Boolean isSameDatacenter(Integer firstId, Integer secondId);

    /**
     * 获取数据中心描述
     * @param datacenterId 第一个数据中心id
     * @return 数据中心 CN US AU EU
     */
    String getDatacenter(Integer datacenterId);
}
