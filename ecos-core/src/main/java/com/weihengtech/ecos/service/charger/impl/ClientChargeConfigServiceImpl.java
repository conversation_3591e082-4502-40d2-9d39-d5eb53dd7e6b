package com.weihengtech.ecos.service.charger.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientChargeConfigMapper;
import com.weihengtech.ecos.model.dos.ClientChargeConfigDo;
import com.weihengtech.ecos.service.charger.ClientChargeConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @program: ecos-server
 * @description: 充电桩充电配置服务实现类
 * @author: jiahao.jin
 * @create: 2024-02-19 16:54
 **/
@Service
@RequiredArgsConstructor
public class ClientChargeConfigServiceImpl
        extends ServiceImpl<ClientChargeConfigMapper, ClientChargeConfigDo>
        implements ClientChargeConfigService {

    @Resource
    private ClientChargeConfigMapper clientChargeConfigMapper;

    @Override
    public Optional<ClientChargeConfigDo> queryOptionalChargeConfigByDeviceId(long deviceId) {
        return Optional.ofNullable(clientChargeConfigMapper.selectOne(Wrappers.<ClientChargeConfigDo>lambdaQuery().eq(ClientChargeConfigDo::getDeviceId, deviceId)));
    }
}
