package com.weihengtech.ecos.service.ele.impl.strategy;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.enums.ele.ChargeTypeEnum;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/1 14:21
 */
@Service
public class ClientEarningStrategyServiceImpl implements ClientEleStrategyService {

    @Override
    public List<EleStrategyDTO> calStrategy(List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(priceDataList)) {
            return Collections.emptyList();
        }

        Double diff = param.getPriceGap().doubleValue();

        // 根据电价数据计算平均值、标准差、变异系数
        List<BigDecimal> priceList = priceDataList.stream()
                .map(EleDayAheadPriceDto::getAverage)
                .collect(Collectors.toList());
        double average = priceList.stream().collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double standardDeviation = calStandardDeviation(priceList, average);
        double variation = Math.abs(standardDeviation / average);
        double chargeThreshold = average * (1 - calThreshold(variation));
        double dischargeThreshold = average * (1 + calThreshold(variation));
        List<EleStrategyDTO> eleStrategyList = calEleStrategyPreviewData(priceDataList, chargeThreshold,
                dischargeThreshold, param, 0L, 4L);
        /*
         * 1、在符合条件的充电电价和放电电价中分别获取最大的充电电价 Pc(max) 和最小的放电电价 Pd(min)；
         * 计算 峰谷价差ΔP = Pd(min) - [ Pc(max) + Ptax ]，Ptax为购电税费；
         * 若ΔP大于等于0.05欧元/度，则当前初始充放电策略即满足收益要求
         */
        boolean resP = dischargeThreshold - (chargeThreshold + param.getPurchaseTax().doubleValue()) >= diff.floatValue();
        if (resP) {
            return eleStrategyList;
        }
        /*
         * 2、取符合条件的所有初始充电电价和放电电价，分别计算充电电价平均值AvgC和放电电价平均值AvgD;
         * 计算 峰谷平均值价差ΔAvg = AvgD - ( AvgC + Ptax)，Ptax为购电税费；
         * 若ΔAvg大于等于0.05欧元/度，取大于等于AcgD的电价时段放电，小于等于AvgC的电价时段充电；
         */
        double chargeAverage = eleStrategyList.stream()
                .filter(i -> ChargeTypeEnum.CHARGE.getType() == i.getChargeType())
                .map(EleStrategyDTO::getPrice)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double dischargeAverage = eleStrategyList.stream()
                .filter(i -> ChargeTypeEnum.DISCHARGE.getType() == i.getChargeType())
                .map(EleStrategyDTO::getPrice)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        boolean resAvg = dischargeAverage - (chargeAverage + param.getPurchaseTax().doubleValue()) >= diff;
        if (resAvg) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (ChargeTypeEnum.CHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() > chargeAverage) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                } else if (ChargeTypeEnum.DISCHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() < dischargeAverage) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        /*
         * 3、取当天中的最高电价Pmax 和最低电价Pmin；
         * 计算最值峰谷价差ΔPm = Pmax - ( Pmin+ Ptax )，Ptax为购电税费；
         * 若ΔPm大于等于0.05欧元/度，取最高电价时段放电，最低电价时段充电；
         */
        double maxPrice = priceDataList.stream()
                .max(Comparator.comparing(EleDayAheadPriceDto::getAverage))
                .map(EleDayAheadPriceDto::getAverage)
                .map(BigDecimal::doubleValue)
                .orElse(0d);
        double minPrice = priceDataList.stream()
                .min(Comparator.comparing(EleDayAheadPriceDto::getAverage))
                .map(EleDayAheadPriceDto::getAverage)
                .map(BigDecimal::doubleValue)
                .orElse(0d);
        boolean resPm = maxPrice - (minPrice + param.getPurchaseTax().doubleValue()) > diff;
        if (resPm) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (ChargeTypeEnum.CHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() > minPrice) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                } else if (ChargeTypeEnum.DISCHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() < maxPrice) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        /*
         * 判断是否存在负电价？若不存在，则全天执行自发自用模式，清空充放电时间段；
         * 若存在负电价，将所有负电价取绝对值，然后判断这些绝对值中是否存在大于等于Ptax的（判断是否存在加上税费后的电价还是不大于0）？
         * 若存在，则满足条件的负电价时段设置充电策略；
         * 若不存在，则全天执行自发自用模式，清空充放电时间段；
         */
        List<EleDayAheadPriceDto> negativePriceList = priceDataList.stream()
                .filter(i -> i.getAverage().compareTo(BigDecimal.ZERO) < 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(negativePriceList)) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                eleStrategy.setPower(0);
            }
            return eleStrategyList;
        }
        Set<Long> chargeTimeSet = negativePriceList.stream()
                .filter(i -> i.getAverage().add(param.getPurchaseTax()).compareTo(BigDecimal.ZERO) < 0)
                .map(EleDayAheadPriceDto::getStartTimeUnix)
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(chargeTimeSet)) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (chargeTimeSet.contains(eleStrategy.getStartTimeUnix())) {
                    eleStrategy.setChargeType(ChargeTypeEnum.CHARGE.getType());
                } else {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        for (EleStrategyDTO eleStrategy : eleStrategyList) {
            eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
            eleStrategy.setPower(0);
        }
        return eleStrategyList;
    }
}