package com.weihengtech.ecos.service.app;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientSessionDo;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 绘画表服务接口
 * @author: jiahao.jin
 * @create: 2024-05-09 15:24
 **/
public interface ClientSessionService extends IService<ClientSessionDo> {

    /**
     * @description: 通过chatId查询会话
     * @param chatId 会话id
     * @return com.weihengtech.ecos.model.dos.ClientSessionDo
     */
    ClientSessionDo queryClientSessionByChatId(String chatId);

    /**
     * @description: 通过userId查询总会话，根据给updateTime排序
     * @param userId 用户id
     * @return com.weihengtech.ecos.model.dos.ClientSessionDo
     */
    List<ClientSessionDo> queryClientSessionByUserId(Long userId);

    ClientSessionDo handleSession(String title, Long userId, ClientSessionDo clientSessionDo);

}
