package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ecos")
public interface ClientCustomizeMapper extends BaseMapper<ClientCustomizeDo> {

    @Select("SELECT d.home_id homeId, \n" +
            "d.device_id deviceId,\n" +
            "c.battery_min batteryMin,\n" +
            "c.max_feed_in maxFeedIn,\n" +
            "c.region,\n" +
            "c.auto_strategy autoStrategy,\n" +
            "c.auto_home_strategy autoHomeStrategy,\n" +
            "c.strategy_mode strategyMode,\n" +
            "c.def_charge_power defChargePower,\n" +
            "c.def_discharge_power defDischargePower,\n" +
            "c.timezone,\n" +
            "c.price_import_type priceImportType,\n" +
            "c.purchase_tax purchaseTax\n" +
            "from client_customize c\n" +
            "join client_home_device d\n" +
            "on d.device_id = c.device_id\n" +
            "where c.strategy_mode > 0\n" +
            "or c.auto_strategy = 1")
    List<StrategyCustomizeDTO> getAutoStrategyDevices();

    List<ClientCustomizeDo> getAutoStrategyDevicesByIds(@Param("deviceIds") List<Long> deviceIds);
}
