package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 家庭表映射类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:24
 **/
@DS("ecos")
public interface ClientHomeMapper extends BaseMapper<ClientHomeDo> {

    /**
     * 查询开启了家庭电价的家庭
     * @return
     */
    @Select("SELECT distinct h.*\n" +
            "from client_home h\n" +
            "join client_home_device hd\n" +
            "on hd.home_id = h.id\n" +
            "where h.ele_price_type is not null\n" +
            "and EXISTS (\n" +
            "  SELECT 1\n" +
            "  from client_customize c\n" +
            "  where c.device_id = hd.device_id\n" +
            "  and c.price_import_type = 1\n" +
            ")")
    List<ClientHomeDo> queryHomePriceHomeList();
}
