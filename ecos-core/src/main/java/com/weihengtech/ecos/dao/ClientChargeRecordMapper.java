package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeRecordDto;
import com.weihengtech.ecos.model.dos.ClientChargeRecordDo;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 充电桩充电记录表映射接口
 * @author: jiahao.jin
 * @create: 2024-02-20 17:16
 **/
@DS("ecos")
public interface ClientChargeRecordMapper extends BaseMapper<ClientChargeRecordDo> {

    /**
     * 根据设备标识查询充电记录list
     *
     * @param deviceFlag 设备标识
     * @param chargeStatus 记录状态
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 充电记录list
     */
    List<ENPlusChargeStationChargeRecordDto> listChargeRecordByCondition(String deviceFlag, Integer chargeStatus, Long startTime, Long endTime);
}
