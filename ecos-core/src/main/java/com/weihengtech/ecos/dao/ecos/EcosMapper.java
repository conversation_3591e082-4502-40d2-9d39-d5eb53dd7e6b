package com.weihengtech.ecos.dao.ecos;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo;
import com.weihengtech.ecos.model.bos.ecos.EcosEventBo;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ecos-event")
public interface EcosMapper {

	/**
	 * 根据条件查询事件列表
	 *
	 * @param deviceFlag 设备标识
	 * @param level      等级
	 * @param start      开始
	 * @param end        结束
	 * @return 事件列表
	 */
	List<EcosEventBo> listEventByCondition(String deviceFlag, String level, Long start, Long end);

	/**
	 * 统计备电次数
	 *
	 * @param deviceFlag 设备标识
	 * @return 备电次数
	 */
	Integer countBackupMode(String deviceFlag);

	/**
	 * 统计eps在某一时间段内的数据
	 *
	 * @param deviceFlag 设备标识
	 * @param start      开始时间戳
	 * @param end        结束时间戳
	 * @return 时间段内所有的eps事件
	 */
	List<EcosEpsEventBo> listEpsEventByCondition(String deviceFlag, long start, long end);

	/**
	 * 获取最近的一条非eps事件
	 *
	 * @param deviceFlag 设备标识
	 * @param id         eps事件id
	 * @return 非eps事件
	 */
	EcosEpsEventBo findNearlyNotEpsEvent(String deviceFlag, int id);
}
