package com.weihengtech.ecos.dao;

import com.weihengtech.ecos.model.dos.ClientHomeSaveCostDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface ClientHomeSaveCostMapper extends BaseMapper<ClientHomeSaveCostDO> {

    List<HomeCostSavingDTO.Detail> calDayCostData(@Param("homeId") Long homeId,
                                                  @Param("startTime") Long startTime,
                                                  @Param("endTime") Long endTime);

    HomeCostSavingDTO.Detail sumCostData(@Param("homeId") Long homeId,
                                         @Param("startTime") Long startTime,
                                         @Param("endTime") Long endTime);

}
