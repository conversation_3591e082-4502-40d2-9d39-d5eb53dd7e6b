package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientChargeRecordReadDo;

/**
 * @program: ecos-server
 * @description: 充电记录通知表
 * @author: jiahao.jin
 * @create: 2024-02-29 11:54
 **/
@DS("ecos")
public interface ClientChargeRecordReadMapper extends BaseMapper<ClientChargeRecordReadDo> {
}
