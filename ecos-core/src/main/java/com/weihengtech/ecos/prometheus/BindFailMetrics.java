package com.weihengtech.ecos.prometheus;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 17:08
 */
@Component
public class BindFailMetrics {

    // 存储各个任务的失败计数器
    private final Map<String, Counter> bindFailureCounters = new ConcurrentHashMap<>();

    private final MeterRegistry registry;

    public BindFailMetrics(MeterRegistry registry) {
        this.registry = registry;
    }

    private Counter registerBindFail(String wifiSn, String errorType) {
        return bindFailureCounters.computeIfAbsent(wifiSn, name ->
                Counter.builder("device.net.bind.failures")
                        .description("设备配网绑定失败")
                        .tag("wifiSn", wifiSn)
                        .tag("errorType", errorType)
                        .register(registry)
        );
    }

    /**
     * 记录绑定失败
     *
     * @param wifiSn
     */
    public void recordFailure(String wifiSn, String errorType) {
        Counter counter = bindFailureCounters.get(wifiSn);
        if (counter != null) {
            counter.increment();
        } else {
            counter = registerBindFail(wifiSn, errorType);
            counter.increment();
        }
    }
}