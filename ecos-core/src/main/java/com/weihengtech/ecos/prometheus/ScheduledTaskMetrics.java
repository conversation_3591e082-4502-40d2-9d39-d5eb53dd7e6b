package com.weihengtech.ecos.prometheus;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 18:46
 */
@Component
public class ScheduledTaskMetrics {

    // 存储各个任务的失败计数器
    private final Map<String, Counter> taskFailureCounters = new ConcurrentHashMap<>();

    private final MeterRegistry registry;

    public ScheduledTaskMetrics(MeterRegistry registry) {
        this.registry = registry;
    }

    /**
     * 注册任务失败计数器
     *
     * @param taskName 任务名称（唯一标识）
     * @param description 任务描述
     */
    public Counter registerTask(String taskName, String description) {
        return taskFailureCounters.computeIfAbsent(taskName, name ->
                Counter.builder("scheduled.task.failures")
                        .description(description)
                        .tag("task", taskName)  // 任务名称作为标签
                        .register(registry)
        );
    }

    /**
     * 记录任务失败
     *
     * @param taskName 任务名称
     */
    public void recordFailure(String taskName) {
        Counter counter = taskFailureCounters.get(taskName);
        if (counter != null) {
            counter.increment();
        } else {
            counter = registerTask(taskName, "定时任务执行失败");
            counter.increment();
        }
    }
}