package com.weihengtech.ecos.aspects;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.model.dos.ClientOperationLogDO;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.service.global.ClientOperationLogService;
import com.weihengtech.ecos.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/6 14:11
 */
@Component
@Aspect
@Slf4j
public class LogAspect {

    @Autowired
    private ClientOperationLogService clientOperationLogService;

    @Pointcut("@annotation(com.weihengtech.ecos.annotation.OperationLog)")
    public void operationLog() {
    }

    @Around("operationLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object res = null;
        long time = System.currentTimeMillis();
        Exception throwable = null;
        try {
            res =  joinPoint.proceed();
            time = System.currentTimeMillis() - time;
            return res;
        } catch (Exception e) {
            throwable = e;
            throw e;
        } finally {
            try {
                //方法执行完成后增加日志
                addOperationLog(joinPoint, res, time, throwable);
            }catch (Exception e){
                log.error("addOperationLog failed");
            }
        }
    }

    private void addOperationLog(JoinPoint joinPoint, Object res, long time, Exception throwable){
        ClientUserDo userInfo = SecurityUtil.getClientUserDo();
        if (userInfo == null) {
            return;
        }
        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();
        String param = Optional.ofNullable(joinPoint.getArgs()).map(JSONUtil::toJsonStr)
                .map(i -> i.length() > 500 ? i.substring(0, 500) : i)
                .orElse(null);
        String result = Optional.ofNullable(res)
                .map(JSONUtil::toJsonStr)
                .map(i -> i.length() > 500 ? i.substring(0, 500) : i)
                .orElse(null);
        ClientOperationLogDO logItem = ClientOperationLogDO.builder()
                .method(signature.getMethod().getName())
                .param(param)
                .result(result)
                .error(Optional.ofNullable(throwable).map(Throwable::getMessage).orElse(null))
                .userName(userInfo.getUsername())
                .ip(request.getRemoteHost())
                .elapsedTime(time)
                .createTime(new Date())
                .build();
        log.info("操作日志：" + JSONUtil.toJsonStr(logItem));
        ThreadUtil.execAsync(() -> clientOperationLogService.save(logItem));
    }
}
