package com.weihengtech.ecos.common.exception;

import com.weihengtech.ecos.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class EcosException extends RuntimeException {

	private final Integer code;

	public EcosException(Integer code, String msg) {
		super(msg);
		this.code = code;
	}

	public EcosException(EcosExceptionEnum exceptionEnum) {
		super(exceptionEnum.getMsg());
		this.code = exceptionEnum.getCode();
	}

	public EcosException(EcosExceptionEnum ecosExceptionEnum, String msg) {
		super(msg);
		this.code = ecosExceptionEnum.getCode();
	}

	@Override
	public String toString() {
		return LocaleUtil.getMessage(this.getMessage()).orElse("----- EcosException -----");
	}

	@Override
	public Throwable fillInStackTrace() {
		return this;
	}
}
