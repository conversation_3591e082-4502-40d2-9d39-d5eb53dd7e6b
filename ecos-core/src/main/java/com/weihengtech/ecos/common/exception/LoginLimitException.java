package com.weihengtech.ecos.common.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class LoginLimitException extends RuntimeException {
	private final Integer times;
	private final Integer minute;

	public LoginLimitException(String message, Integer times, Integer minute) {
		super(message);
		this.times = times;
		this.minute = minute;
	}

	@Override
	public Throwable fillInStackTrace() {
		return this;
	}
}
