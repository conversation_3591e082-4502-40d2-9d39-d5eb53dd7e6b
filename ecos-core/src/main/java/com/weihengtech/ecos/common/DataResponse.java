package com.weihengtech.ecos.common;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.utils.LocaleUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "数据回参类")
public class DataResponse<T> extends EmptyResponse {

	@ApiModelProperty(name = "data", value = "数据体")
	private T data;

	/**
	 * 默认携带数据体回参
	 *
	 * @param data 数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> success(T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(0);
		dataResponse.setMessage("success");
		dataResponse.setSuccess(true);
		return dataResponse;
	}

	/**
	 * 默认带数据体失败回参
	 *
	 * @param data 自定义失败数据体
	 * @param <T>  数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(500);
		dataResponse.setSuccess(false);
		dataResponse.setMessage("Server error");
		return dataResponse;
	}

	/**
	 * 异常枚举类带数据回参
	 *
	 * @param ecosException 异常类
	 * @param data          数据体
	 * @param <T>           数据体类型
	 * @return DataResponse
	 */
	public static <T> DataResponse<T> fail(EcosException ecosException, T data) {
		DataResponse<T> dataResponse = new DataResponse<>();
		dataResponse.setData(data);
		dataResponse.setCode(ecosException.getCode());
		dataResponse.setMessage(LocaleUtil.getMessage(ecosException.getMessage()).orElse("error"));
		return dataResponse;
	}
}
