package com.weihengtech.ecos.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class InResponse<T> {

	@ApiModelProperty(name = "data", value = "数据体")
	private T data;

	@ApiModelProperty(name = "code", value = "状态码")
	private Integer code;

	@ApiModelProperty(name = "message", value = "描述信息")
	private String msg;

	public static <T> InResponse<T> success(T data) {
		InResponse<T> inResponse = new InResponse<>();
		inResponse.setData(data);
		inResponse.setCode(200);
		inResponse.setMsg("success");
		return inResponse;
	}

	public static <T> InResponse<T> success() {
		InResponse<T> inResponse = new InResponse<>();
		inResponse.setData(null);
		inResponse.setCode(200);
		inResponse.setMsg("success");
		return inResponse;
	}

	public static <T> InResponse<T> fail() {
		InResponse<T> inResponse = new InResponse<>();
		inResponse.setData(null);
		inResponse.setCode(500);
		inResponse.setMsg("fail");
		return inResponse;
	}

	public static <T> InResponse<T> fail(T data) {
		InResponse<T> inResponse = new InResponse<>();
		inResponse.setData(data);
		inResponse.setCode(500);
		inResponse.setMsg("fail");
		return inResponse;
	}
}
