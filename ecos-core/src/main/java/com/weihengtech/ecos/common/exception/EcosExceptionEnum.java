package com.weihengtech.ecos.common.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum EcosExceptionEnum {
	/**
	 * Ecos Client异常枚举
	 */
	TWICE_PASSWORD_NOT_EQUALS(20401, "err.twice.password.not.equals"),
	ACCOUNT_ALREADY_EXIST(20402, "err.account.existed"),
	INVALID_PASSWORD(20403, "err.invalid.password"),
	INVALID_PARAM(20404, "err.invalid.param"),
	ASSERT_TRUE(20405, "err.assert.true"),
	INVALID_DEVICE_SN_WITH_KEY(20406, "err.invalid.sn"),
	SYNC_DEVICE_TIME_ZONE_ERROR(20407, "err.sync.time.zone"),
	DEVICE_BIND_TIME_LIMIT_ERROR(20408, "err.device.bind.time.limit"),
	SEND_EMAIL_ERROR(20409, "err.send.email"),
	CODE_SEND_LIMIT(20410, "err.send.code.limit"),
	INVALID_CODE(20411, "err.invalid.code"),
	INVALID_DATA(20412, "err.invalid.data"),
	ASSERT_SINGLE_ACTION(20413, "err.assert.single.action"),
	INVALID_USERNAME_OR_PASSWORD(20414, "err.invalid.username.or.password"),
	UNLICENSED_USER(20415, "err.unlicensed.user"),
	INVALID_TOKEN(20416, "err.invalid.token"),
	INVALID_CLIENT_TYPE(20417, "err.valid.client.type"),
	ORIGINAL_PASSWORD_ERROR(20418, "err.original.password"),
	MISS_DEVICE_COUNT(20419, "err.miss.device.count"),
	CHANGE_DEVICE_CONFIG_ERROR(20420, "err.change.device.config"),
	CONFIG_PARAMS_ERROR(20421, "err.config.param.count"),
	READ_DEVICE_CONFIG_ERROR(20422, "err.config.read"),
	INVALID_PARAM_RANGE(20423, "err.invalid.param.range"),
	INVALID_ACTION_TO_DEVICE(20424, "err.invalid.action.to.device"),
	INVALID_RESULT_LEN(20425, "err.invalid.result.len"),
	TIME_CANNOT_CROSS(20426, "err.time.cannot.cross"),
	TSDB_OVER_TIME_SPAN(20427, "err.tsdb.over.time.span"),
	DONT_BIND_DEVICE_TWICE(20428, "err.dont.bind.device.twice"),
	DEVICE_CONNECT_ERROR(20429, "err.device.connect"),
	CLIENT_VERSION_ERROR(20430, "err.version.fail"),
	CANNOT_UNBIND_SINGLE_DEVICE(20431, "err.cannot.unbind.single.device"),
	DEVICE_BIND_FAILURE(20432, "err.device.bind.failure"),
	SLAVE_BIND_ERROR(20433, "err.slave.device.bind"),
	VALID_CODE_NOT_FIT(20434, "err.code.not.fit"),
	UPDATE_VERSION(20435, "err.update.version"),
	TIME_RANGE(20436, "err.time.range"),
	MAIL_NOT_EXISTED(20437, "err.mail.not.exist"),
	INVALID_DATACENTER(20438, "err.invalid.datacenter"),
	EMAIL_ALREADY_BOUND(20439, "err.email.bound"),
	PHONE_ALREADY_BOUND(20440, "err.phone.bound"),
	PHONE_NOT_BOUND(20441, "err.phone.not.bound"),
	DEVICE_ALREADY_BOUND(20442, "err.device.bound"),
	// 该家庭无法被操作
	HOME_NOT_ALLOWED_OPERATE(20443, "err.home.not.allowed.operate"),
	// 未授权的家庭(您的操作暂未被授权)
	INVALID_ACTION_TO_HOME(20444, "err.invalid.action.to.home"),
	// 请勿重复加入家庭
	DONT_BIND_HOME_TWICE(20445, "err.dont.bind.home.twice"),
	// 用户不在此家庭中
	USER_NOT_IN_HOME(20446, "err.user.not.in.home"),
	// 设备不在此家庭中
	DEVICE_NOT_IN_HOME(20447, "err.device.not.in.home"),
	// 当前状态不可修改充电配置
	UNCHANGEABLE_CHARGE_CONFIG(20448, "err.unchangeable.charge.config"),
	// 未插枪不可充电
	NOT_PLUG_IN(20449, "err.not.plug.in"),
	// 设备不存在
	DEVICE_NOT_FIND(20450, "err.device.not.find"),
	// 家庭不存在
	HOME_NOT_FIND(20450, "err.home.not.find"),
	// 二维码已过期
	QR_CODE_EXPIRED(20451, "err.qr.code.expired"),
	// 正在充电中
	IS_CHARGING(20452, "err.station.is.charging"),
	// 开始充电失败
	START_CHARGE_FAIL(20453,"err.start.charge.fail"),
	// 停止充电失败
	STOP_CHARGE_FAIL(20454,"err.stop.charge.fail"),
	// 间隔不能低于5分钟
	INTERVAL_LESS_THAN_5_MINUTES(20455,"err.interval.less.than.5.minutes"),
	// 间隔不能低于30分钟
	INTERVAL_LESS_THAN_30_MINUTES(20456,"err.interval.less.than.30.minutes"),
	// 回答生成失败
	ANSWER_GENERATE_FAIL(20457,"err.answer.generate.fail"),
	// 很抱歉，您的账户今日对话次数已达上限(20条)。
	CHAT_NUM_OVER_LIMIT(20458,"err.chat.num.over.limit"),
	// 对话内容长度超过上限。
	CHAT_CONTENT_OVER_LIMIT(20459,"err.chat.content.over.limit"),
	// 当前家庭还未添加储能设备，暂无相关统计数据
	NO_ENERGY_DEVICE(20460,"err.no.energy.device"),
	// 当前数据中心没有该设备
	CUR_DATACENTER_NO_DEVICE(20461, "err.cur.datacenter.no.device")
	;


	private final Integer code;
	private final String msg;

	EcosExceptionEnum(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}
}
