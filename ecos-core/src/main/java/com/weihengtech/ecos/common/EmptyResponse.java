package com.weihengtech.ecos.common;

import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.utils.LocaleUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "空数据回参类")
public class EmptyResponse {

	@ApiModelProperty(name = "code", value = "状态码")
	private Integer code;

	@ApiModelProperty(name = "message", value = "描述信息")
	private String message;

	@ApiModelProperty(name = "success", value = "请求描述")
	private boolean success;

	/**
	 * 默认成功回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse success() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(0);
		emptyResponse.setMessage("success");
		emptyResponse.setSuccess(true);
		return emptyResponse;
	}

	/**
	 * 默认成功回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse success200() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(200);
		emptyResponse.setMessage("success");
		emptyResponse.setSuccess(true);
		return emptyResponse;
	}

	/**
	 * 默认失败回参
	 *
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(500);
		emptyResponse.setSuccess(false);
		emptyResponse.setMessage("Server error");
		return emptyResponse;
	}

	/**
	 * 自定义失败回参 异常枚举
	 *
	 * @param exceptionEnum 异常枚举类
	 * @return EmptyResponse
	 */
	public static EmptyResponse fail(EcosExceptionEnum exceptionEnum) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(exceptionEnum.getCode());
		emptyResponse.setMessage(LocaleUtil.getMessage(exceptionEnum.getMsg()).orElse("error"));
		emptyResponse.setSuccess(false);
		return emptyResponse;
	}

	public static EmptyResponse fail(Integer code, String message) {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(code);
		emptyResponse.setMessage(LocaleUtil.getMessage(message).orElse("error"));
		emptyResponse.setSuccess(false);
		return emptyResponse;
	}

	public static EmptyResponse unauthorized() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(HttpStatus.UNAUTHORIZED.value());
		emptyResponse.setMessage(HttpStatus.UNAUTHORIZED.getReasonPhrase());
		emptyResponse.setSuccess(false);
		return emptyResponse;
	}

	public static EmptyResponse invalidToken() {
		EmptyResponse emptyResponse = new EmptyResponse();
		emptyResponse.setCode(EcosExceptionEnum.INVALID_TOKEN.getCode());
		emptyResponse.setMessage(LocaleUtil.getMessage(EcosExceptionEnum.INVALID_TOKEN.getMsg()).orElse("error"));
		emptyResponse.setSuccess(false);
		return emptyResponse;
	}
}
