package com.weihengtech.ecos.common.exception;

import com.weihengtech.ecos.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class UnauthorizedException extends RuntimeException {

	private final Integer code;

	public UnauthorizedException(Integer code, String msg) {
		super(msg);
		this.code = code;
	}

	public UnauthorizedException(EcosExceptionEnum exceptionEnum) {
		super(exceptionEnum.getMsg());
		this.code = exceptionEnum.getCode();
	}

	public UnauthorizedException(EcosExceptionEnum ecosExceptionEnum, String msg) {
		super(msg);
		this.code = ecosExceptionEnum.getCode();
	}

	@Override
	public String toString() {
		return LocaleUtil.getMessage(this.getMessage()).orElse("----- UnauthorizedException -----");
	}

	@Override
	public Throwable fillInStackTrace() {
		return this;
	}
}
