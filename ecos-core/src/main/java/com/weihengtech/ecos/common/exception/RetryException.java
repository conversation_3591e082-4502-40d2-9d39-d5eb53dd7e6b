package com.weihengtech.ecos.common.exception;

import com.weihengtech.ecos.utils.LocaleUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class RetryException extends RuntimeException {

	private final Integer code;

	public RetryException(Integer code, String msg) {
		super(msg);
		this.code = code;
	}

	public RetryException(EcosExceptionEnum exceptionEnum) {
		super(exceptionEnum.getMsg());
		this.code = exceptionEnum.getCode();
	}

	public RetryException(EcosExceptionEnum ecosExceptionEnum, String msg) {
		super(msg);
		this.code = ecosExceptionEnum.getCode();
	}

	@Override
	public String toString() {
		return LocaleUtil.getMessage(this.getMessage()).orElse("----- RetryException -----");
	}

	@Override
	public Throwable fillInStackTrace() {
		return this;
	}
}
