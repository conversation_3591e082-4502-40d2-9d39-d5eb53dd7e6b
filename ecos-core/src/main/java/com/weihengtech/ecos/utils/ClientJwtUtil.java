package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.config.CustomConfig;
import com.weihengtech.ecos.model.bos.user.ClientUserDetails;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ClientJwtUtil {

	@Resource
	private CustomConfig customConfig;

	private static final Key ACCESS_KEY = Keys
			.hmacShaKeyFor("Ht0Iuhx074rGll-UKIG&3V66$F_OJIlhjYzj23EYaq#PP-htw_-DtF+mO2jLcOW=asN6z@IWAO^!6HUk"
					.getBytes(StandardCharsets.UTF_8));
	private static final Key REFRESH_KEY = Keys
			.hmacShaKeyFor("EK5TFRNvKL*j*QxJX#Me66lDxvYW#ucTNBDI-7kqjA!TCesKPSsogmXVZ8uxvtI9TGAU3AdjXc!PVuap"
					.getBytes(StandardCharsets.UTF_8));

	public String generateAccessToken(ClientUserDetails userDetails) {
		return createJwtToken(
				userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()),
				String.valueOf(userDetails.getId()), userDetails.getUsername(), ACCESS_KEY,
				customConfig.getToken().getAccessExpire()
		);
	}

	public String generateRefreshToken(ClientUserDetails userDetails) {
		return createJwtToken(
				userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()),
				String.valueOf(userDetails.getId()), userDetails.getUsername(), REFRESH_KEY,
				customConfig.getToken().getRefreshExpire()
		);
	}

	private String createJwtToken(List<String> authorities, String userId, String username, Key key, long expired) {
		long now = System.currentTimeMillis();
		return Jwts.builder().setId(userId).claim("authorities", String.join(",", authorities)).setSubject(username)
				.setIssuedAt(new Date(now)).setExpiration(new Date(now + expired))
				.signWith(key, SignatureAlgorithm.HS512).compact();
	}

	public Optional<Claims> parseAccessToken(String token) {
		return parseToken(token, ACCESS_KEY);
	}

	private Optional<Claims> parseRefreshToken(String token) {
		return parseToken(token, REFRESH_KEY);
	}

	private Optional<Claims> parseToken(String token, Key key) {
		Claims claims;
		try {
			claims = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token).getBody();
			return Optional.ofNullable(claims);
		} catch (Exception e) {
			return Optional.empty();
		}
	}

	private boolean validateToken(String token, Key key, boolean ignoreExpired) {
		try {
			Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
		} catch (ExpiredJwtException e) {
			return ignoreExpired;
		} catch (MalformedJwtException | SignatureException | IllegalArgumentException e) {
			return false;
		}
		return true;
	}

	public String refreshAccessTokenByRefreshToken(String refreshToken, String accessToken) {
		if (validateToken(refreshToken, REFRESH_KEY, false) && validateToken(accessToken, ACCESS_KEY, true)) {
			Optional<Claims> optionalClaims = parseRefreshToken(refreshToken);
			if (optionalClaims.isPresent()) {
				long now = System.currentTimeMillis();
				Claims claims = optionalClaims.get();
				return Jwts.builder().setId(claims.getId()).setClaims(claims).setSubject(claims.getSubject())
						.setIssuedAt(new Date(now))
						.setExpiration(new Date(now + customConfig.getToken().getAccessExpire()))
						.signWith(ACCESS_KEY, SignatureAlgorithm.HS512).compact();
			}
		}
		throw new EcosException(EcosExceptionEnum.INVALID_TOKEN);
	}
}
