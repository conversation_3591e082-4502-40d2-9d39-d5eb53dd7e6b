package com.weihengtech.ecos.utils;

import cn.hutool.extra.cglib.CglibUtil;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzVo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TransformUtil {

	public static CustomizeInfoEzDto customizeInfoToEz(CustomizeInfoDto customizeInfoDto) {
		CustomizeInfoEzDto customizeInfoEzDto = new CustomizeInfoEzDto();
		CglibUtil.copy(customizeInfoDto, customizeInfoEzDto);
		List<ChargingStructDTO> chargingList = customizeInfoDto.getChargingList().stream()
				.filter(i -> !Objects.equals(i.getStartHour(), i.getEndHour())
						|| !Objects.equals(i.getStartMinute(), i.getEndMinute()))
				.collect(Collectors.toList());
		List<ChargingStructDTO> dischargingList = customizeInfoDto.getDischargingList().stream()
						.filter(i -> !Objects.equals(i.getStartHour(), i.getEndHour())
								|| !Objects.equals(i.getStartMinute(), i.getEndMinute()))
								.collect(Collectors.toList());
		customizeInfoEzDto.setChargingList(chargingList);
		customizeInfoEzDto.setDischargingList(dischargingList);
		return customizeInfoEzDto;
	}

	public static CustomizeInfoVo ezToCustomizeInfoVo(CustomizeInfoEzVo customizeInfoEzVo) {
		CustomizeInfoVo customizeInfoVo = new CustomizeInfoVo();
		Integer chargeUseMode = customizeInfoEzVo.getChargeUseMode();
		CglibUtil.copy(customizeInfoEzVo, customizeInfoVo);
		if (chargeUseMode != 1) {
			return customizeInfoVo;
		}
		List<ChargingStructDTO> chargingList = customizeInfoEzVo.getChargingList();
		List<ChargingStructDTO> dischargingList = customizeInfoEzVo.getDischargingList();

		for (int i = 0; i < chargingList.size(); i++) {
			if (i == 0) {
				customizeInfoVo.setCharge1StartTimeHour(chargingList.get(i).getStartHour());
				customizeInfoVo.setCharge1StartTimeMinute(chargingList.get(i).getStartMinute());
				customizeInfoVo.setCharge1EndTimeHour(chargingList.get(i).getEndHour());
				customizeInfoVo.setCharge1EndTimeMinute(chargingList.get(i).getEndMinute());
				customizeInfoVo.setCharge1Power(chargingList.get(i).getPower());
			} else if (i == 1) {
				customizeInfoVo.setCharge2StartTimeHour(chargingList.get(i).getStartHour());
				customizeInfoVo.setCharge2StartTimeMinute(chargingList.get(i).getStartMinute());
				customizeInfoVo.setCharge2EndTimeHour(chargingList.get(i).getEndHour());
				customizeInfoVo.setCharge2EndTimeMinute(chargingList.get(i).getEndMinute());
				customizeInfoVo.setCharge2Power(chargingList.get(i).getPower());
			}
		}

		for (int i = 0; i < dischargingList.size(); i++) {
			if (i == 0) {
				customizeInfoVo.setDischarge1StartTimeHour(dischargingList.get(i).getStartHour());
				customizeInfoVo.setDischarge1StartTimeMinute(dischargingList.get(i).getStartMinute());
				customizeInfoVo.setDischarge1EndTimeHour(dischargingList.get(i).getEndHour());
				customizeInfoVo.setDischarge1EndTimeMinute(dischargingList.get(i).getEndMinute());
				customizeInfoVo.setDischarge1Power(dischargingList.get(i).getPower());
			} else if (i == 1) {
				customizeInfoVo.setDischarge2StartTimeHour(dischargingList.get(i).getStartHour());
				customizeInfoVo.setDischarge2StartTimeMinute(dischargingList.get(i).getStartMinute());
				customizeInfoVo.setDischarge2EndTimeHour(dischargingList.get(i).getEndHour());
				customizeInfoVo.setDischarge2EndTimeMinute(dischargingList.get(i).getEndMinute());
				customizeInfoVo.setDischarge2Power(dischargingList.get(i).getPower());
			}
		}
		return customizeInfoVo;
	}
}
