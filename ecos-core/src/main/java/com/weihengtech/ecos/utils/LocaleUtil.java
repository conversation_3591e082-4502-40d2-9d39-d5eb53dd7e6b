package com.weihengtech.ecos.utils;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class LocaleUtil {

	public static Optional<String> getMessage(String position) {
		MessageSource messageSource = InitUtil.getBean("messageSource", MessageSource.class);
		try {
			String message = messageSource.getMessage(position, null, LocaleContextHolder.getLocale());
			return Optional.of(message);
		} catch (NoSuchMessageException e) {
			return Optional.empty();
		}
	}

	public static String getLanguage(HttpServletRequest request) {
		String language = request.getHeader("Language");
		if (StrUtil.isBlank(language) || language.split("_").length != 2) {
			return "en_US";
		}
		return language;
	}

	public static String getLanguage() {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
				.getRequest();
		String language = request.getHeader("Language");
		if (StrUtil.isBlank(language) || language.split("_").length != 2) {
			return "en_US";
		}
		return language;
	}

	public static String mapLocaleToDatabaseGetMethod() {
		HttpServletRequest request = ((ServletRequestAttributes) Objects
				.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
		String language = getLanguage(request);
		String[] s = language.split("_");
		StrBuilder strBuilder = new StrBuilder();
		strBuilder.append("get");
		strBuilder.append(s[0].substring(0, 1).toUpperCase());
		strBuilder.append(s[0].substring(1).toLowerCase());
		strBuilder.append(s[1].substring(0, 1).toUpperCase());
		strBuilder.append(s[1].substring(1).toLowerCase());
		return strBuilder.toString();
	}
}
