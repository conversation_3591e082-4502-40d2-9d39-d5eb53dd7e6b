package com.weihengtech.ecos.utils;

import cn.hutool.core.codec.Rot;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class RotUtil {

	public static String encode(String rawStr, Integer offset) {
		return Rot.encode(rawStr, offset, true);
	}

	public static Optional<String> decode(String encodeStr, Integer offset) {
		try {
			return Optional.of(Rot.decode(encodeStr, offset, true));
		} catch (Exception e) {
			return Optional.empty();
		}
	}
}
