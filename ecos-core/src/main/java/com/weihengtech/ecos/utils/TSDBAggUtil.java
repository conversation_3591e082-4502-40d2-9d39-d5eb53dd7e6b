package com.weihengtech.ecos.utils;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TSDBAggUtil {

	/**
	 * 聚合小时到天
	 */
	public static Map<String, LinkedHashMap<Long, Object>> aggregateDeltaQueryHourToDay(
			Map<String, LinkedHashMap<Long, Object>> data, String offset
	) {
		return aggregateDeltaQuery(data, offset, "aggregateDeltaQueryHourToDay");
	}

	/**
	 * 聚合天到月
	 */
	public static Map<String, LinkedHashMap<Long, Object>> aggregateDeltaQueryDayToMonth(
			Map<String, LinkedHashMap<Long, Object>> data, String offset
	) {
		return aggregateDeltaQuery(data, offset, "aggregateDeltaQueryDayToMonth");
	}

	public static Map<String, LinkedHashMap<Long, Object>> aggregateDeltaQueryMonthToYear(
			Map<String, LinkedHashMap<Long, Object>> data, String offset) {
		return aggregateDeltaQuery(data, offset, "aggregateDeltaQueryMonthToYear");
	}


	private static Map<String, LinkedHashMap<Long, Object>> aggregateDeltaQuery(
			Map<String, LinkedHashMap<Long, Object>> data, String offset, String actionFlag
	) {
		Map<String, LinkedHashMap<Long, Object>> aggregateMap = new LinkedHashMap<>();
		for (String metric : data.keySet()) {
			LinkedHashMap<Long, Object> timeData = data.get(metric);
			ArrayList<Long> timeList = new ArrayList<>(timeData.keySet());
			Map<String, List<Long>> timeGroupData = new LinkedHashMap<>();

			switch (actionFlag) {
				case "aggregateDeltaQueryHourToDay":
					timeGroupData = CollStreamUtil.groupByKey(timeList, v -> TimeUtil
							.longTimestampToSerialString(v * 1000L - 8 * 3600 * 1000L, offset, "yyyy-MM-dd"));
					break;
				case "aggregateDeltaQueryDayToMonth":
					timeGroupData = CollStreamUtil.groupByKey(
							timeList,
							v -> TimeUtil
									.longTimestampToSerialString(v * 1000L - 8 * 3600 * 1000L, offset, "yyyy-MM-dd")
									.substring(0, 7)
					);
					break;
				case "aggregateDeltaQueryMonthToYear":
					timeGroupData = CollStreamUtil.groupByKey(
							timeList,
							v -> TimeUtil
									.longTimestampToSerialString(v * 1000L - 8 * 3600 * 1000L, offset, "yyyy-MM-dd")
									.substring(0,4)
					);
					break;

				default:
			}

			LinkedHashMap<Long, Object> aggregateTimeData = new LinkedHashMap<>();
			for (String timeGroupKey : timeGroupData.keySet()) {
				List<Long> timeAggregateList = timeGroupData.get(timeGroupKey);
				BigDecimal value = BigDecimal.ZERO;
				for (Long t : timeAggregateList) {
					// getOrDefault如果key存在，value为null是无法使用默认值的
					String numberStr = timeData.get(t) == null ? "0" : timeData.get(t).toString();
					value = NumberUtil.add(value, new BigDecimal(numberStr));
				}
				aggregateTimeData.put(
						timeAggregateList.get(timeAggregateList.size() - 1),
						NumberUtil.round(value, 2, RoundingMode.HALF_UP)
				);
			}
			aggregateMap.put(metric, aggregateTimeData);
		}
		return aggregateMap;
	}
}
