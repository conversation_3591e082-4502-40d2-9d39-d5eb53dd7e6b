package com.weihengtech.ecos.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class TimezoneUtil {

	/** 时区转换：用户时区转0时区 */
	public static void convertUserTimezoneToGmt(List<ChargingStructDTO> list, String timeZone) {
		if (CollUtil.isEmpty(list)) {
			return;
		}
		Pair<Integer, Integer> offsetHourAndMinute = TimeUtil
				.offsetToNumber(Optional.ofNullable(timeZone).orElse("GMT+00:00"));
		for (ChargingStructDTO chargingStruct : list) {
			setTimezoneToZero(chargingStruct, offsetHourAndMinute, chargingStruct.getStartMinute(),
					chargingStruct.getStartHour(), "setStartMinute", "setStartHour"
			);
			setTimezoneToZero(chargingStruct, offsetHourAndMinute, chargingStruct.getEndMinute(),
					chargingStruct.getEndHour(), "setEndMinute", "setEndHour"
			);
		}
	}

	/** 时区转换：0时区转用户时区 */
	public static void convertGmtToUserTimezone(List<ChargingStructDTO> list, String timeZone) {
		if (CollUtil.isEmpty(list)) {
			return;
		}
		Pair<Integer, Integer> offsetHourAndMinute = TimeUtil
				.offsetToNumber(Optional.ofNullable(timeZone).orElse("GMT+00:00"));

		for (ChargingStructDTO chargingStruct : list) {
			setZeroToTimezone(chargingStruct, offsetHourAndMinute, chargingStruct.getStartMinute(),
					chargingStruct.getStartHour(), "setStartMinute", "setStartHour"
			);
			setZeroToTimezone(chargingStruct, offsetHourAndMinute, chargingStruct.getEndMinute(),
					chargingStruct.getEndHour(), "setEndMinute", "setEndHour"
			);
		}
	}

	public static void convertUserTimezoneToGMT(CustomizeInfoVo customizeInfoVo, String timeZone) {
		Pair<Integer, Integer> offsetHourAndMinute = TimeUtil
				.offsetToNumber(Optional.ofNullable(timeZone).orElse("GMT+00:00"));

		OperationUtil.of(customizeInfoVo.getCharge1StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getCharge1StartTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setCharge1StartTimeMinute", "setCharge1StartTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setCharge1StartTimeMinute", "setCharge1StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoVo.getCharge1EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getCharge1EndTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setCharge1EndTimeMinute", "setCharge1EndTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setCharge1EndTimeMinute", "setCharge1EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoVo.getCharge2StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getCharge2StartTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setCharge2StartTimeMinute", "setCharge2StartTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setCharge2StartTimeMinute", "setCharge2StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoVo.getCharge2EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getCharge2EndTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setCharge2EndTimeMinute", "setCharge2EndTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setCharge2EndTimeMinute", "setCharge2EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoVo.getDischarge1StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getDischarge1StartTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setDischarge1StartTimeMinute", "setDischarge1StartTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setDischarge1StartTimeMinute", "setDischarge1StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoVo.getDischarge1EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getDischarge1EndTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setDischarge1EndTimeMinute", "setDischarge1EndTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setDischarge1EndTimeMinute", "setDischarge1EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoVo.getDischarge2StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getDischarge2StartTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setDischarge2StartTimeMinute", "setDischarge2StartTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setDischarge2StartTimeMinute", "setDischarge2StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoVo.getDischarge2EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoVo.getDischarge2EndTimeMinute()).ifPresentOrElse(
						minute -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, minute, hour,
								"setDischarge2EndTimeMinute", "setDischarge2EndTimeHour"
						),
						() -> setTimezoneToZero(customizeInfoVo, offsetHourAndMinute, 0, hour,
								"setDischarge2EndTimeMinute", "setDischarge2EndTimeHour"
						)
				));
	}

	public static void convertGMTToUserTimezone(CustomizeInfoDto customizeInfoDto, String timeZone) {
		Pair<Integer, Integer> offsetHourAndMinute = TimeUtil
				.offsetToNumber(Optional.ofNullable(timeZone).orElse("GMT+00:00"));

		OperationUtil.of(customizeInfoDto.getCharge1StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getCharge1StartTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setCharge1StartTimeMinute", "setCharge1StartTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setCharge1StartTimeMinute", "setCharge1StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoDto.getCharge1EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getCharge1EndTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setCharge1EndTimeMinute", "setCharge1EndTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setCharge1EndTimeMinute", "setCharge1EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoDto.getCharge2StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getCharge2StartTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setCharge2StartTimeMinute", "setCharge2StartTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setCharge2StartTimeMinute", "setCharge2StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoDto.getCharge2EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getCharge2EndTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setCharge2EndTimeMinute", "setCharge2EndTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setCharge2EndTimeMinute", "setCharge2EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoDto.getDischarge1StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getDischarge1StartTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setDischarge1StartTimeMinute", "setDischarge1StartTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setDischarge1StartTimeMinute", "setDischarge1StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoDto.getDischarge1EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getDischarge1EndTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setDischarge1EndTimeMinute", "setDischarge1EndTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setDischarge1EndTimeMinute", "setDischarge1EndTimeHour"
						)
				));

		OperationUtil.of(customizeInfoDto.getDischarge2StartTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getDischarge2StartTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setDischarge2StartTimeMinute", "setDischarge2StartTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setDischarge2StartTimeMinute", "setDischarge2StartTimeHour"
						)
				));
		OperationUtil.of(customizeInfoDto.getDischarge2EndTimeHour())
				.then(hour -> OperationUtil.of(customizeInfoDto.getDischarge2EndTimeMinute()).ifPresentOrElse(
						minute -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, minute, hour,
								"setDischarge2EndTimeMinute", "setDischarge2EndTimeHour"
						),
						() -> setZeroToTimezone(customizeInfoDto, offsetHourAndMinute, 0, hour,
								"setDischarge2EndTimeMinute", "setDischarge2EndTimeHour"
						)
				));
	}

	private static Integer timeLimitProcessHour(Integer hour) {
		if (hour >= 24) {
			return hour - 24;
		}
		if (hour < 0) {
			return 24 + hour;
		}
		return hour;
	}

	private static Pair<Integer, Integer> timeLimitProcessMinute(Integer minute) {
		if (minute >= 60) {
			return Pair.of(minute - 60, 1);
		}
		if (minute < 0) {
			return Pair.of(minute + 60, -1);
		}
		return Pair.of(minute, 0);
	}

	private static <T> void setTimezoneToZero(
			T t, Pair<Integer, Integer> offsetHourAndMinute,
			Integer minute, Integer hour, String setMinuteMethod, String setHourMethod
	) {
		Pair<Integer, Integer> minuteForHourPair = timeLimitProcessMinute(minute - offsetHourAndMinute.getValue());
		ReflectUtil.invoke(t, setMinuteMethod, minuteForHourPair.getKey());
		ReflectUtil.invoke(t, setHourMethod,
				timeLimitProcessHour(hour - offsetHourAndMinute.getKey() + minuteForHourPair.getValue())
		);
	}

	private static <T> void setZeroToTimezone(
			T t, Pair<Integer, Integer> offsetHourAndMinute,
			Integer minute, Integer hour, String setMinuteMethod, String setHourMethod
	) {
		Pair<Integer, Integer> minuteForHourPair = timeLimitProcessMinute(minute + offsetHourAndMinute.getValue());
		ReflectUtil.invoke(t, setMinuteMethod, minuteForHourPair.getKey());
		ReflectUtil.invoke(t, setHourMethod,
				timeLimitProcessHour(hour + offsetHourAndMinute.getKey() + minuteForHourPair.getValue())
		);
	}


	private static void setTimezoneToZero(
			CustomizeInfoVo customizeInfoVo, Pair<Integer, Integer> offsetHourAndMinute,
			Integer minute, Integer hour, String setMinuteMethod, String setHourMethod
	) {
		Pair<Integer, Integer> minuteForHourPair = timeLimitProcessMinute(minute - offsetHourAndMinute.getValue());
		ReflectUtil.invoke(customizeInfoVo, setMinuteMethod, minuteForHourPair.getKey());
		ReflectUtil.invoke(customizeInfoVo, setHourMethod,
				timeLimitProcessHour(hour - offsetHourAndMinute.getKey() + minuteForHourPair.getValue())
		);
	}

	private static void setZeroToTimezone(
			CustomizeInfoDto customizeInfoDto, Pair<Integer, Integer> offsetHourAndMinute,
			Integer minute, Integer hour, String setMinuteMethod, String setHourMethod
	) {
		Pair<Integer, Integer> minuteForHourPair = timeLimitProcessMinute(minute + offsetHourAndMinute.getValue());
		ReflectUtil.invoke(customizeInfoDto, setMinuteMethod, minuteForHourPair.getKey());
		ReflectUtil.invoke(customizeInfoDto, setHourMethod,
				timeLimitProcessHour(hour + offsetHourAndMinute.getKey() + minuteForHourPair.getValue())
		);
	}
}
