package com.weihengtech.ecos.utils;

import cn.hutool.core.date.DateUtil;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description
 * @create 2023-09-21 10:07
 **/
public class EleUtil {

    public static String createSign(String name,String salt) {

        // 获取现在的时间戳（秒）
        String timestamp = String.valueOf(DateUtil.currentSeconds());

        // 生成待加密字符串
        String authString = name + timestamp + salt;

        // 使用SHA-256对待加密字符串进行加密
        String auth = sha256(authString);

        // 截取加密串10~20位
        String authSubstring = auth.substring(10, 20);

        // 拼接name、timestamp和截取的加密串，并进行Base64编码
        String expectedSign = base64Encode(name + ":" + timestamp + ":" + authSubstring);

        //返回结果
        return expectedSign;
    }

    private static String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes(StandardCharsets.UTF_8));
    }
}
