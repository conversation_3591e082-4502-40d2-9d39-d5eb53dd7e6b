package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.service.app.ClientSessionMessageService;
import com.weihengtech.ecos.service.app.ClientSessionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-05-10 18:56
 **/
@Component
public class SSEListenerUtil {

    private final String apiHost;
    private final ClientSessionService clientSessionService;
    private final ClientSessionMessageService clientSessionMessageService;
    private final SnowFlakeUtil snowFlakeUtil;

    @Autowired
    public SSEListenerUtil(
            @Value("${custom.url.knowledge.api}") String apiHost,
            ClientSessionService clientSessionService,
            ClientSessionMessageService clientSessionMessageService,
            SnowFlakeUtil snowFlakeUtil) {
        this.apiHost = apiHost;
        this.clientSessionService = clientSessionService;
        this.clientSessionMessageService = clientSessionMessageService;
        this.snowFlakeUtil = snowFlakeUtil;
    }

    public String getApiHost() {
        return apiHost;
    }

    public ClientSessionService getClientSessionService() {
        return clientSessionService;
    }

    public ClientSessionMessageService getClientSessionMessageService() {
        return clientSessionMessageService;
    }

    public SnowFlakeUtil getSnowFlakeUtil() {
        return snowFlakeUtil;
    }

    // 其他可能需要的方法...
}
