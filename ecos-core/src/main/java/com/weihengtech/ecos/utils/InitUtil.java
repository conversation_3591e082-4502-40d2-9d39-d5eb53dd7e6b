package com.weihengtech.ecos.utils;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.UrlResource;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.setting.Setting;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.annotation.SetOrder;
import com.weihengtech.ecos.enums.global.AccountClearRecordStateEnum;
import com.weihengtech.ecos.containers.ProcessHandlerContainer;
import com.weihengtech.ecos.tasks.RunnableTask;
import com.weihengtech.ecos.handlers.ICustomProcessHandler;
import com.weihengtech.ecos.interfaces.IAroundProcessHandler;
import com.weihengtech.ecos.interfaces.IGlobalProcessHandler;
import com.weihengtech.ecos.model.dos.ClientAccountClearRecordDo;
import com.weihengtech.ecos.service.global.ClientAccountClearRecordService;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class InitUtil {

	private static final AtomicReference<ApplicationContext> APPLICATION_CONTEXT_ATOMIC_REFERENCE = new AtomicReference<>();
	public static Setting COUNTRY_SETTING;
	public static Setting TIMEZONE_SETTING;
	public static ScheduledThreadPoolExecutor SCHEDULED_EXECUTOR = new ScheduledThreadPoolExecutor(5);

	public static ApplicationContext getApplicationContext() {
		return InitUtil.APPLICATION_CONTEXT_ATOMIC_REFERENCE.get();
	}

	public static void init(ConfigurableApplicationContext applicationContext) {
		InitUtil.APPLICATION_CONTEXT_ATOMIC_REFERENCE.set(applicationContext);
		InitUtil initUtil = new InitUtil();
		initUtil.register(applicationContext, initUtil.buildProcessHandler(applicationContext));
		initCountrySetting();
		initTimezoneSetting();
		String enableTask = applicationContext.getEnvironment().getProperty("custom.xxl.job.enable", "false");
		if ("true".equals(enableTask)) {
			initClearAccountTask();
		}
	}

	private static void initClearAccountTask() {
		ClientAccountClearRecordService clientAccountClearRecordService = getBean(
				ClientAccountClearRecordService.class);
		List<ClientAccountClearRecordDo> clearRecordDoList = clientAccountClearRecordService
				.list(Wrappers.<ClientAccountClearRecordDo>lambdaQuery().eq(
						ClientAccountClearRecordDo::getState,
						AccountClearRecordStateEnum.ING.getCode()
				));
		long currentTimeMillis = System.currentTimeMillis();
		for (ClientAccountClearRecordDo clientAccountClearRecordDo : clearRecordDoList) {
			long deltaMillis = clientAccountClearRecordDo.getClearTime() - currentTimeMillis;
			SCHEDULED_EXECUTOR.schedule(new RunnableTask.ClearAccountTask(clientAccountClearRecordDo.getId()),
					Math.max(deltaMillis, 0L), TimeUnit.MILLISECONDS
			);
		}
	}

	private static void initCountrySetting() {
		ClassPathResource classPathResource = new ClassPathResource("country.setting");
		cn.hutool.core.io.resource.Resource resource = new UrlResource(classPathResource.getUrl());
		COUNTRY_SETTING = new Setting(resource, CharsetUtil.CHARSET_UTF_8, true);
	}

	private static void initTimezoneSetting() {
		ClassPathResource classPathResource = new ClassPathResource("timezone.setting");
		cn.hutool.core.io.resource.Resource resource = new UrlResource(classPathResource.getUrl());
		TIMEZONE_SETTING = new Setting(resource, CharsetUtil.CHARSET_UTF_8, true);
	}

	public static String getEnvironmentProperty(String environmentKey) {
		return APPLICATION_CONTEXT_ATOMIC_REFERENCE.get().getEnvironment().getProperty(environmentKey);
	}

	private <T> void register(ConfigurableApplicationContext applicationContext, T t) {
		DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext
				.getAutowireCapableBeanFactory();
		defaultListableBeanFactory.registerSingleton(t.getClass().getSimpleName(), t);
	}

	private ProcessHandlerContainer buildProcessHandler(ConfigurableApplicationContext applicationContext) {
		Map<String, IGlobalProcessHandler> globalProcessHandlerMap = applicationContext
				.getBeansOfType(IGlobalProcessHandler.class);
		ProcessHandlerContainer processHandlerContainer = ReflectUtil.newInstance(ProcessHandlerContainer.class);
		processHandlerContainer.setGlobalProcessHandlerList(globalProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class)
								.value()))
				.collect(Collectors.toList()));
		Map<String, IAroundProcessHandler> aroundProcessHandlerMap = applicationContext
				.getBeansOfType(IAroundProcessHandler.class);
		processHandlerContainer.setAroundProcessHandlerList(aroundProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class)
								.value()))
				.collect(Collectors.toList()));
		Map<String, ICustomProcessHandler> authorityProcessHandlerMap = applicationContext
				.getBeansOfType(ICustomProcessHandler.class);
		processHandlerContainer.setCustomProcessHandlerList(authorityProcessHandlerMap.values().stream()
				.sorted(Comparator.comparingInt(
						h -> h.getClass().getAnnotation(SetOrder.class)
								.value()))
				.collect(Collectors.toList()));
		return processHandlerContainer;
	}

	public static <T> T getBean(Class<T> t) {
		return getApplicationContext().getBean(t);
	}

	public static <T> T getBean(String beanName, Class<T> t) {
		return getApplicationContext().getBean(beanName, t);
	}
}
