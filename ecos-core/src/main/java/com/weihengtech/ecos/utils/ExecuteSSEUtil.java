package com.weihengtech.ecos.utils;

import cn.hutool.json.JSONObject;
import com.weihengtech.ecos.api.config.SSEListener;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-05-09 20:02
 **/
@Slf4j
@Transactional(rollbackFor = {Exception.class, RuntimeException.class, Error.class})
public class ExecuteSSEUtil {

    public static void executeSSE(String url, String authToken, SSEListener eventSourceListener, String chatGlm) throws EcosException {
        RequestBody formBody = RequestBody.create(chatGlm, MediaType.parse("application/json; charset=utf-8"));
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.addHeader("AuthHeader", authToken);
        Request request = requestBuilder.url(url).post(formBody).build();
        EventSource.Factory factory = EventSources.createFactory(OkHttpUtil.getInstance());
        // 创建事件
        try {
            factory.newEventSource(request, eventSourceListener);
            eventSourceListener.getCountDownLatch().await();
        } catch (Exception e) {
            log.error("Error executing SSE", e);
            throw new EcosException(EcosExceptionEnum.ANSWER_GENERATE_FAIL);
        }
    }

    // 非流式普通POST请求
    public static String knowledgePostReq(String url, String authToken, String chatGlm) throws EcosException {
        OkHttpClient client = OkHttpUtil.getInstance();
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, chatGlm);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("AuthHeader", authToken)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("POST request failed with status code: {}", response.code());
                throw new EcosException(EcosExceptionEnum.ANSWER_GENERATE_FAIL);
            }

            // 返回响应体内容
            if (response.body() != null) {
                JSONObject jsonObject = new JSONObject(response.body().string());
                return jsonObject.getJSONObject("data").getStr("Content");
            } else {
                log.error("AI response body is null");
                throw new EcosException(EcosExceptionEnum.ANSWER_GENERATE_FAIL);
            }
        } catch (IOException e) {
            log.error("Error executing POST request", e);
            throw new EcosException(EcosExceptionEnum.ANSWER_GENERATE_FAIL, e.getMessage());
        }
    }
}
