package com.weihengtech.ecos.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketRandomTimeDto;
import com.weihengtech.ecos.model.vos.socket.SocketAddTimingVo;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import lombok.val;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
public class TimeUtil {

	public static String localDateTimeToSerialString(LocalDateTime localDateTime) {
		return LocalDateTimeUtil.format(localDateTime, "yyyy-MM-dd HH:mm:ss");
	}

	public static String localDateTimeToSerialString(LocalDateTime localDateTime, String format) {
		return LocalDateTimeUtil.format(localDateTime, format);
	}

	public static String longTimestampToSerialString(Long timestamp, String offset) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset)));
	}

	public static String longTimestampToSerialStringOffsetGMT8(Long timestamp, String offset) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset) - 8 * 3600 * 1000L));
	}

	public static String longTimestampToSerialStringOffsetGMT8(Long timestamp, String offset, String format) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset) - 8 * 3600 * 1000L), format);
	}

	public static String longTimestampToSerialString(Long timestamp, String offset, String format) {
		return localDateTimeToSerialString(LocalDateTimeUtil.of(timestamp + offsetToMillis(offset)), format);
	}

	public static Long getLastDayStart(String offset) {
		return getDayStart(-1, offset);
	}

	public static Long getLastDayEnd(String offset) {
		return getDayEnd(-1, offset);
	}

	public static Long getDayStart(Integer dayOffset, String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.add(Calendar.DATE, dayOffset);
		return calendar.getTime().getTime();
	}

	public static Long getDayEnd(Integer dayOffset, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.add(Calendar.DATE, dayOffset);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentTime(String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		return calendar.getTime().getTime();
	}

	public static Integer getDayOfWeekend(long timestamp, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(timestamp);
		int weekDay = calendar.get(Calendar.DAY_OF_WEEK) - 1;
		return weekDay == 0 ? 7 : weekDay;
	}

	public static Integer getHourOfDay(long timestamp, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(timestamp);
		return calendar.get(Calendar.HOUR_OF_DAY);
	}

	public static Integer getDayCountWithOffsetMonth(String offset, Integer offsetMonth) {
		Calendar calendar = getCalendarWithOffset("GMT" + offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.add(Calendar.MONTH, offsetMonth);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		long offsetMonthTimestamp = calendar.getTime().getTime();
		return getDaysInStartAndEnd(offsetMonthTimestamp, System.currentTimeMillis());
	}

	public static Pair<Long, Long> computeStartAndEndByOffsetDay(Integer offsetDay, String offset) {
		return Pair.of(getDayStart(offsetDay, offset), getDayEnd(0, offset));
	}

	public static Integer getDaysInStartAndEnd(long startTime, long endTime) {
		return Integer.parseInt(String.valueOf((endTime - startTime) / (1000 * 3600 * 24)));
	}

	public static Long getCurrentWeekStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentMonthStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentSeasonStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		int month = calendar.get(Calendar.MONTH);
		if (Calendar.APRIL > month) {
			calendar.set(Calendar.MONTH, Calendar.JANUARY);
		} else if (Calendar.JULY > month) {
			calendar.set(Calendar.MONTH, Calendar.APRIL);
		} else if (Calendar.OCTOBER > month) {
			calendar.set(Calendar.MONTH, Calendar.JULY);
		} else {
			calendar.set(Calendar.MONTH, Calendar.OCTOBER);
		}
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	public static Long getCurrentYearStart(String offset) {
		Calendar calendar = getCalendarAtTodayStartWithOffset(offset);
		calendar.set(Calendar.MONTH, Calendar.JANUARY);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime().getTime();
	}

	private static Calendar getCalendarAtTodayStartWithOffset(String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar;
	}

	public static Long getAssignDayStart(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime().getTime();
	}

	public static Long getAssignDayEnd(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.set(Calendar.HOUR_OF_DAY, 23); // Set hour to end of day
		calendar.set(Calendar.MINUTE, 59); // Set minute to end of day
		calendar.set(Calendar.SECOND, 59); // Set second to end of day
		calendar.set(Calendar.MILLISECOND, 999); // Set millisecond to end of day
		return calendar.getTime().getTime();
	}


	public static Long getAssignMonthStart(Long startTime, String offset, Integer offsetMonth) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.add(Calendar.MONTH, offsetMonth);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime().getTime();
	}

	public static Long getAssignMonthEnd(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.add(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime().getTime();
	}

	public static Long getYearStart(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.set(Calendar.MONTH, Calendar.JANUARY); // Set month to January
		calendar.set(Calendar.DAY_OF_MONTH, 1); // Set day to 1st
		calendar.set(Calendar.HOUR_OF_DAY, 0); // Set hour to start of day
		calendar.set(Calendar.MINUTE, 0); // Set minute to start of day
		calendar.set(Calendar.SECOND, 0); // Set second to start of day
		calendar.set(Calendar.MILLISECOND, 0); // Set millisecond to start of day
		return calendar.getTime().getTime();
	}

	public static Long getYearEnd(Long startTime, String offset) {
		Calendar calendar = getCalendarWithOffset(offset);
		calendar.setTimeInMillis(startTime == null ? System.currentTimeMillis() : startTime);
		calendar.set(Calendar.MONTH, Calendar.DECEMBER); // Set month to December
		calendar.set(Calendar.DAY_OF_MONTH, 31); // Set day to 31st
		calendar.set(Calendar.HOUR_OF_DAY, 23); // Set hour to end of day
		calendar.set(Calendar.MINUTE, 59); // Set minute to end of day
		calendar.set(Calendar.SECOND, 59); // Set second to end of day
		calendar.set(Calendar.MILLISECOND, 999); // Set millisecond to end of day
		return calendar.getTime().getTime();
	}

	public static TimeAndWeekList isoToTimeWithGMT8(String time, List<Integer> weekList, String offset) {
		LocalTime localTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));
		long offsetMillis = offsetToMillis(offset);

		// 计算输入时区的时间
		ZonedDateTime inputZoneTime = localTime.atDate(java.time.LocalDate.now())
				.atZone(ZoneOffset.ofTotalSeconds((int)offsetMillis / 1000));

		// 转换为东八区时间
		ZonedDateTime gmt8Time = inputZoneTime.withZoneSameInstant(ZoneOffset.ofHours(8));

		// 定义一个新的weekList来存储转换后的结果
		List<Integer> gmt8WeekList = new ArrayList<Integer>();


		int utcDay = inputZoneTime.getDayOfWeek().getValue();
		int gmt8Day = gmt8Time.getDayOfWeek().getValue();
		int diffDay;
		if (gmt8Day == 1 && utcDay == 7) {
			diffDay = 1;
		} else if (gmt8Day == 7 && utcDay == 1) {
			diffDay = -1;
		} else {
			diffDay = gmt8Day - utcDay;
		}

		// 根据原来的星期数和东八区的星期数来调整“周几的列表”
		for (Integer week : weekList) {
			if (week == 0) {
				gmt8WeekList = Arrays.asList(0);
				break;
			}
			int adjustedWeek = week + diffDay;
			// deal with overflow, if adjustedWeek > 7, then set it to 1, and vice versa.
			adjustedWeek = adjustedWeek > 7 ? 1 : (adjustedWeek < 1 ? 7 : adjustedWeek);
			gmt8WeekList.add(adjustedWeek);
		}

		// 转换为字符串格式的时间
		String gmt8TimeString = gmt8Time.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"));

		// 这里你可以选择合适的方式返回你需要的结果，比如只返回时间、只返回新的周几列表、或者两者都返回
		return new TimeAndWeekList(gmt8TimeString, gmt8WeekList);
	}

	public static ZonedDateTime isoToLocalTimeWithGMT8(String time, String offset) {
		LocalTime localTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));
		long offsetMillis = offsetToMillis(offset);

		// 计算 UTC 时间
		ZonedDateTime utcTime = localTime.atDate(java.time.LocalDate.now())
				.atZone(ZoneOffset.ofTotalSeconds((int)offsetMillis / 1000))
				.withZoneSameInstant(ZoneOffset.UTC);

		// 转换为东八区时间
		ZonedDateTime gmt8Time = utcTime.withZoneSameInstant(ZoneOffset.ofHours(8));

		LocalTime now = LocalTime.now(ZoneOffset.ofHours(8));
		if (now.isAfter(localTime)) { // 当前时间超过指定时间，应返回明天的日期
			gmt8Time = gmt8Time.plusDays(1);
		}
		// 返回东八区的日期和时间
		return gmt8Time;
	}

	private static TimeZone offsetToTimeZone(String offset) {
		offset = getTimezoneCode(offset);
		offset = offset.replace("GMT", "");
		return TimeZone.getTimeZone("GMT" + offset);
	}

	private static Calendar getCalendarWithOffset(String offset) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(offsetToTimeZone(offset));
		return calendar;
	}

	private static long offsetToMillis(String offset) {
		offset = getTimezoneCode(offset);
		offset = offset.replace("GMT", "");
		if (offset.length() > 3) {
			if (offset.startsWith("+")) {
				offset = offset.replace("+", "");
				String[] split = offset.split(":");
				return Long.parseLong(split[0]) * 60 * 60 * 1000 + Long.parseLong(split[1]) * 60 * 1000;
			} else {
				offset = offset.replace("-", "");
				String[] split = offset.split(":");
				return -(Long.parseLong(split[0]) * 60 * 60 * 1000 + Long.parseLong(split[1]) * 60 * 1000);
			}
		} else {
			if (offset.startsWith("+")) {
				offset = offset.replace("+", "");
				return Long.parseLong(offset) * 60 * 60 * 1000;
			} else {
				offset = offset.replace("-", "");
				return -(Long.parseLong(offset) * 60 * 60 * 1000);
			}
		}
	}

	public static Pair<Integer, Integer> offsetToNumber(String timezone) {
		String timezoneCode = getTimezoneCode(timezone);
		String time = timezoneCode.replaceAll("GMT", "");
		if (time.length() <= 3) {
			time = time + ":00";
		}
		if (time.startsWith("+")) {
			time = time.substring(1);
			String[] split = time.split(":");
			return Pair.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
		}
		time = time.substring(1);
		String[] split = time.split(":");
		return Pair.of(-Integer.parseInt(split[0]), -Integer.parseInt(split[1]));
	}

	public static String getTimezoneCode(String timezone) {
		try {
			val instant = Instant.now();
			ZoneId zoneId = ZoneId.of(timezone);
			val timezoneStr = zoneId.getRules().getOffset(instant).toString();
			return "Z".equals(timezoneStr) ? "+00:00" : timezoneStr;
		} catch (Exception ignored) {
		}
		return timezone;
	}

	public static String getLightingTimezone(String timezone) {
		String timezoneCode = getTimezoneCode(timezone);
		timezoneCode = timezoneCode.replaceAll("GMT", "");
		return "GMT" + timezoneCode;
	}

	public static Integer convertMinuteBetweenTimestamp(Long small, Long large) {
		return new BigDecimal((large - small) / 60 / 1000).intValue();
	}

	// 定时任务加密成字符串
	public static String encodeRandomTime(Map<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> schedules) {
		ByteArrayOutputStream byteStream = new ByteArrayOutputStream();

		for (Map.Entry<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> entry : schedules.entrySet()) {
			SocketRandomTimeMapVo.SocketRandomTimeVo dto = entry.getValue();

			// 编码通道号（status）
			byte channel = (byte) (dto.getStatus() & 0x7F);
			byteStream.write(channel);

			// 编码星期
			List<Integer> weeks = dto.getWeek();
			int week = 0;
			if (!weeks.isEmpty() && weeks.get(0) != 0) {
				for (Integer w : weeks) {
					week += (int) Math.pow(2, w);
				}
			}
			byteStream.write((byte) week);

			// 编码开始时间和结束时间
			encodeTime(dto.getStartTime(), byteStream);
			encodeTime(dto.getEndTime(), byteStream);
		}

		// 将字节数组转换为Base64编码的字符串
		return Base64.getEncoder().encodeToString(byteStream.toByteArray());
	}

	private static void encodeTime(String time, ByteArrayOutputStream byteStream) {
		String[] parts = time.split(":");
		int hours = Integer.parseInt(parts[0]);
		int minutes = Integer.parseInt(parts[1]);

		int totalMinutes = hours * 60 + minutes;
		byte hourByte = (byte) (totalMinutes / 256);
		byte minuteByte = (byte) (totalMinutes % 256);

		byteStream.write(hourByte);
		byteStream.write(minuteByte);
	}

	// 定时任务解密成任务列表
	public static Map<Integer, TuyaSocketRandomTimeDto> parseRandomTime(String value) {

		byte[] data = Base64.getDecoder().decode(value);

		Map<Integer, TuyaSocketRandomTimeDto> schedules = new LinkedHashMap<>();
		for (int i = 0; i < data.length; i += 6) {
			if (i + 6 > data.length) {
				break; // 防止数组越界
			}
			int channel = data[i] & 0x7F; // bit7-bit1 代表通道号
			int week = data[i + 1];
			String startTime = decodeTime(data[i + 2], (data[i + 3]));
			String endTime = decodeTime(data[i + 4], data[i + 5]);

			TuyaSocketRandomTimeDto tuyaSocketRandomTimeDto = new TuyaSocketRandomTimeDto();
			tuyaSocketRandomTimeDto.setStatus(channel);
			List<Integer> weeks = new ArrayList<>();
			if (week != 0) {
				weeks = calculatePowerSumArray(week);
			} else {
				weeks.add(0);
			}
			tuyaSocketRandomTimeDto.setWeek(weeks);
			tuyaSocketRandomTimeDto.setStartTime(startTime);
			tuyaSocketRandomTimeDto.setEndTime(endTime);
			schedules.put(i / 6, tuyaSocketRandomTimeDto);
		}
		return schedules;
	}


	private static String decodeTime(byte hour, byte minuteEncoding) {
		int totalMinutes;
		if (minuteEncoding >= 0) {
			// 如果第二个值为正
			totalMinutes = 256 * hour + minuteEncoding;
		} else {
			// 如果第二个值为负
			totalMinutes = 256 * (hour + 1) + minuteEncoding;
		}

		int hours = totalMinutes / 60;
		int minutes = totalMinutes % 60;
		return String.format("%02d:%02d", hours, minutes);
	}


	// 判断定时时间是否有冲突
	public static boolean hasTimeConflict(Map<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> schedules, Integer type) {
		// 过滤启用状态的定时任务
		Map<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> enabledSchedules = new LinkedHashMap<>();
		for (Map.Entry<Integer, SocketRandomTimeMapVo.SocketRandomTimeVo> entry : schedules.entrySet()) {
			if (entry.getValue().getStatus() == 1) {
				enabledSchedules.put(entry.getKey(), entry.getValue());
			}

			int startTime = convertTimeToMinutes(entry.getValue().getStartTime());
			int endTime = convertTimeToMinutes(entry.getValue().getEndTime());

			if (type == CommonConstants.DEVICE_TUYA_DC) {
				if (endTime > startTime && endTime - startTime < 30) {
					throw new EcosException(EcosExceptionEnum.INTERVAL_LESS_THAN_30_MINUTES);
				}
			}

			if (type == CommonConstants.DEVICE_EN_CDZ) {
				if (endTime > startTime && endTime - startTime < 5) {
					throw new EcosException(EcosExceptionEnum.INTERVAL_LESS_THAN_5_MINUTES);
				}
			}
		}

		// 将week为0的任务和非0的任务分别放入不同的列表
		List<SocketRandomTimeMapVo.SocketRandomTimeVo> zeroWeekTasks = new ArrayList<>();
		Map<Integer, List<SocketRandomTimeMapVo.SocketRandomTimeVo>> nonZeroWeekTasksGrouped = new HashMap<>();
		for (SocketRandomTimeMapVo.SocketRandomTimeVo schedule : enabledSchedules.values()) {
			List<Integer> weekList = schedule.getWeek();
			if (weekList.contains(0)) {
				zeroWeekTasks.add(schedule);
			} else {
				for (Integer w : weekList) {
					nonZeroWeekTasksGrouped.computeIfAbsent(w, k -> new ArrayList<>()).add(schedule);
				}
			}
		}

		// 检查week为0的任务之间是否有冲突
		if (checkConflictInGroup(zeroWeekTasks)) {
			return true;
		}

		// 检查week为0的任务与所有非0week任务之间是否有冲突
		for (List<SocketRandomTimeMapVo.SocketRandomTimeVo> nonZeroWeekTasks : nonZeroWeekTasksGrouped.values()) {
			List<SocketRandomTimeMapVo.SocketRandomTimeVo> combinedTasks = new ArrayList<>(zeroWeekTasks);
			combinedTasks.addAll(nonZeroWeekTasks);
			if (checkConflictInGroup(combinedTasks)) {
				return true;
			}
		}

		// 检查相同week的非0任务之间是否有冲突
		for (List<SocketRandomTimeMapVo.SocketRandomTimeVo> nonZeroWeekTasks : nonZeroWeekTasksGrouped.values()) {
			if (checkConflictInGroup(nonZeroWeekTasks)) {
				return true;
			}
		}

		return false; // 未发现冲突
	}

	// 判断定时时间是否有冲突
	public static boolean hasTimeConflictForTiming(List<SocketAddTimingVo> schedules, Integer type) {

		// 将week为0的任务和非0的任务分别放入不同的列表
		List<SocketAddTimingVo> zeroWeekTasks = new ArrayList<>();
		Map<Integer, List<SocketAddTimingVo>> nonZeroWeekTasksGrouped = new HashMap<>();
		for (SocketAddTimingVo schedule : schedules) {
			List<Integer> weekList = schedule.getWeek();
			if (weekList.contains(0)) {
				zeroWeekTasks.add(schedule);
			} else {
				for (Integer w : weekList) {
					nonZeroWeekTasksGrouped.computeIfAbsent(w, k -> new ArrayList<>()).add(schedule);
				}
			}
		}

		// 检查week为0的任务之间是否有冲突
		if (checkConflictInGroup2(zeroWeekTasks)) {
			return true;
		}

		// 检查week为0的任务与所有非0week任务之间是否有冲突
		for (List<SocketAddTimingVo> nonZeroWeekTasks : nonZeroWeekTasksGrouped.values()) {
			List<SocketAddTimingVo> combinedTasks = new ArrayList<>(zeroWeekTasks);
			combinedTasks.addAll(nonZeroWeekTasks);
			if (checkConflictInGroup2(combinedTasks)) {
				return true;
			}
		}

		// 检查相同week的非0任务之间是否有冲突
		for (List<SocketAddTimingVo> nonZeroWeekTasks : nonZeroWeekTasksGrouped.values()) {
			if (checkConflictInGroup2(nonZeroWeekTasks)) {
				return true;
			}
		}



		return false; // 未发现冲突
	}

	private static boolean checkConflictInGroup(List<SocketRandomTimeMapVo.SocketRandomTimeVo> group) {
		for (int i = 0; i < group.size(); i++) {
			for (int j = i + 1; j < group.size(); j++) {
				if (isTimeOverlapping(group.get(i), group.get(j))) {
					return true; // 发现时间重叠
				}
			}
		}
		return false;
	}

	private static boolean checkConflictInGroup2(List<SocketAddTimingVo> group) {
		for (int i = 0; i < group.size(); i++) {
			for (int j = i + 1; j < group.size(); j++) {
				if (isTimeOverlapping2(group.get(i), group.get(j))) {
					return true; // 发现时间重叠
				}
			}
		}
		return false;
	}

	private static boolean isTimeOverlapping(SocketRandomTimeMapVo.SocketRandomTimeVo schedule1, SocketRandomTimeMapVo.SocketRandomTimeVo schedule2) {
		int startTime1 = convertTimeToMinutes(schedule1.getStartTime());
		int endTime1 = convertTimeToMinutes(schedule1.getEndTime());
		int startTime2 = convertTimeToMinutes(schedule2.getStartTime());
		int endTime2 = convertTimeToMinutes(schedule2.getEndTime());

		return startTime1 < endTime2 && startTime2 < endTime1;
	}

	private static boolean isTimeOverlapping2(SocketAddTimingVo schedule1, SocketAddTimingVo schedule2) {
		int startTime1 = convertTimeToMinutes(schedule1.getSocketTime());
		int startTime2 = convertTimeToMinutes(schedule2.getSocketTime());

		return startTime1 == startTime2;
	}

	private static int convertTimeToMinutes(String time) {
		String[] parts = time.split(":");
		int hours = Integer.parseInt(parts[0]);
		int minutes = Integer.parseInt(parts[1]);
		return hours * 60 + minutes;
	}

	public static List<Integer> calculatePowerSumArray(int num) {
		List<Integer> powerArray = new ArrayList<>();
		while (num > 0) {
			int base = (int) (Math.log(num) / Math.log(2));
			powerArray.add(base);
			num -= Math.pow(2, base);
		}
		if (num == -2) {
			powerArray = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
		}
		Collections.sort(powerArray);
		return powerArray;
	}

	/**
	 * 给定时间和时区，计算过去指定数量的工作日的时间区间
	 *
	 * @param timestamp 给定时间戳
	 * @param timezone 时区
	 * @param dayCount 工作日数量
	 * @return 工作日的时间区间
	 */
	public static List<Pair<Long, Long>> calWorkDays(Long timestamp, String timezone, int dayCount) {
		ZoneId zoneId = ZoneId.of(timezone);
		Instant instant = Instant.ofEpochMilli(timestamp);
		ZonedDateTime givenDateTime = ZonedDateTime.ofInstant(instant, zoneId);

		List<Pair<Long, Long>> workdays = new ArrayList<>();
		int count = 0;
		ZonedDateTime current = givenDateTime.minusDays(1);

		while (count < dayCount) {
			DayOfWeek dow = current.getDayOfWeek();

			if (dow != DayOfWeek.SATURDAY && dow != DayOfWeek.SUNDAY) {
				LocalDate date = current.toLocalDate();

				// 计算当天的开始时间
				ZonedDateTime startOfDay = date.atStartOfDay(zoneId);
				long startTime = startOfDay.toInstant().toEpochMilli();

				// 计算当天的结束时间
				ZonedDateTime endOfDay = date.atTime(LocalTime.MAX).atZone(zoneId);
				long endTime = endOfDay.toInstant().toEpochMilli();

				workdays.add(Pair.of(startTime, endTime));
				count++;
			}

			current = current.minusDays(1);
		}

		return workdays;
	}

	/**
	 * 给定时间和时区，计算过去指定数量的周末的时间区间
	 *
	 * @param timestamp 给定时间戳
	 * @param timezone 时区
	 * @param dayCount 周末数量
	 * @return 周末的时间区间
	 */
	public static List<Pair<Long, Long>> calWeekendDays(Long timestamp, String timezone, int dayCount) {
		ZoneId zoneId = ZoneId.of(timezone);
		Instant instant = Instant.ofEpochMilli(timestamp);
		ZonedDateTime givenDateTime = ZonedDateTime.ofInstant(instant, zoneId);

		List<Pair<Long, Long>> weekendDays = new ArrayList<>();
		int count = 0;
		ZonedDateTime current = givenDateTime.minusDays(1);

		while (count < dayCount) {
			DayOfWeek dow = current.getDayOfWeek();

			if (dow == DayOfWeek.SATURDAY || dow == DayOfWeek.SUNDAY) {
				LocalDate date = current.toLocalDate();

				// 计算当天的开始时间
				ZonedDateTime startOfDay = date.atStartOfDay(zoneId);
				long startTime = startOfDay.toInstant().toEpochMilli();

				// 计算当天的结束时间
				ZonedDateTime endOfDay = date.atTime(LocalTime.MAX).atZone(zoneId);
				long endTime = endOfDay.toInstant().toEpochMilli();

				weekendDays.add(Pair.of(startTime, endTime));
				count++;
			}
			current = current.minusDays(1);
		}
		return weekendDays;
	}

	public static int calCurHour(Long timestamp, String timezone) {
		Instant instant = Instant.ofEpochSecond(timestamp);
		ZoneId zone = ZoneId.of(timezone);
		ZonedDateTime zonedDateTime = instant.atZone(zone);
		return zonedDateTime.getHour();
	}

	public static class TimeAndWeekList {
		String time;
		List<Integer> weekList;

		public TimeAndWeekList(String time, List<Integer> weekList) {
			this.time = time;
			this.weekList = weekList;
		}

		public List<Integer> getWeekList() {
			return weekList;
		}

		public String getTime() {
			return time;
		}

	}

	/**
	 * 计算当前时间的起始小时时间戳
	 *
	 * @param timestamp
	 * @param timezone
	 * @return
	 */
	public static Long calHourBegin(Long timestamp, String timezone) {
		return Instant.ofEpochMilli(timestamp)
				.atZone(ZoneId.of(timezone))
				.withMinute(0)
				.withSecond(0)
				.withNano(0)
				.toInstant().toEpochMilli();
	}

	/**
	 * 计算当前时间的指定过去小时数的起始小时时间戳
	 *
	 * @param timestamp
	 * @param hours
     * @param timezone
	 * @return
	 */
	public static Long calHourBegin(Long timestamp, int hours, String timezone) {
		return Instant.ofEpochMilli(timestamp)
				.atZone(ZoneId.of(timezone))
				.plusHours(hours)
				.withMinute(0)
				.withSecond(0)
				.withNano(0)
				.toInstant().toEpochMilli();
	}


}
