package com.weihengtech.ecos.utils;

import cn.hutool.core.util.NumberUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RandomUtil {

	private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	private static final String LOWER = UPPER.toLowerCase();
	private static final String ALPHABET = UPPER + LOWER;

	public static String generateFixedLengthNumberStr(int len) {
		return batchGenerateFixedLengthNumberStr(len, 1).get(0);
	}

	public static List<String> batchGenerateFixedLengthNumberStr(int len, int size) {
		int[] ints = batchGenerateFixedLengthNumberArray(len, size);
		return Arrays.stream(ints).mapToObj(i -> String.format("%0" + len + "d", i)).collect(Collectors.toList());
	}

	public static int[] batchGenerateFixedLengthNumberArray(int len, int size) {
		return NumberUtil.generateRandomNumber(0, (int) Math.pow(10D, len), size);
	}

	public static String generateFixedLengthStr(int len) {
		return cn.hutool.core.util.RandomUtil.randomString(len);
	}

	public static String generateNickname() {
		Random rand = new Random();
		StringBuilder sb = new StringBuilder("Nickname_");

		for (int i = 0; i < 4; i++) {
			int index = rand.nextInt(ALPHABET.length());
			sb.append(ALPHABET.charAt(index));
		}

		return sb.toString();
	}

}
