package com.weihengtech.ecos.utils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ModbusParseUtil {

	public static String parseAscii(List<Integer> list) {
		StringBuilder stringBuilder = new StringBuilder();
		for (Integer l : list) {
			int high = l / 256;
			if (0 == high) {
				break;
			}
			stringBuilder.append((char) high);
			int low = l % 256;
			if (0 == low) {
				break;
			}
			stringBuilder.append((char) low);
		}
		return stringBuilder.toString();
	}
}
