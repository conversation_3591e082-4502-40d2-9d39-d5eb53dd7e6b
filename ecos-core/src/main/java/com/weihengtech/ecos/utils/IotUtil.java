package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.sdk.iot.ecos.model.constant.CloudCategoryEnum;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description iot sdk相关工具类
 * @create 2023-10-16 15:44
 **/
public class IotUtil {

    public static CloudCategoryEnum getCategoryEnum(String category) {
        Optional<CloudCategoryEnum> cloudCategoryEnum = Arrays.stream(CloudCategoryEnum.values()).filter(c -> c.getValue().equals(category)).findFirst();
        if (!cloudCategoryEnum.isPresent()) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        return cloudCategoryEnum.get();
    }

}
