package com.weihengtech.ecos.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.weihengtech.ecos.model.vos.thirdpart.XxlJobInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-01-03 09:29
 **/
@Slf4j
@Component
public class XxlJobUtil {

    @Value("${xxl.job.admin.addresses2}")
    private String adminAddresses;

    @Value("${xxl.job.executor.appname}")
    private String appname;

    private RestTemplate restTemplate = new RestTemplate();

    private static final String PAGE_LIST_URL = "/jobinfo/pageListJob";
    private static final String ADD_URL = "/jobinfo/addJob";
    private static final String UPDATE_URL = "/jobinfo/updateJob";
    private static final String REMOVE_URL = "/jobinfo/removeJob";
    private static final String PAUSE_URL = "/jobinfo/pauseJob";
    private static final String START_URL = "/jobinfo/startJob";
    private static final String ADD_START_URL = "/jobinfo/addAndStart";
    private static final String GET_GROUP_ID = "/jobgroup/getGroupId";

    public JSONObject pageList(int start, int length, int jobGroup, int triggerStatus, String jobDesc, String executorHandler, String author){
        // 构建URL
        String url = adminAddresses + PAGE_LIST_URL
                + "?start=" + start
                + "&length=" + length
                + "&jobGroup=" + jobGroup
                + "&triggerStatus=" + triggerStatus
                + "&jobDesc=" + jobDesc
                + "&executorHandler=" + executorHandler
                + "&author=" + author;

        return doGet(url);
    }

    public JSONObject add(XxlJobInfo jobInfo){
        // 查询对应groupId:
        Map<String,Object> param = new HashMap<>();
        param.put("appname", appname);
        String json = JSON.toJSONString(param);
        JSONObject jsonObject = doPost(adminAddresses + GET_GROUP_ID, json);

        String groupId = jsonObject.getString("content");
        jobInfo.setJobGroup(Integer.parseInt(groupId));
        String json2 = JSON.toJSONString(jobInfo);
        return doPost(adminAddresses + ADD_URL, json2);
    }

    public JSONObject update(XxlJobInfo jobInfo){
        String json = JSON.toJSONString(jobInfo);
        return doPost(adminAddresses + UPDATE_URL, json);
    }

    public JSONObject remove(int id){
        Map<String,Object> param = new HashMap<>();
        param.put("id", id);
        String json = JSON.toJSONString(param);
        return doPost(adminAddresses + REMOVE_URL, json);
    }

    public JSONObject pause(int id){
        Map<String,Object> param = new HashMap<>();
        param.put("id", id);
        String json = JSON.toJSONString(param);
        return doPost(adminAddresses + PAUSE_URL, json);
    }

    public JSONObject start(int id){
        Map<String,Object> param = new HashMap<>();
        param.put("id", id);
        String json = JSON.toJSONString(param);
        return doPost(adminAddresses + START_URL, json);
    }

    public JSONObject addAndStart(XxlJobInfo jobInfo){
        Map<String,Object> param = new HashMap<>();
        param.put("appname", appname);
        String json = JSON.toJSONString(param);
        JSONObject jsonObject = doPost(adminAddresses + GET_GROUP_ID, json);

        String groupId = jsonObject.getString("content");
        jobInfo.setJobGroup(Integer.parseInt(groupId));
        String json2 = JSON.toJSONString(jobInfo);

        return doPost(adminAddresses + ADD_START_URL, json2);
    }

    public JSONObject doPost(String url, String json){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(json ,headers);
        log.info(entity.toString());
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, entity, String.class);
        return JSON.parseObject(stringResponseEntity.getBody().toString());
    }

    public JSONObject doGet(String url){
        ResponseEntity<String> stringResponseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(stringResponseEntity.getBody().toString());
    }

    public String convertWeekdayForXXLJob(List<Integer> weekday) {
        if (weekday.size() == 0) {
            return "*";
        }

        return weekday.stream()
                .map(day -> (day % 7) + 1) // 星期往后延一天，保持在1到7的范围内
                .map(Object::toString)
                .collect(Collectors.joining(","));
    }

}