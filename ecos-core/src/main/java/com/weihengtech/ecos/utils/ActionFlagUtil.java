package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import lombok.val;

/**
 * <AUTHOR>
 */
public class ActionFlagUtil {

	public static void assertTrue(boolean bool) {
		if (!bool) {
			throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
		}
	}

	public static void assertSingleAction(int flag) {
		val need = 1;
		if (need != flag) {
			throw new EcosException(EcosExceptionEnum.ASSERT_SINGLE_ACTION);
		}
	}
}
