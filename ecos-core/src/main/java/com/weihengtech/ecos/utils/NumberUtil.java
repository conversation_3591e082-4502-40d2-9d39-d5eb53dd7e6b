package com.weihengtech.ecos.utils;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.HashSet;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
@Slf4j
public class NumberUtil {

	public static Integer validIntegerValue(
			Integer value, Integer min, Integer max, Integer defaultValue,
			Boolean useDefault
	) {
		if (null == value) {
			if (useDefault) {
				return defaultValue;
			} else {
				log.warn("参数错误");
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM_RANGE);
			}
		} else {
			if (value >= min && value <= max) {
				return value;
			} else if (useDefault) {
				return defaultValue;
			} else {
				log.warn("参数错误");
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM_RANGE);
			}
		}
	}

	public static void validChargeTimeCross(CustomizeInfoVo customizeInfoVo) {
		LinkedList<Pair<Integer, Integer>> pairLinkedList = ListUtil.toLinkedList();
		Integer charge1StartTimeHour = customizeInfoVo.getCharge1StartTimeHour();
		Integer charge1StartTimeMinute = customizeInfoVo.getCharge1StartTimeMinute();
		Integer charge1EndTimeHour = customizeInfoVo.getCharge1EndTimeHour();
		Integer charge1EndTimeMinute = customizeInfoVo.getCharge1EndTimeMinute();

		if (charge1StartTimeHour != null && charge1StartTimeMinute != null && charge1EndTimeHour != null
				&& charge1EndTimeMinute != null) {
			pairLinkedList.add(Pair.of(
					customizeInfoVo.getCharge1StartTimeHour() * 60 + customizeInfoVo.getCharge1StartTimeMinute(),
					customizeInfoVo.getCharge1EndTimeHour() * 60 + customizeInfoVo.getCharge1EndTimeMinute()
			));
		}

		Integer charge2StartTimeHour = customizeInfoVo.getCharge2StartTimeHour();
		Integer charge2StartTimeMinute = customizeInfoVo.getCharge2StartTimeMinute();
		Integer charge2EndTimeHour = customizeInfoVo.getCharge2EndTimeHour();
		Integer charge2EndTimeMinute = customizeInfoVo.getCharge2EndTimeMinute();

		if (charge2StartTimeHour != null && charge2StartTimeMinute != null && charge2EndTimeHour != null
				&& charge2EndTimeMinute != null) {
			pairLinkedList.add(Pair.of(
					customizeInfoVo.getCharge2StartTimeHour() * 60 + customizeInfoVo.getCharge2StartTimeMinute(),
					customizeInfoVo.getCharge2EndTimeHour() * 60 + customizeInfoVo.getCharge2EndTimeMinute()
			));
		}

		Integer discharge1StartTimeHour = customizeInfoVo.getDischarge1StartTimeHour();
		Integer discharge1StartTimeMinute = customizeInfoVo.getDischarge1StartTimeMinute();
		Integer discharge1EndTimeHour = customizeInfoVo.getDischarge1EndTimeHour();
		Integer discharge1EndTimeMinute = customizeInfoVo.getDischarge1EndTimeMinute();

		if (discharge1StartTimeHour != null && discharge1StartTimeMinute != null && discharge1EndTimeHour != null
				&& discharge1EndTimeMinute != null) {
			pairLinkedList.add(Pair.of(
					customizeInfoVo.getDischarge1StartTimeHour() * 60 + customizeInfoVo.getDischarge1StartTimeMinute(),
					customizeInfoVo.getDischarge1EndTimeHour() * 60 + customizeInfoVo.getDischarge1EndTimeMinute()
			));
		}

		Integer discharge2StartTimeHour = customizeInfoVo.getDischarge2StartTimeHour();
		Integer discharge2StartTimeMinute = customizeInfoVo.getDischarge2StartTimeMinute();
		Integer discharge2EndTimeHour = customizeInfoVo.getDischarge2EndTimeHour();
		Integer discharge2EndTimeMinute = customizeInfoVo.getDischarge2EndTimeMinute();

		if (discharge2StartTimeHour != null && discharge2StartTimeMinute != null && discharge2EndTimeHour != null
				&& discharge2EndTimeMinute != null) {
			pairLinkedList.add(Pair.of(
					customizeInfoVo.getDischarge2StartTimeHour() * 60 + customizeInfoVo.getDischarge2StartTimeMinute(),
					customizeInfoVo.getDischarge2EndTimeHour() * 60 + customizeInfoVo.getDischarge2EndTimeMinute()
			));
		}

		HashSet<Integer> hashSet = new HashSet<>();
		int count = 0;

		for (Pair<Integer, Integer> pair : pairLinkedList) {
			Integer start = pair.getKey();
			Integer end = pair.getValue();

			if (end < start) {
				val max = 24 * 60;
				count += max - start;
				count += end;
				for (int i = start; i < max; i++) {
					hashSet.add(i);
				}
				for (int i = 0; i < end; i++) {
					hashSet.add(i);
				}
			} else {
				count += end - start;
				for (int i = start; i < end; i++) {
					hashSet.add(i);
				}
			}
		}

		if (count > hashSet.size()) {
			log.warn("时间不能交错");
			throw new EcosException(EcosExceptionEnum.TIME_CANNOT_CROSS);
		}
	}
}
