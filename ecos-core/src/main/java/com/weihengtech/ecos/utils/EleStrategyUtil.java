package com.weihengtech.ecos.utils;

import cn.hutool.core.lang.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EleStrategyUtil {

	/**
	 * 每个点按照10个工作日中同一时间的点计算平均值
	 *
	 * @param allDayPowers 所有日期
	 * @return 每个点在不同日期的平均值
	 */
	public static Map<Integer, Integer> calAverage(List<Map<Integer, Integer>> allDayPowers) {
		Map<Integer, Integer> resMap = new HashMap<>();
		for (int i = 0; i < 24; i++) {
			int sum = 0;
			int count = 0;
            for (Map<Integer, Integer> allDayPower : allDayPowers) {
                if (!allDayPower.containsKey(i)) {
                    continue;
                }
                int curPointPower = allDayPower.get(i);
                sum += curPointPower;
                count++;
            }
			resMap.put(i, sum == 0 ? 0 : sum / count);
		}
		return resMap;
	}
}
