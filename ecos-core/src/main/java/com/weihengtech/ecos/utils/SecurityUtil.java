package com.weihengtech.ecos.utils;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.consts.KnowledgeCommonConst;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.service.user.ClientUserService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import org.springframework.security.core.context.SecurityContextHolder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class SecurityUtil {

	public static String getUsername() {
		return String.valueOf(SecurityContextHolder.getContext().getAuthentication().getPrincipal());
	}

	public static ClientUserDo getClientUserDo() {
		ClientUserService clientUserService = InitUtil.getApplicationContext().getBean(ClientUserService.class);
		return clientUserService.queryOptionalUserByUsername(getUsername())
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.INVALID_DATA));
	}

	public static MiddleClientUserDeviceDo getMiddleClientUserDeviceDoByDeviceId(String deviceId, String userId) {
		MiddleClientUserDeviceService middleClientUserDeviceService = InitUtil.getApplicationContext()
				.getBean(MiddleClientUserDeviceService.class);
		return middleClientUserDeviceService.getOne(Wrappers.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getUserId, userId).eq(MiddleClientUserDeviceDo::getDeviceId, deviceId));
	}

	public static String generateAuthHeader(String appName) {
		String name = appName.replaceAll(":", "-");
		long timestamp = new Date().getTime() / 1000; // Get current timestamp in seconds
		String sign = name + timestamp;
		String md5 = getMd5(sign);
		String auth = name + ":" + timestamp + ":" + md5.substring(10, 20);
		return Base64.getUrlEncoder().encodeToString(auth.getBytes());
	}

	public static String getMd5(String input) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] messageDigest = md.digest(input.getBytes());
			StringBuilder sb = new StringBuilder();
			for (byte b : messageDigest) {
				sb.append(String.format("%02x", b));
			}
			return sb.toString();
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) {
		String appName = KnowledgeCommonConst.AUTH_HEADER;
		String authHeader = generateAuthHeader(appName);
		System.out.println("AuthHeader: " + authHeader);
	}
}
