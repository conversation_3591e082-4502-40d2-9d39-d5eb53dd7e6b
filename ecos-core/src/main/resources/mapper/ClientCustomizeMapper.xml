<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.ecos.dao.ClientCustomizeMapper">

    <select id="getAutoStrategyDevicesByIds" resultType="com.weihengtech.ecos.model.dos.ClientCustomizeDo">
        SELECT *
        from client_customize
        where device_id in
        <foreach collection="deviceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (auto_strategy = 1 or strategy_mode > 0)
    </select>
</mapper>
