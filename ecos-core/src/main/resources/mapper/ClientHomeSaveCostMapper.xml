<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.ecos.dao.ClientHomeSaveCostMapper">

    <select id="calDayCostData" resultType="com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO$Detail">
        select t.day, sum(t.grid_cost) gridCost, SUM(t.load_cost) loadCost, SUM(t.feed_earnings) feedEarnings
        from client_home_save_cost t
        where t.home_id = #{homeId}
        and t.day BETWEEN FROM_UNIXTIME(#{startTime}) and FROM_UNIXTIME(#{endTime})
        GROUP BY t.`day`
        order by t.day
    </select>
    <select id="sumCostData" resultType="com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO$Detail">
        select sum(t.grid_cost) gridCost, SUM(t.load_cost) loadCost, SUM(t.feed_earnings) feedEarnings
        from client_home_save_cost t
        where t.home_id = #{homeId}
          and t.day BETWEEN FROM_UNIXTIME(#{startTime}) and FROM_UNIXTIME(#{endTime})
    </select>
</mapper>
