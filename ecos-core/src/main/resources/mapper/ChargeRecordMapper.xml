<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weihengtech.ecos.dao.ClientChargeRecordMapper">

    <select id="listChargeRecordByCondition" resultType="com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeRecordDto">
        SELECT
        `id`, start_time, end_time, charge_capacity, charge_status
        FROM
        `client_charge_record`
        <where>
            <if test="deviceFlag != null and deviceFlag != ''">
                AND device_id = #{deviceFlag}
            </if>
            <if test="chargeStatus != null">
                AND charge_status = #{chargeStatus}
            </if>
            <if test="startTime != null">
                AND start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND start_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY start_time DESC
    </select>

</mapper>