<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weihengtech.ecos.dao.ecos.EcosMapper">

    <select id="listEventByCondition" resultType="com.weihengtech.ecos.model.bos.ecos.EcosEventBo">
        SELECT
        `subsystem`, `code`, `level`, english, chinese, upload_time
        FROM
        `device_event`
        <where>
            <if test="level == null or level == '' or level == 'alarm'">
                AND `level` != 'alarm'
            </if>
            <if test="deviceFlag != null and deviceFlag != ''">
                AND device_name = #{deviceFlag}
                AND english != 'Recover From Error'
            </if>
            <if test="level != null and level != '' and level != 'alarm'">
                AND `level` = #{level}
            </if>
            <if test="start != null">
                AND upload_time &gt;= #{start}
            </if>
            <if test="end != null">
                AND upload_time &lt;= #{end}
            </if>
        </where>
        ORDER BY upload_time DESC
    </select>


    <select id="countBackupMode" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM `device_event`
        WHERE `device_name` = #{deviceFlag}
          AND `code` = 2
          AND `level` = 'event'
    </select>

    <select id="listEpsEventByCondition" resultType="com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo">
        SELECT
        `id`, `device_name`, `code`, `level`, `upload_time`
        FROM
        `device_event`
        <where>
            `device_name` = #{deviceFlag}
            AND
            `code` = 2
            AND
            `level` = 'event'
            <if test="start != null and start != 0">
                AND upload_time &gt;= #{start}
            </if>
            <if test="end != null and end != 0">
                AND upload_time &lt;= #{end}
            </if>
        </where>
    </select>

    <select id="findNearlyNotEpsEvent" resultType="com.weihengtech.ecos.model.bos.ecos.EcosEpsEventBo">
        SELECT `id`,
               `device_name`,
               `code`,
               `level`,
               `upload_time`
        FROM `device_event`
        WHERE `device_name` = #{deviceFlag}
          AND `code` != 2
          AND `level` = 'event'
          AND `id` > #{id}
        ORDER BY `id`
        LIMIT 1
    </select>

</mapper>