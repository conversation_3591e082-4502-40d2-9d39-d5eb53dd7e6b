package com.weihengtech.ecos.controller.app;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.adapter.GlobalAdapter;
import com.weihengtech.ecos.api.pojo.dtos.GlobalDeviceConfigDto;
import com.weihengtech.ecos.api.pojo.vos.EcosDeviceConfigQueryVo;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.config.CustomConfig;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.global.GlobalConfigDto;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalReduceCarbonEmissionsDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.dtos.global.ResourceCategoryTreeDto;
import com.weihengtech.ecos.model.vos.global.TuyaLoginLogVo;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ClientJwtUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("global")
@Api(tags = {"10_GlobalApi"})
@RequiredArgsConstructor
public class GlobalController {

	@Resource
	private CustomConfig customConfig;

	@Resource
	private ClientJwtUtil clientJwtUtil;

	@Resource
	private GlobalAdapter globalAdapter;

	@Resource
	private HubService hubService;

	@GetMapping("token/refresh")
	@ApiOperation(value = "刷新access_token")
	public DataResponse<String> refreshToken(
			@RequestParam("refreshToken") String refreshToken,
			@RequestHeader("Authorization") String authorization
	) {
		if (StrUtil.isBlank(refreshToken) || StrUtil.isBlank(authorization)) {
			throw new EcosException(EcosExceptionEnum.INVALID_TOKEN);
		}
		String accessToken = authorization.replace(customConfig.getToken().getTypePrefix(), "").trim();
		return DataResponse.success(clientJwtUtil.refreshAccessTokenByRefreshToken(refreshToken, accessToken));
	}

	@GetMapping("statistics/reduceCarbonEmissions")
	@ApiOperation(value = "减少碳排放量和节约标准煤")
	public DataResponse<GlobalReduceCarbonEmissionsDto> reduceCarbonEmissionsStatistics(
			@RequestParam(value = "deviceId", required = false) String deviceId
	) {
		if (StrUtil.isBlank(deviceId)) {
			return DataResponse.success(globalAdapter.reduceCarbonEmissionsStatistics());
		}
		return DataResponse.success(globalAdapter.deviceReduceCarbonEmissionsStatistics(deviceId));
	}

	@GetMapping("config")
	@ApiOperation(value = "全局配置接口")
	public DataResponse<GlobalConfigDto> getGlobalConfig() {
		return DataResponse.success(globalAdapter.getGlobalConfig());
	}

	@GetMapping("version")
	@ApiOperation(value = "全局ecos版本接口")
	public DataResponse<GlobalVersionDto> getGlobalVersion() {
		return DataResponse.success(globalAdapter.getGlobalVersion());
	}

	@GetMapping("enest/version")
	@ApiOperation(value = "全局enest版本接口")
	public DataResponse<GlobalEnestLatestVersionDto> getEnestLatestVersion() {
		return DataResponse.success(globalAdapter.getEnestLatestVersion());
	}

	@PostMapping("device/config")
	@ApiOperation(value = "获取设备自定义配置信息")
	public DataResponse<GlobalDeviceConfigDto> getDeviceConfig(@RequestBody @Valid EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo) {
		DataResponse<GlobalDeviceConfigDto> globalDeviceConfigDtoDataResponse = hubService.queryDeviceConfig(ecosDeviceConfigQueryVo);
		if (globalDeviceConfigDtoDataResponse.getCode() == 200) {
			return DataResponse.success(globalDeviceConfigDtoDataResponse.getData());
		}
		return DataResponse.fail(globalDeviceConfigDtoDataResponse.getData());
	}

	@GetMapping("/resource/category/tree")
	@ApiOperation(value = "全局获取资源列表")
	public DataResponse<List<ResourceCategoryTreeDto>> getResourceCategoryTree() {
		return DataResponse.success(hubService.resourceCategoryTree());
	}

	@PostMapping("/login/log")
	@ApiOperation(value = "涂鸦登录日志")
	public EmptyResponse saveTuyaLoginLog(@RequestBody TuyaLoginLogVo tuyaLoginLogVo) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		globalAdapter.saveTuyaLoginLog(clientUserDo,tuyaLoginLogVo);
		return EmptyResponse.success();
	}
}
