package com.weihengtech.ecos.controller.app;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.adapter.CustomizeAdapter;
import com.weihengtech.ecos.api.pojo.dtos.EleDataSourceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleTimeZoneDto;
import com.weihengtech.ecos.api.pojo.vos.AheadPriceVo;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.ele.EleRegionDTO;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.model.dtos.app.RippleControlDTO;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzVo;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.TransformUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("customize")
@Api(tags = {"04_Customize"})
public class CustomizeController {

	@Resource
	private CustomizeAdapter customizeAdapter;

	@GetMapping("info")
	@ApiOperation(value = "自定义页面信息")
	public DataResponse<CustomizeInfoEzDto> queryCustomizeInfo(@RequestParam("deviceId") String deviceId) {
		CustomizeInfoDto customizeInfoDto = customizeAdapter.readCustomize(deviceId);
		return DataResponse.success(TransformUtil.customizeInfoToEz(customizeInfoDto));
	}

	@PostMapping("info")
	@ApiOperation(value = "配置自定义页面信息")
	public EmptyResponse configureCustomizeInfo(@RequestBody @Valid CustomizeInfoEzVo customizeInfoEzVo) {
		customizeAdapter.writeCustomize(TransformUtil.ezToCustomizeInfoVo(customizeInfoEzVo));
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("infoV2")
	@ApiOperation(value = "新的配置自定义页面信息")
	public EmptyResponse configureCustomizeInfoV2(@RequestBody @Valid CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {
		customizeAdapter.writeCustomizeV2(customizeInfoEzV2Vo);
		customizeAdapter.backupLastTimeList(customizeInfoEzV2Vo);
		return EmptyResponse.success();
	}

	@GetMapping("info_last")
	@ApiOperation(value = "查询自定义充放上次记录")
	public DataResponse<TimeListLastDTO> lastTimeList(String deviceId) {
		TimeListLastDTO resItem = customizeAdapter.queryLastTimeList(deviceId);
		return DataResponse.success(resItem);
	}

	@GetMapping("timezone")
	@ApiOperation(value = "获取电价时区")
	public DataResponse<List<EleTimeZoneDto>> getEleTimeZone() {
		return DataResponse.success(customizeAdapter.getEleTimeZone());
	}

	@GetMapping("country")
	@ApiOperation(value = "获取电价国家地区")
	public DataResponse<List<EleRegionDTO>> getEleCountryRegion() {
		return DataResponse.success(customizeAdapter.getEleCountryRegion());
	}

	@GetMapping("dataSource")
	@ApiOperation(value = "获取电价数据来源")
	public DataResponse<List<EleDataSourceDto>> getEleDataSource() {
		return DataResponse.success(customizeAdapter.getEleDataSource());
	}

	@PostMapping ("dayAheadPrice")
	@ApiOperation(value = "获取预测电价")
	public DataResponse<List<EleDayAheadPriceDto>> getEleAheadPrice(@RequestBody @Valid AheadPriceVo aheadPriceVo) {
		return DataResponse.success(customizeAdapter.getEleAheadPrice(aheadPriceVo));
	}

	@PostMapping ("/strategy/preview")
	@ApiOperation(value = "自动策略预览")
	public DataResponse<List<EleStrategyDTO>> strategyPreview(@RequestBody EleStrategyPreviewVO param) {
		if (StrUtil.isBlank(param.getTimezone())) {
			param.setTimezone(SecurityUtil.getClientUserDo().getTimeZone());
		}
		return DataResponse.success(customizeAdapter.strategyPreview(param));
	}

	@PostMapping ("/strategy/history")
	@ApiOperation(value = "自动策略历史计算")
	public DataResponse<Map<String, List<ChargingStructDTO>>> strategyHistory(@RequestBody EleStrategyPreviewVO param) {
		CustomizeInfoEzDto item = customizeAdapter.queryStrategy(param);
		Map<String, List<ChargingStructDTO>> resMap = new HashMap<>();
		resMap.put("chargingList", item.getChargingList());
		resMap.put("dischargingList", item.getDischargingList());
		return DataResponse.success(resMap);
	}

	@GetMapping("/ripple/query")
	@ApiOperation(value = "脉冲控制查询")
	public DataResponse<RippleControlDTO> rippleControlQuery(@RequestParam Long deviceId) {
		return DataResponse.success(customizeAdapter.rippleControlQuery(deviceId));
	}

	@PostMapping("/ripple/config")
	@ApiOperation(value = "脉冲控制配置")
	public EmptyResponse rippleControlConfig(@RequestBody @Valid RippleControlDTO param) {
		customizeAdapter.rippleControlConfig(param);
		return EmptyResponse.success();
	}

	@GetMapping("/dynamic/export")
	@ApiOperation(value = "获取动态输出信息")
	public DataResponse<DynamicExportDTO> dynamicExport(@RequestParam String deviceName) {
		return DataResponse.success(customizeAdapter.dynamicExport(deviceName));
	}

	@GetMapping("/dynamic/design")
	@ApiOperation(value = "获取设备设计相关信息")
	public DataResponse<DynamicDesignDTO> designInfo(@RequestParam String deviceName) {
		DynamicDesignDTO res = customizeAdapter.designInfo(deviceName);
		return DataResponse.success(res);
	}

	@PostMapping("/dynamic/save")
	@ApiOperation(value = "保存动态输出信息")
	public EmptyResponse dynamicSave(@RequestBody @Valid DynamicSaveVO param) {
		customizeAdapter.dynamicSave(param);
		return EmptyResponse.success();
	}

	@GetMapping("/dynamic/test")
	@ApiOperation(value = "动态输出生效校验")
	public DataResponse<DynamicTestDTO> dynamicTest(@RequestParam String deviceName) {
		return DataResponse.success(customizeAdapter.dynamicTest(deviceName));
	}

	@PostMapping("/dynamic/switch")
	@ApiOperation(value = "动态输出使能开关")
	public EmptyResponse dynamicSwitch(@RequestBody @Valid DynamicSwitchVO param) {
		customizeAdapter.dynamicSwitch(param);
		return EmptyResponse.success();
	}
}
