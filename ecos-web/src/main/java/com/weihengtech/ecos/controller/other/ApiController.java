package com.weihengtech.ecos.controller.other;

import com.weihengtech.ecos.adapter.ApiAdapter;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.thirdpart.AccountBindInfoDTO;
import com.weihengtech.ecos.model.vos.thirdpart.ApiBindAccountVo;
import com.weihengtech.ecos.model.vos.thirdpart.ApiUnBindAccountVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("api")
//@ApiIgnore
@Api(tags = {"20_Api"})
@Slf4j
public class ApiController {

	@Resource
	private ApiAdapter apiAdapter;

	@GetMapping("/device/getMasterAccountDo")
	@ApiOperation(value = "根据设备id获取主账号信息")
	public InResponse<ClientUserDo> getDeviceMasterAccountDo(@RequestParam("deviceId") Long deviceId) {
		return InResponse.success(apiAdapter.getDeviceMasterAccountDo(deviceId));
	}

	@GetMapping("/device/getMasterAndSubAccount")
	@ApiOperation(value = "根据设备id获取主账号和子账号列表")
	public InResponse<Map<String, List<String>>> getDeviceMasterAndSubAccount(@RequestParam("deviceId") Long deviceId) {
		return InResponse.success(apiAdapter.getDeviceMasterAndSubAccount(deviceId));
	}


	@PostMapping("/device/bindAccount")
	@ApiOperation(value = "设备绑定指定Ecos账号")
	public InResponse<Integer> bindAccount(@RequestBody @Valid ApiBindAccountVo apiBindAccountVo) {
		try {
			// 0 绑定失败 1 绑定成功 2 已有主账号 3 账号不存在
			Integer bindState = apiAdapter.bindMasterAccount(
					apiBindAccountVo.getDevice(),
					apiBindAccountVo.getUsername()
			);
			return InResponse.success(bindState);
		} catch (Exception e) {
			log.warn(e.getMessage());
			return InResponse.fail(0);
		}
	}

	@PostMapping("/device/unbindAccount")
	@ApiOperation(value = "解除设备绑定的所有Ecos账号")
	public EmptyResponse unbindAccount(@RequestBody @Valid ApiUnBindAccountVo unBindAccountVo) {
		apiAdapter.unbindAccount(unBindAccountVo.getDeviceIdList());
		return EmptyResponse.success200();
	}

	@PostMapping("/device/updateCustomize")
	@ApiOperation(value = "更新自定义配置页信息")
	public EmptyResponse updateCustomizeInfo(@RequestBody ClientCustomizeDo param) {
		apiAdapter.updateCustomize(param);
		return EmptyResponse.success();
	}

	@GetMapping("/device/by_account")
	@ApiOperation(value = "根据账号获取绑定的各类设备")
	public InResponse<AccountBindInfoDTO> getDevicesByAccount(@RequestParam("account") String account) {
		return InResponse.success(apiAdapter.getDevicesByAccount(account));
	}

}
