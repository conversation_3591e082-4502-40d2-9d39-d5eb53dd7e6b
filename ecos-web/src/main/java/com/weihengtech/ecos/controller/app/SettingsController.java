package com.weihengtech.ecos.controller.app;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.adapter.SettingsAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dtos.settings.SettingsBindDeviceListDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsDeviceDetailDto;
import com.weihengtech.ecos.model.dtos.settings.SettingsUserInfoDto;
import com.weihengtech.ecos.model.vos.settings.SettingBindEmailVo;
import com.weihengtech.ecos.model.vos.settings.SettingBindPhoneVo;
import com.weihengtech.ecos.model.vos.settings.SettingEmailVo;
import com.weihengtech.ecos.model.vos.settings.SettingHelpEmailVo;
import com.weihengtech.ecos.model.vos.settings.SettingPhoneVo;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.settings.SettingTransferMainVo;
import com.weihengtech.ecos.model.vos.settings.SettingTransferVo;
import com.weihengtech.ecos.model.vos.settings.SettingsDeviceDetailUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsResetDeviceVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserPasswordUpdateVo;
import com.weihengtech.ecos.model.vos.settings.SettingsUserUpdateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("settings")
@Api(tags = {"05_Setting"})
public class SettingsController {

	@Resource
	private SettingsAdapter settingsAdapter;

	@OperationLog
	@PostMapping("user/update")
	@ApiOperation(value = "修改个人信息")
	public EmptyResponse updateUserInfo(@RequestBody @Valid SettingsUserUpdateVo settingsUserUpdateVo) {
		settingsAdapter.updateUserInfo(settingsUserUpdateVo);
		return EmptyResponse.success();
	}

	@GetMapping("user/info")
	@ApiOperation(value = "查询我的信息")
	public DataResponse<SettingsUserInfoDto> queryUserInfo() {
		return DataResponse.success(settingsAdapter.queryUserInfo());
	}

	@OperationLog
	@PostMapping("user/password")
	@ApiOperation(value = "修改密码")
	public EmptyResponse updateUserPassword(
			@RequestBody @Valid SettingsUserPasswordUpdateVo settingsUserPasswordUpdateVo
	) {
		settingsUserPasswordUpdateVo.checkParams();
		settingsAdapter.updateUserPassword(settingsUserPasswordUpdateVo);
		return EmptyResponse.success();
	}

	@GetMapping("device/list")
	@ApiOperation(value = "设备列表")
	public DataResponse<List<SettingsBindDeviceListDto>> queryBindDeviceList() {
		return DataResponse.success(settingsAdapter.queryBindDeviceList());
	}

	@GetMapping("device/detail")
	@ApiOperation(value = "设备详情")
	public DataResponse<SettingsDeviceDetailDto> queryDeviceDetail(@RequestParam("deviceId") String deviceId) {
		return DataResponse.success(settingsAdapter.queryDeviceDetail(deviceId));
	}

	@OperationLog
	@PostMapping("device/detail")
	@ApiOperation(value = "修改设备详情")
	public EmptyResponse updateDeviceDetail(
			@RequestBody @Valid SettingsDeviceDetailUpdateVo settingsDeviceDetailUpdateVo
	) {
		settingsAdapter.updateDeviceDetail(settingsDeviceDetailUpdateVo);
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("device/reset")
	@ApiOperation(value = "设备恢复出厂设置")
	public EmptyResponse deviceReset(
			@RequestBody @Valid SettingsResetDeviceVo settingsResetDeviceVo
	) {
		settingsAdapter.resetDevice(settingsResetDeviceVo.getDeviceId());
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("device/removeAccount")
	@ApiOperation(value = "移除设备绑定账号")
	public EmptyResponse removeDeviceBindAccount(
			@RequestBody @Valid SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo
	) {
		settingsAdapter.removeDeviceBindAccount(settingRemoveDeviceBindAccountVo);
		return EmptyResponse.success();
	}

	@GetMapping("device/qrCode")
	@ApiOperation(value = "生成二维码需要密文")
	public DataResponse<String> qrCodeEncryption(@RequestParam("deviceId") String deviceId) {
		return DataResponse.success(settingsAdapter.qrCodeEncryption(deviceId));
	}

	@PostMapping("device/qrCodeTransferMain")
	@ApiOperation(value = "转移设备主账号，生成二维码需要密文")
	public DataResponse<String> qrCodeEncryptionTransferMain(@RequestBody @Valid SettingTransferMainVo dto) {
		return DataResponse.success(settingsAdapter.qrCodeEncryptionTransferMain(dto));
	}

	@GetMapping("device/slaveBind")
	@ApiOperation(value = "二维码绑定从账号")
	public EmptyResponse bindSlaveAccount(@RequestParam("code") String code) {
		if (StrUtil.isBlank(code)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		settingsAdapter.bindSlaveAccount(code);
		return EmptyResponse.success();
	}

	@OperationLog
	@GetMapping("user/delAccountEmail")
	@ApiOperation(value = "发送注销邮件")
	public EmptyResponse delAccountEmail() {
		settingsAdapter.delAccountEmail();
		return EmptyResponse.success();
	}

	@OperationLog
	@GetMapping("user/delAccountPhone")
	@ApiOperation(value = "发送注销短信")
	public EmptyResponse delAccountPhone() {
		settingsAdapter.delAccountPhone();
		return EmptyResponse.success();
	}

	@OperationLog
	@GetMapping("user/delAccount")
	@ApiOperation(value = "注销账号")
	public EmptyResponse delAccount(@RequestParam("code") String code) {
		settingsAdapter.delAccount(code);
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/help/email")
	@ApiOperation(value = "发送售后邮件")
	public EmptyResponse sendHelpEmail(@RequestBody @Valid SettingHelpEmailVo settingHelpEmailVo) {
		settingsAdapter.sendHelpEmail(settingHelpEmailVo);
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/user/sendEmailBind")
	@ApiOperation(value = "发送邮箱绑定验证码")
	public EmptyResponse sendEmailBind(@RequestBody @Valid SettingEmailVo settingEmailVo) {
		settingsAdapter.sendBindEmail(settingEmailVo.getEmail());
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/user/sendPhoneBind")
	@ApiOperation(value = "发送手机绑定验证码")
	public EmptyResponse sendEPhoneBind(@RequestBody @Valid SettingPhoneVo settingPhoneVo) {
		settingsAdapter.sendBindPhone(settingPhoneVo.getPhone());
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/user/bindEmail")
	@ApiOperation(value = "账号绑定邮箱")
	public EmptyResponse userBindEmail(@RequestBody @Valid SettingBindEmailVo settingBindEmailVo) {
		settingsAdapter.userBindEmail(settingBindEmailVo.getEmail(), settingBindEmailVo.getCode());
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/user/bindPhone")
	@ApiOperation(value = "账号绑定手机")
	public EmptyResponse userBindPhone(@RequestBody @Valid SettingBindPhoneVo settingBindPhoneVo) {
		settingsAdapter.userBindPhone(settingBindPhoneVo.getPhone(), settingBindPhoneVo.getCode());
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("/device/transfer")
	@ApiOperation(value = "hub向ecos转移相关设备")
	public EmptyResponse deviceTransfer(@RequestBody @Valid SettingTransferVo req) {
		settingsAdapter.deviceTransfer(req.getCode());
		return EmptyResponse.success();
	}
}
