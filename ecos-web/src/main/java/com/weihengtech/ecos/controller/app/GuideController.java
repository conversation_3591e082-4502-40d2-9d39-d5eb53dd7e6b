package com.weihengtech.ecos.controller.app;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.ecos.adapter.GuideAdapter;
import com.weihengtech.ecos.api.pojo.dtos.GuideAgreementVersionDto;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.model.dtos.global.GuideCountryDto;
import com.weihengtech.ecos.model.dtos.global.GuideDatacenterListDto;
import com.weihengtech.ecos.model.dtos.global.GuideTokenDto;
import com.weihengtech.ecos.model.dtos.global.TimezoneDto;
import com.weihengtech.ecos.model.vos.guide.GuideCheckEmailAndPasswordStrengthVo;
import com.weihengtech.ecos.model.vos.guide.GuideCheckPhoneAndPasswordStrengthVo;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideClientUserRegisterPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideEmailPasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideForgetPasswordVo;
import com.weihengtech.ecos.model.vos.guide.GuidePhonePasswordLoginVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendEmailVo;
import com.weihengtech.ecos.model.vos.guide.GuideSendPhoneVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidateEmailCodeVo;
import com.weihengtech.ecos.model.vos.guide.GuideValidatePhoneCodeVo;
import com.weihengtech.ecos.service.thirdpart.HubService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("guide")
@Api(tags = {"01_GuidePage"})
public class GuideController {

	@Resource
	private GuideAdapter guideAdapter;
	@Resource
	private HubService hubService;


	@PostMapping("register")
	@ApiOperation(value = "邮箱用户注册")
	public DataResponse<GuideTokenDto> register(
			@RequestBody @Valid GuideClientUserRegisterEmailVo guideClientUserRegisterVo
	) {
		guideClientUserRegisterVo.checkParams();
		return DataResponse.success(guideAdapter.registerEmail(guideClientUserRegisterVo));
	}

	@PostMapping("registerOem")
	@ApiOperation(value = "邮箱用户注册OEM")
	public DataResponse<GuideTokenDto> registerOem(
			@RequestBody @Valid GuideClientUserRegisterEmailVo guideClientUserRegisterVo
	) {
		guideClientUserRegisterVo.checkParams();
		return DataResponse.success(guideAdapter.registerEmail(guideClientUserRegisterVo));
	}

	@PostMapping("registerPhone")
	@ApiOperation(value = "手机用户注册")
	public DataResponse<GuideTokenDto> registerPhone(
			@RequestBody @Valid GuideClientUserRegisterPhoneVo guideClientUserRegisterPhoneVo
	) {
		guideClientUserRegisterPhoneVo.checkParams();
		return DataResponse.success(guideAdapter.registerPhone(guideClientUserRegisterPhoneVo));
	}

	@PostMapping("checkAccountAndPassword")
	@ApiOperation(value = "检查邮箱账号,密码强度, 验证码是否正确")
	public DataResponse<Boolean> checkEmailAccount(
			@RequestBody @Valid GuideCheckEmailAndPasswordStrengthVo guideCheckEmailAndPasswordStrengthVo
	) {
		guideAdapter.checkEmailAccount(guideCheckEmailAndPasswordStrengthVo.getEmail());
		guideAdapter.checkPasswordStrength(guideCheckEmailAndPasswordStrengthVo.getPassword());
		guideAdapter.checkEmailRegisterCode(
				guideCheckEmailAndPasswordStrengthVo.getEmail(),
				guideCheckEmailAndPasswordStrengthVo.getCode()
		);
		return DataResponse.success(true);
	}

	@PostMapping("checkPhoneAndPassword")
	@ApiOperation(value = "检查手机账号,密码强度, 验证码是否正确")
	public DataResponse<Boolean> checkPhoneAccount(
			@RequestBody @Valid GuideCheckPhoneAndPasswordStrengthVo guideCheckPhoneAndPasswordStrengthVo
	) {
		guideAdapter.checkPhoneAccount(guideCheckPhoneAndPasswordStrengthVo.getPhone());
		guideAdapter.checkPasswordStrength(guideCheckPhoneAndPasswordStrengthVo.getPassword());
		guideAdapter.checkPhoneRegisterCode(
				guideCheckPhoneAndPasswordStrengthVo.getPhone(),
				guideCheckPhoneAndPasswordStrengthVo.getCode()
		);
		return DataResponse.success(true);
	}

	@PostMapping("checkRegisterCodeEmail")
	@ApiOperation(value = "检查邮箱注册验证码")
	public DataResponse<Boolean> checkRegisterCodeEmail(
			@RequestBody @Valid GuideValidateEmailCodeVo guideValidateEmailCodeVo
	) {
		guideAdapter.checkEmailRegisterCode(
				guideValidateEmailCodeVo.getEmail(),
				guideValidateEmailCodeVo.getCode()
		);
		return DataResponse.success(true);
	}

	@PostMapping("checkRegisterCodePhone")
	@ApiOperation(value = "检查手机注册验证码")
	public DataResponse<Boolean> checkRegisterCodePhone(
			@RequestBody @Valid GuideValidatePhoneCodeVo guideValidatePhoneCodeVo
	) {
		guideAdapter.checkPhoneRegisterCode(
				guideValidatePhoneCodeVo.getPhone(),
				guideValidatePhoneCodeVo.getCode()
		);
		return DataResponse.success(true);
	}


	@PostMapping("login")
	@ApiOperation(value = "邮箱登录")
	public DataResponse<GuideTokenDto> login(@RequestBody @Valid GuideEmailPasswordLoginVo guideEmailPasswordLoginVo) {
		return DataResponse.success(guideAdapter.emailPasswordLogin(guideEmailPasswordLoginVo));
	}

	@PostMapping("loginPhone")
	@ApiOperation(value = "手机登录")
	public DataResponse<GuideTokenDto> loginPhone(@RequestBody @Valid GuidePhonePasswordLoginVo guidePhonePasswordLoginVo) {
		return DataResponse.success(guideAdapter.phonePasswordLogin(guidePhonePasswordLoginVo));
	}

	@PostMapping("sendRegisterEmail")
	@ApiOperation(value = "发送注册邮箱验证码")
	public EmptyResponse sendRegisterEmail(@RequestBody @Valid GuideSendEmailVo guideSendEmailVo) {
		guideAdapter.sendRegisterEmail(guideSendEmailVo);
		return EmptyResponse.success();
	}

	@PostMapping("sendRegisterPhone")
	@ApiOperation(value = "发送注册手机验证码")
	public EmptyResponse sendRegisterPhone(@RequestBody @Valid GuideSendPhoneVo guideSendPhoneVo) {
		guideAdapter.sendRegisterPhone(guideSendPhoneVo);
		return EmptyResponse.success();
	}

	@PostMapping("sendEmail")
	@ApiOperation(value = "发送忘记密码邮箱验证码")
	public EmptyResponse sendForgetPasswordEmail(@RequestBody @Valid GuideSendEmailVo guideSendEmailVo) {
		guideAdapter.sendForgetPasswordEmail(guideSendEmailVo);
		return EmptyResponse.success();
	}

	@PostMapping("sendForgetPassPhone")
	@ApiOperation(value = "发送忘记密码手机验证码")
	public EmptyResponse sendForgetPassPhone(@RequestBody @Valid GuideSendPhoneVo guideSendPhoneVo) {
		guideAdapter.sendForgetPasswordPhone(guideSendPhoneVo);
		return EmptyResponse.success();
	}

	@PostMapping("validateCode")
	@ApiOperation(value = "邮箱校验忘记密码验证码")
	public DataResponse<String> validateCode(@RequestBody @Valid GuideValidateEmailCodeVo guideValidateEmailCodeVo) {
		return DataResponse.success(guideAdapter.validateForgetPasswordEmailCode(guideValidateEmailCodeVo));
	}

	@PostMapping("validatePhoneCode")
	@ApiOperation(value = "手机校验忘记密码验证码")
	public DataResponse<String> validatePhoneCode(@RequestBody @Valid GuideValidatePhoneCodeVo guideValidatePhoneCodeVo) {
		return DataResponse.success(guideAdapter.validateForgetPasswordPhoneCode(guideValidatePhoneCodeVo));
	}

	@PostMapping("forgetPassword")
	@ApiOperation(value = "忘记密码重置密码")
	public EmptyResponse forgetPassword(@RequestBody @Valid GuideForgetPasswordVo guideForgetPasswordVo) {
		guideForgetPasswordVo.checkParams();
		guideAdapter.resetPassword(guideForgetPasswordVo);
		return EmptyResponse.success();
	}

	@GetMapping("agreement")
	@ApiOperation(value = "查询隐私协议版本号")
	public DataResponse<GuideAgreementVersionDto> latestAgreementVersion() {
		return DataResponse.success(hubService.getAgreementVersion());
	}

	@GetMapping("datacenter")
	@ApiOperation(value = "查询数据中心列表")
	public DataResponse<List<GuideDatacenterListDto>> listDatacenter() {
		return DataResponse.success(guideAdapter.listDatacenter());
	}

	@GetMapping("timezoneLocale")
	@ApiOperation(value = "获取国际化时区")
	public DataResponse<List<TimezoneDto>> queryTimeZoneLocale() {
		return DataResponse.success(guideAdapter.queryTimeZoneLocale().orElse(ListUtil.empty()));
	}

	@GetMapping("country")
	@ApiOperation(value = "获取国家")
	public DataResponse<List<GuideCountryDto>> queryCountryList() {
		return DataResponse.success(guideAdapter.queryCountryList().orElse(ListUtil.empty()));
	}
}
