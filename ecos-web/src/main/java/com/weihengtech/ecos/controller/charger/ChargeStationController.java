package com.weihengtech.ecos.controller.charger;

import cn.hutool.extra.servlet.ServletUtil;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.dtos.charger.ChargeStationHistoryCapacityDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeRecordDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationChargeTimeDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationInfoDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationRunDataDto;
import com.weihengtech.ecos.model.dtos.charger.ENPlusChargeStationTimingChargeDto;
import com.weihengtech.ecos.model.vos.charger.BindChargeStationCardIdVo;
import com.weihengtech.ecos.model.vos.charger.ChargePlugSwitchVO;
import com.weihengtech.ecos.model.vos.charger.ChargeStationHistoryCapacityVo;
import com.weihengtech.ecos.model.vos.charger.ClientCreateChargeTaskVo;
import com.weihengtech.ecos.model.vos.charger.ClientUpdateChargeTaskVo;
import com.weihengtech.ecos.model.vos.charger.DeleteChargeStationCardVo;
import com.weihengtech.ecos.model.vos.charger.UpdateMaxChargePowerVo;
import com.weihengtech.ecos.service.charger.ChargeStationService;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @program: ecos-server
 * @description: 充电桩控制类
 * @author: jiahao.jin
 * @create: 2024-02-18 11:22
 **/
@RestController
@RequestMapping("/v2/device/chargeStation")
@Api(tags = {"103_chargeStation"})
public class ChargeStationController {

    @Resource
    private ChargeStationService chargeStationService;

    @OperationLog
    @PostMapping("/bind")
    @ApiOperation(value = "绑定EN+_充电桩设备")
    public EmptyResponse bindClientUserDevice(
            @RequestBody @Valid V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo,
            HttpServletRequest request // 添加HttpServletRequest参数
    ) {
        v2ClientHomeBindDeviceVo.buildParam();
        String ip = ServletUtil.getClientIP(request);
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo, ip);
        return EmptyResponse.success();
    }

    @GetMapping("/status")
    @ApiOperation(value = "查询充电桩状态")
    public DataResponse<Integer> getChargeStationStatus(
            @RequestParam("deviceId") String deviceId
    ) {
        return DataResponse.success(chargeStationService.getChargeStationStatus(deviceId));
    }

    @GetMapping("/info")
    @ApiOperation(value = "查询充电桩详情")
    public DataResponse<ENPlusChargeStationInfoDto> queryChargeStationInfo(
            @RequestParam("deviceId") String deviceId
    ) {
        return DataResponse.success(chargeStationService.queryChargeStationInfo(deviceId));
    }

    @GetMapping("/maxPower")
    @ApiOperation(value = "获取最大充电功率")
    public DataResponse<Double> queryMaxChargePower(
            @RequestParam("deviceId") String deviceId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(chargeStationService.queryMaxChargePower(String.valueOf(clientUserDo.getId()), deviceId));
    }

    @OperationLog
    @PostMapping("/maxPower")
    @ApiOperation(value = "更新最大充电功率")
    public EmptyResponse updateMaxChargePower(
            @RequestBody @Valid UpdateMaxChargePowerVo updateMaxChargePowerVo
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.updateMaxChargePower(String.valueOf(clientUserDo.getId()), updateMaxChargePowerVo.getDeviceId(), updateMaxChargePowerVo.getMaxPower(), true);
        return EmptyResponse.success();
    }

    @OperationLog
    @GetMapping("/startCharge")
    @ApiOperation(value = "充电桩开始充电")
    public EmptyResponse startCharging(
            @RequestParam("deviceId") String deviceId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.startCharging(String.valueOf(clientUserDo.getId()), deviceId,null);
        return EmptyResponse.success();
    }

    @OperationLog
    @GetMapping("/stopCharge")
    @ApiOperation(value = "充电桩停止充电")
    public EmptyResponse stopCharging(
            @RequestParam("deviceId") String deviceId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.stopCharging(String.valueOf(clientUserDo.getId()), deviceId);
        return EmptyResponse.success();
    }

    @GetMapping("/runData")
    @ApiOperation(value = "充电桩充电时充电数据")
    public DataResponse<ENPlusChargeStationRunDataDto> runData(
            @RequestParam("deviceId") String deviceId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(chargeStationService.runData(clientUserDo, deviceId));
    }

    @GetMapping("/chargeRecord/last")
    @ApiOperation(value = "充电桩最新充电记录")
    public DataResponse<ENPlusChargeStationChargeRecordDto> lastChargeRecord(
            @RequestParam("deviceId") String deviceId
    ) {
        return DataResponse.success(chargeStationService.lastChargeRecord(deviceId));
    }

    @PostMapping("/chargeRecord/list")
    @ApiOperation(value = "充电桩历史充电记录列表")
    public DataResponse<PageInfoDTO<ENPlusChargeStationChargeRecordDto>> pageHistoryChargeRecord(
            @RequestBody @Valid V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo
    ) {
        v2ClientChargeRecordPageVo.checkParams();
        return DataResponse.success(chargeStationService.pageHistoryChargeRecord(v2ClientChargeRecordPageVo));
    }

    @GetMapping("/chargeRecord/fail")
    @ApiOperation(value = "充电桩预约充电失败记录列表（read：0：未读，1：已读）")
    public DataResponse<List<ENPlusChargeStationTimingChargeDto>> queryNotReadFailChargeRecord(
            @RequestParam("deviceId") String deviceId, @RequestParam("read") Integer read
    ) {
        return DataResponse.success(chargeStationService.queryNotReadFailChargeRecord(deviceId, read));
    }

    @GetMapping("/charge/task/list")
    @ApiOperation(value = "查询充电定时任务")
    public DataResponse<List<ENPlusChargeStationChargeTimeDto>> queryChargeScheduledTaskList(@RequestParam("deviceId") String deviceId){
        return DataResponse.success(chargeStationService.queryChargeScheduledTaskList(deviceId));
    }

    @OperationLog
    @PostMapping("/charge/task")
    @ApiOperation(value = "创建充电定时任务")
    public EmptyResponse createChargeScheduledTask(@RequestBody @Valid ClientCreateChargeTaskVo clientCreateChargeTaskVo){
        clientCreateChargeTaskVo.checkParams();
        chargeStationService.createChargeScheduledTask(clientCreateChargeTaskVo);
        return EmptyResponse.success();
    }

    @OperationLog
    @GetMapping("/charge/task/delete")
    @ApiOperation(value = "删除充电定时任务")
    public EmptyResponse deleteChargeScheduledTask(@RequestParam("taskId") String taskId){
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.deleteChargeScheduledTask(String.valueOf(clientUserDo.getId()), taskId);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/charge/task/update")
    @ApiOperation(value = "更新充电定时任务")
    public DataResponse<Boolean> updateChargeScheduledTask(@RequestBody @Valid ClientUpdateChargeTaskVo clientUpdateChargeTaskVo){
        clientUpdateChargeTaskVo.checkParams();
        return DataResponse.success(chargeStationService.updateChargeScheduledTask(clientUpdateChargeTaskVo));
    }

    @GetMapping("/charge/card/query")
    @ApiOperation(value = "查询充电桩绑定卡片")
    public DataResponse<List<String>> queryChargeStationCards(@RequestParam("deviceId") String deviceId){
        return DataResponse.success(chargeStationService.queryChargeStationCards(deviceId));
    }

    @OperationLog
    @PostMapping("/charge/card/add")
    @ApiOperation(value = "充电桩绑定卡片")
    public EmptyResponse bindChargeStationCardId(@RequestBody @Valid BindChargeStationCardIdVo bindChargeStationCardIdVo ){
        chargeStationService.bindChargeStationCardId(bindChargeStationCardIdVo.getDeviceId(), bindChargeStationCardIdVo.getCardId());
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/charge/card/delete")
    @ApiOperation(value = "充电桩批量移除绑定卡片")
    public EmptyResponse deleteChargeStationCard(@RequestBody @Valid DeleteChargeStationCardVo deleteChargeStationCardVo){
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        chargeStationService.deleteChargeStationCard(String.valueOf(clientUserDo.getId()), deleteChargeStationCardVo.getDeviceId(), deleteChargeStationCardVo.getCardIds());
        return EmptyResponse.success();
    }

    // 充电桩历史充电量（天、月、年）
    @PostMapping("/capacity/history")
    @ApiOperation(value = "充电桩历史充电量（天、月、年）")
    public DataResponse<ChargeStationHistoryCapacityDto> queryChargeStationHistoryCapacity(
            @RequestBody @Valid ChargeStationHistoryCapacityVo chargeStationHistoryCapacityVo
    ) {
        return DataResponse.success(chargeStationService.queryChargeStationHistoryCapacity(chargeStationHistoryCapacityVo));
    }

    @PostMapping("/plug/switch")
    @ApiOperation(value = "即插即充开关")
    public DataResponse<Boolean> updChargerConfiguration(@RequestBody @Valid ChargePlugSwitchVO request) {
        return DataResponse.success(chargeStationService.updChargerConfiguration(request));
    }

}
