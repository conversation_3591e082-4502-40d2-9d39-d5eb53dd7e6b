package com.weihengtech.ecos.controller.app;

import com.weihengtech.ecos.adapter.OemAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("oem")
@Api(tags = {"06_Oem"})
public class OemController {

	@Resource
	private OemAdapter oemAdapter;

	@PostMapping("energy")
	@ApiOperation(value = "Energy页面")
	public DataResponse<OemEnergyDto> oemEnergy(@RequestBody @Valid OemEnergyVo oemEnergyVo) {
		return DataResponse.success(oemAdapter.computeOemEnergy(oemEnergyVo));
	}

	@PostMapping("realTimePower")
	@ApiOperation(value = "实时功率曲线")
	public DataResponse<OemRealTimePowerDto> oemRealTimePower(
			@RequestBody @Valid HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo
	) {
		return DataResponse.success(oemAdapter.oemRealTimePower(homeNowDeviceRealtimeVo));
	}

	@PostMapping("historyEnergy")
	@ApiOperation(value = "历史能耗数据")
	public DataResponse<OemHistoryEnergyDto> oemHistoryEnergy(
			@RequestBody @Valid OemHistoryEnergyVo oemHistoryEnergyVo
	) {
		oemHistoryEnergyVo.checkParams();
		return DataResponse.success(oemAdapter.oemHistoryEnergy(oemHistoryEnergyVo));
	}
}
