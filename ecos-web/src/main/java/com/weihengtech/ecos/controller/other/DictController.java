package com.weihengtech.ecos.controller.other;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.dos.DictDO;
import com.weihengtech.ecos.model.dos.DictItemDO;
import com.weihengtech.ecos.model.dtos.ecos.DictItemDTO;
import com.weihengtech.ecos.service.ecos.DictItemService;
import com.weihengtech.ecos.service.ecos.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/dict")
@Api(tags = {"23_数据字典"})
public class DictController {

    @Resource
    private DictService dictService;
    @Resource
    private DictItemService dictItemService;

    @GetMapping("/item")
    @ApiOperation(value = "数据字典查询")
    public DataResponse<List<DictItemDTO>> query(@RequestParam String code,
                                                 @RequestParam Boolean isTranslate,
                                                 HttpServletRequest request) {
        DictDO dict = dictService.getOne(Wrappers.<DictDO>lambdaQuery()
                .eq(DictDO::getCode, code));
        List<DictItemDO> itemList = dictItemService.list(Wrappers.<DictItemDO>lambdaQuery()
                .eq(DictItemDO::getDictId, dict.getId()));
        if (CollUtil.isEmpty(itemList)) {
            return DataResponse.success(Collections.emptyList());
        }
        String prop;
        if (isTranslate) {
            String lang = request.getHeader("Language");
            String[] s = lang.split("_");
            prop = s[0] + s[1];
        } else {
            prop = "zhCN";
        }
        List<DictItemDTO> resList = itemList.stream()
                .map(i -> DictItemDTO.builder()
                        .code(i.getCode())
                        .name(isTranslate ? BeanUtil.getFieldValue(i, prop).toString() : i.getZhCN())
                        .pic(i.getPic())
                        .build())
                .collect(Collectors.toList());
        return DataResponse.success(resList);
    }
}
