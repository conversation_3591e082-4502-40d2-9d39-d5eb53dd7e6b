package com.weihengtech.ecos.controller.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.adapter.HomeAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.thirdpart.DailyWeatherDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceSketchDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryBatteryDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryGridDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistoryHomeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeHistorySolarDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRealtimeDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceStatisticsDto;
import com.weihengtech.ecos.model.dtos.thirdpart.HourWeatherDto;
import com.weihengtech.ecos.model.dtos.thirdpart.NowWeatherDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.thirdpart.QueryCityDto;
import com.weihengtech.ecos.model.vos.app.home.HomeClientUserBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.HomeClientUserUnbindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.HomeDeviceCityUpdateVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeHistoryVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRunDataVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowIncreaseRefreshVo;
import com.weihengtech.ecos.model.vos.app.home.HomeOrderedDeviceListVo;
import com.weihengtech.ecos.utils.LocaleUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("home")
@Api(tags = {"02_HomePage"})
public class HomePageController {

	@Value("${custom.weather.key}")
	private String key;

	@Resource
	private HomeAdapter homeAdapter;

	@OperationLog
	@PostMapping("device/bind")
	@ApiOperation(value = "用户绑定设备")
	public EmptyResponse bindClientUserDevice(
			@RequestBody @Valid HomeClientUserBindDeviceVo homeClientUserBindDeviceVo
	) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		homeAdapter.bindClientUserDevice(homeClientUserBindDeviceVo, clientUserDo);
		return EmptyResponse.success();
	}

	@GetMapping("device/bindStatus")
	@ApiOperation(value = "设备绑定状态")
	public DataResponse<Integer> bindClientUserDevice(@RequestParam("wifiSn") String wifiSn) {
		if (StrUtil.isBlank(wifiSn)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		return DataResponse.success(homeAdapter.checkDeviceBindStatus(wifiSn));
	}

	@OperationLog
	@PostMapping("device/bindAsync")
	@ApiOperation(value = "用户异步绑定设备")
	public EmptyResponse bindClientUserDeviceAsync(
			@RequestBody @Valid HomeClientUserBindDeviceVo homeClientUserBindDeviceVo
	) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		ThreadUtil.execAsync(() -> homeAdapter.bindClientUserDevice(homeClientUserBindDeviceVo, clientUserDo));
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("device/unbind")
	@ApiOperation(value = "用户解绑设备")
	public EmptyResponse unbindClientUserDevice(
			@RequestBody @Valid HomeClientUserUnbindDeviceVo homeClientUserUnbindDeviceVo
	) {
		homeAdapter.unbindClientUserDevice(homeClientUserUnbindDeviceVo.getDeviceId());
		return EmptyResponse.success();
	}

	@GetMapping("device/list")
	@ApiOperation(value = "首页设备列表")
	public DataResponse<List<HomeDeviceListDto>> deviceList() {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
		return DataResponse.success(homeAdapter.queryUserDeviceList(String.valueOf(clientUserDo.getId())));
	}

	@GetMapping("device/count")
	@ApiOperation(value = "用户绑定设备数量")
	public DataResponse<Integer> deviceCount() {
		return DataResponse.success(homeAdapter.queryUserBindDeviceCount());
	}

	@PostMapping("device/order")
	@ApiOperation(value = "首页设备列表排序")
	public EmptyResponse orderDeviceList(@RequestBody @Valid HomeOrderedDeviceListVo homeOrderedDeviceListVo) {
		List<String> deviceIdList = homeOrderedDeviceListVo.getDeviceIdList();
		if (CollUtil.isEmpty(deviceIdList)) {
			return EmptyResponse.success();
		}
		homeAdapter.orderDeviceList(deviceIdList);
		return EmptyResponse.success();
	}

	@PostMapping("events/fault")
	@ApiOperation(value = "首页events-fault搜索")
	public DataResponse<PageInfoDTO<HomeEventsFaultDto>> pageEventFault(
			@RequestBody @Valid HomeEventsFaultVo homeEventsFaultVo
	) {
		homeEventsFaultVo.checkParams();
		return DataResponse.success(homeAdapter.pageEventFault(homeEventsFaultVo));
	}

	@PostMapping("events/backup/statistics")
	@ApiOperation(value = "首页events-backup备电统计")
	public DataResponse<HomeEventsBackupStatisticsDto> backupStatistics(
			@RequestBody @Valid HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo
	) {
		return DataResponse.success(homeAdapter.backupStatistics(homeEventsBackupStatisticsVo));
	}

	@PostMapping("events/backup/list")
	@ApiOperation(value = "首页events-backup备电列表")
	public DataResponse<PageInfoDTO<HomeEventsBackupPageDto>> pageBackup(
			@RequestBody @Valid HomeEventsBackupPageVo homeEventsBackupPageVo
	) {
		homeEventsBackupPageVo.checkParams();
		return DataResponse.success(homeAdapter.pageBackup(homeEventsBackupPageVo));
	}

	@PostMapping("history/home")
	@ApiOperation(value = "首页history家庭能耗")
	public DataResponse<HomeHistoryHomeDto> queryHistoryHome(@RequestBody @Valid HomeHistoryVo homeHistoryVo) {
		homeHistoryVo.checkParams();
		return DataResponse.success(homeAdapter.queryHistoryHome(homeHistoryVo));
	}

	@PostMapping("history/solar")
	@ApiOperation(value = "首页history光伏供能")
	public DataResponse<HomeHistorySolarDto> queryHistorySolar(@RequestBody @Valid HomeHistoryVo homeHistoryVo) {
		homeHistoryVo.checkParams();
		return DataResponse.success(homeAdapter.queryHistorySolar(homeHistoryVo));
	}

	@PostMapping("history/battery")
	@ApiOperation(value = "首页history电池充放")
	public DataResponse<HomeHistoryBatteryDto> queryHistoryBattery(@RequestBody @Valid HomeHistoryVo homeHistoryVo) {
		homeHistoryVo.checkParams();
		return DataResponse.success(homeAdapter.queryHistoryBattery(homeHistoryVo));
	}

	@GetMapping("history/battery/statistics")
	@ApiOperation(value = "首页history电池总放电量")
	public DataResponse<BigDecimal> queryHistoryBatteryStatistics(@RequestParam("deviceId") String deviceId) {
		if (StrUtil.isBlank(deviceId)) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		return DataResponse.success(homeAdapter.queryHistoryBatteryStatistics(deviceId));
	}

	@PostMapping("history/grid")
	@ApiOperation(value = "首页history电网充放")
	public DataResponse<HomeHistoryGridDto> queryHistoryGrid(@RequestBody @Valid HomeHistoryVo homeHistoryVo) {
		homeHistoryVo.checkParams();
		return DataResponse.success(homeAdapter.queryHistoryGrid(homeHistoryVo));
	}

	@PostMapping("now/device/statistics")
	@ApiOperation(value = "设备统计信息")
	public DataResponse<HomeNowDeviceStatisticsDto> queryNowDeviceStatistics(
			@RequestBody @Valid HomeNowDeviceStatisticsVo homeNowDeviceStatisticsVo
	) {
		return DataResponse.success(homeAdapter.queryNowDeviceStatistics(homeNowDeviceStatisticsVo));
	}

	@PostMapping("now/device/realtime")
	@ApiOperation(value = "设备实时能源")
	public DataResponse<HomeNowDeviceRealtimeDto> queryNowDeviceRealtime(
			@RequestBody @Valid HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo
	) {
		return DataResponse.success(homeAdapter.queryNowDeviceRealtime(homeNowDeviceRealtimeVo.getDeviceId()));
	}

	@PostMapping("now/device/runData")
	@ApiOperation(value = "设备实时运行数据")
	public DataResponse<HomeNowDeviceRunDataDto> queryNowDeviceRunData(
			@RequestBody @Valid HomeNowDeviceRunDataVo homeNowDeviceRunDataVo
	) {
		return DataResponse.success(homeAdapter.queryNowDeviceRunData(homeNowDeviceRunDataVo.getDeviceId()));
	}

	@PostMapping("now/incrRefresh")
	@ApiOperation(value = "提高设备的采集率")
	public EmptyResponse nowDataIncreaseRefresh(@RequestBody @Valid HomeNowIncreaseRefreshVo homeNowIncreaseRefreshVo) {
		homeAdapter.nowDataIncreaseRefresh(homeNowIncreaseRefreshVo.getDeviceId());
		return EmptyResponse.success();
	}

	@GetMapping("device/sketch")
	@ApiOperation(value = "家庭设备概况")
	public DataResponse<HomeDeviceSketchDto> getDeviceSketch() {
		return DataResponse.success(homeAdapter.getDeviceSketch());
	}

	@GetMapping("/weather/now")
	@ApiOperation(value = "获取实时天气")
	public DataResponse<NowWeatherDto> getNowWeather(
			@RequestParam(name = "location", required = true) String location,
			@RequestParam(name = "unit", required = false) String unit,
			@NonNull HttpServletRequest httpServletRequest
	) {
		// 获取请求的语言环境
		String language = LocaleUtil.getLanguage(httpServletRequest);
		String lang = language.split("_")[0];
		NowWeatherDto nowWeather = homeAdapter.getNowWeather(key, location, lang, unit);
		return DataResponse.success(nowWeather);
	}

	@GetMapping("/weather/3d")
	@ApiOperation(value = "获取3天天气")
	public DataResponse<List<DailyWeatherDto>> get3dWeather(
			@RequestParam(name = "location", required = true) String location,
			@RequestParam(name = "unit", required = false) String unit,
			@NonNull HttpServletRequest httpServletRequest
	) {
		// 获取请求的语言环境
		String language = LocaleUtil.getLanguage(httpServletRequest);
		String lang = language.split("_")[0];
		List<DailyWeatherDto> threeDayWeather = homeAdapter.get3dWeather(key, location, lang, unit);
		return DataResponse.success(threeDayWeather);
	}

	@GetMapping("/weather/7d")
	@ApiOperation(value = "获取7天天气")
	public DataResponse<List<DailyWeatherDto>> get7dWeather(
			@RequestParam(name = "location", required = true) String location,
			@RequestParam(name = "unit", required = false) String unit,
			@NonNull HttpServletRequest httpServletRequest
	) {
		// 获取请求的语言环境
		String language = LocaleUtil.getLanguage(httpServletRequest);
		String lang = language.split("_")[0];
		List<DailyWeatherDto> sevenDayWeather = homeAdapter.get7dWeather(key, location, lang, unit);
		return DataResponse.success(sevenDayWeather);
	}

	@GetMapping("/weather/24h")
	@ApiOperation(value = "获取未来24小时天气")
	public DataResponse<List<HourWeatherDto>> get24hWeather(
			@RequestParam(name = "location", required = true) String location,
			@RequestParam(name = "unit", required = false) String unit,
			@NonNull HttpServletRequest httpServletRequest
	) {
		// 获取请求的语言环境
		String language = LocaleUtil.getLanguage(httpServletRequest);
		String lang = language.split("_")[0];
		List<HourWeatherDto> hourlyWeather = homeAdapter.get24hWeather(key, location, lang, unit);
		return DataResponse.success(hourlyWeather);
	}

	@GetMapping("/city/lookup")
	@ApiOperation(value = "城市搜索")
	public DataResponse<List<QueryCityDto>> queryCity(
			@RequestParam(name = "location", required = true) String location,
			@NonNull HttpServletRequest httpServletRequest
	) {
		// 获取请求的语言环境
		String language = LocaleUtil.getLanguage(httpServletRequest);
		String lang = language.split("_")[0];
		List<QueryCityDto> queryCityDto = homeAdapter.queryCity(key, location, lang);

		return DataResponse.success(queryCityDto);
	}

	@PostMapping("device/city")
	@ApiOperation(value = "更新设备经纬度")
	public EmptyResponse updateDeviceCity(
			@RequestBody @Valid HomeDeviceCityUpdateVo homeDeviceCityUpdateVo
	) {
		homeAdapter.updateDeviceCity(homeDeviceCityUpdateVo);
		return EmptyResponse.success();
	}

	@GetMapping("/device/homeId")
	@ApiOperation(value = "获取设备所在家庭ID")
	public DataResponse<String> queryDeviceHomeId(
			@RequestParam(name = "deviceId") String deviceId
	) {
		String tuyaDeviceHomeId = homeAdapter.getTuyaDeviceHomeId(deviceId);
		return DataResponse.success(tuyaDeviceHomeId);
	}
}
