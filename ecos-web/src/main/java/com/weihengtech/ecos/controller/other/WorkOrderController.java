package com.weihengtech.ecos.controller.other;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.api.pojo.dtos.WorkOrderDetailDto;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.vos.thirdpart.order.AddWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.CloseWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.PageWorkOrderVo;
import com.weihengtech.ecos.model.vos.thirdpart.order.QuizWorkOrderVo;
import com.weihengtech.ecos.service.thirdpart.IWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("order")
@Api(tags = {"07_WorkOrder"})
public class WorkOrderController {

	@Resource
	private IWorkOrderService workOrderService;

	@OperationLog
	@PostMapping("add")
	@ApiOperation(value = "新增工单")
	public EmptyResponse addWorkOrder(@RequestBody @Valid AddWorkOrderVo addWorkOrderVo) {
		if (StrUtil.isBlank(addWorkOrderVo.getContent()) && CollUtil.isEmpty(addWorkOrderVo.getPicList())) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		workOrderService.addWorkOrder(addWorkOrderVo);
		return EmptyResponse.success();
	}

	@PostMapping("quiz")
	@ApiOperation(value = "工单提问")
	public EmptyResponse quizWorkOrder(@RequestBody @Valid QuizWorkOrderVo quizWorkOrderVo) {
		if (StrUtil.isBlank(quizWorkOrderVo.getContent()) && CollUtil.isEmpty(quizWorkOrderVo.getPicList())) {
			throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		workOrderService.quizWorkOrder(quizWorkOrderVo);
		return EmptyResponse.success();
	}

	@OperationLog
	@PostMapping("close")
	@ApiOperation(value = "关闭工单")
	public EmptyResponse closeWorkOrder(@RequestBody @Valid CloseWorkOrderVo closeWorkOrderVo) {
		workOrderService.closeWorkIOrder(closeWorkOrderVo.getWorkOrderId());
		return EmptyResponse.success();
	}

	@PostMapping("page")
	@ApiOperation(value = "工单列表")
	public DataResponse<PageInfoDTO<WorkOrderDetailDto>> pageWorkOrder(@RequestBody @Valid PageWorkOrderVo pageWorkOrderVo) {
		pageWorkOrderVo.checkParams();
		return DataResponse.success(workOrderService.pageWorkOrder(pageWorkOrderVo));
	}

	@GetMapping("detail")
	@ApiOperation(value = "工单详情")
	public DataResponse<WorkOrderDetailDto> detailWorkOrder(@RequestParam("workOrderId") String workOrderId) {
		if (StrUtil.isBlank(workOrderId)) throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		return DataResponse.success(workOrderService.detailWorkOrder(workOrderId));
	}
}
