package com.weihengtech.ecos.controller.other;

import com.weihengtech.ecos.adapter.EnestAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.vos.bind.EnestBindDeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Enest相关接口
 *
 * <AUTHOR>
 * @date 2024/6/6 10:00
 * @version 1.0
 */
@RestController
@RequestMapping("/enest")
@Api(tags = {"21_Enest"})
public class EnestController {

    @Resource
    private EnestAdapter enestAdapter;

    @OperationLog
    @PostMapping("/device/bind")
    @ApiOperation(value = "用户绑定设备")
    public DataResponse<Long> bindClientUserDevice(@RequestBody @Valid EnestBindDeviceVO bindParam) {
        Long deviceId = enestAdapter.deviceBind(bindParam);
        return DataResponse.success(deviceId);
    }
}
