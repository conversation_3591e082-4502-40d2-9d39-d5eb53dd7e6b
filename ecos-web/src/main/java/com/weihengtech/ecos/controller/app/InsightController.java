package com.weihengtech.ecos.controller.app;

import com.weihengtech.ecos.adapter.InsightAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyAvgStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("insight")
@Api(tags = {"03_Insight"})
public class InsightController {

	@Resource
	private InsightAdapter insightAdapter;

	@GetMapping("compute/latestDayDeviceEnergy")
	@ApiOperation(value = "根据日期偏移量设备平均耗能")
	public DataResponse<InsightDeviceEnergyAvgStatisticsDto> computeOffsetDayDeviceEnergy(
			@RequestParam("deviceId") String deviceId, @RequestParam("offsetDay") Integer offsetDay
	) {
		if (offsetDay < -7) {
			offsetDay = -7;
		} else if (offsetDay > 7) {
			offsetDay = 7;
		}
		return DataResponse.success(insightAdapter.getOffsetDaysDeviceEnergy(deviceId, offsetDay, null));
	}

	@GetMapping("compute/latestMonthDeviceEnergy")
	@ApiOperation(value = "根据月份偏移量设备平均耗能")
	public DataResponse<InsightDeviceEnergyAvgStatisticsDto> computeOffsetMonthDeviceEnergy(
			@RequestParam("deviceId") String deviceId, @RequestParam("offsetMonth") Integer offsetMonth
	) {
		return DataResponse.success(null);
	}

	@GetMapping("compute/latestDayDeviceEnergyHeatmap")
	@ApiOperation(value = "根据日期偏移量计算设备耗能热图")
	public DataResponse<InsightDeviceEnergyHeatmapStatisticsDto> computeOffsetDayDeviceEnergyHeatmap(
			@RequestParam("deviceId") String deviceId, @RequestParam("offsetDay") Integer offsetDay
	) {
		if (offsetDay < -7) {
			offsetDay = -7;
		} else if (offsetDay > 7) {
			offsetDay = 7;
		}
		return DataResponse.success(insightAdapter.getOffsetDayDeviceEnergyHeatmap(deviceId, offsetDay));
	}

	@GetMapping("more/energyNotify")
	@ApiOperation(value = "获取能量通知配置")
	public DataResponse<InsightMoreInformationEnergyNotifyDto> getEnergyNotifyConfig(
			@RequestParam("deviceId") String deviceId
	) {
		return DataResponse.success(insightAdapter.getEnergyNotifyConfig(deviceId));
	}

	@OperationLog
	@PostMapping("more/energyNotify")
	@ApiOperation(value = "更新能量通知配置")
	public DataResponse<InsightMoreInformationEnergyNotifyDto> updateEnergyNotifyConfig(
			@RequestBody @Valid InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
	) {
		insightMoreInformationEnergyNotifyVo.checkParams();
		return DataResponse.success(insightAdapter.updateEnergyNotifyConfig(insightMoreInformationEnergyNotifyVo));
	}
}
