package com.weihengtech.ecos.controller.price;

import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.model.dos.ClientElePriceFixedDO;
import com.weihengtech.ecos.model.vos.price.ElePriceFixedVO;
import com.weihengtech.ecos.service.ele.ClientElePriceFixedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/ele/price/fixed")
@Api(tags = {"22_固定电价"})
public class ElePriceFixedController {

    @Resource
    private ClientElePriceFixedService clientElePriceFixedService;

    @GetMapping("/get")
    @ApiOperation(value = "查询家庭固定电价")
    public DataResponse<ClientElePriceFixedDO> query(@RequestParam String homeId) {
        return DataResponse.success(clientElePriceFixedService.queryFixedPrice(homeId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新固定电价")
    public EmptyResponse updateFixedPrice(@RequestBody @Valid ElePriceFixedVO param) {
        clientElePriceFixedService.updateFixedPrice(param);
        return EmptyResponse.success();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除固定电价")
    public EmptyResponse deleteFixedPrice(@RequestParam String id) {
        clientElePriceFixedService.removeById(id);
        return EmptyResponse.success();
    }
}
