package com.weihengtech.ecos.controller.app;

import com.weihengtech.ecos.adapter.V2AIAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dtos.app.V2AISessionDto;
import com.weihengtech.ecos.model.dtos.app.V2AISessionMessagesDto;
import com.weihengtech.ecos.model.vos.app.V2AIChatCompletionsVo;
import com.weihengtech.ecos.model.vos.app.V2AIChatMessagesPageVo;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;

/**
 * @program: ecos-server
 * @description: Ai问答相关接口
 * @author: jiahao.jin
 * @create: 2024-05-09 14:59
 **/
@RestController
@RequestMapping("/v2/ai")
@Api(tags = {"V2_02_AI"})
public class V2AIController {

    @Resource
    private V2AIAdapter v2AiAdapter;

    @PostMapping(value = "/chat/completions")
    @ApiOperation(value = "向AI发起问答请求")
    public void chatCompletions (@RequestBody @Valid V2AIChatCompletionsVo chatContent, HttpServletResponse rp, Locale locale) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        // 向AI发起问答请求
        v2AiAdapter.chatCompletions(chatContent, clientUserDo, rp, locale);
    }

    @GetMapping("/chat/conversations")
    @ApiOperation(value = "查询用户聊天会话列表")
    public DataResponse<List<V2AISessionDto>> chatConversations() {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        // 查询聊天会话
        return DataResponse.success(v2AiAdapter.chatConversations(clientUserDo));
    }

    @ApiOperation(value = "查询某个会话的聊天记录")
    @PostMapping("/chat/conversations")
    public DataResponse<V2AISessionMessagesDto> chatConversations(@RequestBody @Valid V2AIChatMessagesPageVo historyParam,
            Locale locale) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        historyParam.checkParams();
        // 查询某个会话的聊天记录
        return DataResponse.success(v2AiAdapter.chatConversationMessages(clientUserDo, historyParam, locale));
    }




}
