package com.weihengtech.ecos.controller.app;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.EcosAccountDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceBindStatusDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceListDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeDeviceModeDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoAllDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeInfoDto;
import com.weihengtech.ecos.model.dtos.app.home.V2HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.vos.app.DeviceWifiSnVO;
import com.weihengtech.ecos.model.vos.app.V2NowDataIncreaseRefreshVo;
import com.weihengtech.ecos.model.vos.app.V2UpdateDeviceRemarkVo;
import com.weihengtech.ecos.model.vos.bind.WhBindDeviceVO;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeCreateHomeVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeInsightDeviceDataVo;
import com.weihengtech.ecos.model.vos.app.home.V2HomeUpdateHomeVo;
import com.weihengtech.ecos.model.vos.app.home.V2TransferDeviceFamilyVo;
import com.weihengtech.ecos.model.vos.settings.SettingRemoveDeviceBindAccountVo;
import com.weihengtech.ecos.model.vos.settings.V2SettingTransferVo;
import com.weihengtech.ecos.model.vos.thirdpart.InstallBoundVO;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @program: ecos-server
 * @description: V2家庭相关接口
 * @author: jiahao.jin
 * @create: 2024-01-21 11:37
 **/
@RestController
@RequestMapping("/v2/home")
@Api(tags = {"V2_01_Home"})
public class V2HomeController {

    @Resource
    private V2HomeAdapter v2HomeAdapter;

    // 创建家庭
    @OperationLog
    @PostMapping("/family/add")
    @ApiOperation(value = "创建家庭")
    public EmptyResponse createHome(
            @RequestBody @Valid V2HomeCreateHomeVo v2HomeCreateHomeVo
    ) {
        // 创建普通家庭
        v2HomeAdapter.createHome(v2HomeCreateHomeVo, CommonConstants.HOME_COMMON);
        return EmptyResponse.success();
    }

    // 删除家庭
    @OperationLog
    @GetMapping("/family/delete")
    @ApiOperation(value = "删除家庭")
    public EmptyResponse deleteHome(@RequestParam("homeId") String homeId) {
        // 删除指定家庭
        v2HomeAdapter.deleteHome(homeId);
        return EmptyResponse.success();
    }

    // 查询家庭列表
    @GetMapping("/family/query")
    @ApiOperation(value = "查询家庭列表")
    public DataResponse<List<V2HomeInfoDto>> queryHomeList() {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(v2HomeAdapter.queryHomeList(String.valueOf(clientUserDo.getId())));
    }

    // 查询家庭详情
    @GetMapping("/family/info")
    @ApiOperation(value = "查询家庭信息详情")
    public DataResponse<V2HomeInfoAllDto> queryHomeInfo(@RequestParam("homeId") String homeId) {
        return DataResponse.success(v2HomeAdapter.queryHomeInfo(homeId));
    }

    // 更新家庭信息
    @OperationLog
    @PostMapping("/family/update")
    @ApiOperation(value = "更新家庭信息")
    public DataResponse<V2HomeInfoDto> updateHomeInfo(@RequestBody @Valid V2HomeUpdateHomeVo v2HomeUpdateHomeVo) {
        return DataResponse.success(v2HomeAdapter.updateHomeInfo(v2HomeUpdateHomeVo));
    }

    @GetMapping("/family/price_info")
    @ApiOperation(value = "获取家庭电价数据")
    public DataResponse<HomeElePriceDTO> familyPriceInfo(@RequestParam String homeId) {
        HomeElePriceDTO priceInfo = v2HomeAdapter.familyPriceInfo(homeId);
        return DataResponse.success(priceInfo);
    }

    // 家庭添加成员
    @OperationLog
    @GetMapping("/member/join")
    @ApiOperation(value = "用户扫码加入家庭")
    public EmptyResponse joinHome(@RequestParam("code") String code) {
        v2HomeAdapter.joinHome(code);
        return EmptyResponse.success();
    }

    // 生成邀请家庭成员二维码
    @GetMapping("/member/invite")
    @ApiOperation(value = "生成邀请家庭成员二维码密文")
    public DataResponse<String> inviteMemberQrCode(@RequestParam("homeId") String homeId) {
        return DataResponse.success(v2HomeAdapter.inviteMemberQrCode(homeId));
    }


    // 家庭删除成员
    @OperationLog
    @GetMapping("/member/delete")
    @ApiOperation(value = "批量删除家庭成员")
    public EmptyResponse deleteHomeMember(@RequestParam("homeId") String homeId, @RequestParam("userIds") List<String> userIds) {
        v2HomeAdapter.deleteHomeMember(homeId, userIds);
        return EmptyResponse.success();
    }

    @OperationLog
    @GetMapping("/member/exit")
    @ApiOperation(value = "家庭成员退出家庭")
    public EmptyResponse memberExitHome(@RequestParam("homeId") String homeId) {
        v2HomeAdapter.memberExitHome(homeId);
        return EmptyResponse.success();
    }

    // 家庭添加设备
    @OperationLog
    @PostMapping("/device/bind")
    @ApiOperation(value = "家庭添加设备（目前仅支持已配网设备）")
    public EmptyResponse bindClientUserAndHomeDevice(
            @RequestBody @Valid V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo,
            HttpServletRequest request // 添加HttpServletRequest参数
    ) {
        v2ClientHomeBindDeviceVo.buildParam();
        String ip = ServletUtil.getClientIP(request);
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        ThreadUtil.execAsync(() -> v2HomeAdapter.bindClientUserAndHomeDevice(v2ClientHomeBindDeviceVo, clientUserDo, ip));
        return EmptyResponse.success();
    }

    @GetMapping("/device/bindStatus")
    @ApiOperation(value = "设备绑定状态")
    public DataResponse<V2HomeDeviceBindStatusDto> bindClientUserDevice(@RequestParam("wifiSn") String wifiSn) {
        if (StrUtil.isBlank(wifiSn)) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        return DataResponse.success(v2HomeAdapter.checkDeviceBindStatus(wifiSn));
    }

    // 家庭添加设备
    @OperationLog
    @PostMapping("/device/bind/iot")
    @ApiOperation(value = "家庭添加设备（自研棒子）")
    public DataResponse<Long> iotDeviceBind(
            @RequestBody @Valid WhBindDeviceVO bindParam
    ) {
        Long deviceId = v2HomeAdapter.iotDeviceBind(bindParam);
        return DataResponse.success(deviceId);
    }

    @GetMapping("/device/is_online")
    @ApiOperation(value = "配网流程判断设备是否在线（自研棒子）")
    public DataResponse<Boolean> iotIsOnline(@RequestParam String wifiSn) {
        return DataResponse.success(v2HomeAdapter.iotIsOnline(wifiSn));
    }

    @GetMapping("/device/bind_info")
    @ApiOperation(value = "设备绑定账号信息（自研棒子）")
    public DataResponse<EcosAccountDTO> deviceBindAccountInfo(@RequestParam String deviceSn) {
        return DataResponse.success(v2HomeAdapter.deviceBindAccountInfo(deviceSn));
    }

    @OperationLog
    @PostMapping("/device/reset/iot")
    @ApiOperation("配网过程中iot平台解绑设备")
    public EmptyResponse iotResetDevice(@RequestBody DeviceWifiSnVO param) {
        v2HomeAdapter.iotResetDevice(param.getWifiSn());
        return EmptyResponse.success();
    }


    // 家庭删除设备
    @OperationLog
    @GetMapping("/device/unbind")
    @ApiOperation(value = "家庭删除设备")
    public EmptyResponse unbindClientUserAndHomeDevice(
            @RequestParam("homeId") String homeId, @RequestParam("deviceIds") List<Long> deviceIds
    ) {
        v2HomeAdapter.unbindClientUserAndHomeDevice(homeId, deviceIds);
        return EmptyResponse.success();
    }

    // 查询家庭设备列表
    @GetMapping("/device/query")
    @ApiOperation(value = "查询家庭中设备列表")
    public DataResponse<List<V2HomeDeviceListDto>> queryHomeDeviceList(@RequestParam("homeId") String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(v2HomeAdapter.queryHomeDeviceList(clientUserDo, homeId));
    }

    @GetMapping("/device/runData")
    @ApiOperation(value = "查询家庭中设备的实时运行数据")
    public DataResponse<V2HomeNowDeviceRunDataDto> queryHomeDeviceRunData(@RequestParam("homeId") String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(v2HomeAdapter.queryHomeDeviceRunData(homeId, clientUserDo));
    }

    @OperationLog
    @PostMapping("/device/remark")
    @ApiOperation(value = "更改家庭中设备的别名")
    public EmptyResponse updateDeviceRemark(@RequestBody @Valid V2UpdateDeviceRemarkVo v2UpdateDeviceRemarkVo) {
        v2HomeAdapter.updateDeviceRemark(v2UpdateDeviceRemarkVo.getDeviceId(), v2UpdateDeviceRemarkVo.getRemark());
        return EmptyResponse.success();
    }

    @GetMapping("/device/mode")
    @ApiOperation(value = "查询家庭中设备的mode")
    public DataResponse<List<V2HomeDeviceModeDto>> queryHomeDeviceMode(@RequestParam("homeId") String homeId) {
        return DataResponse.success(v2HomeAdapter.queryHomeDeviceMode(homeId));
    }

    @GetMapping("/device/energy")
    @ApiOperation(value = "家庭获取最近一周光伏、电网、碳减排、节约标准煤数据")
    public DataResponse<HomeDeviceEnergyStatisticsDto> queryHomeSolarAndGridEnergyData(@RequestParam("homeId") String homeId) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(v2HomeAdapter.queryHomeSolarAndGridEnergyData(homeId, clientUserDo));
    }

    @PostMapping("/device/insight")
    @ApiOperation(value = "家庭所有储能获取Insight-设备Insight功耗页面数据")
    public DataResponse<InsightDeviceDataDto> queryHomeDeviceInsightData(@RequestBody @Valid V2HomeInsightDeviceDataVo v2HomeInsightDeviceDataVo) {
        v2HomeInsightDeviceDataVo.checkParams();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(v2HomeAdapter.queryHomeDeviceInsightData(v2HomeInsightDeviceDataVo.getHomeId(), v2HomeInsightDeviceDataVo.getPeriodType(), v2HomeInsightDeviceDataVo.getTimestamp(), clientUserDo));
    }

    @OperationLog
    @PostMapping("/device/transfer/family")
    @ApiOperation(value = "转移设备的家庭")
    public EmptyResponse transferDeviceFamily(@RequestBody @Valid V2TransferDeviceFamilyVo v2TransferDeviceFamilyVo) {
        v2HomeAdapter.transferDeviceFamily(v2TransferDeviceFamilyVo.getDeviceId(), v2TransferDeviceFamilyVo.getHomeId());
        return EmptyResponse.success();
    }

    // 分享设备生成二维码
    @GetMapping("/device/share/qrCode/client")
    @ApiOperation(value = "分享设备给子账号生成二维码")
    public DataResponse<String> shareDeviceQRCodeClient(@RequestParam("deviceId") String deviceId,
                                                        @RequestParam(value = "saveDeviceTime", required = false) Integer saveDeviceTime) {
        return DataResponse.success(v2HomeAdapter.shareDeviceQRCodeClient(deviceId, saveDeviceTime));
    }

    // 添加为设备的子账号
    @OperationLog
    @GetMapping("device/share/slaveBind")
    @ApiOperation(value = "二维码绑定从账号")
    public EmptyResponse bindSlaveAccount(@RequestParam("code") String code) {
        if (StrUtil.isBlank(code)) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        v2HomeAdapter.bindSlaveAccount(code);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/device/removeAccount")
    @ApiOperation(value = "移除设备绑定账号")
    public EmptyResponse removeDeviceBindAccount(
            @RequestBody @Valid SettingRemoveDeviceBindAccountVo settingRemoveDeviceBindAccountVo
    ) {
        v2HomeAdapter.removeDeviceBindAccount(settingRemoveDeviceBindAccountVo);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/device/transfer")
    @ApiOperation(value = "hub向ecos转移相关设备")
    public EmptyResponse deviceTransfer(@RequestBody @Valid V2SettingTransferVo req) {
        v2HomeAdapter.deviceTransfer(req);
        return EmptyResponse.success();
    }

    @PostMapping("/device/incrRefresh")
    @ApiOperation(value = "提高设备的采集率")
    public EmptyResponse nowDataIncreaseRefresh(@RequestBody @Valid V2NowDataIncreaseRefreshVo v2NowDataIncreaseRefreshVo) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        v2HomeAdapter.nowDataIncreaseRefresh(v2NowDataIncreaseRefreshVo.getHomeId(), clientUserDo);
        return EmptyResponse.success();
    }

    @GetMapping("/device/info")
    @ApiOperation(value = "获取设备信息")
    public DataResponse<HybridSinglePhaseDO> deviceInfo(@RequestParam String deviceSn) {
        return DataResponse.success(v2HomeAdapter.deviceInfo(deviceSn));
    }

    @OperationLog
    @PostMapping("/install/upd")
    @ApiOperation(value = "安装商更新保留时间")
    public EmptyResponse installUpdSaveTime(@RequestBody @Valid InstallBoundVO req) {
        v2HomeAdapter.installUpdSaveTime(req);
        return EmptyResponse.success();
    }

    @GetMapping("/cost/info")
    @ApiOperation(value = "获取家庭节省成本数据")
    public DataResponse<HomeCostSavingDTO> costInfo(@RequestParam String homeId) {
        return DataResponse.success(v2HomeAdapter.costInfo(homeId));
    }
}
