package com.weihengtech.ecos.controller.single;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.dtos.single.SinglePhaseBaseInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupPageDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsBackupStatisticsDto;
import com.weihengtech.ecos.model.dtos.ecos.HomeEventsFaultDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.app.home.HomeDeviceEnergyStatisticsDto;
import com.weihengtech.ecos.model.dtos.app.home.HomeNowDeviceRunDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceDataDto;
import com.weihengtech.ecos.model.dtos.insight.InsightDeviceEnergyHeatmapStatisticsDto;
import com.weihengtech.ecos.model.dtos.insight.InsightMoreInformationEnergyNotifyDto;
import com.weihengtech.ecos.model.dtos.oem.OemEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemHistoryEnergyDto;
import com.weihengtech.ecos.model.dtos.oem.OemRealTimePowerDto;
import com.weihengtech.ecos.model.vos.app.InsightDeviceDataVo;
import com.weihengtech.ecos.model.vos.app.InsightMoreInformationEnergyNotifyVo;
import com.weihengtech.ecos.model.vos.app.OemEnergyVo;
import com.weihengtech.ecos.model.vos.app.OemHistoryEnergyVo;
import com.weihengtech.ecos.model.vos.app.PowerLimitVO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.app.home.HomeClientUserUnbindDeviceVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupPageVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsBackupStatisticsVo;
import com.weihengtech.ecos.model.vos.app.home.HomeEventsFaultVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRealtimeVo;
import com.weihengtech.ecos.model.vos.app.home.HomeNowDeviceRunDataVo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.service.single.SinglePhaseService;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.TransformUtil;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @program ecos-server
 * @description  单相机产品业务接口
 * @create 2023-10-27 10:38
 **/
@RestController
@RequestMapping("/v2/device/single")
@Api(tags = {"100_single-phase"})
public class SinglePhaseController {

    @Resource
    private SinglePhaseService singlePhaseService;

    @OperationLog
    @PostMapping("/device/bind")
    @ApiOperation(value = "用户绑定设备")
    public EmptyResponse bindClientUserDevice(
            @RequestBody @Valid V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo
    ) {
        v2ClientHomeBindDeviceVo.buildParam();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        singlePhaseService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo);
        return EmptyResponse.success();
    }

    @GetMapping("/device/bindStatus")
    @ApiOperation(value = "设备绑定状态")
    public DataResponse<Integer> bindClientUserDevice(@RequestParam("wifiSn") String wifiSn) {
        if (StrUtil.isBlank(wifiSn)) {
            throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
        }
        return DataResponse.success(singlePhaseService.checkDeviceBindStatus(wifiSn));
    }

    @OperationLog
    @PostMapping("/device/bindAsync")
    @ApiOperation(value = "用户异步绑定设备")
    public EmptyResponse bindClientUserDeviceAsync(
            @RequestBody @Valid V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo
    ) {
        v2ClientHomeBindDeviceVo.buildParam();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        ThreadUtil.execAsync(() -> singlePhaseService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo));
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/device/unbind")
    @ApiOperation(value = "用户解绑设备")
    public EmptyResponse unbindClientUserDevice(
            @RequestBody @Valid HomeClientUserUnbindDeviceVo homeClientUserUnbindDeviceVo
    ) {
        singlePhaseService.unbindClientUserDevice(homeClientUserUnbindDeviceVo.getDeviceId());
        return EmptyResponse.success();
    }

    @PostMapping("/events/fault")
    @ApiOperation(value = "首页events-fault搜索")
    public DataResponse<PageInfoDTO<HomeEventsFaultDto>> pageEventFault(
            @RequestBody @Valid HomeEventsFaultVo homeEventsFaultVo
    ) {
        homeEventsFaultVo.checkParams();
        return DataResponse.success(singlePhaseService.pageEventFault(homeEventsFaultVo));
    }

    @PostMapping("/events/backup/statistics")
    @ApiOperation(value = "首页events-backup备电统计")
    public DataResponse<HomeEventsBackupStatisticsDto> backupStatistics(
            @RequestBody @Valid HomeEventsBackupStatisticsVo homeEventsBackupStatisticsVo
    ) {
        return DataResponse.success(singlePhaseService.backupStatistics(homeEventsBackupStatisticsVo));
    }

    @PostMapping("/events/backup/list")
    @ApiOperation(value = "首页events-backup备电列表")
    public DataResponse<PageInfoDTO<HomeEventsBackupPageDto>> pageBackup(
            @RequestBody @Valid HomeEventsBackupPageVo homeEventsBackupPageVo
    ) {
        homeEventsBackupPageVo.checkParams();
        return DataResponse.success(singlePhaseService.pageBackup(homeEventsBackupPageVo));
    }

    @PostMapping("/now/device/runData")
    @ApiOperation(value = "设备实时运行数据")
    public DataResponse<HomeNowDeviceRunDataDto> queryNowDeviceRunData(
            @RequestBody @Valid HomeNowDeviceRunDataVo homeNowDeviceRunDataVo
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.queryNowDeviceRunData(clientUserDo, homeNowDeviceRunDataVo.getDeviceId()));
    }

    @GetMapping("/compute/latestDayDeviceEnergyHeatmap")
    @ApiOperation(value = "根据日期偏移量计算设备耗能热图")
    public DataResponse<InsightDeviceEnergyHeatmapStatisticsDto> computeOffsetDayDeviceEnergyHeatmap(
            @RequestParam("deviceId") String deviceId, @RequestParam("offsetDay") Integer offsetDay
    ) {
        if (offsetDay < -7) {
            offsetDay = -7;
        } else if (offsetDay > 7) {
            offsetDay = 7;
        }
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.getOffsetDayDeviceEnergyHeatmap(clientUserDo, deviceId, offsetDay));
    }

    @GetMapping("/more/energyNotify")
    @ApiOperation(value = "Insight-获取能量通知配置")
    public DataResponse<InsightMoreInformationEnergyNotifyDto> getEnergyNotifyConfig(
            @RequestParam("deviceId") String deviceId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.getEnergyNotifyConfig(clientUserDo, deviceId));
    }

    @OperationLog
    @PostMapping("/more/energyNotify")
    @ApiOperation(value = "Insight-更新能量通知配置")
    public DataResponse<InsightMoreInformationEnergyNotifyDto> updateEnergyNotifyConfig(
            @RequestBody @Valid InsightMoreInformationEnergyNotifyVo insightMoreInformationEnergyNotifyVo
    ) {
        insightMoreInformationEnergyNotifyVo.checkParams();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.updateEnergyNotifyConfig(clientUserDo, insightMoreInformationEnergyNotifyVo));
    }

    @PostMapping("/oem/energy")
    @ApiOperation(value = "OEM-Energy页面")
    public DataResponse<OemEnergyDto> oemEnergy(@RequestBody @Valid OemEnergyVo oemEnergyVo) {
        return DataResponse.success(singlePhaseService.computeOemEnergy(oemEnergyVo));
    }

    @PostMapping("/oem/realTimePower")
    @ApiOperation(value = "OEM-实时功率曲线")
    public DataResponse<OemRealTimePowerDto> oemRealTimePower(
            @RequestBody @Valid HomeNowDeviceRealtimeVo homeNowDeviceRealtimeVo
    ) {
        return DataResponse.success(singlePhaseService.oemRealTimePower(homeNowDeviceRealtimeVo));
    }

    @PostMapping("/oem/historyEnergy")
    @ApiOperation(value = "OEM-历史能耗数据")
    public DataResponse<OemHistoryEnergyDto> oemHistoryEnergy(
            @RequestBody @Valid OemHistoryEnergyVo oemHistoryEnergyVo
    ) {
        oemHistoryEnergyVo.checkParams();
        return DataResponse.success(singlePhaseService.oemHistoryEnergy(oemHistoryEnergyVo));
    }

    @GetMapping("/home/<USER>")
    @ApiOperation(value = "Home-获取最近一周光伏、电网、碳减排、节约标准煤数据(类似于‘根据日期偏移量设备平均耗能’)")
    public DataResponse<HomeDeviceEnergyStatisticsDto> lastWeekSolarAndGridEnergyData(
            @RequestParam("deviceId") String deviceId,
            @ApiParam(value = "填：-7，偏移天数，表示相对于当前日期的偏移量", required = true)
            @RequestParam("offsetDay") Integer offsetDay
    ) {
        if (offsetDay < -7) {
            offsetDay = -7;
        } else if (offsetDay > 7) {
            offsetDay = 7;
        }
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.getLastWeekSolarAndGridEnergyData(clientUserDo, deviceId, offsetDay, null));
    }

    @PostMapping("/device/insight")
    @ApiOperation(value = "Insight-设备Insight功耗页面数据")
    public DataResponse<InsightDeviceDataDto> queryDeviceInsight(
            @RequestBody @Valid InsightDeviceDataVo insightDeviceDataVo
    ) {
        insightDeviceDataVo.checkParams();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePhaseService.queryDeviceInsightData(clientUserDo, insightDeviceDataVo));
    }

    @GetMapping("/mode/info")
    @ApiOperation(value = "Mode-自定义页面信息")
    public DataResponse<CustomizeInfoEzDto> queryCustomizeInfo(@RequestParam("deviceId") String deviceId) {
        CustomizeInfoDto customizeInfoDto = singlePhaseService.readCustomize(deviceId);
        return DataResponse.success(TransformUtil.customizeInfoToEz(customizeInfoDto));
    }

    @OperationLog
    @PostMapping("/mode/infoV2")
    @ApiOperation(value = "Mode-配置自定义页面信息")
    public EmptyResponse configureCustomizeInfoV2(@RequestBody @Valid CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {
        singlePhaseService.writeCustomizeV2(customizeInfoEzV2Vo);
        return EmptyResponse.success();
    }

    @GetMapping("/device/detail")
    @ApiOperation(value = "设备详情")
    public DataResponse<SinglePhaseBaseInfoDto> queryDeviceDetail(@RequestParam("deviceId") String deviceId) {
        return DataResponse.success(singlePhaseService.queryDeviceDetail(deviceId));
    }

    @GetMapping("/device/power_limit")
    @ApiOperation(value = "入户侧功率限制")
    public DataResponse<Integer> powerLimit(@RequestParam("deviceId") Long deviceId) {
        return DataResponse.success(singlePhaseService.getPowerLimit(deviceId));
    }

    @OperationLog
    @PostMapping("/device/power_limit")
    @ApiOperation(value = "保存入户侧功率限制")
    public EmptyResponse savePowerLimit(@RequestBody PowerLimitVO powerLimit) {
        singlePhaseService.savePowerLimit(powerLimit);
        return EmptyResponse.success();
    }

}
