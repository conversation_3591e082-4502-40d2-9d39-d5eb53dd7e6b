package com.weihengtech.ecos.controller.other;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共服务controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/20 10:10
 */
@RestController
@RequestMapping("/common")
@Api(tags = "10_Common services")
public class CommonController {


    @GetMapping("/health")
    @ApiOperation("heart beat")
    public String health() {
        return "heart beat";
    }
}
