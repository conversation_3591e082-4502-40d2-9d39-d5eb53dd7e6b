package com.weihengtech.ecos.controller.price;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;
import com.weihengtech.ecos.model.vos.price.TibberEleVO;
import com.weihengtech.ecos.service.ele.ClientElePriceRetailerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/ele/price/retailer")
@Api(tags = {"25_零售商电价"})
public class ElePriceRetailerController {

    @Resource
    private ClientElePriceRetailerService clientElePriceRetailerService;

    @GetMapping("/get")
    @ApiOperation(value = "查询零售商电价")
    public DataResponse<ClientElePriceRetailerDO> query(@RequestParam String homeId) {
        ClientElePriceRetailerDO elePriceRetailer = clientElePriceRetailerService.queryRetailerPrice(homeId);
        return DataResponse.success(elePriceRetailer);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新零售商电价")
    public EmptyResponse updateRetailerPrice (@RequestBody @Valid ElePriceRetailerVO param) {
        clientElePriceRetailerService.updateRetailerPrice(param);
        return EmptyResponse.success();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除零售商电价")
    public EmptyResponse deleteRetailerPrice(@RequestParam String homeId) {
        clientElePriceRetailerService.deleteRetailerPrice(homeId);
        return EmptyResponse.success();
    }

    @GetMapping("/tibber/homes")
    @ApiOperation(value = "查询tibber家庭列表")
    public DataResponse<List<TibberHomeDTO>> queryTibberHomes(@RequestParam String token) {
        List<TibberHomeDTO> tibberHomeList = clientElePriceRetailerService.queryTibberHomes(token);
        return DataResponse.success(tibberHomeList);
    }

    @PostMapping("/tibber/ele")
    @ApiOperation(value = "查询tibber电价信息")
    public DataResponse<List<EleDayAheadPriceDto>> queryTibberEle(@RequestBody @Valid TibberEleVO param) {
        List<EleDayAheadPriceDto> eleList = clientElePriceRetailerService.queryTibberEle(param);
        return DataResponse.success(eleList);
    }
}
