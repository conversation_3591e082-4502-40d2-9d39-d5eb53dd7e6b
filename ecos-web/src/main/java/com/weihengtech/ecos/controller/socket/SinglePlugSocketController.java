package com.weihengtech.ecos.controller.socket;

import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.annotation.OperationLog;
import com.weihengtech.ecos.model.dtos.socket.SocketHistoryEleDto;
import com.weihengtech.ecos.model.dtos.socket.SocketSwitchLogsDto;
import com.weihengtech.ecos.model.dtos.socket.SocketTimingListDto;
import com.weihengtech.ecos.model.dtos.socket.TuyaSocketBaseInfoDto;
import com.weihengtech.ecos.model.vos.socket.SocketAddTimingVo;
import com.weihengtech.ecos.model.vos.socket.SocketCountdownVo;
import com.weihengtech.ecos.model.vos.socket.SocketHistoryEleVo;
import com.weihengtech.ecos.model.vos.socket.SocketInfoVo;
import com.weihengtech.ecos.model.vos.socket.SocketOverChargeSwitchVo;
import com.weihengtech.ecos.model.vos.socket.SocketRandomTimeMapVo;
import com.weihengtech.ecos.model.vos.socket.SocketSwitchLogsVo;
import com.weihengtech.ecos.model.vos.socket.SocketUpdateTimingVo;
import com.weihengtech.ecos.model.vos.socket.SwitchSocketVo;
import com.weihengtech.ecos.service.socket.SinglePlugSocketService;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.vos.app.home.V2ClientHomeBindDeviceVo;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @program: ecos-server
 * @description: 单插插座控制类
 * @author: jiahao.jin
 * @create: 2024-01-27 16:18
 **/
@RestController
@RequestMapping("/v2/device/singlePlugSocket")
@Api(tags = {"102_singlePlugSocket"})
public class SinglePlugSocketController {

    @Resource
    private SinglePlugSocketService singlePlugSocketService;

    @OperationLog
    @PostMapping("/bind")
    @ApiOperation(value = "绑定Tuya_单插插座设备")
    public EmptyResponse bindClientUserDevice(
            @RequestBody @Valid V2ClientHomeBindDeviceVo v2ClientHomeBindDeviceVo
    ) {
        v2ClientHomeBindDeviceVo.buildParam();
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        singlePlugSocketService.bindClientUserDevice(v2ClientHomeBindDeviceVo, clientUserDo);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/switch")
    @ApiOperation(value = "开关插座")
    public EmptyResponse switchTuyaSocket(
            @RequestBody @Valid SwitchSocketVo switchSocketVo
    ) {
        singlePlugSocketService.switchSocket(switchSocketVo);
        return EmptyResponse.success();
    }

    @PostMapping("/query")
    @ApiOperation(value = "单插插座详情")
    public DataResponse<TuyaSocketBaseInfoDto> queryTuyaSocketInfo(
            @RequestBody @Valid SocketInfoVo socketInfoVo
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        return DataResponse.success(singlePlugSocketService.queryTuyaSocketInfo(clientUserDo, socketInfoVo));
    }

    @OperationLog
    @PostMapping("/time/countdown")
    @ApiOperation(value = "倒计时开关（自动到点开关）")
    public EmptyResponse updateTuyaSocketCountdown(
            @RequestBody @Valid SocketCountdownVo socketCountdownVo
    ) {
        socketCountdownVo.checkParams();
        singlePlugSocketService.updateTuyaSocketCountdown(socketCountdownVo);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/time/random")
    @ApiOperation(value = "随机定时（时间范围内随机开关）")
    public EmptyResponse updateTuyaSocketRandomTime(
            @RequestBody @Valid SocketRandomTimeMapVo socketRandomTimeMapVo
    ) {
        socketRandomTimeMapVo.checkParams();
        singlePlugSocketService.updateTuyaSocketRandomTime(socketRandomTimeMapVo);
        return EmptyResponse.success();
    }

    @OperationLog
    @PostMapping("/time/timing/add")
    @ApiOperation(value = "添加定时开关任务")
    public EmptyResponse addSocketTiming(
            @RequestBody @Valid SocketAddTimingVo socketAddTimingVo
    ) {
        socketAddTimingVo.checkParams();
        singlePlugSocketService.addSocketTiming(socketAddTimingVo);
        return EmptyResponse.success();
    }

    @GetMapping("/time/timing/query")
    @ApiOperation(value = "查询单插定时开关任务列表")
    public DataResponse<List<SocketTimingListDto>> querySocketTimingList(
            @RequestParam("deviceId") String deviceId
    ) {
        return DataResponse.success(singlePlugSocketService.querySocketTimingList(deviceId));
    }

    @OperationLog
    @PostMapping("/time/timing/update")
    @ApiOperation(value = "更新定时开关任务")
    public DataResponse<Boolean> updateSocketTiming(
            @RequestBody @Valid SocketUpdateTimingVo socketUpdateTimingVo
    ) {
        socketUpdateTimingVo.checkParams();
        return DataResponse.success(singlePlugSocketService.updateSocketTiming(socketUpdateTimingVo));
    }

    @OperationLog
    @GetMapping("/time/timing/delete")
    @ApiOperation(value = "删除单插定时开关任务")
    public EmptyResponse deleteSocketTimingTask(
            @RequestParam("taskId") String taskId
    ) {
        ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();
        singlePlugSocketService.deleteSocketTimingTask(String.valueOf(clientUserDo.getId()), taskId);
        return EmptyResponse.success();
    }

    @PostMapping("/logs/switch")
    @ApiOperation(value = "开关日志（分页）")
    public DataResponse<SocketSwitchLogsDto> querySocketSwitchLogs(
            @RequestBody @Valid SocketSwitchLogsVo socketSwitchLogsVo
    ) {
        return DataResponse.success(singlePlugSocketService.querySocketSwitchLogs(socketSwitchLogsVo));
    }

    @PostMapping("/ele")
    @ApiOperation(value = "历史用电量")
    public DataResponse<SocketHistoryEleDto> querySocketHistoryEle(
            @RequestBody @Valid SocketHistoryEleVo socketHistoryEleVo
    ) {
        return DataResponse.success(singlePlugSocketService.querySocketHistoryEle(socketHistoryEleVo));
    }

    @OperationLog
    // 过冲保护接口
    @PostMapping("/overcharge")
    @ApiOperation(value = "过充保护")
    public EmptyResponse switchSocketOverCharge(
            @RequestBody @Valid SocketOverChargeSwitchVo socketOverChargeSwitchVo
    ) {
        singlePlugSocketService.switchSocketOverCharge(socketOverChargeSwitchVo);
        return EmptyResponse.success();
    }

    @OperationLog
    @GetMapping("/clear/power")
    @ApiOperation(value = "清除电量数据")
    public EmptyResponse clearSocketPowerData(
            @RequestParam String deviceId
    ) {
        singlePlugSocketService.clearSocketPowerData(deviceId);
        return EmptyResponse.success();
    }

}
