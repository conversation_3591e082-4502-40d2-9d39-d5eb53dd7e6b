package com.weihengtech.ecos.intercepters;

import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {

	@Override
	public void afterCompletion(
			@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
			@NonNull Object handler, Exception ex
	) throws Exception {
		SecurityContextHolder.clearContext();
	}
}
