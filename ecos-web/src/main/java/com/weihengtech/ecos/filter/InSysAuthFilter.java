package com.weihengtech.ecos.filter;

import cn.hutool.core.collection.ListUtil;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@ToString
@Slf4j
public class InSysAuthFilter extends OncePerRequestFilter {

	private final String accessSecret;
	private final String accessKey;

	public InSysAuthFilter(String accessKey, String accessSecret) {
		this.accessKey = accessKey;
		this.accessSecret = accessSecret;
	}

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest httpServletRequest,
									@NonNull HttpServletResponse httpServletResponse,
									@NonNull FilterChain filterChain) throws ServletException, IOException {
		try {
			String key = httpServletRequest.getHeader("AccessKey");
			String secret = httpServletRequest.getHeader("AccessSecret");
			if (accessKey.equals(key) && accessSecret.equals(secret)) {
				setAuthentication();
			} else {
				SecurityContextHolder.clearContext();
			}
		} catch (Exception e) {
			log.warn("Auth warning !!!! ===> {}", e.getMessage());
		}
		filterChain.doFilter(httpServletRequest, httpServletResponse);
	}

	private void setAuthentication() {
		List<SimpleGrantedAuthority> authorities = ListUtil.toList(new SimpleGrantedAuthority("ROLE_SYSTEM"));
		val usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken("InSystem", null,
				authorities
		);
		SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
	}
}
