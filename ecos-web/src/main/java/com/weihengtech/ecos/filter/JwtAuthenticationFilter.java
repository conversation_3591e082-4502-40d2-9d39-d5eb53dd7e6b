package com.weihengtech.ecos.filter;

import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.config.CustomConfig;
import com.weihengtech.ecos.utils.ClientJwtUtil;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

	private final CustomConfig customConfig;
	private final ClientJwtUtil clientJwtUtil;

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest httpServletRequest,
									@NonNull HttpServletResponse httpServletResponse,
									@NonNull FilterChain filterChain) throws ServletException, IOException {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (null == authentication) {
			try {
				String token = httpServletRequest.getHeader(customConfig.getToken().getHeaderKey())
						.replace(customConfig.getToken().getTypePrefix(), "").trim();
				Optional<Claims> claims = clientJwtUtil.parseAccessToken(token);
				if (claims.isPresent()) {
					setupAuthenticationToken(claims.get());
				} else {
					httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
					httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
					httpServletResponse.setCharacterEncoding("UTF-8");
					httpServletResponse.getWriter().println(JSONUtil.toJsonPrettyStr(EmptyResponse.unauthorized()));
					return;
				}
			} catch (Exception e) {
				httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
				httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
				httpServletResponse.setCharacterEncoding("UTF-8");
				httpServletResponse.getWriter().println(JSONUtil.toJsonPrettyStr(EmptyResponse.unauthorized()));
				return;
			}
		}
		filterChain.doFilter(httpServletRequest, httpServletResponse);
	}

	private void setupAuthenticationToken(Claims claims) {
		String roles = claims.get("authorities", String.class);
		String username = claims.getSubject();
		List<SimpleGrantedAuthority> authorities = Arrays.stream(roles.split(",")).map(SimpleGrantedAuthority::new)
				.collect(Collectors.toList());
		val usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(username, null, authorities);
		SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
	}
}
