package com.weihengtech.ecos.config;

import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 */
public class UrlFilterConfig {

	/**
	 * 需要用户和服务认证判断的资源
	 *
	 * @return 资源路径URL集合
	 */
	public static ArrayList<String> getIncludePathPatterns() {
		ArrayList<String> list = new ArrayList<>(32);
		String[] urls = {"/**"};
		Collections.addAll(list, urls);
		return list;
	}

	/**
	 * 不需要用户和服务认证判断的资源路径
	 *
	 * @return 排除的资源路径URL集合
	 */
	public static ArrayList<String> getExcludePathPatterns() {
		ArrayList<String> list = new ArrayList<>(32);
		String[] urls = {"swagger-ui.html"};
		Collections.addAll(list, urls);
		return list;
	}
}
