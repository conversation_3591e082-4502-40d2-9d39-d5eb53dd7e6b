package com.weihengtech.ecos.config;

import cn.hutool.core.map.MapUtil;
import com.weihengtech.ecos.utils.ClientJwtUtil;
import com.weihengtech.ecos.filter.InSysAuthFilter;
import com.weihengtech.ecos.filter.JwtAuthenticationFilter;
import com.weihengtech.ecos.service.user.impl.UserDetailServiceImpl;
import com.weihengtech.ecos.service.user.impl.UserDetailsPasswordServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.DelegatingPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig extends WebSecurityConfigurerAdapter {

	@Resource
	private UserDetailServiceImpl userDetailService;

	@Resource
	private UserDetailsPasswordServiceImpl userDetailsPasswordService;

	@Resource
	private ClientJwtUtil clientJwtUtil;

	@Resource
	private CustomConfig customConfig;

	@Value("${custom.auth.access-key}")
	private String accessKey;

	@Value("${custom.auth.access-secret}")
	private String accessSecret;

	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http.authorizeRequests(req -> req.antMatchers("/global/**").hasRole("USER").anyRequest().authenticated())
				.addFilterBefore(inSysAuthFilter(), UsernamePasswordAuthenticationFilter.class)
				.addFilterAt(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
				.csrf(AbstractHttpConfigurer::disable);
	}

	@Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
		auth.userDetailsService(userDetailService).userDetailsPasswordManager(userDetailsPasswordService)
				.passwordEncoder(passwordEncoder());
	}

	@Override
	public void configure(WebSecurity web) {
		web.ignoring().antMatchers("/doc.html", "/swagger-ui.html", "/webjars/**", "/swagger-resources", "/v2/api-docs",
				"/guide/**", "/global/token/refresh", "/global/version", "/global/config", "/global/enest/version", "/global/resource/category/tree",
				"/instances/**", "/assets/**", "/actuator/**", "/common/health"
		);
	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		val idForDefault = "bcrypt";
		Map<String, PasswordEncoder> encoders = MapUtil.of(idForDefault, new BCryptPasswordEncoder());
		return new DelegatingPasswordEncoder(idForDefault, encoders);
	}

	private InSysAuthFilter inSysAuthFilter() {
		return new InSysAuthFilter(accessKey, accessSecret);
	}

	private JwtAuthenticationFilter jwtAuthenticationFilter() {
		return new JwtAuthenticationFilter(customConfig, clientJwtUtil);
	}
}
