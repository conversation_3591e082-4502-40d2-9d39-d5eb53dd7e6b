package com.weihengtech.ecos.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@EnableSwagger2
@Configuration
public class SwaggerConfig implements EnvironmentAware {

	private Environment environment;

	@Bean(value = "defaultApi1")
	public Docket defaultApi1() {
		return docket("app", "com.weihengtech.ecos.controller.app");
	}
	@Bean(value = "defaultApi2")
	public Docket defaultApi2() {
		return docket("price", "com.weihengtech.ecos.controller.price");
	}
	@Bean(value = "defaultApi3")
	public Docket defaultApi3() {
		return docket("single", "com.weihengtech.ecos.controller.single");
	}
	@Bean(value = "defaultApi4")
	public Docket defaultApi4() {
		return docket("three", "com.weihengtech.ecos.controller.three");
	}
	@Bean(value = "defaultApi5")
	public Docket defaultApi5() {
		return docket("socket", "com.weihengtech.ecos.controller.socket");
	}
	@Bean(value = "defaultApi6")
	public Docket defaultApi6() {
		return docket("charger", "com.weihengtech.ecos.controller.charger");
	}
	@Bean(value = "defaultApi7")
	public Docket defaultApi7() {
		return docket("other", "com.weihengtech.ecos.controller.other");
	}

	private ApiInfo apiInfo() {
		return new ApiInfoBuilder().title("Ecos Client 文档")
				.description("Ecos 客户端 接口文档")
				.termsOfServiceUrl("http://localhost:20400")
				.version("1.0.0")
				.build();
	}

	private Docket docket(String groupName, String basePackage) {
		return new Docket(DocumentationType.SWAGGER_2)
				.enable("dev".equals(environment.getProperty("spring.profiles.active", "prod")))
				.apiInfo(apiInfo())
				.groupName(groupName).select()
				.apis(RequestHandlerSelectors.basePackage(basePackage))
				.paths(PathSelectors.any()).build()
				.securitySchemes(Collections.singletonList(new ApiKey("Authorization", "Authorization", "header")));
	}

	@Override
	public void setEnvironment(@NotNull Environment environment) {
		this.environment = environment;
	}
}
