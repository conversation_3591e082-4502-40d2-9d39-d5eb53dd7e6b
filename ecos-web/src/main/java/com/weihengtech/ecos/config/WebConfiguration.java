package com.weihengtech.ecos.config;

import com.weihengtech.ecos.intercepters.AuthorizationInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
@Profile({"dev", "prod"})
public class WebConfiguration implements WebMvcConfigurer {

	@Resource
	private AuthorizationInterceptor authorizationInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(authorizationInterceptor).addPathPatterns(UrlFilterConfig.getIncludePathPatterns())
				.excludePathPatterns(UrlFilterConfig.getExcludePathPatterns());
	}

	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {

		registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");

		registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");

		registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
	}
}
