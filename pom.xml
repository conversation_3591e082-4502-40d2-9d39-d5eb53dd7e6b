<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <name>ecos-server</name>
    <description>Ecos业务接口</description>
    <packaging>pom</packaging>
    <modules>
        <module>ecos-application</module>
        <module>ecos-common</module>
        <module>ecos-core</module>
        <module>ecos-web</module>
    </modules>

    <groupId>com.weihengtech.ecos</groupId>
    <artifactId>ecos-server</artifactId>
    <version>1.0.0</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.8.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring.boot.versoin>2.3.8.RELEASE</spring.boot.versoin>
        <mysql.connector.version>8.0.22</mysql.connector.version>
        <mybatis.starter.version>2.1.2</mybatis.starter.version>
        <mybatis.version>3.5.2</mybatis.version>
        <mybatis.plus.starter.version>3.2.0</mybatis.plus.starter.version>
        <mybatis.plus.generator.version>2.0</mybatis.plus.generator.version>
        <druid.version>1.2.9</druid.version>
        <pagehelper.version>1.2.3</pagehelper.version>
        <dynamic.datasource.version>3.4.1</dynamic.datasource.version>
        <hutool.version>5.7.8</hutool.version>
        <autho.jwt.version>3.18.2</autho.jwt.version>
        <knife4j.version>3.0.3</knife4j.version>
        <aliyun.java.sdk.iot.version>7.31.0</aliyun.java.sdk.iot.version>
        <aliyun.java.sdk.core>4.5.6</aliyun.java.sdk.core>
        <iot20180120.version>3.0.0</iot20180120.version>
        <tea.openapi.version>0.0.19</tea.openapi.version>
        <cglib.version>3.2.7</cglib.version>
        <tsdb.version>0.3.6</tsdb.version>
        <redisson.version>3.16.8</redisson.version>
        <jjwt.version>0.11.2</jjwt.version>
        <hystrix.version>2.2.7.RELEASE</hystrix.version>
        <tuya.version>1.1.1</tuya.version>
        <lindorm.java.native.version>1.0.0</lindorm.java.native.version>
        <okhttp.latest.version>4.9.3</okhttp.latest.version>
        <spring.boot.admin.version>2.3.1</spring.boot.admin.version>
        <fastjson.version>1.2.75</fastjson.version>
        <aliyun.sms.version>2.0.9</aliyun.sms.version>
        <xxl.version>2.3.1</xxl.version>
        <prometheus.version>1.9.3</prometheus.version>
        <openfeign.version>2.2.7.RELEASE</openfeign.version>
        <openfeign.okhttp.version>10.10.1</openfeign.okhttp.version>
        <aliyun.sdk.oss.version>3.16.0</aliyun.sdk.oss.version>
        <iot.sdk.version>8.0.1.7-SNAPSHOT</iot.sdk.version>
        <influx.client.version>8.0.0.2-SNAPSHOT</influx.client.version>
        <asyncTool.version>asyncTool-V1.4-79651da39d-1</asyncTool.version>
        <jackson.version>2.11.4</jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Framework -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.versoin}</version>
                <!-- 排除掉默认的日志框架 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${openfeign.okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                <version>${hystrix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                <version>${hystrix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>

            <!-- spring 组件 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.versoin}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>

            <!-- redis -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.versoin}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- MySQL -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-generator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${mybatis.plus.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>

            <!-- sdk -->
            <dependency>
                <groupId>com.aliyun.lindorm</groupId>
                <artifactId>lindorm-tsdb-client</artifactId>
                <version>${lindorm.java.native.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.weihengtech.lib</groupId>
                <artifactId>ecos-iot-sdk</artifactId>
                <version>${iot.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.influxdb</groupId>
                        <artifactId>influxdb-client-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.weihengtech.influx</groupId>
                <artifactId>influx-client</artifactId>
                <classifier>plain</classifier>
                <version>${influx.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gitee.jd-platform-opensource</groupId>
                <artifactId>asyncTool</artifactId>
                <version>${asyncTool.version}</version>
            </dependency>

            <!-- lindorm sdk 依赖冲突 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.latest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp.latest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>iot20180120</artifactId>
                <version>${iot20180120.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>tea-openapi</artifactId>
                <version>${tea.openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>hitsdb-client</artifactId>
                <version>${tsdb.version}</version>
            </dependency>

            <!-- 监控 -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <!-- 工具包 -->
            <!-- http://repo1.maven.org/maven2/com/xuxueli/xxl-job-core/ -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId> <!-- or jjwt-gson if Gson is preferred -->
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-kotlin</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- 单元测试 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Swagger -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>

</project>