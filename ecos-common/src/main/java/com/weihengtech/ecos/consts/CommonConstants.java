package com.weihengtech.ecos.consts;

import cn.hutool.core.collection.ListUtil;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonConstants {

	Integer TAG_DELETE = 0;
	Integer TAG_ACTIVE = 1;
	Integer VERSION_BIND_OFF = 0;
	Integer VERSION_BIND_ON = 1;
	Integer DEVICE_UPGRADE_ING = 2;
	Integer DEVICE_UPGRADE_FINISH = 1;
	Integer DEVICE_UPGRADE_FAILURE = 0;
	Integer DEVICE_STATE_ON = 0;
	Integer DEVICE_STATE_OFF = 1;
	Integer DEVICE_STATE_RESTART = 2;
	Integer DEVICE_MASTER = 1;
	Integer DEVICE_SALVE = 0;
	int PERIOD_DAY = 0;
	int PERIOD_WEEK = 1;
	int PERIOD_MONTH = 2;
	int PERIOD_SEASON = 3;
	int PERIOD_YEAR = 4;
	int PERIOD_LIFETIME = 5;

	// 储能点位
	List<String> METRIC_LIST = ListUtil.toLinkedList(
			// 今日发电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			// 电池今日充电
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			// 电池今日放电
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE,
			// 电网今日卖电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID,
			// 电网今日买电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID
	);

	List<String> METRIC_LIST_V2 = ListUtil.toLinkedList(
			// 光伏总发电量(累计值)
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			// 光伏总发电量(累计值)2
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID,
			// 总充电(累计值)
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			// 总放电(累计值)
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE,
			// 总卖电(累计值)
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID,
			// 总买电(累计值)
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID,
			// 总应急电量(累计值)
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_EPS
	);

	List<String> ELECTRICITY_E_TOTAL_PV_TO_GRID = ListUtil.toLinkedList(
			// 今日发电量2
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID
	);

	List<String> HOME_ENERGY = ListUtil.toLinkedList(
			// 今日发电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			// 今日发电量2
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID,
			// 电池今日充电
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			// 电池今日放电
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE,
			// 电网今日卖电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID,
			// 电网今日买电量
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID
	);

	List<String> SH_REALTIME_POWER = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P
	);

	List<String> SH_REALTIME_POWER_V2 = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// 光伏功率2
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P,
			// 应急电源有功功率2
			TsdbMetricsConstants.EPS_PS,
			// 应急电源有功功率3
			TsdbMetricsConstants.EPS_PT,
			// 电池SOC
			TsdbMetricsConstants.BAT_SOC
	);

	List<String> TH_REALTIME_POWER = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P,
			// 应急电源有功功率2
			TsdbMetricsConstants.EPS_PS,
			// 应急电源有功功率3
			TsdbMetricsConstants.EPS_PT
	);

	List<String> TH_REALTIME_POWER_V2 = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// 光伏功率2
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// 应急电源有功功率1
			TsdbMetricsConstants.EPS_P,
			// 应急电源有功功率2
			TsdbMetricsConstants.EPS_PS,
			// 应急电源有功功率3
			TsdbMetricsConstants.EPS_PT,
			// 电池SOC
			TsdbMetricsConstants.BAT_SOC
	);

	List<String> NOW_RUN_DATA = ListUtil.toLinkedList(
			// 光伏功率
			TsdbMetricsConstants.METER_P_PV,
			// 光伏功率2
			TsdbMetricsConstants.METER_PV_P,
			// 电网功率
			TsdbMetricsConstants.AC_P,
			// 电池功率
			TsdbMetricsConstants.BAT_P,
			// 仪表功率
			TsdbMetricsConstants.METER_P,
			// EPS功率
			TsdbMetricsConstants.EPS_P_1,
			// 电池剩余电量
			TsdbMetricsConstants.BAT_SOC,
			TsdbMetricsConstants.SYS_POWER_CONFIG
	);

	List<String> METER_PV_P = ListUtil.toLinkedList(
			// 光伏功率2
			TsdbMetricsConstants.METER_PV_P
	);

	List<String> BAT_SOC = ListUtil.toLinkedList(
			// 电池SOC
			TsdbMetricsConstants.BAT_SOC,
			TsdbMetricsConstants.BAT_P
	);
	List<String> HOME_DEVICE_SKETCH_V1 = ListUtil.toList(
			TsdbMetricsConstants.METER_P_PV,
			TsdbMetricsConstants.METER_PV_P,
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID,
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE
	);

	List<String> HOME_DEVICE_SKETCH_V2 = ListUtil.toList(
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV,
			TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID,
			TsdbMetricsConstants.BAT_E_TOTAL_CHARGE,
			TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE
	);


	// 单插点位
	List<String> SOCKET_HISTORY_ELE = ListUtil.toLinkedList(
			// 历史电量
			TsdbMetricsConstants.ADD_ELE_SUM
	);

	// 充电桩点位

	LinkedList<String> CHARGE_NOW_RUN_DATA = ListUtil.toLinkedList(
			TsdbMetricsConstants.VOLTAGE_L1,
			TsdbMetricsConstants.VOLTAGE_L2,
			TsdbMetricsConstants.VOLTAGE_L3,
			TsdbMetricsConstants.CURRENT_IMPORT_L1,
			TsdbMetricsConstants.CURRENT_IMPORT_L2,
			TsdbMetricsConstants.CURRENT_IMPORT_L3,
			TsdbMetricsConstants.POWER_ACTIVE_IMPORT
	);

	List<String> CHARGE_HISTORY_CAPACITY = ListUtil.toLinkedList(
			// 历史电量
			TsdbMetricsConstants.ENERGY_ACTIVE_IMPORT_REGISTER
	);


	int GRAPH_HOME = 0;
	int GRAPH_SOLAR = 1;
	int GRAPH_BATTERY = 2;
	int GRAPH_GRID = 3;
	int GRAPH_SOLAR_2 = 4;
	int GRAPH_HOME_V2 = 5;
	int HOME_DEVICE_PERIOD_TODAY = 1;
	int HOME_DEVICE_PERIOD_YESTERDAY = 2;
	int HOME_DEVICE_PERIOD_WEEK = 3;
	int HOME_DEVICE_PERIOD_MONTH = 4;

	int DEVICE_ELINK_CN = 100;
	int DEVICE_TUYA_CN = 200;
	int DEVICE_TUYA_DC = 201;
	int DEVICE_EN_CDZ = 300;

	// 家庭成员标识
	int HOME_MEMBER = 0;
	// 家庭所有者标识
	int HOME_OWNER = 1;

	// 被分享设备家庭标识
	int HOME_SHARED = 0;
	// 普通家庭标识
	int HOME_COMMON = 1;

	// 家庭负载电量
	String LOAD_ELE = "loadEle";
	// 电网取电电量
	String GRID_ELE = "gridEle";
	// 馈网电量
	String FEED_ELE = "feedEle";

}
