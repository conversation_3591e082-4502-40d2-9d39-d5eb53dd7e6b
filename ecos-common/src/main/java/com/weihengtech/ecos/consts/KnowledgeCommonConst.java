package com.weihengtech.ecos.consts;

/**
 * @program: ecos-server
 * @description: 知识库相关常值
 * @author: jiahao.jin
 * @create: 2024-05-09 19:27
 **/
public class KnowledgeCommonConst {

    /**
     * 知识库 本程序请求AuthHeader
     */
    public static final String AUTH_HEADER = "ecos";

    /**
     * 知识库 普通问答地址
     */
    public static final String LLM_CHAT_URL =  "/conversation/llm";

    /**
     * 知识库 流式问答地址
     */
    public static final String LLM_STREAM_CHAT_URL =  "/conversation/llm-stream";

    /**
     * gpt 模型
     */
    public static final String GPT_MODEL_4 = "gpt-4";

    /**
     * 阿里 模型
     */
    public static final String ALI_MODEL = "qwen-max";

    /**
     * 默认历史上下文大小
     */
    public static final int DEFAULT_HISTORY_CHAT_SIZE = 5;

    /**
     * 默认内容token大小
     */
    public static final int DEFAULT_CONTENT_TOKEN_SIZE = 4096;

    /**
     * 预设 客服prompt
     */
    public static final String PROMPT = "When responding, please provide the answer in the language of the question asked, as this is very important! I want you to act as a professional eCactus brand private after-sales or customer service representative in the energy storage field. You should answer users' questions based on your expertise in energy storage, photovoltaics, power, new energy, and the Internet of Things, using the language in which the questions are asked. These questions may involve daily electricity usage, charging, power consumption data, or product usage issues. Users may have limited knowledge in these fields and hope to reduce electricity costs after purchasing energy storage products, combining them with home photovoltaic systems, charging stations, smart sockets, etc., to achieve a certain level of self-generation and self-consumption, and also contribute to environmental protection and carbon neutrality. You need to enhance their user satisfaction. You only need to answer questions related to energy storage, photovoltaics, new energy, electrical, and the Internet of Things, and politely inform users that you cannot answer life-related questions. In this process, you should also avoid asking users about their personal privacy. If you cannot answer the user's question, you should respond in the corresponding language: \"Sorry, as a non-general artificial intelligence model, I am temporarily unable to answer your question.\"";

    /**
     * 预设 主题生成prompt
     */
    public static final String TOPIC_GENERATE_PROMPT = "Use four to five words or phrases to return directly to the brief topic of the sentence, no explanation, no punctuation, no intonation, no extra text, no bolding, if there is no topic, please return directly to the \"new chat\", please return to the language of the question above to return to the language of the corresponding";


    /**
     * 按照语言翻译某段内容prompt
     */
    public static final String TRANSLATE_PROMPT = "请使用【language】语言，翻译这句话：【content】。";
    /**
     * 预设 AI回答
     */
    public static final String AI_ANSWER = "OK";

    /**
     * 系统预设回答消息类型
     */
    public static final String SYSTEM_MESSAGE_TYPE = "system";

    /**
     * 知识库回答消息类型
     */
    public static final String KNOWLEDGE_MESSAGE_TYPE = "knowledge";

    /**
     * 回答消息结束类型
     */
    public static final String END_MESSAGE_TYPE = "end";

    /**
     * 流式接口报错类型
     */
    public static final String ERROR_MESSAGE_TYPE = "error";


}
