package com.weihengtech.ecos.consts;

/**
 * <AUTHOR>
 */
public class RedisRefConstants {

	public static final Integer CACHE_REDIS_TIMEOUT = 60;
	public static final Long CODE_REDIS_TIMEOUT = 10 * 60L;

	public static final String DEVICE_BIND = "BIND:";

	public static final String AI_CHAT = "CHAT:";

	public static String buildDeviceBindKey(String mail, String wifiSn) {
		return DEVICE_BIND + mail + ":" + wifiSn;
	}

	public static String buildAiChatKey(String userId) {
		return AI_CHAT + userId;
	}

	public static final String SPEEDUP = "SPEEDUP:";
	public static final String SPEEDUP_LIKE = "SPEEDUP:*";
	public static final Long SPEEDUP_TUYA_TIMEOUT = 5 * 60L;
	public static final String SPEEDUP_TUYA_FLAG = "11";
	public static final Long SPEEDUP_ELINK_TIMEOUT = 30L;
	public static final String SPEEDUP_ELINK_FLAG = "00";

	public static String buildSpeedupLikeKey(String datacenter) {
		return datacenter + ":" + SPEEDUP_LIKE;
	}

	public static String buildSpeedupRedisKey(String deviceFlag, String datacenter) {
		return datacenter + ":" + SPEEDUP + deviceFlag;
	}

	private static final String EMAIL_FOCUSED_DEVICE_OFFLINE = "EMAIL:FOCUSED:";

	public static String buildFocusedDeviceOfflineKey(Integer focusedId) {
		return EMAIL_FOCUSED_DEVICE_OFFLINE + focusedId;
	}

	public static final String ROLE_PREFIX = "ROLE:";

	public static String buildRoleKey(String role, String serviceName) {
		return ROLE_PREFIX + serviceName + ":" + role;
	}

	public static final String DATACENTER_PREFIX = "DATACENTER:SYNC:";

	public static String buildDatacenterKey(String datacenter) {
		return DATACENTER_PREFIX + datacenter;
	}

	public static final String COST_TODAY_KEY = "COST:TODAY:";

	public static String buildCostTodayKey(Long homeId) {
		return COST_TODAY_KEY + homeId;
	}

	public static final String TIBBER_TODAY_PRICE_KEY = "TIBBER:";

	public static String buildTibberTodayPriceKey(Long homeId) {
		return TIBBER_TODAY_PRICE_KEY + homeId;
	}
}
