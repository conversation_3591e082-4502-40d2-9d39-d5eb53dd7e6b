package com.weihengtech.ecos.consts;

import cn.hutool.core.map.MapUtil;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class TuyaDataPointTypeConstants {

	public static Map<String, Integer> ignoreMap = MapUtil
			.<String, Integer>builder()
			.put("json_command", 1)
			.put("raw_1", 1)
			.put("raw_2", 1)
			.put("raw_3", 1)
			.put("raw_4", 1)
			.put("raw_5", 1)
			.build();

	public static Map<String, Integer> jsonMap = MapUtil
			.<String, Integer>builder()
			.put("j0", 1)
			.put("j1", 1)
			.put("j2", 1)
			.put("j3", 1)
			.put("j4", 1)
			.put("j5", 1)
			.put("j6", 1)
			.put("j7", 1)
			.put("j8", 1)
			.put("j9", 1)
			.put("j10", 1)
			.put("j11", 1)
			.put("j12", 1)
			.put("j13", 1)
			.put("j14", 1)
			.put("j15", 1)
			.put("j16", 1)
			.put("j17", 1)
			.put("j18", 1)
			.put("j19", 1)
			.put("j20", 1)
			.put("j21", 1)
			.put("j22", 1)
			.put("j23", 1)
			.put("j24", 1)
			.put("j25", 1)
			.put("raw_json_0", 1)
			.put("raw_json_1", 1)
			.put("raw_json_2", 1)
			.put("raw_json_3", 1)
			.put("raw_json_4", 1)
			.put("raw_json_5", 1)
			.put("raw_json_6", 1)
			.put("raw_json_7", 1)
			.put("raw_json_8", 1)
			.put("raw_json_9", 1)
			.build();
	
}
