package com.weihengtech.ecos.consts;

import com.weihengtech.ecos.enums.global.MailModelEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
public class MailModel {

	public static MailInfoBO getMailInfo(MailModelEnum mailModelEnum, String... params) {
		MailInfoBO mailInfoBO = new MailInfoBO();
		switch (mailModelEnum) {
			case HUB_REGISTER:
				mailInfoBO.setSubject("WeiHeng Register Code");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case CLIENT_REGISTER:
				mailInfoBO.setSubject("Register Code");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case FORGET_PASSWORD:
				mailInfoBO.setSubject("Forget Password");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case CLIENT_FORGET_PASSWORD:
				mailInfoBO.setSubject("Email verification code");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case HUB_FOCUS_DEVICE_OFFLINE:
				mailInfoBO.setSubject("WeiHeng Focused Device Offline");
				mailInfoBO.setContent("您重点关注的设备【" + params[0] + "】离线了，快去看看吧!");
				break;
			case HUB_CLEAR_SUBMIT:
				mailInfoBO.setSubject("WeiHeng ECOS Hub Delete Account Notify");
				mailInfoBO.setContent("We‘re sorry to see you requested to delete your ECOS Hub Account. \n"
						+ "If you change your mind, you have until " + params[0] + "GMT+08:00 to let us know. "
						+ "Otherwise, all your information and data will be permanently deleted. If this was a mistake, "
						+ "or you want to keep your account after all, please login your ECOS Hub account again and let us know as soon as possible.");
				break;
			case CLIENT_CLEAR_SUBMIT:
				mailInfoBO.setSubject("Delete Account Notify");
				mailInfoBO.setContent("We‘re sorry to see you requested to delete your Account. \n"
						+ "If you change your mind, you have until " + params[0] + "GMT+08:00 to let us know. "
						+ "Otherwise, all your information and data will be permanently deleted. If this was a mistake, "
						+ "or you want to keep your account after all, please login again and let us know as soon as possible.");
				break;
			case HUB_CLEAR:
			case CLIENT_DEL_ACCOUNT:
				mailInfoBO.setSubject("Clear Account");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case CLIENT_BIND_EMAIL:
				mailInfoBO.setSubject("Bind Email");
				mailInfoBO.setContent("code will expire in 5 minutes: <br /> <h1>" + params[0] + "</h1>");
				break;
			case CLIENT_ENERGY_NOTIFY:
				mailInfoBO.setSubject("Energy Notify");
				mailInfoBO.setContent("<h3>Your household electricity consumption today has exceeded the set threshold,"
						+ "the following is an overview of your daily household electricity consumption for the last 7 days:</h3>  <br />"
						+ "<div>\n"
						+ "    <div style='display:flex; justify-content: space-around;background-color: #666; width: 500px; color: white;font-size: 33px;'>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px;'>Min</div>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px;'>Avg</div>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px;'>Max</div>\n"
						+ "    </div>\n"
						+ "    <div style='display:flex; justify-content: space-around;background-color: #eee; width: 500px;'>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px; font-size: 33px;'>"
						+ params[2] + "</div>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px; font-size: 33px;'>"
						+ params[1] + "</div>\n"
						+ "      <div style='display: inline-block;border: none;text-align: center;line-height: 50px; font-size: 33px;'>"
						+ params[0] + "</div>\n" + "    </div>\n" + "</div>");
				break;
			case HELP_EMAIL:
				mailInfoBO.setSubject("After Sale Mail");
				mailInfoBO.setContent("<h3>" + params[0] + "</h3>: \n" + "    " + params[1]);
				break;
			case CLIENT_CLEAR_FINISH:
				mailInfoBO.setSubject("Account deletion reminder");
				mailInfoBO.setContent("Hello!\n" +
						"Your account " + params[0] + " has been deleted and all personal and device data has been permanently erased. We look forward to seeing you again!\n" +
						"This is a system message, please do not reply.\n");
				break;
			default:
		}
		return mailInfoBO;
	}

	@Getter
	@Setter
	@ToString
	public static class MailInfoBO {

		private String subject;

		private String content;
	}
}
