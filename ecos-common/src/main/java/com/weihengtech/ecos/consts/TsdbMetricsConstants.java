package com.weihengtech.ecos.consts;

/**
 * <AUTHOR>
 */
public interface TsdbMetricsConstants {

	/**
	 * 电网有功功率
	 */
	String AC_P = "ac_p";

	/**
	 * R相eps
	 */
	String EPS_P = "eps_p";

	/**
	 * S相eps
	 */
	String EPS_PS = "eps_ps";
	/**
	 * T相eps
	 */
	String EPS_PT = "eps_pt";

	/**
	 * 应急电源有功功率1
	 */
	String EPS_P_1 = "eps_p";
	/**
	 * 电池功率
	 */
	String BAT_P = "bat_p";

	/**
	 * 电池剩余电量
	 */
	String BAT_SOC = "bat_soc";

	/**
	 * 电池健康度
	 */
	String BAT_SOH = "bat_soh";

	/**
	 * 光伏总发电量(累计值)
	 */
	String ELECTRICITY_E_TOTAL_PV = "electricity_e_total_to_grid_pv";

	/**
	 * 光伏总发电量(累计值)2
	 */
	String ELECTRICITY_E_TOTAL_PV_TO_GRID = "electricity_e_total_pv_to_grid";

	/**
	 * 总充电(累计值)
	 */
	String BAT_E_TOTAL_CHARGE = "bat_e_total_charge";

	/**
	 * 总放电(累计值)
	 */
	String BAT_E_TOTAL_DISCHARGE = "bat_e_total_discharge";

	/**
	 * 总卖电(累计值)
	 */
	String ELECTRICITY_E_TOTAL_TO_GRID = "electricity_e_total_to_grid";

	/**
	 * 总买电(累计值)
	 */
	String ELECTRICITY_E_TOTAL_FROM_GRID = "electricity_e_total_from_grid";

	/**
	 * 总应急电量(累计值)
	 */
	String ELECTRICITY_E_TOTAL_EPS = "electricity_e_total_eps";

	/**
	 * 仪表功率
	 */
	String METER_P = "meter_p";

	/**
	 * PV功率1
	 */
	String METER_P_PV = "meter_p_pv";

	/**
	 * PV功率2
	 */
	String METER_PV_P = "meter_pv_p";

	/**
	 * 设备运行状态
	 */
	String SYS_RUN_MODE = "sys_run_mode";

	/**
	 * 电池运行次数(累计值)
	 */
	String BAT_CYCLE_TIME = "bat_cycle_time";

	/**
	 * 系统功率配置
	 */
	String SYS_POWER_CONFIG = "sys_power_config";

	// 单插点位
	/**
	 * 单插历史电量增量
	 */
	String ADD_ELE = "add_ele";

	String ADD_ELE_TIME = "add_ele_time";

	String ADD_ELE_SUM = "add_ele_sum";


	// 充电桩点位
	/**
	 * 充电桩目前最大充电电流
	 */
	String CURRENT_OFFERED = "current_offered";
	/**
	 * 充电桩目前最大充电电流
	 */
	String POWER_ACTIVE_IMPORT_REGISTER = "power_active_import_register";
	/**
	 * 输入到EV持瞬时电流L3
	 */
	String CURRENT_IMPORT_L3 = "current_import_l3";
	/**
	 * 输入到EV持瞬时电流L2
	 */
	String CURRENT_IMPORT_L2 = "current_import_l2";
	/**
	 * 输入到EV持瞬时电流L1
	 */
	String CURRENT_IMPORT_L1 = "current_import_l1";
	/**
	 * 充电桩瞬时充电功率
	 */
	String POWER_ACTIVE_IMPORT = "power_active_import";
	/**
	 * 电压L1
	 */
	String VOLTAGE_L1 = "voltage_l1";
	/**
	 * 电压L2
	 */
	String VOLTAGE_L2 = "voltage_l2";
	/**
	 * 电压L3
	 */
	String VOLTAGE_L3 = "voltage_l3";
	/**
	 * 充电桩当前状态
	 */
	String CONNECTOR_STATUS = "connector_status";
	/**
	 * 充电桩编号（1就是第一把枪，有些桩是两把或者三把枪的）
	 */
	String CONNECTOR_ID = "connector_id";
	/**
	 * 充电事务编号
	 */
	String TRANSACTION_ID = "transaction_id";
	/**
	 * 充电桩功耗
	 */
	String ENERGY_ACTIVE_IMPORT_REGISTER = "energy_active_import_register";

}
