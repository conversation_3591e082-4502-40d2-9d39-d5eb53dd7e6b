package com.weihengtech.ecos.enums.socket;

import com.weihengtech.ecos.enums.charger.ChargerStatusEnum;
import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 单插在线状态
 * @author: jiahao.jin
 * @create: 2024-03-06 15:00
 **/
@Getter
public enum SocketStatusEnum {

    ON(13, "on"),
    OFF(14, "off");

    final int dbCode;
    final String status;

    SocketStatusEnum(int dbCode, String status) {
        this.dbCode = dbCode;
        this.status = status;
    }

    public static Integer getCodeByStatus(String status) {
        for (ChargerStatusEnum chargerStatusEnum : ChargerStatusEnum.values()) {
            if (chargerStatusEnum.getStatus().equals(status)) {
                return chargerStatusEnum.getDbCode();
            }
        }
        return null;
    }
}
