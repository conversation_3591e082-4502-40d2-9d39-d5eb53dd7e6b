package com.weihengtech.ecos.enums.thirdpart;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: 家庭预设prompt枚举类
 * @author: jiahao.jin
 * @create: 2024-05-11 17:10
 **/
public enum KnowledgePresetspromptEnum {
    /**
     * 家庭预设prompt
     */
    ENERGY_CONSUMPTION_MONTH(
            createPrompts(
                    "Home Usage (last month）",
                    "上月用电量",
                    "Hausnutzung (letzter Monat)",
                    "Thuisgebruik (vorige maand)",
                    "Uso doméstico (el mes pasado)",
                    "Utilizzo domestico (mese scorso)",
                    "Utilisation domestique (le mois dernier)",
                    "Uso doméstico (mês passado)",
                    "Użycie domowe (ostatni miesiąc)"
            ),
            createTemplates(
                    "According to statistics, your household [homeName] consumed a total of [Number] kWh of electricity last month.",
                    "据统计，您的家庭[homeName]上个月共用电[Number]kWh.",
                    "Gemäß Statistik hat Ihr Haushalt [homeName] im letzten Monat insgesamt [Number] kWh Strom verbraucht.",
                    "Volgens de statistieken heeft uw huishouden [homeName] de afgelopen maand in totaal [Number] kWh elektriciteit verbruikt.",
                    "Según las estadísticas, su hogar [homeName] ha consumido un total de [Number] kWh de electricidad el mes pasado.",
                    "Secondo le statistiche, la casa [homeName] ha consumato un totale di [Number] kWh di energia elettrica il mese scorso.",
                    "Selon les statistiques, votre foyer [homeName] a consommé au total [Number] kW h d’électricité le mois dernier.",
                    "De acordo com as estatísticas, o seu agregado familiar [homeName] consumiu um total de [Number] kWh de eletricidade no mês passado.",
                    "Według statystyk w zeszłym miesiącu Twoje gospodarstwo [homeName] zużyło łącznie [Number] kWh prądu."
            )),
    SELF_CONSUMPTION_RATE(
            createPrompts(
                    "Self Powered Ratio",
                    "自发自用率",
                    "Selbstversorgungsquote",
                    "Zelfvoorzieningsratio",
                    "Ratio de autoabastecimiento",
                    "Rapporto di autoalimentazione",
                    "Ratio d'autosuffisance",
                    "Taxa de autossuficiência",
                    "Wskaźnik samowystarczalności"
            ),
            createTemplates(
                    "According to statistics, the self-consumption ratio of your household [homeName] is [Number]%.",
                    "据统计，您的家庭[homeName]自发自用率为[Number]%.",
                    "Gemäß Statistik beträgt der Eigenverbrauchsanteil Ihres Haushalts [homeName] [Number] %.",
                    "Volgens de statistieken is de verhouding eigen verbruik van uw huishouden [homeName] [Number] %.",
                    "Según las estadísticas la proporción de autoconsumo de su hogar [homeName] es del [Number] %.",
                    "Secondo le statistiche, il tasso di autoconsumo della casa [homeName] è del [Number] %.",
                    "Selon les statistiques, le taux d’autoconsommation de votre foyer [homeName] est de [Number] %.",
                    "De acordo com as estatísticas, o rácio de autoconsumo do seu agregado familiar [homeName] é de [Number] %.",
                    "Według statystyk stosunek auto-konsumpcji Twojego gospodarstwa [homeName] wyniósł [Number] %."
            )),
    GRID_CONSUMPTION_TODAY(
            createPrompts(
                    "Grid Input (today)",
                    "今日电网取电量",
                    "Netzeinspeisung (heute)",
                    "Netinvoer (vandaag)",
                    "Entrada de red (hoy)",
                    "Ingresso di rete (oggi)",
                    "Entrée réseau (aujourd'hui)",
                    "Entrada da rede (hoje)",
                    "Wejście z sieci (dzisiaj)"
            ),
            createTemplates(
                    "According to statistics, your household [homeName] consumed [Number] kWh of power drawn from the grid today.",
                    "据统计，您的家庭[homeName]今天从电网取电[Number]kWh.",
                    "Gemäß Statistik hat Ihr Haushalt [homeName] heute [Number] kWh Strom aus dem Netz verbraucht.",
                    "Volgens de statistieken heeft uw huishouden [homeName] vandaag [Number] kWh stroom uit het net gehaald.",
                    "Según las estadísticas, su hogar [homeName] ha consumido [Number] kWh de energía obtenida de la red hoy.",
                    "Secondo le statistiche, oggi la casa [homeName] ha consumato [Number] kWh di energia elettrica ricavata dalla rete.",
                    "Selon les statistiques, votre foyer [homeName] a consommé [Number] kW h d’électricité à partir du réseau aujourd’hui.",
                    "De acordo com as estatísticas, o seu agregado familiar [homeName] consumiu hoje [Number] kWh de energia retirada da rede.",
                    "Według statystyk Twoje gospodarstwo [homeName] pobrało dziś [Number] kWh prądu z sieci."
            )),
    PV_GENERATION_YEAR(
            createPrompts(
                    "PV Output (This Year)",
                    "今年光伏发电量",
                    "PV-Leistung (dieses Jahr)",
                    "PV-opbrengst (dit jaar)",
                    "Producción de PV (este año)",
                    "Produzione di PV (quest'anno)",
                    "Production PV (cette année)",
                    "Saída de PV (este ano)",
                    "Wydajność PV (ten rok)"
            ),
            createTemplates(
                    "As of now, your PV system has generated a total of [Number] kWh of electricity this year.",
                    "截至目前，您的光伏系统本年共发电[Number]kWh.",
                    "Momentan hat Ihre PV-Anlage in diesem Jahr insgesamt [Number] kWh Strom erzeugt.",
                    "Momenteel heeft uw PV-systeem dit jaar in totaal [Number] kWh elektriciteit opgewekt.",
                    "En la actualidad, su sistema fotovoltaico ha generado un total de [Number] kWh de electricidad este año.",
                    "Finora, il tuo impianto fotovoltaico ha generato un totale di [Number] kWh di energia elettrica quest’anno.",
                    "À ce jour, votre système photovoltaïque a produit un total de [Number] kW h d’électricité cette année.",
                    "Até ao momento, o seu sistema fotovoltaico produziu um total de [Number] kWh de eletricidade este ano.",
                    "Na teraz Twój system PV wygenerował łącznie [Number] kWh prądu w tym roku."
            )),
    ELECTRICITY_CONSUMPTION_WEEK(
            createPrompts(
                    "Last Week's Electricity Consumption",
                    "上周用电情况",
                    "Detail zur Hausnutzung (letzte Woche)",
                    "Gedetailleerd thuisgebruik (vorige week)",
                    "Detalle del uso doméstico (la semana pasada)",
                    "Dettaglio sull'utilizzo domestico (la scorsa settimana)",
                    "Détail de l'utilisation domestique (la semaine dernière)",
                    "Detalhes do uso doméstico (na última semana)",
                    "Szczegóły użytkowania domowego (w zeszłym tygodniu)"
            ),
            createTemplates(
                    "According to statistics, your home [homeName] electricity consumption last week was:\nMonday: [Week1] kWh\nTuesday: [Week2] kWh\nWednesday: [Week3] kWh\nThursday: [Week4] kWh\nFriday: [Week5] kWh\nSaturday: [Week6] kWh\nSunday: [Week7] kWh",
                    "据统计，上周家庭【homeName】用电量分别是：\n周一：【Week1】kWh\n周二：【Week2】kWh\n周三：【Week3】kWh\n周四：【Week4】kWh\n周五：【Week5】kWh\n周六：【Week6】kWh\n周日：【Week7】kWh",
                    "Gemäß Statistik ist der Stromverbrauch des Haushalts [homeName] in der vorherigen Woche wie folgt:\nMontag: [Week1] kWh\nDienstag: [Week2] kWh\nMittwoch: [Week3] kWh\nDonnerstag: [Week4] kWh\nFreitag: [Week5] kWh\nSamstag: [Week6] kWh\nSonntag: [Week7] kWh",
                    "Volgens de statistieken is het elektriciteitsverbruik van het huishouden [homeName] de afgelopen week als volgt:\nMaandag: [Week1] kWh\nDinsdag: [Week2] kWh\nWoensdag: [Week3] kWh\nDonderdag: [Week4] kWh\nVrijdag: [Week5] kWh\nZaterdag: [Week6] kWh\nZondag: [Week7] kWh",
                    "Según las estadísticas, el consumo de electricidad del hogar [homeName] de la semana pasada es el siguiente:\nLunes: [Week1] kWh\nMartes: [Week2] kWh\nMiércoles: [Week3] kWh\nJueves: [Week4] kWh\nViernes: [Week5] kWh\nSábado: [Week6] kWh\nDomingo: [Week7] kWh",
                    "Secondo le statistiche, il consumo di energia elettrica della settimana scorsa della casa [homeName] è stato il seguente:\nLunedì: [Week1] kWh\nMartedì: [Week2] kWh\nMercoledì: [Week3] kWh\nGiovedì: [Week4] kWh\nVenerdì: [Week5] kWh\nSabato: [Week6] kWh\nDomenica: [Week7] kWh",
                    "Selon les statistiques, la consommation d’électricité du foyer [homeName] la semaine dernière est la suivante :\nLundi : [Week1] kWh\nMardi : [Week2] kWh\nMercredi : [Week3] kWh\nJeudi : [Week4] kWh\nVendredi : [Week5] kWh\nSamedi : [Week6] kWh\nDimanche : [Week7] kWh",
                    "De acordo com as estatísticas, o consumo de eletricidade do agregado familiar [homeName] na semana passada foi o seguinte:\nSegunda-feira: [Week1] kWh\nTerça-feira: [Week2] kWh\nQuarta-feira: [Week3] kWh\nQuinta-feira: [Week4] kWh\nSexta-feira: [Week5] kWh\nSábado: [Week6] kWh\nDomingo: [Week7] kWh",
                    "Według statystyk zużycie prądu gospodarstwa [homeName] w zeszłym tygodniu jest następujące:\nPoniedziałek: [Week1] kWh\nWtorek: [Week2] kWh\nŚroda: [Week3] kWh\nCzwartek: [Week4] kWh\nPiątek: [Week5] kWh\nSobota: [Week6] kWh\nNiedziela: [Week7] kWh"
            )),
    DELETE_HOME(
            createPrompts(
                    "Delete Home",
                    "删除家庭",
                    "Lösche Home-Funktion",
                    "Verwijder functie thuis",
                    "Eliminar función del hogar",
                    "Elimina funzione casa",
                    "Supprimer la fonctionnalité domestique",
                    "Excluir recurso doméstico",
                    "Usuń funkcję domową"
            ),
            createTemplates(
                    "You can delete your home by following these steps: Go to [My] - [Home Management] - [Select Home] - [Delete Home]. Please note that you cannot delete your only home.",
                    "您可以根据以下操作删除您的家庭：前往【我的】-【家庭管理】-【选择家庭】-【删除家庭】，请注意您无法删除唯一的家庭。",
                    "Sie können Ihr Zuhause löschen, indem Sie diesen Schritten folgen: Gehen Sie zu [Ich] - [Hausverwaltung] - [Zuhause auswählen] - [Löschen]. Bitte beachten Sie, dass wenn es nur ein Zuhause gibt, kann es nicht gelöscht werden.",
                    "U kunt uw huis verwijderen door de volgende stappen te volgen: Ga naar [Ik] - [Huisbeheer] - [Selecteer huis] - [Verwijderen]. Houd er rekening mee dat als er slechts één huis is, kan het niet worden verwijderd.",
                    "Puede eliminar su hogar siguiendo estos pasos: Vaya a [Yo] - [Gestión del hogar] - [Seleccionar hogar] - [Eliminar]. Tenga en cuenta que si solo hay un hogar, no se puede eliminar.",
                    "Puoi eliminare la tua casa seguendo questi passaggi: Vai a [Io] - [Gestione casa] - [Seleziona casa] - [Elimina]. Si prega di notare che se c'è solo una casa, non può essere eliminata.",
                    "Vous pouvez supprimer votre domicile en suivant ces étapes : Allez dans [Moi] - [Gestion du foyer] - [Sélectionner domicile] - [Supprimer]. Veuillez noter que s'il n'y a qu'un seul domicile, il ne peut pas être supprimé.",
                    "Você pode excluir sua casa seguindo estes passos: Vá para [Eu] - [Gerenciamento de casa] - [Selecionar casa] - [Excluir]. Por favor, note que se houver apenas uma casa, ela não pode ser excluída.",
                    "Możesz usunąć swój dom, postępując zgodnie z tymi krokami: Przejdź do [Ja] - [Zarządzanie domem] - [Wybierz dom] - [Usuń]. Należy pamiętać, że jeśli istnieje tylko jedno mieszkanie, nie można go usunąć."
            )),
    SET_HOME_WEATHER(
            createPrompts(
                    "Set Home Weather",
                    "设置天气",
                    "Wettereinstellung",
                    "Weerinstelling",
                    "Configuración del clima",
                    "Impostazione meteo",
                    "Réglage météo",
                    "Configuração do clima",
                    "Ustawienia pogodowe"
            ),
            createTemplates(
                    "You can set the weather for your home location by following these steps: [My] - [Home Management] - [Home Location]. Search for the city where your home is located, and you can view the weather on the homepage.",
                    "您可以通过以下步骤设置家庭所在地的天气：【我的】-【家庭管理】-【家庭位置】，搜索家庭所在城市后，即可在首页查看天气。",
                    "Sie können das Haushaltswetter einstellen, indem Sie diesen Schritten folgen: Gehen Sie zu [Ich] - [Hausverwaltung] - [Standort des Hauses], suchen Sie nach der Stadt, in der sich Ihr Zuhause befindet, und dann können Sie das Wetter auf der Startseite anzeigen.",
                    "U kunt het huishoudweer instellen door de volgende stappen te volgen: Ga naar [Ik] - [Huisbeheer] - [Locatie van huis], zoek naar de stad waar uw huis zich bevindt, en dan kunt u het weer bekijken op de startpagina.",
                    "Puede configurar el clima del hogar siguiendo estos pasos: Vaya a [Yo] - [Administración del hogar] - [Ubicación del hogar], busque la ciudad donde se encuentra su hogar y luego podrá ver el clima en la página de inicio.",
                    "Puoi impostare il meteo domestico seguendo questi passaggi: Vai a [Io] - [Gestione casa] - [Posizione casa], cerca la città in cui si trova la tua casa e poi puoi visualizzare il meteo nella homepage.",
                    "Vous pouvez définir la météo de votre domicile en suivant ces étapes : Allez dans [Moi] - [Gestion du foyer] - [Emplacement du foyer], recherchez la ville où se trouve votre domicile, puis vous pourrez consulter la météo sur la page d'accueil.",
                    "Você pode definir o clima doméstico seguindo estas etapas: Vá para [Mim] - [Gerenciamento de casa] - [Localização da casa], pesquise a cidade onde sua casa está localizada e, em seguida, você pode visualizar o clima na página inicial.",
                    "Możesz ustawić pogodę w domu, postępując zgodnie z tymi krokami: Przejdź do [Ja] - [Zarządzanie domem] - [Lokalizacja domu], wyszukaj miasto, w którym znajduje się Twój dom, a następnie możesz zobaczyć pogodę na stronie głównej."
            )),
    OTHER_PROMPT(
            createPrompts(
                    "Other",
                    "其他",
                    "Andere",
                    "Overig",
                    "Otro",
                    "Altro",
                    "Autre",
                    "Outro",
                    "Inny"
            ),
            createTemplates(
                    "Other",
                    "其他",
                    "Andere",
                    "Overig",
                    "Otro",
                    "Altro",
                    "Autre",
                    "Outro",
                    "Inny"
            ));

    private final Map<Locale, String> prompts;
    private final Map<Locale, String> templates;

    KnowledgePresetspromptEnum(Map<Locale, String> prompts, Map<Locale, String> templates) {
        this.prompts = prompts;
        this.templates = templates;
    }

    public String getPrompt(Locale locale) {
        return prompts.getOrDefault(locale, prompts.get(Locale.ENGLISH)); // 默认返回英文
    }

    public String getTemplate(Locale locale) {
        return templates.getOrDefault(locale, templates.get(Locale.ENGLISH)); // 默认返回英文
    }

    // 中文	英文	德语	荷兰语	西班牙语	意大利语	法语	葡萄牙语	波兰语
    private static Map<Locale, String> createPrompts(String english, String chinese, String german, String dutch, String spanish, String italian, String french, String portuguese, String polish) {
        Map<Locale, String> prompts = new HashMap<>();
        prompts.put(Locale.US, english);
        prompts.put(Locale.CHINA, chinese);
        prompts.put(Locale.GERMANY, german);
        prompts.put(new Locale("nl","NL"), dutch);
        prompts.put(new Locale("es","ES"), spanish);
        prompts.put(new Locale("it","IT"), italian);
        prompts.put(Locale.FRANCE, french);
        prompts.put(new Locale("pt","PT"), portuguese);
        prompts.put(new Locale("pl","PL"), polish);
        return prompts;
    }

    private static Map<Locale, String> createTemplates(String english, String chinese , String german, String dutch, String spanish, String italian, String french, String portuguese, String polish) {
        Map<Locale, String> templates = new HashMap<>();
        templates.put(Locale.US, english);
        templates.put(Locale.CHINA, chinese);
        templates.put(Locale.GERMANY, german);
        templates.put(new Locale("nl","NL"), dutch);
        templates.put(new Locale("es","ES"), spanish);
        templates.put(new Locale("it","IT"), italian);
        templates.put(Locale.FRANCE, french);
        templates.put(new Locale("pt","PT"), portuguese);
        templates.put(new Locale("pl","PL"), polish);
        return templates;
    }

    public static List<String> getAllPromptsByLocale(Locale locale) {
        return Arrays.stream(KnowledgePresetspromptEnum.values())
                .filter(preset -> preset != OTHER_PROMPT) // 排除 OTHER_PROMPT
                .map(preset -> preset.getPrompt(locale))
                .collect(Collectors.toList());
    }

    public static List<String> getAllTemplatesByLocale(Locale locale) {
        return Arrays.stream(KnowledgePresetspromptEnum.values())
                .filter(preset -> preset != OTHER_PROMPT) // 排除 OTHER_PROMPT
                .map(preset -> preset.getTemplate(locale))
                .collect(Collectors.toList());
    }
}
