package com.weihengtech.ecos.enums.socket;

import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 单插插座DP点位
 * @author: jiahao.jin
 * @create: 2024-01-28 17:12
 **/
@Getter
public enum SinglePlugSocketPropertyEnum {

    SWITCH_1("开关1", "switch_1"),
    COUNTDOWN_1("开关 1 倒计时", "countdown_1"),
    ADD_ELE("增加电量", "add_ele"),
    CUR_CURRENT("当前电流", "cur_current"),
    CUR_POWER("当前功率", "cur_power"),
    CUR_VOLTAGE("当前电压", "cur_voltage"),
    RELAY_STATUS("上电状态设置", "relay_status"),
    OVERCHARGE_SWITCH("过充保护", "overcharge_switch"),
    LIGHT_MODE("指示灯状态设置", "light_mode"),
    CHILD_LOCK("童锁", "child_lock"),
    CYCLE_TIME("循环定时", "cycle_time"),
    RANDOM_TIME("随机定时", "random_time"),
    SWITCH_INCHING("点动开关", "switch_inching"),
    FAULT("故障告警", "fault"),
    OTHER("其他未知", "other");

    String name;
    String identifier;

    SinglePlugSocketPropertyEnum(String name, String identifier) {
        this.name = name;
        this.identifier = identifier;
    }

    // 根据identifier查找枚举常量
    public static SinglePlugSocketPropertyEnum fromIdentifier(String identifier) {
        for (SinglePlugSocketPropertyEnum value : SinglePlugSocketPropertyEnum.values()) {
            if (value.getIdentifier().equals(identifier)) {
                return value;
            }
        }
        return SinglePlugSocketPropertyEnum.OTHER;
    }
}

