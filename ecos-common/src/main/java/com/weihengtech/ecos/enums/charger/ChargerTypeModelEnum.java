package com.weihengtech.ecos.enums.charger;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @program: ecos-server
 * @description: 充电桩型号枚举
 * @author: jiahao.jin
 * @create: 2024-02-19 11:58
 **/
@AllArgsConstructor
@Getter
public enum ChargerTypeModelEnum {

    SINGLE_0x30(48, 10, "Aloe SH", "WH-ECA-702EUA", "7KW", "2KW","32A", "270V", "140V"),
    SINGLE_0x31(49, 10, "Aloe SH", "WH-ECA-702EUB", "7KW", "2KW","32A","270V", "140V"),
    THREE_0x32(50, 11, "Aloe TH", "WH-ECA-113EUA", "11KW", "4KW","16A", "270V", "140V"),
    THREE_0x33(51, 11, "Aloe TH", "WH-ECA-113EUB", "11KW", "4KW","16A", "270V", "140V");

    final Integer typeCode;
    final Integer typeId;
    final String type;
    final String model;
    final String ratedPower;
    final String minRatedPower;
    final String ratedCurrent;
    final String voltageUp;
    final String voltageDown;

    public static Integer getTypeIdByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.typeId)
                .findFirst()
                .orElse(null);
    }

    public static String getRatedPowerByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.ratedPower)
                .findFirst()
                .orElse(null);
    }
    public static String getMinRatedPowerByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.minRatedPower)
                .findFirst()
                .orElse(null);
    }

    public static String getRatedCurrentByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.ratedCurrent)
                .findFirst()
                .orElse(null);
    }

    public static String getVoltageUpByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.voltageUp)
                .findFirst()
                .orElse(null);
    }

    public static String getVoltageDownByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.voltageDown)
                .findFirst()
                .orElse(null);
    }

    public static Integer getTypeByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.typeId)
                .findFirst()
                .orElse(null);
    }

    public static String getModelByCode(Integer code) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeCode.equals(code))
                .map(i -> i.model)
                .findFirst()
                .orElse(null);
    }

    public static Integer getTypeCodeByModel(String model) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.model.equals(model))
                .map(i -> i.typeCode)
                .findFirst()
                .orElse(null);
    }

    public static ChargerTypeModelEnum getEnumById(Integer typeId) {
        return Arrays.stream(ChargerTypeModelEnum.values())
                .filter(i -> i.typeId.equals(typeId))
                .findFirst()
                .orElse(null);
    }

}

