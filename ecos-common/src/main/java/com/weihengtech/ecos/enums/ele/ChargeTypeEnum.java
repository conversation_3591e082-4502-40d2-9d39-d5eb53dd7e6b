package com.weihengtech.ecos.enums.ele;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 18:35
 */
@AllArgsConstructor
@Getter
public enum ChargeTypeEnum {

    CHARGE(-1, "充电"),
    NONE_CHARGE(0, "不充不放"),
    DISCHARGE(1, "放电");

    /**
     * 步骤
     */
    private final int type;

    /**
     * 步骤名称
     */
    private final String name;
}
