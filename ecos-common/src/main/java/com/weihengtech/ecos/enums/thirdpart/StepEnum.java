package com.weihengtech.ecos.enums.thirdpart;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 18:35
 */
public enum StepEnum {

    STEP_0(0, "未开始"),
    STEP_1(1, "系统详情"),
    STEP_2(2, "添加设备"),
    STEP_3(3, "检查预设配置"),
    STEP_4(4, "转移系统");

    /**
     * 步骤
     */
    private final int stepNum;

    /**
     * 步骤名称
     */
    private final String stepName;

    StepEnum(int stepNum, String stepName) {
        this.stepNum = stepNum;
        this.stepName = stepName;
    }

    public int getStepNum() {
        return stepNum;
    }

    public String getStepName() {
        return stepName;
    }
}
