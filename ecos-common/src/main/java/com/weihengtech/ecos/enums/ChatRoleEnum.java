package com.weihengtech.ecos.enums;

import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 问答角色枚举类
 * @author: jiahao.jin
 * @create: 2024-05-10 10:35
 **/
@Getter
public enum ChatRoleEnum {

    /**
     * AI问答角色枚举类
     */
    SYSTEM(0, "system"), AI(1, "AI"), USER(2, "user"), OTHER(3, "other");
    int code;
    String role;

    ChatRoleEnum(int code, String role) {
        this.code = code;
        this.role = role;
    }

    public static String getRoleByCode(int code) {
        for (ChatRoleEnum roleEnum : ChatRoleEnum.values()) {
            if (roleEnum.code == code) {
                return roleEnum.role;
            }
        }
        // 如果没有找到匹配的code，可以返回一个默认值或者抛出异常
        return OTHER.role;
    }
}
