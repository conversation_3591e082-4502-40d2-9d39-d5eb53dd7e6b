package com.weihengtech.ecos.enums.ele;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 18:35
 */
@Getter
public enum ElePriceTypeEnum {

    FIXED(0, "固定电价"),
    TIME_OF_USE(1, "分时电价"),
    WHOLESALE(2, "批发电价"),
    RETAIL(3, "零售商电价"),
    NULL(999, "无");

    /**
     * 步骤
     */
    private final Integer code;

    /**
     * 步骤名称
     */
    private final String name;

    ElePriceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ElePriceTypeEnum getEnum(Integer code) {
        for (ElePriceTypeEnum e : ElePriceTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return ElePriceTypeEnum.NULL;
    }
}
