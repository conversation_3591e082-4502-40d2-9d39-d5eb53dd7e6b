package com.weihengtech.ecos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系列枚举
 *
 * <AUTHOR>
 * @date 2023/11/8 19:34
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum SeriesEnum {
	Single_phase(101, "Single-phase"),
	Three_phase(102, "Three-phase"),
	NA_Device(103, "NA-Device"),
	AC_Charger(104, "AC-Charger"),
	Smart_Plug(105, "Smart-Plug");

	final Integer id;
	final String code;

	public static boolean isEnergyStorageDevice(int seriesId) {
		return Single_phase.getId() == seriesId || Three_phase.getId() == seriesId ||
				NA_Device.getId() == seriesId;
	}
}
