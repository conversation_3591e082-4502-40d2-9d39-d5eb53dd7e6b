package com.weihengtech.ecos.enums.ele;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 18:35
 */
@Getter
public enum UseDayTypeEnum {

    WEEK(1, "周末"),
    WORK(0, "工作日");

    /**
     * 步骤
     */
    private final int code;

    /**
     * 步骤名称
     */
    private final String name;

    UseDayTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean isWeek(int code) {
        return code == UseDayTypeEnum.WEEK.code;
    }

    public static boolean isWork(int code) {
        return code == UseDayTypeEnum.WORK.code;
    }
}
