package com.weihengtech.ecos.enums.global;

import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 设备具体类型
 * @author: jiahao.jin
 * @create: 2024-02-18 11:50
 **/
@Getter
public enum DeviceTypeInfoEnum {
    ELINK(0, "翼联"),
    TUYA(1, "涂鸦"),
    OCPP(2, "EN+"),
    WH(3, "whIOT平台")
    ;

    int datasource;
    String name;

    DeviceTypeInfoEnum(int datasource, String name) {
        this.datasource = datasource;
        this.name = name;
    }
}
