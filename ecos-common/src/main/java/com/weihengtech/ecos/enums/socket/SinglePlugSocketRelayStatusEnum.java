package com.weihengtech.ecos.enums.socket;

import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 上电状态
 * @author: jiahao.jin
 * @create: 2024-01-28 18:37
 **/
@Getter
public enum SinglePlugSocketRelayStatusEnum {

    power_off("断电", 0),
    power_on("通电", 1),
    last("保持通电前状态", 2),
    ;
    String name;
    Integer value;

    SinglePlugSocketRelayStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }


}
