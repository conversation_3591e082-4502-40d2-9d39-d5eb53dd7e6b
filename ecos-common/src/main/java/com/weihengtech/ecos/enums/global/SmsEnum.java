package com.weihengtech.ecos.enums.global;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;

public enum SmsEnum {

    FORGET_PASSWORD,
    REGISTER,
    CLEAR,
    COMMON,

    PhoneTemplateEnum() {};

    public static Pair<String, String> create(SmsEnum smsEnum, String phone, String code) {
        SmsEnum.PhoneTemplateEnum template = phone.startsWith("86") ?
                EnumUtil.fromString(SmsEnum.PhoneTemplateEnum.class, "DOMESTIC_" + smsEnum.name()) :
                EnumUtil.fromString(SmsEnum.PhoneTemplateEnum.class, "INTERNAL_" + smsEnum.name());
        return Pair.of(template.code, StrUtil.format(template.param, code));
    }

    public enum PhoneTemplateEnum {
        DOMESTIC_FORGET_PASSWORD("SMS_172745017", "{\"code\":\"{}\"}"),
        INTERNAL_FORGET_PASSWORD("SMS_461970875","{\"code\":\"{}\"}"),

        DOMESTIC_CLEAR("SMS_172745021","{\"code\":\"{}\"}"),
        INTERNAL_CLEAR("SMS_462015931","{\"code\":\"{}\"}"),

        DOMESTIC_REGISTER("SMS_172745018","{\"code\":\"{}\"}"),
        INTERNAL_REGISTER("SMS_462020911","{\"code\":\"{}\",\"timeout\":10}"),

        DOMESTIC_COMMON("SMS_462061074","{\"code\":\"{}\"}"),
        INTERNAL_COMMON("SMS_196147086","{\"code\":\"{}\"}")

        ;

        final String param;
        final String code;

        PhoneTemplateEnum(String code, String param) {
            this.param = param;
            this.code = code;
        }

        public String getParam() {
            return param;
        }

        public String getCode() {
            return code;
        }
    }
}
