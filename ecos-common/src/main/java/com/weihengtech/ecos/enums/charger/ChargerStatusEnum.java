package com.weihengtech.ecos.enums.charger;

import lombok.Getter;

/**
 * @program: ecos-server
 * @description: 充电桩状态枚举
 * @author: jiahao.jin
 * @create: 2024-02-19 09:41
 **/
@Getter
public enum ChargerStatusEnum {
    Available(6, "Available"),
    Preparing(7, "Preparing"),
    SuspendedEV(8, "SuspendedEV"),
    Charging(9, "Charging"),
    SuspendedEVSE(10, "SuspendedEVSE"),
    Finishing(11, "Finishing"),
    Fault(12, "Faulted");

    final int dbCode;
    final String status;

    ChargerStatusEnum(int dbCode, String status) {
        this.dbCode = dbCode;
        this.status = status;
    }

    public static Integer getCodeByStatus(String status) {
        for (ChargerStatusEnum chargerStatusEnum : ChargerStatusEnum.values()) {
            if (chargerStatusEnum.getStatus().equals(status)) {
                return chargerStatusEnum.getDbCode();
            }
        }
        return null;
    }
}