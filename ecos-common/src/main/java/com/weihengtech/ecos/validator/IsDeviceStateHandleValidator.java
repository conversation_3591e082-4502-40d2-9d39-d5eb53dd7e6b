package com.weihengtech.ecos.validator;

import cn.hutool.core.collection.ListUtil;
import com.weihengtech.ecos.annotation.validator.IsDeviceStateHandleAction;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
public class IsDeviceStateHandleValidator implements ConstraintValidator<IsDeviceStateHandleAction, String> {

	@Override
	public void initialize(IsDeviceStateHandleAction constraintAnnotation) {
		ConstraintValidator.super.initialize(constraintAnnotation);
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
		LinkedList<String> actionList = ListUtil.toLinkedList("0", "1", "2");
		return actionList.contains(value);
	}
}
