package com.weihengtech.ecos.validator;

import cn.hutool.core.util.StrUtil;
import com.weihengtech.ecos.annotation.validator.IsAllowedVersionName;
import lombok.val;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class IsAllowedVersionNameValidator implements ConstraintValidator<IsAllowedVersionName, String> {

	@Override
	public boolean isValid(String versionName, ConstraintValidatorContext constraintValidatorContext) {
		if (StrUtil.isNotBlank(versionName)) {
			String[] splitStr = versionName.split("-");
			val splitLen = 3;
			val firstPart = 3;
			val secondPart = 5;
			val thirdPart = 2;
			return (versionName.contains("V") && versionName.contains(".")) ||
					(
							splitStr.length == splitLen &&
									splitStr[0].length() == firstPart &&
									splitStr[1].length() == secondPart &&
									splitStr[2].length() == thirdPart
					);
		}
		return false;
	}
}
