package com.weihengtech.ecos.validator;

import cn.hutool.core.util.ReUtil;
import com.weihengtech.ecos.annotation.validator.PasswordValidate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class PasswordValidator implements ConstraintValidator<PasswordValidate, String> {

	@Override
	public boolean isValid(String password, ConstraintValidatorContext constraintValidatorContext) {
		String regex = "^(?=.*[0-9])(?=.*[a-zA-Z]).{8,16}$";
		return ReUtil.isMatch(regex, password);
	}
}
