package com.weihengtech.ecos.validator;

import com.weihengtech.ecos.annotation.validator.IsDeviceEventLevel;
import com.weihengtech.ecos.enums.DeviceEventLevelEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class IsDeviceEventLevelValidator implements ConstraintValidator<IsDeviceEventLevel, String> {

	@Override
	public void initialize(IsDeviceEventLevel constraintAnnotation) {
		ConstraintValidator.super.initialize(constraintAnnotation);
	}

	@Override
	public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
		for (DeviceEventLevelEnum levelEnum : DeviceEventLevelEnum.values()) {
			if (levelEnum.getEnglish().equals(s)) {
				return true;
			}
		}
		String optionalStr = Optional.ofNullable(s).orElse("");
		return optionalStr.length() == 0;
	}
}
