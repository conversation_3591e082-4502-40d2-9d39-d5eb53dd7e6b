package com.weihengtech.ecos.annotation.validator;

import com.weihengtech.ecos.validator.IsDeviceStateHandleValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IsDeviceStateHandleValidator.class)
public @interface IsDeviceStateHandleAction {

	/**
	 * 约束注解校验时的默认输出信息
	 */
	String message() default "不存在的设备操作";

	/**
	 * 约束直接在验证时所属的组别
	 */
	Class<?>[] groups() default {};

	/**
	 * 约束注解的有效负载
	 */
	Class<? extends Payload>[] payload() default {};
}
