package com.weihengtech.ecos;

import com.weihengtech.ecos.utils.InitUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableCircuitBreaker
@EnableTransactionManagement
@EnableWebMvc
@EnableScheduling
@MapperScan(basePackages = "com.weihengtech.ecos.dao")
@EnableRetry
public class EcosClientApplication {

	public static void main(String[] args) {
		InitUtil.init(SpringApplication.run(EcosClientApplication.class, args));
	}
}
