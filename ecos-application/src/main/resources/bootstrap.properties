# spring profiles
spring.profiles.active=${SPRING_PROFILES_ACTIVE:dev}
spring.application.name=EcosClient
server.port=${SERVER_PORT:20400}

# snowflake
snowflake.work=${SNOWFLAKE_WORD:1}
snowflake.datacenter=${SNOWFLAKE_DADACENTER:1}

# feign
feign.client.config.default.loggerLevel=full
feign.client.config.default.connect-timeout=10000
feign.client.config.default.read-timeout=20000
feign.httpclient.enabled=false
feign.okhttp.enabled=true

# hystrix
feign.hystrix.enabled=true
hystrix.command.default.execution.timeout.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=11000
hystrix.command.default.circuitBreaker.requestVolumeThreshold=30
hystrix.command.default.metrics.rollingStats.timeInMilliseconds=5000
hystrix.command.default.circuitBreaker.errorThresholdPercentage=60
hystrix.command.default.circuitBreaker.sleepWindowInMilliseconds=3000

# in-system
custom.auth.access-key=${CUSTOM_ACCESS_KEY:ICPAFMUWOBauFZZQrNpQ}
custom.auth.access-secret=${CUSTOM_ACCESS_SECRET:vQKDzs8EV4aqOzxXPAxU}

# 关闭discovery健康检查
spring.cloud.discovery.client.composite-indicator.enabled=false

# notify服务
custom.url.notify=${CUSTOM_URL_NOTIFY:http://notify.dev.weiheng-tech.com}
custom.notify.token=${CUSTOM_NOTIFY_TOKEN:9YD#=#gHJUas0&ibs-gCUtvoCHR%fSX06!z^3s#srRsk@^}
custom.url.order=${CUSTOM_URL_ORDER:http://localhost:8080}
custom.order.token=${CUSTOM_ORDER_TOKEN:123}
custom.order.project=${CUSTOM_ORDER_PROJECT:6424e1c8e4b03574a656a719}
custom.url.config=${CUSTOM_URL_CONFIG:https://dcdn-config.weiheng-tech.com/test}

# log配置
logging.config=classpath:log4j2-spring.xml
logging.level.org.springframework.boot.actuate.endpoint.EndpointId=ERROR

# 配置数据中心
custom.datacenter.name=${CUSTOM_DATACENTER_NAME:CN}

# 电价系统
custom.ele.salt=${CUSTOM_ELE_SALT:M3T5vWSoK4B4oZu4}
custom.ele.name=${CUSTOM_ELE_NAME:ECOS}

# 天气系统
custom.weather.key=${CUSTOM_WEATHER_KEY:3ec50552d5e44e20b9d18088456f9c03}

# 知识库系统模型
custom.knowledge.model=${CUSTOM_KNOWLEDGE_MODEL:qwen-max}
custom.knowledge.chat.size=${CUSTOM_KNOWLEDGE_CHAT_SIZE:20}

# tsdb数据库类型
tsdb.database.type=${TSDB_DATABASE_TYPE:influxdb}
tsdb.database.elink.type=${TSDB_DATABASE_ELINK_TYPE:influxdb}
tsdb.database.charger.type=${TSDB_DATABASE_CHARGER_TYPE:influxdb}