# mysql
spring.autoconfigure.exclude=com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=10
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=100
spring.datasource.druid.max-wait=3000
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=30
spring.datasource.druid.time-between-eviction-runs-millis=30000
spring.datasource.druid.min-evictable-idle-time-millis=30000
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-return=true
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.remove-abandoned-timeout=120
spring.datasource.druid.connection-error-retry-attempts=3
# mysql 数据源
spring.datasource.dynamic.primary=ecos
spring.datasource.dynamic.strict=true
spring.datasource.dynamic.datasource.ecos.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos.url=${MYSQL_ECOS_URL:************************************************************************************************************************}
spring.datasource.dynamic.datasource.ecos.username=${MYSQL_ECOS_USERNAME:root}
spring.datasource.dynamic.datasource.ecos.password=${MYSQL_ECOS_PASSWORD:test1234}
spring.datasource.dynamic.datasource.ecos-event.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ecos-event.url=${MYSQL_ECOS_EVENT_URL:******************************************************************************************************************************}
spring.datasource.dynamic.datasource.ecos-event.username=${MYSQL_ECOS_EVENT_USERNAME:root}
spring.datasource.dynamic.datasource.ecos-event.password=${MYSQL_ECOS_EVENT_PASSWORD:test1234}
# mybatis-plus
mybatis-plus.mapper-locations=classpath:/mapper/**/*.xml
mybatis-plus.global-config.db-config.id-type=auto
# page helper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=false
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql
# redis
spring.redis.host=${REDIS_HOST:***************}
spring.redis.port=${REDIS_PORT:31270}
spring.redis.database=${REDIS_DB:1}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.timeout=2000
spring.redis.jedis.pool.max-wait=2000
spring.redis.jedis.pool.time-between-eviction-runs=20000
# i18n
spring.messages.basename=messages
# log
logging.level.root=INFO
logging.level.com.weihengtech.dao=WARN
logging.level.com.alibaba.nacos.client.naming=WARN
logging.file.name=D:\\logs\\ecos-client.log
# wifi middleware
elink.bridge.url=${ELINK_BRIDGE_URL:http://***************:20091}
tuya.bridge.url=${TUYA_BRIDGE_URL:http://127.0.0.1:20300}
# custom
custom.avatar.location=D:\\avatar
custom.token.access-expire=${CUSTOM_TOKEN_ACCESS_EXPIRE:604800000}
custom.token.refresh-expire=${CUSTOM_TOKEN_REFRESH_EXPIRE:5184000000}
custom.token.header-key=Authorization
custom.token.type-prefix=Bearer
custom.timezone=${CUSTOM_TIMEZONE:GMT+8}
# lindorm
custom.lindorm.url=${CUSTOM_LINDORM_URL:http://**************:8242}
custom.lindorm.elink.url=${CUSTOM_LINDORM_ELINK_URL:http://**************:8242}
custom.lindorm.charger.url=${CUSTOM_LINDORM_CHARGER_URL:http://**************:8242}
custom.lindorm.elink.database=${CUSTOM_LINDORM_ELINK_DATABASE:default}
custom.lindorm.elink.table=${CUSTOM_LINDORM_ELINK_TABLE:e_linter_by_sn_dev}
custom.lindorm.tuya.database=${CUSTOM_LINDORM_TUYA_DATABASE:default}
custom.lindorm.tuya.table=${CUSTOM_LINDORM_TUYA_TABLE:by_device_sn}
custom.lindorm.charger.database=${CUSTOM_LINDORM_CHARGER_DATABASE:default}
custom.lindorm.charger.table=${CUSTOM_LINDORM_CHARGER_TABLE:charging_point}
# mqtt
ali.cloud.iot.endpoint=${ALI_CLOUD_MQTT_IOT_ENDPOINT:iot.cn-shanghai.aliyuncs.com}
ali.cloud.product.key=${ALI_CLOUD_MQTT_PRODUCT_KEY:a1CcUmvwswd}
ali.cloud.instance-id=${ALI_CLOUD_INSTANCEID:iot-060a5wsr}
# admin
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
# xxl-job
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses2=${XXL_JOB_ADMIN_ADDRESSES2:http://xxl-job-jin.dev.weiheng-tech.com/xxl-job-admin-jin}
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=${XXL_JOB_ACCESS_TOKEN:Qz4&iwdaNkeVLQh&cL}
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=${XXL_JOB_EXECUTOR_APPNAME:ecos-client}
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=${XXL_JOB_EXECUTOR_IP:***********}
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=${XXL_JOB_EXECUTOR_PORT_CLIENT:10400}
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=${XXL_JOB_EXECUTOR_LOGPATH:D:\\logs\\xxl\\client}
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays=${XXL_JOB_EXECUTOR_LOGRETENTIONDAYS:10}
### 是否注册调度器
custom.xxl.job.enable=${XXL_JOB_ENABLE:false}
# url
custom.url.service=${CUSTOM_URL_SERVICE:http://api-ecos-cn.dev.weiheng-tech.com/api/service}
custom.url.ele=${CUSTOM_URL_ELE:http://nginx.dev.weiheng-tech.com:31146}
custom.url.weather.api=${CUSTOM_URL_WEATHER_API:https://api.qweather.com/v7/weather}
custom.url.weather.geoapi=${CUSTOM_URL_WEATHER_GEOAPI:https://geoapi.qweather.com/v2/city}
custom.url.knowledge.api=${CUSTOM_URL_KNOWLEDGE_API:http://knowledge-api.dev.weiheng-tech.com}
# iot
sdk.iot.ecos.base-url=${SDK_IOT_ECOS_BASE_URL:http://ecos-iot.dev.weiheng-tech.com}
sdk.iot.ecos.read-timeout=${SDK_IOT_ECOS_READ_TIMEOUT:15}
# influxdb
influxdb.url=${INFLUXDB_URL:http://influx-proxy.dev.weiheng-tech.com}
influxdb.elink-url=${INFLUXDB_ELINK_URL:http://influx-proxy.dev.weiheng-tech.com}
influxdb.charger-url=${INFLUXDB_CHARGER_URL:http://influx-proxy.dev.weiheng-tech.com}
influxdb.org=${INFLUXDB_ORG:weiheng}
influxdb.token=${INFLUXDB_TOKEN:8ydw+mbBxkpzsrxy}
influxdb.tuya.database=${INFLUXDB_TUYA_DATABASE:iot-ecos}
influxdb.tuya.table=${INFLUXDB_TUYA_TABLE:by_device_sn}
influxdb.elink.database=${INFLUXDB_ELINK_DATABASE:default}
influxdb.elink.table=${INFLUXDB_ELINK_TABLE:e_linter_by_sn_dev}
influxdb.charger.database=${INFLUXDB_CHARGER_DATABASE:charging_point}
influxdb.charger.table=${INFLUXDB_CHARGER_TABLE:charging_point}